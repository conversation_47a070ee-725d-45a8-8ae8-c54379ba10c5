<template>
	<view class="tab-bar">
		<view v-for="(item, index) in tabItems" :key="index"
			:class="['tab-item', { active: currentIndex === index, middle: index === middleIndex }]"
			@click="switchTab(index)">
			<image :src="currentIndex === index ? item.iconActive : item.icon" class="icon" />
			<text>{{ item.label }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIndex: 0,
				middleIndex: 1, // 中间凸出项的索引
				tabItems: [{
						label: '首页',
						icon: '/static/home.png',
						iconActive: '/static/home-active.png'
					},
					{
						label: '',
						icon: '/static/middle.png',
						iconActive: '/static/middle-active.png'
					},
					{
						label: '我的',
						icon: '/static/user.png',
						iconActive: '/static/user-active.png'
					},
				],
			};
		},
		methods: {
			switchTab(index) {
				this.currentIndex = index;
				const pages = ['/pages/home/<USER>', '/pages/publish/publish', '/pages/user/user'];
				uni.switchTab({
					url: pages[index],
				});
			},
		},
	};
</script>

<style scoped>
	.tab-bar {
		display: flex;
		justify-content: space-around;
		align-items: center;
		height: 300rpx;
		background-color: #ffffff;
		border-top: 1px solid #eaeaea;
	}

	.tab-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
	}

	.icon {
		width: 50rpx;
		height: 50rpx;
	}

	.middle {
		width: 100rpx;
		height: 100rpx;
		background-color: #55bb8a;
		color: #ffffff;
		border-radius: 50%;
		padding: 10rpx;
		margin-top: -20rpx;
		/* 使中间项凸出 */
	}

	.active {
		color: #55bb8a;
	}
</style>