<template>
	<view class="mainContainer">
		<threenav style="background-color: #2bb98f;height: 150rpx;" title="异常问题"></threenav>
		<view class="tabletitle">
			<view class="titletag"></view>
			<view class="titletext">
				异常问题
			</view>
		</view>
		<view class="liuchengtitle" style="padding: 10rpx 40rpx;">
			<view style="margin-right: 40rpx;font-weight: bold;">工单编号</view>
			<view style="font-weight: bold;">{{listData.appNo}}</view>
		</view>
		<!-- 列表 -->
		<view class="listcontainer">
			<view class="datatext">
				<view class="datacontainer">
					<text style="font-weight: bold;">{{listData.workOrderType}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<text style="font-weight: bold;">{{listData.abnorDesc}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<text style="font-weight: bold;">{{listData.checkDate}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
		},
		data() {
			return {
				show: false,
				radio: '',
				value: '',
				listData:{},
			}
		},
		onLoad(options) {
			if (options.data) {
				this.listData = JSON.parse(decodeURIComponent(options.data));
			}
		},
		onReady() {

		},
		methods: {
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	.custom-select {
		width: 100%;
		position: relative;
	}

	.select-box {
		border: 1rpx solid #ddd;
		width: 100%;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.arrow {
		float: right;
		border: solid #999;
		border-width: 0 2rpx 2rpx 0;
		padding: 6rpx;
		margin-top: 6rpx;
	}

	.down {
		transform: rotate(45deg);
	}

	.up {
		transform: rotate(-135deg);
	}

	.dropdown {
		position: absolute;
		width: 100%;
		border: 1rpx solid #eee;
		background: #fff;
		z-index: 999;
	}

	.dropdown-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.mainContainer {
		.liuchengtitle{
			display: flex;
			align-items: center;
			justify-content: left;
		}
		.tabletitle {
			.titletag {
				width: 10rpx;
				height: 30rpx;
				background-color: #07ac7c;
				border-radius: 4rpx;
			}
		
			.titletext {
				font-size: 34rpx;
				margin-left: 20rpx;
				font-weight: bold;
			}
		
			display: flex;
			justify-content: left;
			align-items: center;
			padding: 40rpx 0 20rpx 20rpx;
		}
		.popupcontainer {
			height: 1500rpx;
			padding: 0 40rpx;

			.poptitle {
				text-align: center;
				font-size: 36rpx;
				font-weight: bold;
				padding: 50rpx 0;
			}

			.formitem {

				// font-weight: bold;
				.calendarContainer {
					width: 100%;

					.calInput {
						width: 100%;
						// margin-right: 30rpx;
					}
				}
			}

			.ubutton {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 400rpx 0 0;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			margin: 20rpx;

			.datatext {
				padding: 10rpx 0 10rpx 0rpx;

				.datacontainer {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 30rpx 20rpx 30rpx 20rpx;

					.datatitle {
						width: 300rpx;
						color: darkgray;
						font-weight: bold;
					}
				}
			}

		}
	}
</style>