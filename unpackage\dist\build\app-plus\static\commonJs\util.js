import qs from 'qs';
export const bizCode = '2010700011';
export const url = 'http://10.131.136.68:17002/fqd2test/WXAPI/RequestSBCF';
export function getUUID() {
    var s = [];
    var hexDigits = '0123456789abcdef';
    for (var i = 0; i < 32; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23];
    var uuid = s.join('');
    return uuid;
}

export function getCurrentTimestamp() {
    return Date.now();
}

export function getQueryStringObj() {
    // 从URL中提取查询字符串部分
    const _queryString = window.location.href.split('?')[1];

    if (!_queryString) return null;

    // 创建一个正则表达式，去除url最后拼接的路由
    const regex = /#\/.*$/;

    const queryString = _queryString.trim().replace(regex, '');

    const queryStringObj = qs.parse(queryString);

    return queryStringObj;
}

export const port = getQueryStringObj()?.port||'';

const utils = {
    bizCode,
    url,
    getUUID,
    getCurrentTimestamp,
    port
};

export default utils;