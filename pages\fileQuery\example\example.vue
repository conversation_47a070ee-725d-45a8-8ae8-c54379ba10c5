<template>
	<view class="example-container">
		<!-- 基本使用 -->
		<custom-navbar title="示例页面"></custom-navbar>
		
		<view class="content">
			<view class="section">
				<view class="section-title">基本用法</view>
				<view class="code-block">
					<text>&lt;custom-navbar title="示例页面"&gt;&lt;/custom-navbar&gt;</text>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">自定义左侧按钮</view>
				<view class="demo-navbar">
					<custom-navbar title="自定义左侧">
						<template #left>
							<svg-icon name="home" color="#000000" size="24"></svg-icon>
						</template>
					</custom-navbar>
				</view>
				<view class="code-block">
					<text>&lt;custom-navbar title="自定义左侧"&gt;
  &lt;template #left&gt;
    &lt;svg-icon name="home" color="#000000" size="24"&gt;&lt;/svg-icon&gt;
  &lt;/template&gt;
&lt;/custom-navbar&gt;</text>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">自定义右侧按钮</view>
				<view class="demo-navbar">
					<custom-navbar title="自定义右侧">
						<template #right>
							<svg-icon name="plus" color="#000000" size="24"></svg-icon>
						</template>
					</custom-navbar>
				</view>
				<view class="code-block">
					<text>&lt;custom-navbar title="自定义右侧"&gt;
  &lt;template #right&gt;
    &lt;svg-icon name="plus" color="#000000" size="24"&gt;&lt;/svg-icon&gt;
  &lt;/template&gt;
&lt;/custom-navbar&gt;</text>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">自定义标题</view>
				<view class="demo-navbar">
					<custom-navbar>
						<template #title>
							<view class="custom-title">
								<text class="title-text">自定义标题</text>
								<text class="subtitle-text">副标题</text>
							</view>
						</template>
					</custom-navbar>
				</view>
				<view class="code-block">
					<text>&lt;custom-navbar&gt;
  &lt;template #title&gt;
    &lt;view class="custom-title"&gt;
      &lt;text class="title-text"&gt;自定义标题&lt;/text&gt;
      &lt;text class="subtitle-text"&gt;副标题&lt;/text&gt;
    &lt;/view&gt;
  &lt;/template&gt;
&lt;/custom-navbar&gt;</text>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">隐藏返回按钮</view>
				<view class="demo-navbar">
					<custom-navbar title="隐藏返回按钮" :showBack="false"></custom-navbar>
				</view>
				<view class="code-block">
					<text>&lt;custom-navbar title="隐藏返回按钮" :showBack="false"&gt;&lt;/custom-navbar&gt;</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import svgIcon from '@/components/svg-icon.vue';
	
	export default {
		components: {
			svgIcon
		},
		data() {
			return {};
		},
		methods: {
			handleClick() {
				uni.showToast({
					title: '按钮被点击',
					icon: 'none'
				});
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f8f8f8;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.example-container {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.content {
	flex: 1;
	padding: 15px;
}

.section {
	margin-bottom: 20px;
	background-color: #FFFFFF;
	border-radius: 8px;
	padding: 15px;
	box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.section-title {
	font-size: 16px;
	font-weight: 500;
	color: #1d2129;
	margin-bottom: 10px;
}

.code-block {
	background-color: #f5f7fa;
	padding: 10px;
	border-radius: 6px;
	margin-top: 10px;
	
	text {
		font-family: monospace;
		font-size: 12px;
		color: #666;
		word-break: break-all;
		white-space: pre-wrap;
	}
}

.demo-navbar {
	margin: 10px 0;
	border: 1px solid #eee;
	border-radius: 8px;
	overflow: hidden;
}

.custom-title {
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.title-text {
		font-size: 16px;
		color: #1d2129;
	}
	
	.subtitle-text {
		font-size: 12px;
		color: #86909c;
		margin-top: 2px;
	}
}
</style> 