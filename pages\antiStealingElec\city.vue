<template>
	<view class="city-container">
		<!-- 自定义导航栏 -->
		<custom-navbar :title="navTitle" :show-back="true" @leftClick="goBack" background-color="#00c389"
			title-color="#FFFFFF" />

		<!-- 城市选择主体内容 -->
		<view class="city-content">
			<!-- 地市单位 -->
			<view class="section-title">电力公司</view>
			<view class="city-grid">
				<view v-for="(city,index) in citiesList" :key="city.mgtOrgCode" class="city-item"
					:class="{ active: selectedCityIndex == index }" @click="selectCity(city,index)">
					{{ city.shortName }}
				</view>
			</view>

			<!-- 区县单位 -->
			<view class="section-title">供电公司</view>
			<!-- 加载状态 -->
			<view class="loading-container" v-if="isLoading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 区县列表 -->
			<view class="city-grid" v-else>
				<view v-for="district in districtsList" :key="district.mgtOrgCode" class="city-item" :class="{ 
						'active': selectedDistrict === district.mgtOrgCode,
						'long-text': district.shortName.length > 6
					}" @click="selectDistrict(district)">
					{{ district.shortName }}
				</view>
				<!-- 无数据提示 -->
				<view class="empty-tip" v-if="districtsList.length === 0">
					<text>暂无区县单位数据</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		data() {
			return {
				navBarHeight: 44,
				selectedCityIndex: null, // 默认选中安徽
				selectedDistrict: '', // 选中的区县ID
				navTitle: "", //导航标题
				// 城市列表数据
				citiesList: [
					{
						mgtOrgCode:"31102",
						shortName:'上海市电力公司'
					}
				],
				// 区县列表数据
				districtsList: [
					{
						mgtOrgCode:"31401",
						shortName:'市区供电公司'
					},
					{
						mgtOrgCode:"31402",
						shortName:'市南供电公司'
					},
					{
						mgtOrgCode:"31403",
						shortName:'浦东供电公司'
					},
					{
						mgtOrgCode:"31404",
						shortName:'崇明供电公司'
					},
					{
						mgtOrgCode:"31405",
						shortName:'长兴供电公司'
					},
					{
						mgtOrgCode:"31406",
						shortName:'市北供电公司'
					},
					{
						mgtOrgCode:"31409",
						shortName:'嘉定供电公司'
					},
					{
						mgtOrgCode:"31415",
						shortName:'奉贤供电公司'
					},
					{
						mgtOrgCode:"31416",
						shortName:'松江供电公司'
					},
					{
						mgtOrgCode:"31417",
						shortName:'金山供电公司'
					},
					{
						mgtOrgCode:"31418",
						shortName:'青浦供电公司'
					}
				],

				// 加载状态
				isLoading: false,
				sURL: '',
				token: "",
				userInfo: {},
				mappId: "",
				poolId: "",
				mgtOrgCode:"",
				username:"",
				isMockData:true,
				nameCode:""
			};
		},
		onLoad() {
			// 获取导航栏高度以便设置内容区域的paddingTop
			this.getNavBarHeight();
			if(this.isMockData) {
				
			}else{
				this.init();
			}
			
		},
		onReady() {
		},
		methods: {
			init2() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
					vm.nameCode = data?.nameCode;
				});
			},
			init() {
				try {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							"data": JSON.stringify({
								"bizCode": bizCode,
								"espFlowId": getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryUserInfoByBody",
									"data": {
										"operatorId": this.nameCode,
									}
								}
							})
						},
						success: (res) => {
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									this.mgtOrgCode = rtnData.data.mgtOrgCode;
									this.loadDistrictsByCity(this.mgtOrgCode)
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				}
			},
			// 获取导航栏高度
			getNavBarHeight() {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				// 状态栏高度
				const statusBarHeight = systemInfo.statusBarHeight;
				// 导航栏高度，不同平台可能有差异
				let navBarHeight = 44; // 默认导航栏高度

				// 不同平台可能需要不同的高度计算
				// #ifdef APP-PLUS
				navBarHeight = 44;
				// #endif

				// #ifdef MP-WEIXIN
				navBarHeight = 48;
				// #endif

				this.navBarHeight = statusBarHeight + navBarHeight;
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 选择城市
			selectCity(city, index) {
				console.log(city)
				this.selectedCityIndex = index;
				this.selectedDistrict = ''; // 清空已选区县
				if(this.isMockData) {
					setTimeout(() => {
						// 选择完成后返回上一页
						uni.navigateBack({
							success: () => {
								// 通过事件总线或其他方式传递选中的城市和区县数据
								const selectedCity = this.citiesList[this.selectedCityIndex]
								uni.$emit('citySelected', {
									city: selectedCity,
									district: city
								});
							}
						});
					}, 300);
				}else{
					// 根据城市编码加载区县数据
					this.loadDistrictsList(city.mgtOrgCode);
				}
				
			},

			// 根据城市编码加载区县数据
			loadDistrictsByCity(cityCode) {
				if (!cityCode) return;
				this.isLoading = true;
				this.districtsList = []; // 清空当前区县列表
				uni.request({
					url: `http://127.0.0.1:${port}/xczs/forward/for`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json',
						'token':this.token
					},
					data: {
						token: this.token,
						method:"PutHuaYun",
						uri:url,
						data: JSON.stringify({
							"bizCode": bizCode,
							"espFlowId": getUUID(),
							"espRsvField1": "",
							"espRsvField2": "",
							"espRsvField3": "",
							"espSign": "",
							"espTimestamp": getCurrentTimestamp(),
							"espInformation": {
								"service": "AseCommonController",
								"method": "queryNextOrgByBody",
								"data": {
									"mgtOrgCode": cityCode
								},
							}
						})
					},
					success: (res) => {
						console.log(res)
						if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
								this.citiesList = rtnData.data;
								this.loadDistrictsList(rtnData.data[0].mgtOrgCode);
								this.isLoading = false;
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
						}else{
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					},
					fail: (error) => {
						this.isLoading = false;
						uni.showToast({
							title: '暂无数据',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			//加载区县
			loadDistrictsList(districtCode) {
				if (!districtCode) return;
				this.isLoading = true;
				uni.request({
					url: `http://127.0.0.1:${port}/xczs/forward/for`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json',
						'token':this.token
					},
					data: {
						token: this.token,
						method:"PutHuaYun",
						uri:url,
						data: JSON.stringify({
							"bizCode": bizCode,
							"espFlowId": getUUID(),
							"espRsvField1": "",
							"espRsvField2": "",
							"espRsvField3": "",
							"espSign": "",
							"espTimestamp": getCurrentTimestamp(),
							"espInformation": {
								"service": "AseCommonController",
								"method": "queryNextOrgByBody",
								"data": {
									"mgtOrgCode": districtCode
								},
							}
						})
					},
					success: (res) => {
						console.log(res)
						if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
								this.districtsList = rtnData.data;
								this.isLoading = false;
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
						}else{
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					},
					fail: (error) => {
						this.isLoading = false;
						uni.showToast({
							title: '暂无数据',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			// 选择区县
			selectDistrict(district) {
				this.selectedDistrict = district.mgtOrgCode;
				// 选择完区县后可能需要返回上一页或执行其他操作
				setTimeout(() => {
					// 选择完成后返回上一页
					uni.navigateBack({
						success: () => {
							// 通过事件总线或其他方式传递选中的城市和区县数据
							const selectedCity = this.citiesList[this.selectedCityIndex]
							uni.$emit('citySelected', {
								city: selectedCity,
								district: district
							});
						}
					});
				}, 300);
			}
		}
	};
</script>

<style lang="scss" scoped>
	.city-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.city-content {
		flex: 1;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
		padding-bottom: 30rpx;
	}

	.section-title {
		font-size: 28rpx;
		color: #999;
		padding: 30rpx 30rpx 20rpx;
		font-weight: normal;
	}

	.city-grid {
		display: flex;
		flex-wrap: wrap;
		padding: 0 20rpx;
		margin-bottom: 10rpx;
		min-height: 100rpx;
		/* 确保空状态下仍有一定高度 */
	}

	.city-item {
		width: calc(33.33% - 20rpx);
		height: 70rpx;
		margin: 10rpx;
		background-color: #ffffff;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #333;
		box-sizing: border-box;
		text-align: center;

		&.active {
			background-color: #07ac7c;
			color: #ffffff;
			font-weight: 500;
		}

		&.long-text {
			width: calc(50% - 20rpx);
			font-size: 28rpx;
		}
	}

	/* 加载状态 */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #07ac7c;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.loading-text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #999;
	}

	/* 错误提示 */
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0;
	}

	.error-text {
		font-size: 28rpx;
		color: #ff5a5f;
		margin-bottom: 20rpx;
	}

	.retry-btn {
		width: 180rpx;
		height: 70rpx;
		background-color: #07ac7c;
		color: white;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
	}

	/* 无数据提示 */
	.empty-tip {
		width: 100%;
		padding: 40rpx 0;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #999;
		font-size: 28rpx;
	}
</style>