<template>
	<view class="mainContainer">
		<custom-navbar title="供服详情"></custom-navbar>
		<!-- 查询 -->
		<view class="searchContainer">
			<view class="calendarContainer">
				<uni-datetime-picker v-model="queryDateStart" type="date" placeholder="开始日期" :end="queryDateEnd"
					@change="handleStartDate">
				</uni-datetime-picker>
				<view style="margin: 0 10rpx;">-</view>
				<uni-datetime-picker v-model="queryDateEnd" type="date" placeholder="结束日期" :start="queryDateStart"
					@change="handleEndDate">
				</uni-datetime-picker>
			</view>
			<view class="buttonContainer">
				<u-button type="primary" style="color: #fcfefd;width: 300rpx;" color="#07ac7c" text="查询"
					@click="search"></u-button>
				<u-button type="default" style="border: 1px solid lightgray;width: 300rpx;" text="重置"
					@click="reset"></u-button>
			</view>
		</view>
		<!-- 柱状图 -->
		<view class="charts-box">
			<text class="chartstitle">查处情况图</text>
			<view v-if="dataShow"
				style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
				<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
				<view style="color: gainsboro;">
					数据为空
				</view>
			</view>
			<qiun-data-charts v-if="chartShow" type="column" :opts="opts" :chartData="chartData" :ontouch="true" />
		</view>
		<!-- 列表 -->
		<view v-if="dataShow"
			style="padding: 20rpx 40rpx 20rpx 40rpx;margin: 20rpx;height: 500rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;">
			<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
			<view style="color: gainsboro;">
				数据为空
			</view>
		</view>
		<view class="listcontainer" v-for="(item,index) in listArray" :key="index">
			<view class="listtitle">详情</view>
			<view class="list">
				<view class="listleft">
					<view class="lefttitle">{{item.date}}</view>
					<view class="listone">
						<view class="listdata">
							<view>线索数</view>
							<view style="color:black;">{{item.total}}</view>
						</view>
						<view class="listdata">
							<view>检查数</view>
							<view style="color:black;">{{item.checkNum}}</view>
						</view>
						<view class="listdata">
							<view>查实数</view>
							<view style="color:black;">{{item.realNum}}</view>
						</view>
						<view class="listdata">
							<view>处理数</view>
							<view style="color:black;">{{item.handleNum}}</view>
						</view>
					</view>
					<view class="listtwo">
						<view class="listdata">
							<view>处理金额(元)</view>
							<view style="color:black;">{{item.money}}</view>
						</view>
						<view class="listdata">
							<view>线索检查率</view>
							<view style="color:black;">{{item.checkRate}}%</view>
						</view>
						<view class="listdata">
							<view>线索查实率</view>
							<view style="color:black;">{{item.realRate}}%</view>
						</view>
						<view class="listdata">
							<view>窃电处理率</view>
							<view style="color:black;">{{item.handleRate}}%</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import qiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
	import utils from '@/static/commonJs/util.js'
	export default {
		components: {
			qiunDataCharts
		},
		data() {
			return {
				testdev:true,
				chartShow: false,
				dataShow: true,
				urlMgtorgCode: "",
				listArray: [],
				queryDateStart: "2025-04-25",
				queryDateEnd: "2025-06-25",
				show: false,
				value: '',
				chartData: {},
				opts: {
					color: ["#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					background: "white",
					padding: [15, 15, 15, 5],
					touchMoveLimit: 24,
					enableScroll: true,
					legend: {},
					xAxis: {
						disableGrid: true,
						scrollShow: true,
						itemCount: 4
					},
					yAxis: {
						// format:(val)=>{
						// 	return Math.floor(val)
						// },
						minInterval: 1,
						data: [{
							min: 0
						}]
					},
					extra: {
						xAxis: {
							itemGap: 15,
						},
						column: {
							categoryGap: 8,
							type: "group",
							width: 15,
							activeBgColor: "#000000",
							activeBgOpacity: 0.08,
							linearType: "custom",
							seriesGap: 5,
							linearOpacity: 0.5,
							barBorderCircle: true,
							customColor: [
								"#FAC858", "#EE6666", "#73C0DE", "#3CA272"
							]
						}
					}
				}
			}
		},
		onLoad(options) {
			// this.init();
			this.urlMgtorgCode = options.mgtOrgCode;
			this.search();
		},
		onReady() {},
		methods: {
			reset() {
				this.queryDateEnd = ""
				this.queryDateStart = ""
			},
			handleStartDate(value) {
				this.queryDateStart = value
				if (this.queryDateEnd && new Date(this.queryDateEnd) < new Date(value)) {
					this.queryDateEnd = ''
				}
			},
			handleEndDate(value) {
				this.queryDateEnd = value
			},
			async search() {
				if (this.testdev) {
					this.chartShow = true;
					this.dataShow = false;
					let dateArray = [2025.04,2025.05,2025.06];
					let totalArray = [421,389,437];
					let checkNumArray = [419,374,421];
					let realNumArray = [180,184,182];
					let handleNumArray = [167,160,155];
					this.listArray = [{
						total:"421",
						checkNum:"419",
						realNum:"180",
						handleNum:"167",
						money:"1965526.32",
						checkRate:"99.52",
						realRate:"42.76",
						handleRate:"39.67"
					}]
					let res = {
						categories: dateArray,
						series: [{
								name: "线索数",
								data: totalArray
							},
							{
								name: "检查数",
								data: checkNumArray
							},
							{
								name: "查实数",
								data: realNumArray
							},
							{
								name: "处理数",
								data: handleNumArray
							}
						]
					};
					this.chartData = JSON.parse(JSON.stringify(res));
				}else{
					// 调用后端登录接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "AppOrderController",
										"method": "orderStatByDate",
										"data": {
											"mgtOrgCode": this.urlMgtorgCode,
											"queryDateEnd": this.queryDateEnd,
											"queryDateStart": this.queryDateStart
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.chartShow = true;
							this.dataShow = false;
							this.listArray = data;
							let dateArray = [];
							let totalArray = [];
							let checkNumArray = [];
							let realNumArray = [];
							let handleNumArray = [];
							data.forEach(item => {
								dateArray.push(item.date)
								totalArray.push(item.total)
								checkNumArray.push(item.checkNum)
								realNumArray.push(item.realNum)
								handleNumArray.push(item.handleNum)
							})
							let res = {
								categories: dateArray,
								series: [{
										name: "线索数",
										data: totalArray
									},
									{
										name: "检查数",
										data: checkNumArray
									},
									{
										name: "查实数",
										data: realNumArray
									},
									{
										name: "处理数",
										data: handleNumArray
									}
								]
							};
							this.chartData = JSON.parse(JSON.stringify(res));
						} else {
							this.chartShow = false;
							this.dataShow = true;
						}
					} catch (error) {
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
						this.chartShow = false;
						this.dataShow = true;
					}
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	page {
		background-color: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	.mainContainer {
		.searchContainer {
			::v-deep .u-popup__content .u-popup__content__close {
				display: none !important;
			}

			background-color: white;
			padding: 30rpx 10rpx 15rpx;
			border-radius: 20rpx;
			margin: 20rpx 20rpx;

			.calendarContainer {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 30rpx;

				.calText {
					font-weight: bold;
					padding: 0 30rpx 0 0;
				}
			}

			.buttonContainer {
				padding-top: 30rpx;
				padding-bottom: 20rpx;
				padding-left: 30rpx;
				padding-right: 30rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
		}

		.charts-box {
			height: 600rpx;
			background-color: white;
			border-radius: 20rpx;
			padding: 20rpx 40rpx 20rpx 40rpx;
			margin: 20rpx;

			.chartstitle {
				font-weight: bold;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			margin: 20rpx;

			.listtitle {
				font-weight: bold;
				padding: 20rpx 40rpx;
			}

			.list {
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-top: 1px solid silver;
				color: gray;

				.listleft {
					flex: 1;
					padding-bottom: 40rpx;

					.lefttitle {
						padding: 30rpx 10rpx;
					}

					.listone {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.listdata {
							padding: 10rpx 40rpx;
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							font-size: 24rpx;

							view {
								margin-bottom: 20rpx;
							}
						}
					}

					.listtwo {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.listdata {
							padding: 10rpx 8rpx;
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							font-size: 24rpx;

							view {
								margin-bottom: 20rpx;
							}
						}
					}
				}
			}
		}
	}
</style>