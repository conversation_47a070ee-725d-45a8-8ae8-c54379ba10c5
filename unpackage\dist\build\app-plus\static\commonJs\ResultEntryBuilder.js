const RET_SUCCESS = "0";
const RET_ERROR = "1";
const RET_PROCESS = "2";

function buildErrorEntry(msg) {
	var result = {
		"RT_F": RET_ERROR,
		"RT_D": msg
	}
	return result;
}

function buildSuccessEntry(msg, data) {
	var result = {
		"RT_F": RET_SUCCESS,
		"RT_D": msg,
		"DATA": data
	}
	return result;
}

function buildProcessEntry(msg) {
	var result = {
		"RT_F": RET_PROCESS,
		"RT_D": msg
	}
	return result;
}

export default {
	"buildErrorEntry": buildErrorEntry,
	"buildSuccessEntry": buildSuccessEntry,
	"buildProcessEntry": buildProcessEntry
}
/*
var ResultEntryBuilder = {
	default: this,
	buildErrorEntry: function(msg) {
		var result = {
			"RT_F": RET_ERROR,
			"RT_D": msg
		}
		return result;
	},
	buildSuccessEntry: function(msg, data) {
		var result = {
			"RT_F": RET_SUCCESS,
			"RT_D": msg,
			"DATA": data
		}
		return result;
	},
	buildProcessEntry: function(msg) {
		var result = {
			"RT_F": RET_PROCESS,
			"RT_D": msg
		}
		return result;
	},
}
module.exports = ResultEntryBuilder;
 */
