<template>
	<view class="problem-detail-container">
		<!-- 使用公共导航栏组件 -->
		<custom-navbar 
			title="问题详情" 
			:show-back="true" 
			@leftClick="goBack"
			background-color="#00c389"
			title-color="#FFFFFF"
		/>
		
		<!-- 问题详情内容 -->
		<scroll-view 
			class="detail-scroll" 
			scroll-y 
			id="detailScroll"
			:scroll-top="scrollTop"
		>
			<!-- 问题信息部分 -->
			<view class="detail-section">
				<!-- 问题类型 -->
				<view class="problem-type-row">
					<view class="label">问题类型</view>
					<view class="value type-value">{{ problemDetail.questionTypeName }}</view>
				</view>
				
				<!-- 问题标题 -->
				<view class="problem-row">
					<view class="label">标题</view>
					<view class="value">{{ problemDetail.questionTitle }}</view>
				</view>
				
				<!-- 问题内容 -->
				<view class="problem-row">
					<view class="label">内容</view>
					<view class="value">{{ problemDetail.questionCount }}</view>
				</view>
				
				<!-- 提问人信息 -->
				<view class="problem-row">
					<view class="label">提问人</view>
					<view class="value">
						<text>{{ problemDetail.submitterName }}</text>
						<text class="company">{{ problemDetail.mgtOrgName }}</text>
					</view>
				</view>
				
				<!-- 提问时间 -->
				<view class="problem-row">
					<view class="label">提问时间</view>
					<view class="value time-value">{{ problemDetail.questionTime }}</view>
				</view>
			</view>
			
			<!-- 分隔线 -->
			<view class="divider"></view>
			
			<!-- 回复列表 -->
			<view class="reply-section">
				<view class="reply-header">全部回复 ({{ replyList.length }})</view>
				<view class="reply-list">
					<view class="reply-item" v-for="(item, index) in replyList" :key="index">
						<view class="reply-top">
							<view class="reply-user-info">
								<text class="username">{{ item.responderName }}</text>
								<text class="company">{{ item.mgtOrgName }}</text>
							</view>
							<text class="reply-time">{{ item.replyTime }}</text>
						</view>
						<view class="reply-content">{{ item.replyCont }}</view>
					</view>
				</view>
				<!-- 添加一个底部占位元素，用于滚动定位 -->
				<view id="scrollBottom"></view>
			</view>
		</scroll-view>
		
		<!-- 底部输入框 -->
		<view class="input-footer">
			<view class="input-area">
				<view class="input-box">
					<input 
						type="text" 
						v-model="replyContent" 
						placeholder="说点什么吧..." 
						placeholder-style="color: #C8C9CC;"
					/>
				</view>
				<view class="send-btn" @click="sendReply">
					<uni-icons type="paperplane-filled" color="#FFFFFF" size="36rpx"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import CustomNavbar from '@/components/custom-navbar.vue';
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				scrollTop: 0,
				oldScrollTop: 0,
				replyContent: '',
				token:null,
				nameCode:'',
				currentUser: {
					username: '',
					company: '',
					questionId: null,
					userId:null
				},
				problemDetail: {
					questionTypeName:'',
					questionTitle:'',
					questionCount:'',
					submitterName:'',
					mgtOrgName:'',
					questionTime:''
				},
				replyList: [],
				isMockData:true
			};
		},
		onLoad(options) {
			const item = JSON.parse(decodeURIComponent(options.item));
			console.log('获取上页面参数',item)
			if(item) {
				this.currentUser.questionId = item.questionId;
				this.currentUser.username = item.submitterName;
				this.currentUser.company = item.mgtOrgName;
				this.currentUser.userId = this.nameCode;
				this.problemDetail ={
					...item
				}
				this.getReplyData(item);
			}
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
					vm.nameCode = data?.nameCode;
				});
			},
			goBack() {
				// uni.navigateBack();
				uni.navigateTo({
					url: '/pages/problemManage/problemList'
				});
			},
			getReplyData(item) {
				if(this.isMockData) {
					this.replyList  = [
						{
							'questionId':'1793583533075009538',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'replyId':'1793836252660355074',
							'responderId':'T00033391',
							'responderName':'岳恒',
							'replyCont':'okokkokookokkokookokkokookokkokookokkokookokkokookokkokookokkoko',
							'replyTime':'2024-05-24'
						},
						{
							'questionId':'1793583533075009538',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'replyId':'1793836178979016705',
							'responderId':'T00033391',
							'responderName':'岳恒',
							'replyCont':'好的  好的 好的 好的 好的 好的 好的 好的 好的 好的 ',
							'replyTime':'2024-05-24'
						}
					]
				}else{
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"MobileQuestionManageController",
									"method":"questionDetail",
									"data": {
										"questionId":item.questionId,
										"replyCont":this.replyContent,
										"mgtOrgCode":item.mgtOrgCode,
										"distLv":item.distLv,
										"responderId":this.currentUser.userId
									},
								}
							})
						},
						success: (res) => {
							if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
								 this.replyList = rtnData.data;
								 // if(rtnData.message) {
								 // 	uni.showToast({
								 // 		title: rtnData.message,
								 // 		icon: 'none',
								 // 	});
								 // }
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
			},
			sendReply() {
				if (!this.replyContent.trim()) {
					uni.showToast({
						title: '请输入回复内容',
						icon: 'none'
					});
					return;
				}
				
				this.getReplyData(this.problemDetail);
				this.replyContent = '';
				
				// 滚动到底部 - 使用更可靠的方法
				this.$nextTick(() => {
					// 方法1：使用scroll-view的scrollToElement
					const scrollView = uni.createSelectorQuery().in(this).select('#detailScroll');
					const scrollBottom = uni.createSelectorQuery().in(this).select('#scrollBottom');
					
					scrollBottom.boundingClientRect(bottomRect => {
						scrollView.boundingClientRect(viewRect => {
							// 强制重新渲染滚动条
							this.scrollTop = this.oldScrollTop;
							this.$nextTick(() => {
								// 计算需要滚动的位置
								const scrollDistance = 100000; // 使用一个足够大的值确保滚动到底部
								this.oldScrollTop = scrollDistance;
								this.scrollTop = scrollDistance;
							});
						}).exec();
					}).exec();
				});
			},
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			}
		}
	}
</script>

<style lang="scss">
.problem-detail-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.detail-scroll {
	flex: 1;
	overflow: hidden;
}

.detail-section {
	background-color: #FFFFFF;
	padding: 0;
}

.problem-type-row {
	display: flex;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 2rpx solid #f5f5f5;
	
	.label {
		color: #262626;
		font-size: 28rpx;
	}
	
	.type-value {
		color: #00c389;
		font-size: 28rpx;
		font-weight: 500;
	}
}

.problem-row {
	display: flex;
	padding: 30rpx;
	border-bottom: 2rpx solid #f5f5f5;
	
	&:last-child {
		border-bottom: none;
	}
	
	.label {
		width: 160rpx;
		color: #262626;
		font-size: 28rpx;
		flex-shrink: 0;
	}
	
	.value {
		flex: 1;
		color: #8c8c8c;
		font-size: 28rpx;
		line-height: 1.5;
		text-align: right;
		.company {
			color: #8c8c8c;
			font-size: 26rpx;
			margin-left: 20rpx;
		}
	}
	
	.time-value {
		color: #8c8c8c;
	}
}

.divider {
	height: 20rpx;
	background-color: #f5f5f5;
}

.reply-section {
	background-color: #FFFFFF;
	padding: 30rpx;
	
	.reply-header {
		font-size: 32rpx;
		font-weight: 500;
		color: #262626;
		margin-bottom: 30rpx;
	}
	
	.reply-list {
		.reply-item {
			padding-bottom: 30rpx;
			margin-bottom: 30rpx;
			border-bottom: 2rpx solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
				margin-bottom: 0;
			}
			
			.reply-top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				
				.reply-user-info {
					.username {
						font-size: 28rpx;
						color: #262626;
						margin-right: 20rpx;
					}
					
					.company {
						font-size: 24rpx;
						color: #8c8c8c;
					}
				}
				
				.reply-time {
					font-size: 24rpx;
					color: #8c8c8c;
				}
			}
			
			.reply-content {
				font-size: 28rpx;
				color: #262626;
				line-height: 1.6;
				word-wrap: break-word;
			}
		}
	}
}

.input-footer {
	height: 100rpx;
	background-color: #FFFFFF;
	border-top: 2rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	
	.input-area {
		display: flex;
		align-items: center;
		width: 100%;
		
		.input-box {
			flex: 1;
			height: 72rpx;
			background-color: #f5f5f5;
			border-radius: 36rpx;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			
			input {
				width: 100%;
				height: 100%;
				font-size: 28rpx;
			}
		}
		
		.send-btn {
			width: 72rpx;
			height: 72rpx;
			border-radius: 50%;
			background-color: #00c389;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 20rpx;
		}
	}
}
</style>
