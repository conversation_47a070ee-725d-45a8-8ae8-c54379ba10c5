<template>
	<view class="file-query-container">
		<!-- 使用自定义导航栏组件 -->
		<custom-navbar title="档案查询" :path="path"></custom-navbar>

		<!-- 功能卡片区域 -->
		<view class="content-area">
			<view class="card-grid">
				<!-- 第一行卡片 -->
				<view class="card-row">
					<view class="card-item" hover-class="card-hover" @click="navigateTo('userDataQuery')">
						<view class="card-inner">
							<view class="icon-box icon-green">
								<svg-icon name="yonghushuju" color="#3CC792" :size="iconSize"></svg-icon>
							</view>
							<text class="card-text">用户数据查询</text>
						</view>
					</view>
					<view class="card-item" hover-class="card-hover" @click="navigateTo('areaView')">
						<view class="card-inner">
							<view class="icon-box icon-orange">
								<svg-icon name="taiqushitu" color="#F49B2C" :size="iconSize"></svg-icon>
							</view>
							<text class="card-text">台区视图</text>
						</view>
					</view>
				</view>

				<!-- 第二行卡片 -->
				<view class="card-row">
					<view class="card-item" hover-class="card-hover" @click="navigateTo('keyAreaScreen')">
						<view class="card-inner">
							<view class="icon-box icon-red">
								<svg-icon name="zhongdiantaiqu" color="#FF5A5F" :size="iconSize"></svg-icon>
							</view>
							<text class="card-text">重点台区筛查</text>
						</view>
					</view>
					<view class="card-item" hover-class="card-hover" @click="navigateTo('areaVisitDetail')">
						<view class="card-inner">
							<view class="icon-box icon-cyan">
								<svg-icon name="gongzuoxiangqing" color="#1CBBB4" :size="iconSize"></svg-icon>
							</view>
							<text class="card-text small-text">台区违窃工作详情</text>
						</view>
					</view>
				</view>

				<!-- 第三行卡片 -->
				<view class="card-row">
					<view class="card-item" hover-class="card-hover" @click="navigateTo('jobMonitor')">
						<view class="card-inner">
							<view class="icon-box icon-blue">
								<svg-icon name="jiankong" color="#4A89DC" :size="iconSize"></svg-icon>
							</view>
							<text class="card-text">工单监控</text>
						</view>
					</view>
					<view class="card-item" hover-class="card-hover" @click="navigateTo('indicatorBoard')">
						<view class="card-inner">
							<view class="icon-box icon-purple">
								<svg-icon name="zhibiao" color="#8A70D6" :size="iconSize"></svg-icon>
							</view>
							<text class="card-text">指标看板</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				iconSize: 80,
				path:window.location.hash
			};
		},
		onLoad() {
		},
		methods: {
			// 页面导航
			navigateTo(page) {
				uni.navigateTo({
					url: `/pages/fileQuery/${page}/${page}`
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	.file-query-container {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background: #f8f8f8;
	}

	/* 内容区域 */
	.content-area {
		flex: 1;
		overflow-y: auto;
		background: #f8f8f8;
	}

	/* 卡片网格 */
	.card-grid {
		padding: 30rpx 32rpx;
	}

	.card-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 30rpx;
	}

	.card-item {
		width: 48%;
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.03);
		background-color: #FFFFFF;
	}

	.card-hover {
		transform: scale(0.98);
		transition: all 0.2s;
		opacity: 0.9;
	}

	.card-inner {
		height: 280rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 30rpx 20rpx;
		background-color: #FFFFFF;
	}

	/* 渐变背景 - 完全匹配原型图 */
	.orange-green-gradient {
		background: linear-gradient(to bottom, #f68d04, #6dbd4e);
	}

	.icon-box {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		background-color: #f5f5f5;
		overflow: hidden;
		box-sizing: border-box;
	}

	.icon-green {
		background-color: #dcf7eb;
	}

	.icon-orange {
		background-color: #feead1;
	}

	.icon-orange-dark {
		background-color: #feead1;
	}

	.icon-blue {
		background-color: #e1eefb;
	}

	.icon-purple {
		background-color: #e9e1ff;
	}

	.icon-cyan {
		background-color: #d7f6f5;
	}

	.icon-red {
		background-color: #ffe5e5;
	}

	.card-text {
		font-size: 32rpx;
		color: #1d2129;
		font-weight: normal;
		text-align: center;
		line-height: 1.4;
		margin-top: 24rpx;
		width: 100%;
		padding: 0 10rpx;
		box-sizing: border-box;
		word-break: break-word;
	}

	.small-text {
		font-size: 30rpx;
		letter-spacing: -0.6rpx;
	}

	/* 功能服务卡片样式 */
	.function-card {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		margin: 30rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
	}
</style>