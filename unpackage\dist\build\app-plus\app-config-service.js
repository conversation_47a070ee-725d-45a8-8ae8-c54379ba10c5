
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/fileQuery/index","pages/toolBox/dataRead/dataList","pages/toolBox/dataRead/dataItem","pages/toolBox/lawLibrary/lawList","pages/toolBox/expertLibrary/expertList","pages/toolBox/caseLibrary/caseList","pages/antiStealingElec/list","pages/problemManage/problemList","pages/fileQuery/userDataQuery/userFileQuery","pages/fileQuery/userDataQuery/historyEvents","pages/fileQuery/userDataQuery/abnormalDetail","pages/antiStealingElec/submit","pages/antiStealingElec/city","pages/antiStealingElec/query","pages/fileQuery/keyAreaScreen/keyAreaResults","pages/fileQuery/areaView/powersupplyCompany","pages/fileQuery/areaView/supplyService","pages/fileQuery/areaView/supplyServicelist","pages/antiStealingElec/statistics","pages/fileQuery/userDataQuery/userDataQuery","pages/fileQuery/keyAreaScreen/keyAreaScreen","pages/fileQuery/areaView/areaView","pages/fileQuery/example/example","pages/fileQuery/areaVisitDetail/areaVisitDetail","pages/fileQuery/jobMonitor/mainMonitor","pages/fileQuery/jobMonitor/timeMonitor","pages/fileQuery/jobMonitor/orderDetail","pages/fileQuery/jobMonitor/orderDetail","pages/fileQuery/jobMonitor/orderProblem","pages/fileQuery/jobMonitor/orderFullDetail","pages/fileQuery/jobMonitor/liuchenghuanjie","pages/fileQuery/indicatorBoard/indicatorBoard","pages/fileQuery/jobMonitor/jobMonitor","pages/fileQuery/userDataQuery/queryResult","pages/fileQuery/userDataQuery/loadData","pages/fileQuery/userDataQuery/powerData","pages/fileQuery/userDataQuery/abnormal","pages/fileQuery/userDataQuery/lineLoss","pages/problemManage/problemReport","pages/problemManage/problemDetail","pages/antiStealingElec/detail","pages/antiStealingElec/submit","pages/toolBox/tool","pages/toolBox/caseLibrary/caseDetail","pages/toolBox/caseLibrary/caseSearch","pages/toolBox/expertLibrary/expertSearch","pages/toolBox/lawLibrary/lawSearch","pages/toolBox/lawLibrary/lawDetail","pages/toolBox/dataRead/dataResult"],"window":{"navigationBarTextStyle":"white","navigationBarTitleText":"uni-app","navigationStyle":"custom","navigationBarBackgroundColor":"#ffffff","backgroundColor":"#F8F8F8","rpxCalcMaxDeviceWidth":960,"rpxCalcBaseDeviceWidth":375,"rpxCalcIncludeWidth":750},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"1001046","compilerVersion":"4.45","entryPagePath":"pages/fileQuery/index","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/fileQuery/index","meta":{"isQuit":true},"window":{}},{"path":"/pages/toolBox/dataRead/dataList","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/dataRead/dataItem","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/lawLibrary/lawList","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/expertLibrary/expertList","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/caseLibrary/caseList","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/antiStealingElec/list","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/problemManage/problemList","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/userDataQuery/userFileQuery","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/userDataQuery/historyEvents","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/userDataQuery/abnormalDetail","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/antiStealingElec/submit","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/antiStealingElec/city","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/antiStealingElec/query","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/keyAreaScreen/keyAreaResults","meta":{},"window":{}},{"path":"/pages/fileQuery/areaView/powersupplyCompany","meta":{},"window":{}},{"path":"/pages/fileQuery/areaView/supplyService","meta":{},"window":{}},{"path":"/pages/fileQuery/areaView/supplyServicelist","meta":{},"window":{}},{"path":"/pages/antiStealingElec/statistics","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/userDataQuery/userDataQuery","meta":{},"window":{"navigationBarBackgroundColor":"#00c087"}},{"path":"/pages/fileQuery/keyAreaScreen/keyAreaScreen","meta":{},"window":{}},{"path":"/pages/fileQuery/areaView/areaView","meta":{},"window":{}},{"path":"/pages/fileQuery/example/example","meta":{},"window":{}},{"path":"/pages/fileQuery/areaVisitDetail/areaVisitDetail","meta":{},"window":{}},{"path":"/pages/fileQuery/jobMonitor/mainMonitor","meta":{},"window":{}},{"path":"/pages/fileQuery/jobMonitor/timeMonitor","meta":{},"window":{}},{"path":"/pages/fileQuery/jobMonitor/orderDetail","meta":{},"window":{}},{"path":"/pages/fileQuery/jobMonitor/orderProblem","meta":{},"window":{}},{"path":"/pages/fileQuery/jobMonitor/orderFullDetail","meta":{},"window":{}},{"path":"/pages/fileQuery/jobMonitor/liuchenghuanjie","meta":{},"window":{}},{"path":"/pages/fileQuery/indicatorBoard/indicatorBoard","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/jobMonitor/jobMonitor","meta":{},"window":{}},{"path":"/pages/fileQuery/userDataQuery/queryResult","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/userDataQuery/loadData","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/userDataQuery/powerData","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/userDataQuery/abnormal","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/fileQuery/userDataQuery/lineLoss","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/problemManage/problemReport","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/problemManage/problemDetail","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/antiStealingElec/detail","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/tool","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/caseLibrary/caseDetail","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/caseLibrary/caseSearch","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/expertLibrary/expertSearch","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/lawLibrary/lawSearch","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/lawLibrary/lawDetail","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/toolBox/dataRead/dataResult","meta":{},"window":{"navigationBarTitleText":""}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
