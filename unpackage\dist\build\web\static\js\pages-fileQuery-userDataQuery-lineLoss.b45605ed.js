(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-fileQuery-userDataQuery-lineLoss"],{"066d":function(a,t,e){"use strict";e.r(t);var o=e("aaae"),i=e("b8f4");for(var r in i)["default"].indexOf(r)<0&&function(a){e.d(t,a,(function(){return i[a]}))}(r);e("e178");var n=e("828b"),d=Object(n["a"])(i["default"],o["b"],o["c"],!1,null,"feaddfb4",null,!1,o["a"],void 0);t["default"]=d.exports},"992b":function(a,t,e){var o=e("c86c");t=o(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-feaddfb4]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-feaddfb4]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-feaddfb4]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-feaddfb4]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-feaddfb4]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-feaddfb4]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-feaddfb4]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-feaddfb4]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-feaddfb4]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-feaddfb4]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-feaddfb4]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-feaddfb4]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-feaddfb4]::after{border:none}.u-hover-class[data-v-feaddfb4]{opacity:.7}.u-primary-light[data-v-feaddfb4]{color:#ecf5ff}.u-warning-light[data-v-feaddfb4]{color:#fdf6ec}.u-success-light[data-v-feaddfb4]{color:#f5fff0}.u-error-light[data-v-feaddfb4]{color:#fef0f0}.u-info-light[data-v-feaddfb4]{color:#f4f4f5}.u-primary-light-bg[data-v-feaddfb4]{background-color:#ecf5ff}.u-warning-light-bg[data-v-feaddfb4]{background-color:#fdf6ec}.u-success-light-bg[data-v-feaddfb4]{background-color:#f5fff0}.u-error-light-bg[data-v-feaddfb4]{background-color:#fef0f0}.u-info-light-bg[data-v-feaddfb4]{background-color:#f4f4f5}.u-primary-dark[data-v-feaddfb4]{color:#398ade}.u-warning-dark[data-v-feaddfb4]{color:#f1a532}.u-success-dark[data-v-feaddfb4]{color:#53c21d}.u-error-dark[data-v-feaddfb4]{color:#e45656}.u-info-dark[data-v-feaddfb4]{color:#767a82}.u-primary-dark-bg[data-v-feaddfb4]{background-color:#398ade}.u-warning-dark-bg[data-v-feaddfb4]{background-color:#f1a532}.u-success-dark-bg[data-v-feaddfb4]{background-color:#53c21d}.u-error-dark-bg[data-v-feaddfb4]{background-color:#e45656}.u-info-dark-bg[data-v-feaddfb4]{background-color:#767a82}.u-primary-disabled[data-v-feaddfb4]{color:#9acafc}.u-warning-disabled[data-v-feaddfb4]{color:#f9d39b}.u-success-disabled[data-v-feaddfb4]{color:#a9e08f}.u-error-disabled[data-v-feaddfb4]{color:#f7b2b2}.u-info-disabled[data-v-feaddfb4]{color:#c4c6c9}.u-primary[data-v-feaddfb4]{color:#3c9cff}.u-warning[data-v-feaddfb4]{color:#f9ae3d}.u-success[data-v-feaddfb4]{color:#5ac725}.u-error[data-v-feaddfb4]{color:#f56c6c}.u-info[data-v-feaddfb4]{color:#909399}.u-primary-bg[data-v-feaddfb4]{background-color:#3c9cff}.u-warning-bg[data-v-feaddfb4]{background-color:#f9ae3d}.u-success-bg[data-v-feaddfb4]{background-color:#5ac725}.u-error-bg[data-v-feaddfb4]{background-color:#f56c6c}.u-info-bg[data-v-feaddfb4]{background-color:#909399}.u-main-color[data-v-feaddfb4]{color:#303133}.u-content-color[data-v-feaddfb4]{color:#606266}.u-tips-color[data-v-feaddfb4]{color:#909193}.u-light-color[data-v-feaddfb4]{color:#c0c4cc}.u-safe-area-inset-top[data-v-feaddfb4]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-feaddfb4]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-feaddfb4]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-feaddfb4]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-feaddfb4]{z-index:10090}uni-toast .uni-toast[data-v-feaddfb4]{z-index:10090}[data-v-feaddfb4]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */.line-loss-content[data-v-feaddfb4]{display:flex;flex-direction:column;background-color:#f5f5f5;position:relative;width:100%;height:100%}\r\n/* 内容区域 */.content-section[data-v-feaddfb4]{box-sizing:border-box;background-color:#f5f5f5;padding:10px 15px;-webkit-overflow-scrolling:touch;\r\n  /* 增强iOS滚动体验 */overflow-y:auto;width:100%}\r\n/* 加载中提示 */.loading-container[data-v-feaddfb4]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0;margin-bottom:10px;background-color:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,.05)}.loading-text[data-v-feaddfb4]{font-size:14px;color:#8c8c8c;margin-top:10px;text-align:center}\r\n/* 线损数据内容 */.line-loss-data[data-v-feaddfb4]{-webkit-animation:fade-in-data-v-feaddfb4 .3s ease;animation:fade-in-data-v-feaddfb4 .3s ease}@-webkit-keyframes fade-in-data-v-feaddfb4{from{opacity:0;-webkit-transform:translateY(5px);transform:translateY(5px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fade-in-data-v-feaddfb4{from{opacity:0;-webkit-transform:translateY(5px);transform:translateY(5px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}\r\n/* 图表容器 */.chart-container[data-v-feaddfb4]{background-color:#fff;border-radius:12px;\r\n  /* 增加圆角 */box-shadow:0 2px 8px rgba(0,0,0,.06);\r\n  /* 优化阴影效果 */overflow:hidden;margin-bottom:15px}.chart-header[data-v-feaddfb4]{padding:15px 18px;\r\n  /* 增加内边距 */border-bottom:1px solid #f0f0f0;display:flex;justify-content:space-between;align-items:center}.chart-title[data-v-feaddfb4]{font-size:16px;\r\n  /* 增大字体 */color:#333;font-weight:500}.chart-content[data-v-feaddfb4]{width:100%;height:280px;\r\n  /* 增加图表高度，提供更多空间 */position:relative}\r\n/* 数据表格 */.data-table[data-v-feaddfb4]{background-color:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,.05);overflow:hidden;margin-bottom:10px}.table-row[data-v-feaddfb4]{display:flex;border-bottom:1px solid #f0f0f0}.table-row[data-v-feaddfb4]:last-child{border-bottom:none}.table-cell[data-v-feaddfb4]{padding:10px 15px;font-size:14px}.label[data-v-feaddfb4]{color:#8c8c8c}.value[data-v-feaddfb4]{flex:1;color:#262626;text-align:right;overflow:hidden;\r\n  /* 隐藏超出内容 */text-overflow:ellipsis;\r\n  /* 显示省略号 */white-space:nowrap}.highlight[data-v-feaddfb4]{color:#07ac7c}.highlight-orange[data-v-feaddfb4]{color:orange}\r\n/* 无数据提示 */.empty-container[data-v-feaddfb4]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0;background-color:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,.05);-webkit-animation:fade-in-data-v-feaddfb4 .3s ease;animation:fade-in-data-v-feaddfb4 .3s ease}.empty-text[data-v-feaddfb4]{font-size:14px;color:#999;margin-top:10px}\r\n/* 底部间距 */.bottom-space[data-v-feaddfb4]{height:20px}',""]),a.exports=t},aaae:function(a,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return r})),e.d(t,"a",(function(){return o}));var o={qiunDataCharts:e("a3e6").default,uEmpty:e("57a9").default},i=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("v-uni-view",{staticClass:"line-loss-content"},[e("v-uni-scroll-view",{staticClass:"content-section",style:{height:a.contentHeight+"px"},attrs:{"scroll-y":"true","refresher-enabled":!0,"refresher-triggered":a.isRefreshing},on:{refresherrefresh:function(t){arguments[0]=t=a.$handleEvent(t),a.onRefresh.apply(void 0,arguments)}}},[!a.isLoading||a.isRefreshing?e("v-uni-view",{staticClass:"line-loss-data"},[e("v-uni-view",{staticClass:"chart-container"},[e("v-uni-view",{staticClass:"chart-header"},[e("v-uni-text",{staticClass:"chart-title"},[a._v("用户用电量与线损率")])],1),e("v-uni-view",{staticClass:"chart-content"},[e("qiun-data-charts",{attrs:{type:"mix",opts:a.chartOpts,chartData:a.chartData,canvas2d:!0,ontouch:!0,canvasId:"lineLossChart"}})],1)],1),e("v-uni-view",{staticClass:"data-table"},[e("v-uni-view",{staticClass:"table-row"},[e("v-uni-view",{staticClass:"table-cell label"},[a._v("用户编号")]),e("v-uni-view",{staticClass:"table-cell value highlight"},[a._v(a._s(a.lineLossData.custNo))])],1),e("v-uni-view",{staticClass:"table-row"},[e("v-uni-view",{staticClass:"table-cell label"},[a._v("用户名称")]),e("v-uni-view",{staticClass:"table-cell value"},[a._v(a._s(a.lineLossData.custName))])],1),e("v-uni-view",{staticClass:"table-row"},[e("v-uni-view",{staticClass:"table-cell label"},[a._v("台区编号")]),e("v-uni-view",{staticClass:"table-cell value"},[a._v(a._s(a.lineLossData.tgNo))])],1),e("v-uni-view",{staticClass:"table-row"},[e("v-uni-view",{staticClass:"table-cell label"},[a._v("台区名称")]),e("v-uni-view",{staticClass:"table-cell value"},[a._v(a._s(a.lineLossData.tgName))])],1),e("v-uni-view",{staticClass:"table-row"},[e("v-uni-view",{staticClass:"table-cell label"},[a._v("台区用户数")]),e("v-uni-view",{staticClass:"table-cell value"},[a._v(a._s(a.lineLossData.userCount))])],1),e("v-uni-view",{staticClass:"table-row"},[e("v-uni-view",{staticClass:"table-cell label"},[a._v("台区低压表计数")]),e("v-uni-view",{staticClass:"table-cell value"},[a._v(a._s(a.lineLossData.lowUserCount))])],1),e("v-uni-view",{staticClass:"table-row"},[e("v-uni-view",{staticClass:"table-cell label"},[a._v("台区总容量 (KVA)")]),e("v-uni-view",{staticClass:"table-cell value highlight-orange"},[a._v(a._s(a.lineLossData.contractCapCount))])],1),e("v-uni-view",{staticClass:"table-row"},[e("v-uni-view",{staticClass:"table-cell label"},[a._v("台区供电量")]),e("v-uni-view",{staticClass:"table-cell value highlight-orange"},[a._v(a._s(a.lineLossData.ppq))])],1)],1)],1):a._e(),a.isLoading||a.isRefreshing||a.hasData?a._e():e("v-uni-view",{staticClass:"empty-container"},[e("u-empty",{attrs:{mode:"data",icon:"/static/icons/nodata.jpg"}})],1),e("v-uni-view",{staticClass:"bottom-space"})],1)],1)},r=[]},af12:function(a,t,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("bf0f"),e("dc8a"),e("795c"),e("c223"),e("aa9c"),e("fd3c"),e("473f"),e("64aa"),e("d4b5"),e("c1a3"),e("18f7"),e("de6c"),e("2797");var i=o(e("9b1b")),r=o(e("9004")),n=e("b3d7"),d={components:{qiunDataCharts:function(){return Promise.resolve().then((function(){return(0,r.default)(e("ec2a"))}))}},props:{queryParams:{type:Object,default:function(){return{}}}},data:function(){return{isRefreshing:!1,isLoading:!0,contentHeight:0,statusBarHeight:0,navbarHeight:44,tabNavHeight:50,hasData:!0,date:"",token:null,chartOpts:{color:["#1890FF","#00C389"],padding:[5,10,15,10],enableScroll:!0,legend:{show:!0,position:"top",float:"center",padding:5,margin:5,fontSize:12,lineHeight:16,fontColor:"#666666",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",itemGap:20},xAxis:{disableGrid:!0,scrollShow:!0,itemCount:3,fontSize:12,fontColor:"#999999"},yAxis:{data:[{position:"left",title:"用户电量",titleFontSize:13,titleFontColor:"#07ac7c",fontSize:12,fontColor:"#999999",format:function(a){return a.toFixed(1)}},{position:"right",title:"线损率",titleFontSize:13,titleFontColor:"#1890FF",fontSize:12,fontColor:"#999999",format:function(a){return a.toFixed(1)}}],showTitle:!0,gridType:"dash",dashLength:4,splitNumber:5,gridColor:"#eeeeee"},extra:{mix:{column:{width:18,radius:4,linearType:"none",barBorderCircle:!1}},line:{type:"straight",width:2.5,activeType:"hollow",linePosition:"top"},tooltip:{showBox:!0,showArrow:!0,bgColor:"#000000",bgOpacity:.7,borderRadius:4,borderWidth:0}},background:"#FFFFFF",dataLabel:!1,dataPointShape:!0,dataPointShapeType:"solid"},chartData:{categories:[],series:[{name:"用户电量",data:[],type:"column",color:"#07ac7c",index:0},{name:"台区线损率",data:[],type:"line",color:"#1890FF",index:1,yAxisIndex:1}]},lineLossData:{},isMockData:!0}},onLoad:function(){this.loadLineLossData()},mounted:function(){this.getSystemInfo(),this.calcContentHeight()},activated:function(){console.log("线损数据组件被激活"),this.loadLineLossData()},methods:{init:function(){var a=this;wx.invoke("ext_DataCache_GetInfo",{data:{key:"1002838.userInfo"}},(function(t){var e=JSON.parse(t.result);a.token=null===e||void 0===e?void 0:e.token}))},getSystemInfo:function(){var a=uni.getSystemInfoSync();this.statusBarHeight=a.statusBarHeight||0,this.windowHeight=a.windowHeight||0},calcContentHeight:function(){this.windowHeight||this.getSystemInfo();var a=this.statusBarHeight+this.navbarHeight+this.tabNavHeight;this.contentHeight=this.windowHeight-a},getEffectiveQueryParams:function(){if(console.log("11",this.queryParams),this.queryParams&&Object.keys(this.queryParams).length>0)return(0,i.default)({},this.queryParams);var a=uni.getStorageSync("userDataQueryParams");return a&&Object.keys(a).length>0?(0,i.default)({},a):{}},loadLineLossData:function(){var a=this;this.isRefreshing||(this.isLoading=!0),uni.showLoading({title:"加载中..."});var t=this.getEffectiveQueryParams();console.log("线损参数",t);var e={custNo:t.custNo||"",custName:t.custName||"",meterAssetNo:t.meterAssetNo||"",pageNum:1,pageSize:10};if(t.dateRange&&t.dateRange.length>0){var o=function(a){if(!a)return"";var t=new Date(a),e=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0");return"".concat(e,"-").concat(o,"-").concat(i)};e.startDate=o(t.dateRange[0]),e.endDate=o(t.dateRange[t.dateRange.length-1])}var i=new Date(t.dateRange[0]),r=new Date(t.dateRange[1]),d=[],s=new Date(i);while(s<=r)d.push(new Date(s).toISOString().split("T")[0]),s.setDate(s.getDate()+1);if(console.log(d),this.isMockData){this.lineLossData={statDate:"2025/4/22",orgName:"嘉定营销分中心",custNo:"3100030169550",custName:"顾伟弟",meterAssetNo:"110007839864",elecAddr:"火车站路151弄3号101室",tgName:"车站路四号站_配变",tgNo:"94231_00",tgId:"10001727465",contractCapCount:"",userCount:"1",lowUserCount:"1"},this.date=this.lineLossData.statDate;var l=[{llRate:"0.95",ppq:"857.6",statDate:"2025-07-12"},{llRate:"1.03",ppq:"923.2",statDate:"2025-07-13"}];this.chartData.series[0].data=l.map((function(a){return a.ppq})),this.chartData.series[1].data=l.map((function(a){return a.llRate})),this.lineLossData.ppq=this.chartData.series[0].data.reduce((function(a,t){return a+Number(t)}),0).toFixed(2),this.chartData.categories=l.map((function(a){return a.statDate})),this.isLoading=!1,setTimeout((function(){a.isRefreshing=!1}),500),uni.hideLoading()}else uni.request({url:"http://127.0.0.1:".concat(n.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:this.token},data:{token:this.token,method:"PutHuaYun",uri:n.url,data:JSON.stringify({bizCode:n.bizCode,espFlowId:(0,n.getUUID)(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:(0,n.getCurrentTimestamp)(),espInformation:{service:"DtsUserController",method:"getUserLoss",data:{custNo:t.custNo||"",tgNo:t.tgNo||"",statDateStart:t.dateRange[0],statDateEnd:t.dateRange[1]}}})},success:function(e){if(console.log(e),e&&1===e.data.Tag){var o=e.data.Data.espInformation;if(o&&200==o.code){var i;a.lineLossData=o.data;var r=new Map;null!==o&&void 0!==o&&null!==(i=o.data)&&void 0!==i&&i.llRateList.length&&(o.data.llRateList.forEach((function(a){r.set(a.statDate,a)})),a.chartData.series[0].data=d.map((function(a){var t=r.get(a);return t?t.ppq:0})),a.chartData.series[1].data=d.map((function(a){var t=r.get(a);return t?t.llRate:0})),a.lineLossData.ppq=a.chartData.series[0].data.reduce((function(a,t){return a+Number(t)}),0).toFixed(2)),a.chartData.categories=d,a.date=t.dateRangeText,console.log(a.chartData.series),a.isLoading=!1,a.isRefreshing=!1}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3});uni.hideLoading()},fail:function(t){a.isLoading=!1,uni.hideLoading(),a.isRefreshing&&(a.isRefreshing=!1),uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}})},onRefresh:function(){this.isRefreshing=!0,this.loadLineLossData()}}};t.default=d},b8f4:function(a,t,e){"use strict";e.r(t);var o=e("af12"),i=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(a){e.d(t,a,(function(){return o[a]}))}(r);t["default"]=i.a},e178:function(a,t,e){"use strict";var o=e("e74e"),i=e.n(o);i.a},e74e:function(a,t,e){var o=e("992b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[a.i,o,""]]),o.locals&&(a.exports=o.locals);var i=e("967d").default;i("c79e8ff6",o,!0,{sourceMap:!1,shadowMode:!1})}}]);