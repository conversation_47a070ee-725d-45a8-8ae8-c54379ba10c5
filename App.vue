<script>
	// var VConsole = require('./static/commonJs/vconsole.min.js');
	var jsApiLists = ['getZipAppDirectory',
		'ext_FileManager_Write',
		'ext_FileManager_Read',
		'ext_FileManager_Remove',
		'ext_FileManager_Create',
		'ext_FileManager_RemoveDirectory',
		'ext_FileManager_Exist',
		'ext_FileManager_IsDirectory',
		'ext_FileManager_Information',

		'ext_Socket_Init',
		'ext_Socket_UnInit',
		'ext_Socket_CreateChannel',
		'ext_Socket_CloseChannel',
		'ext_Socket_SendData',

		'multiWindows_subscribe',

		'ext_SGMap_init',
		'ext_SGMap_Operation',
		'ext_SGMap_Search',
		'ext_SGMap_Location',

		'ext_Etas_List',
		'ext_Etas_Init',
		'ext_Etas_Reg',
		'ext_Etas_UnReg',
		'ext_Etas_checkStatus',
		'ext_Etas_Verify',
		'ext_Etas_SaveData',
		'ext_Etas_GetData',
		'ext_Etas_RemoveData',
		'ext_Etas_Clear',
		'ext_Etas_OffLineVerify',

		'ext_ISCP_Init',
		'ext_ISCP_ConnectService',
		'ext_ISCP_GetLocalPort',
		'ext_ISCP_Close',
		'ext_ISCP_Status',

		'ext_Database_Open',
		'ext_Database_Exec',
		'ext_Database_Query',
		'ext_Database_Close',

		'ext_rlble_initBluetoothAdapter',
		'ext_rlble_scanBluetoothDev',
		'ext_rlble_startConnectBluetoothDev',
		'ext_rlble_onBluetoothDevConnDataRecv',
		'ext_rlble_BluetoothDevSendData',
		'ext_rlble_closeBluetoothDevConnection',
		'ext_rlble_stopBluetoothDevScan',

		'ext_Popover_Open',
		'ext_Popover_Close',

		"multiWindows_open",
		"multiWindows_close",
		"multiWindows_startWidget",
		"multiWindows_finishWidget",
		"multiWindows_subscribe",
		"multiWindows_publish",

		'selectEnterpriseContact',
		'openChatWithMsg',
		'openUserProfile',

		'onMenuShareAppMessage',
		'onMenuShareWechat',
		'shareAppMessage',
		'shareWechatMessage',
		'onMenuShareTimeline',

		'chooseImage',
		'getLocalImgData',
		'previewImage',
		'uploadImage',
		'downloadImage',
		'previewFile',

		'chooseVideo',
		'uploadVideo',
		'downloadVideo',

		'startRecord',
		'stopRecord',
		'onVoiceRecordEnd',
		'playVoice',
		'playVoice',
		'stopVoice',
		'onVoicePlayEnd',
		'startRecordVoiceBuffer',
		'onRecordBufferReceived',
		'stopRecordVoiceBuffer',
		'uploadVoice',
		'downloadVoice',
		'translateVoice',

		'startWifi',
		'stopWifi',
		'connectWifi',
		'getWifiList',
		'onGetWifiList',
		'onWifiConnected',
		'getConnectedWifi',

		'openBluetoothAdapter',
		'closeBluetoothAdapter',
		'getBluetoothAdapterState',
		'onBluetoothAdapterStateChange',
		'startBluetoothDevicesDiscovery',
		'stopBluetoothDevicesDiscovery',
		'getBluetoothDevices',
		'onBluetoothDeviceFound',
		'getConnectedBluetoothDevices',
		'createBLEConnection',
		'closeBLEConnection',
		'onBLEConnectionStateChange',
		'getBLEDeviceServices',
		'getBLEDeviceCharacteristics',
		'readBLECharacteristicValue',
		'writeBLECharacteristicValue',
		'notifyBLECharacteristicValueChange',
		'onBLECharacteristicValueChange',

		'setClipboardData',
		'getClipboardData',

		'getNetworkType',
		'onNetworkStatusChange',

		'openLocation',
		'getLocation',
		'startAutoLBS',
		'stopAutoLBS',
		'onLocationChange',

		'onHistoryBack',
		'hideOptionMenu',
		'showOptionMenu',
		'closeWindow',
		'hideMenuItems',
		'showMenuItems',
		'hideAllNonBaseMenuItem',
		'showAllNonBaseMenuItem',
		'onUserCaptureScreen',
		'openUrl',

		'scanQRCode',

		'getStepCount',
		'getAllPhoneContacts',
		'addCalendarEvent',

		'showWatermark',
		'hideWatermark',

		'checkIsSupportSoterAuthentication',
		'checkIsSoterEnrolledInDevice',
		'startSoterAuthentication',
		'bioassayAuthentication',

		'launch3rdApp',
		'request3rdApp',
		'getInstallState',

		'ocr',

		'checkJsApi',

		'ext_gislocation_init',
		'ext_gislocation_start',
		'ext_gislocation_stop',

		'ext_SandBox_Reg',
		'ext_SandBox_StartSubApp',
		'ext_SandBox_CloseSubApp',
		'ext_SandBox_ScreenPolicy',
		'ext_SandBox_isRoot',
		'ext_SandBox_isSimulator',
		'ext_SandBox_WaterMark',
		'ext_SandBox_ScreenShot',
		'ext_SandBox_DelePhoto',
		'ext_SandBox_UploadShot',

		'ext_wxlog_setLogOpen',
		'ext_wxlog_getLogFile',
		'ext_SGMap_init',
		'ext_SGMap_Location',

		'ext_DataCache_Get',
		'ext_DataCache_Save',
		'ext_DataCache_SaveInfo',
		'ext_DataCache_GetInfo',
		'ext_DataCache_RemoveInfo',

		'ext_Net_Upload',
		'ext_Net_CancelUpload',
		'ext_Net_Download',
		'ext_Net_SimpleDownload',
		'ext_Net_MultiDownload',

		'ext_screenControl_direction',

		'ext_WeMeet_Init',
		'ext_WeMeet_Login',
		'ext_WeMeet_Logout',
		'ext_WeMeet_Join',

		'ext_DeviceInfo_GetInfo',

		'ext_Compressor_Size',
		'ext_Compressor_Quality',

		'ext_OfflineFaceRec_add',
		'ext_OfflineFaceRec_delete',
		'ext_OfflineFaceRec_recognition',
		'ext_OfflineFaceRec_capture',

		'ext_unzip_untie',
		'ext_unzip_compression',
		'ext_Intent_Dial'
	];
	//import {jWeixin} from "static/commonJs/jweixin-1.0.0.js";
	export default {
		mounted() {
			// var vConsole = new VConsole();
		},
		onLaunch: function() {
			// uni.redirectTo({
			// 	url:"/pages/fileQuery/index"
			// })
			console.log('App Launch')
			// Android环境 uni-app内置wx对象，避免与i国网（企信版）冲突，需重新定义
			console.log("（第1/2步）非uni项目可直接进行第二步");
			window.wx = jWeixin;
			wx.ready(function() {
				// config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
				console.log("（第2/2步）wxConfig 配置完成");
				// wx.onHistoryBack(function() {
				// 	console.log("允许返回键回退页面");
				// 	return true;
				// });
			});

			wx.error(function(res) {
				// config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
				console.log("（第2/2步）wxConfig 配置失败");
			});
			wx.config({
				beta: true, // 调用wx.invoke形式的接口值时，该值必须为true。
				debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
				appId: localStorage.getItem("wx_cropID"), // 必填，政务微信的cropID
				timestamp: localStorage.getItem("wx_timestamp"), // 必填，生成签名的时间戳
				nonceStr: localStorage.getItem("wx_nonceStr"), // 必填，生成签名的随机串
				signature: localStorage.getItem("wx_signature"), // 必填，签名，见附录1
				jsApiList: jsApiLists // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
			});
		},
		onShow: function() {
			console.log('App Show')
			// 判断用户是否已登录
			// const loggedIn = uni.getStorageSync('loggedIn');
			// if (!loggedIn) {
			// 	uni.redirectTo({
			// 		url: '/pages/Login/Login'
			// 	});
			// }
		},
		onHide: function() {
			console.log('App Hide')
		},
		globalData: {
			// 全局方法
			getData(message) {
			  uni.showToast({
				title: message,
				icon: 'none'
			  });
			}
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import "@/uni_modules/uview-ui/index.scss";

	* {
		margin: 0;
		padding: 0;
		list-style: none;
		box-sizing: border-box;
	}

	.uni-page-head .uni-page-head-ft {
		padding: 0 0.8rem;
	}

	.uni-page-head-hd .uni-page-head-ft {
		margin-right: 0;
		margin-left: 0;
		padding: 0;
	}

	.uni-page-head-search-placeholder:before {
		display: none;
	}

	.uni-page-head-search-placeholder,
	.uni-page-head-search-input {
		padding-left: 0.93rem;
	}

	.uni-modal .uni-modal__ft {
		line-height: 2.5rem;
		font-size: 1rem;

		.uni-modal__btn_primary {
			color: #25aab1 !important;
		}
	}

	.cell-hover-class {
		background-color: rgb(235, 237, 238);
	}

	uni-page-body {
		background-color: #f2f2f2 !important;
		min-height: 100% !important;
		height: auto !important;
	}

	@import url("./static/iconfont/iconfont.css");
</style>