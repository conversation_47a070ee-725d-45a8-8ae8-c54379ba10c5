<template>
	<view class="load-data-content">
		<!-- 内容区域 - 负荷数据 -->
		<scroll-view class="content-section" scroll-y="true"
			:style="{ height: contentHeight + 'px' }">
			<!-- 电压曲线图表 -->
			<view class="chart-item-container">
				<!-- 图表标题 -->
				<view class="chart-header">
					<text class="chart-title">电压曲线</text>
					<view class="chart-toggle">
						<view :class="['toggle-btn', { active: viewModes.voltage === 'chart' }]"
							@click="switchViewMode('voltage', 'chart')">
							<!-- <view class="chart-icon">图</view> -->
							<svg-icon :name="viewModes.voltage === 'chart' ? 'line' : 'lineGreen'" size="36"></svg-icon>
						</view>
						<view :class="['toggle-btn', { active: viewModes.voltage === 'data' }]"
							@click="switchViewMode('voltage', 'data')">
							<!-- <view class="chart-icon">表</view> -->
							<uni-icons type="list" size="16"
								:color="viewModes.voltage === 'data' ? '#ffffff' : '#07ac7c'"></uni-icons>
						</view>
						<view class="screen-rotate" @click="screenRotate"></view>
					</view>
				</view>

				<!-- 图表视图 -->
				<view class="chart-view" v-if="viewModes.voltage === 'chart'">
					<view class="chart-container" :style="{ height: chartHeight + 'px' }">
						<!-- 加载中提示 -->
						<view class="chart-loading" v-if="loadingStatus.voltage">
							<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
						</view>

						<!-- qiun-data-charts组件 -->
						<qiun-data-charts type="line" :opts="chartOpts.voltage" :chartData="chartData.voltage" canvas2d
							:ontouch="true" :onzoom="true" :disableScroll="true" canvasId="voltageLineChart" :style="{
								opacity: loadingStatus.voltage ? 0 : 1,
								transition: 'opacity 0.3s ease'
							}" />
					</view>
				</view>

				<!-- 表格数据视图 -->
				<view class="data-view" v-if="viewModes.voltage === 'data'" :style="{ height: chartHeight + 'px' }">
					<!-- 日期选择器 -->
					<scroll-view scroll-x class="date-scroll" v-if="dateList.voltage && dateList.voltage.length">
						<view v-for="(date, index) in dateList.voltage" :key="date"
							:class="['date-item', { active: selectedDates.voltage === date }]"
							@click="selectDateForTable('voltage', date)">
							{{ formatDateLabel(date) }}
						</view>
					</scroll-view>

					<!-- 移动端优化的表格 -->
					<view class="data-table">
						<view class="table-header">
							<view class="table-cell time-cell">时间点</view>
							<view class="table-cell">A相(V)</view>
							<view class="table-cell">B相(V)</view>
							<view class="table-cell">C相(V)</view>
						</view>
						<scroll-view scroll-y class="table-body" :style="{ height: (chartHeight - 80) + 'px' }">
							<template v-if="tableData.voltage && tableData.voltage[selectedDates['voltage']]">
								<view v-for="(row, index) in tableData.voltage[selectedDates['voltage']]" :key="index" class="table-row">
									<view class="table-cell time-cell">{{ row.time }}</view>
									<view class="table-cell">{{ row.phaseA }}</view>
									<view class="table-cell">{{ row.phaseB }}</view>
									<view class="table-cell">{{ row.phaseC }}</view>
								</view>
							</template>
							<view v-else class="empty-data">
								<view class="empty-icon">!</view>
								<text class="empty-text">暂无数据</text>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>

			<!-- 电流曲线图表 -->
			<view class="chart-item-container">
				<!-- 图表标题 -->
				<view class="chart-header">
					<text class="chart-title">电流曲线</text>
					<view class="chart-toggle">
						<view :class="['toggle-btn', { active: viewModes.current === 'chart' }]"
							@click="switchViewMode('current', 'chart')">
							<!-- <view class="chart-icon">图</view> -->
							<svg-icon :name="viewModes.current === 'chart' ? 'line' : 'lineGreen'" size="36"></svg-icon>
						</view>
						<view :class="['toggle-btn', { active: viewModes.current === 'data' }]"
							@click="switchViewMode('current', 'data')">
							<!-- <view class="chart-icon">表</view> -->
							<uni-icons type="list" size="16"
								:color="viewModes.current === 'data' ? '#ffffff' : '#07ac7c'"></uni-icons>
						</view>
						<view class="screen-rotate" @click="screenRotate"></view>
					</view>
				</view>

				<!-- 图表视图 -->
				<view class="chart-view" v-if="viewModes.current === 'chart'">
					<view class="chart-container" :style="{ height: chartHeight + 'px' }">
						<!-- 加载中提示 -->
						<view class="chart-loading" v-if="loadingStatus.current">
							<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
						</view>

						<!-- qiun-data-charts组件 -->
						<qiun-data-charts type="line" :opts="chartOpts.current" :chartData="chartData.current" canvas2d
							:ontouch="true" :onzoom="true" :disableScroll="true" canvasId="currentLineChart" :style="{
								opacity: loadingStatus.current ? 0 : 1,
								transition: 'opacity 0.3s ease'
							}" />
					</view>
				</view>

				<!-- 表格数据视图 -->
				<view class="data-view" v-if="viewModes.current === 'data'" :style="{ height: chartHeight + 'px' }">
					<!-- 日期选择器 -->
					<scroll-view scroll-x class="date-scroll" v-if="dateList.current && dateList.current.length">
						<view v-for="(date, index) in dateList.current" :key="date"
							:class="['date-item', { active: selectedDates.current === date }]"
							@click="selectDateForTable('current', date)">
							{{ formatDateLabel(date) }}
						</view>
					</scroll-view>

					<!-- 移动端优化的表格 -->
					<view class="data-table">
						<view class="table-header">
							<view class="table-cell time-cell">时间点</view>
							<view class="table-cell">A相(A)</view>
							<view class="table-cell">B相(A)</view>
							<view class="table-cell">C相(A)</view>
						</view>
						<scroll-view scroll-y class="table-body" :style="{ height: (chartHeight - 80) + 'px' }">
							<template v-if="tableData.current && tableData.current[selectedDates['current']]">
								<view v-for="(row, index) in tableData.current[selectedDates['current']]" :key="index" class="table-row">
									<view class="table-cell time-cell">{{ row.time }}</view>
									<view class="table-cell">{{ row.phaseA }}</view>
									<view class="table-cell">{{ row.phaseB }}</view>
									<view class="table-cell">{{ row.phaseC }}</view>
								</view>
							</template>
							<view v-else class="empty-data">
								<view class="empty-icon">!</view>
								<text class="empty-text">暂无数据</text>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>

			<!-- 功率曲线图表 -->
			<view class="chart-item-container">
				<!-- 图表标题 -->
				<view class="chart-header">
					<text class="chart-title">功率曲线</text>
					<view class="chart-toggle">
						<view :class="['toggle-btn', { active: viewModes.power === 'chart' }]"
							@click="switchViewMode('power', 'chart')">
							<!-- <view class="chart-icon">图</view> -->
							<svg-icon :name="viewModes.power === 'chart' ? 'line' : 'lineGreen'" size="36"></svg-icon>
						</view>
						<view :class="['toggle-btn', { active: viewModes.power === 'data' }]"
							@click="switchViewMode('power', 'data')">
							<!-- <view class="chart-icon">表</view> -->
							<uni-icons type="list" size="16"
								:color="viewModes.power === 'data' ? '#ffffff' : '#07ac7c'"></uni-icons>
						</view>
						<view class="screen-rotate" @click="screenRotate"></view>
					</view>
				</view>

				<!-- 图表视图 -->
				<view class="chart-view" v-if="viewModes.power === 'chart'">
					<view class="chart-container" :style="{ height: chartHeight + 'px' }">
						<!-- 加载中提示 -->
						<view class="chart-loading" v-if="loadingStatus.power">
							<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
						</view>

						<!-- qiun-data-charts组件 -->
						<qiun-data-charts type="line" :opts="chartOpts.power" :chartData="chartData.power" canvas2d
							:ontouch="true" :onzoom="true" :disableScroll="true" canvasId="powerLineChart" :style="{
								opacity: loadingStatus.power ? 0 : 1,
								transition: 'opacity 0.3s ease'
							}" />
					</view>
				</view>

				<!-- 表格数据视图 -->
				<view class="data-view" v-if="viewModes.power === 'data'" :style="{ height: chartHeight + 'px' }">
					<!-- 日期选择器 -->
					<scroll-view scroll-x class="date-scroll" v-if="dateList.power && dateList.power.length">
						<view v-for="(date, index) in dateList.power" :key="date"
							:class="['date-item', { active: selectedDates.power === date }]"
							@click="selectDateForTable('power', date)">
							{{ formatDateLabel(date) }}
						</view>
					</scroll-view>

					<!-- 移动端优化的表格 -->
					<view class="data-table">
						<view class="table-header">
							<view class="table-cell time-cell">时间点</view>
							<view class="table-cell">A相(kW)</view>
							<view class="table-cell">B相(kW)</view>
							<view class="table-cell">C相(kW)</view>
						</view>
						<scroll-view scroll-y class="table-body" :style="{ height: (chartHeight - 80) + 'px' }">
							<template v-if="tableData.power && tableData.power[selectedDates['power']]">
								<view v-for="(row, index) in tableData.power[selectedDates['power']]" :key="index" class="table-row">
									<view class="table-cell time-cell">{{ row.time }}</view>
									<view class="table-cell">{{ row.phaseA }}</view>
									<view class="table-cell">{{ row.phaseB }}</view>
									<view class="table-cell">{{ row.phaseC }}</view>
								</view>
							</template>
							<view v-else class="empty-data">
								<view class="empty-icon">!</view>
								<text class="empty-text">暂无数据</text>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>

			<!-- 功率因数曲线图表 -->
			<view class="chart-item-container">
				<!-- 图表标题 -->
				<view class="chart-header">
					<text class="chart-title">功率因数曲线</text>
					<view class="chart-toggle">
						<view :class="['toggle-btn', { active: viewModes.powerFactor === 'chart' }]"
							@click="switchViewMode('powerFactor', 'chart')">
							<!-- <view class="chart-icon">图</view> -->
							<svg-icon :name="viewModes.powerFactor === 'chart' ? 'line' : 'lineGreen'"
								size="36"></svg-icon>
						</view>
						<view :class="['toggle-btn', { active: viewModes.powerFactor === 'data' }]"
							@click="switchViewMode('powerFactor', 'data')">
							<!-- <view class="chart-icon">表</view> -->
							<uni-icons type="list" size="16"
								:color="viewModes.powerFactor === 'data' ? '#ffffff' : '#07ac7c'"></uni-icons>
						</view>
						<view class="screen-rotate" @click="screenRotate"></view>
					</view>
				</view>

				<!-- 图表视图 -->
				<view class="chart-view" v-if="viewModes.powerFactor === 'chart'">
					<view class="chart-container" :style="{ height: chartHeight + 'px' }">
						<!-- 加载中提示 -->
						<view class="chart-loading" v-if="loadingStatus.powerFactor">
							<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
						</view>

						<!-- qiun-data-charts组件 -->
						<qiun-data-charts type="line" :opts="chartOpts.powerFactor" :chartData="chartData.powerFactor"
							canvas2d :ontouch="true" :onzoom="true" :disableScroll="true" canvasId="powerFactorLineChart" :style="{
								opacity: loadingStatus.powerFactor ? 0 : 1,
								transition: 'opacity 0.3s ease'
							}" />
					</view>
				</view>

				<!-- 表格数据视图 -->
				<view class="data-view" v-if="viewModes.powerFactor === 'data'"
					:style="{ height: chartHeight + 'px' }">
					<!-- 日期选择器 -->
					<scroll-view scroll-x class="date-scroll"
						v-if="dateList.powerFactor && dateList.powerFactor.length">
						<view v-for="(date, index) in dateList.powerFactor" :key="date"
							:class="['date-item', { active: selectedDates.powerFactor === date }]"
							@click="selectDateForTable('powerFactor', date)">
							{{ formatDateLabel(date) }}
						</view>
					</scroll-view>

					<!-- 移动端优化的表格 -->
					<view class="data-table">
						<view class="table-header">
							<view class="table-cell time-cell">时间点</view>
							<view class="table-cell">A相</view>
							<view class="table-cell">B相</view>
							<view class="table-cell">C相</view>
						</view>
						<scroll-view scroll-y class="table-body" :style="{ height: (chartHeight - 80) + 'px' }">
							<template v-if="tableData.powerFactor && tableData.powerFactor[selectedDates['powerFactor']]">
								<view v-for="(row, index) in tableData.powerFactor[selectedDates['powerFactor']]" :key="index" class="table-row">
									<view class="table-cell time-cell">{{ row.time }}</view>
									<view class="table-cell">{{ row.phaseA }}</view>
									<view class="table-cell">{{ row.phaseB }}</view>
									<view class="table-cell">{{ row.phaseC }}</view>
								</view>
							</template>
							<view v-else class="empty-data">
								<view class="empty-icon">!</view>
								<text class="empty-text">暂无数据</text>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>

			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		bizCode,
		url,
		getUUID,
		getCurrentTimestamp,
		port
	} from '@/static/commonJs/util.js'
	export default {
		components: {
			qiunDataCharts: () => import('@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue')
		},
		props: {
			queryParams: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				dailyData:[],
				token:null,
				isMockData: true,
				isRefreshing: false,
				isChartLoading: true, // 图表加载状态
				loadingStatus: {
					voltage: true,
					current: true,
					power: true,
					powerFactor: true
				},
				chartRendered: false, // 图表是否已经成功渲染
				chartHeight: 320, // 图表容器高度固定
				contentHeight: 0, // 内容区域高度
				statusBarHeight: 0, // 状态栏高度
				navbarHeight: 44, // 导航栏高度
				tabNavHeight: 50, // 标签导航高度
				direction: '1', // 屏幕方向：1为竖屏，0为横屏
				screenWidth: 0, // 屏幕宽度
				screenHeight: 0, // 屏幕高度
				viewModes: {
					voltage: 'chart',
					current: 'chart',
					power: 'chart',
					powerFactor: 'chart'
				},
				selectedDates: {
					voltage: '',
					current: '',
					power: '',
					powerFactor: ''
				},
				tableData: {
					voltage: [],
					current: [],
					power: [],
					powerFactor: []
				}, // 表格数据（多日期完整数据）
				mobileTableData: {
					voltage: [],
					current: [],
					power: [],
					powerFactor: []
				}, // 移动端优化的表格数据（选定日期的数据）
				dateList: {
					voltage: [],
					current: [],
					power: [],
					powerFactor: []
				},
				phaseColors: {
					'A相': ['#1890FF', '#91d5ff', '#69c0ff', '#40a9ff', '#1890ff', '#096dd9', '#0050b3'],
					'B相': ['#2FC25B', '#95de64', '#73d13d', '#52c41a', '#389e0d', '#237804', '#135200'],
					'C相': ['#F04864', '#ffadd2', '#ff85c0', '#f759ab', '#eb2f96', '#c41d7f', '#9e1068']
				},
				dataPointShapes: ['circle', 'rect', 'triangle', 'diamond', 'star', 'star2', 'solid'],
				lineStyles: ['solid', 'dash'],
				chartData: {
					voltage: {
						categories: [],
						series: []
					},
					current: {
						categories: [],
						series: []
					},
					power: {
						categories: [],
						series: []
					},
					powerFactor: {
						categories: [],
						series: []
					}
				},
				chartOpts: {
					voltage: {
						color: ["#1890FF", "#2FC25B", "#F04864", "#FACC14", "#8543E0", "#13C2C2", "#3436C7"],
						padding: [15, 15, 10, 5],
						enableScroll: true,
						enableMarkLine: false,
						dataPointShape: true,
						dataLabel: false,
						legend: {
							show: true,
							position: 'top',
							float: 'center',
							padding: 5,
							margin: 0,
							backgroundColor: 'rgba(0,0,0,0)',
							borderColor: 'rgba(0,0,0,0)',
							itemGap: 10,
							fontSize: 10,
							lineHeight: 12,
							fontColor: '#333333',
							pageMode: true,
							pageIconColor: '#1890FF',
							pageIconInactiveColor: '#CCCCCC',
							pageIconSize: 8,
							pageIconMargin: 10,
							pageTextStyle: {
								color: '#666666',
								fontSize: 10
							},
							formatter: (name) => {
								if (name.includes(' ')) {
									const parts = name.split(' ');
									return parts[0];
								}
								if (name.length > 10) {
									return name.substring(0, 10) + '...';
								}
								return name;
							},
							filter: (legendItem) => {
								return legendItem.showInLegend !== false;
							}
						},
						xAxis: {
							disableGrid: true,
							scrollShow: true,
							scrollAlign: 'left',
							scrollBackgroundColor: '#F6F6F6',
							scrollColor: '#A8A8A8',
							itemCount: 4,
							labelCount: 4,
							boundaryGap: 'center',
							axisLine: true,
							axisLineColor: '#CCCCCC',
							rotateLabel: false,
							fontSize: 12,
							margin: 15,
							format: (val) => {
								return val;
							}
						},
						yAxis: {
							data: [{
								min: 0,
								max: 500,
								title: '',
								format: (val) => {
									return val.toFixed(0);
								}
							}],
							showTitle: true,
							titleFontSize: 12,
							titleOffsetX: 0,
							titleOffsetY: 0,
							titleAlign: 'center',
							gridType: 'dash',
							dashLength: 4,
							splitNumber: 5
						},
						extra: {
							line: {
								type: "curve",
								width: 2.5,
								activeType: "none",
								linearType: "custom",
								activeOpacity: 0.8,
								lineCap: "round"
							},
							tooltip: {
								showBox: true,
								showArrow: true,
								showCategory: true,
								borderWidth: 0,
								borderRadius: 4,
								borderColor: '#000000',
								borderOpacity: 0.7,
								bgColor: '#000000',
								bgOpacity: 0.7,
								gridType: 'dash',
								dashLength: 4,
								gridColor: '#CCCCCC',
								fontColor: '#FFFFFF',
								horizentalLine: false,
								xAxisLabel: true,
								yAxisLabel: false,
								labelBgColor: '#FFFFFF',
								labelBgOpacity: 0.7,
								labelFontColor: '#666666',
								maxWidth: 300,
								scrollable: true,
								formatter: (item, category, index, opts) => {
									const timePoint = category;
									let tipText = timePoint + '\n';
									const data = opts.series;
									data.forEach(series => {
										const value = series.data[index];
										if (value !== null && value !== undefined) {
											const valueFixed = parseFloat(value).toFixed(2);
											const date = series.date;
											if (date) {
												const dateParts = date.split('-');
												let formattedDate = date;
												if (dateParts.length === 3) {
													const [year, month, day] = dateParts;
													formattedDate =
														`${year}年${parseInt(month)}月${parseInt(day)}日`;
												}
												const baseName = series.name.split(' ')[0];
												tipText += `${baseName} (${formattedDate}): ${valueFixed}V\n`;
											} else {
												tipText += `${series.name}: ${valueFixed}V\n`;
											}
										}
									});
									return tipText;
								}
							},
							area: {
								opacity: 0.2,
								gradient: true
							}
						},
						touchMoveLimit: 120,
						touchMoveThreshold: 0,
						animation: false,
						duration: 800,
						rotateLock: false,
						background: '#ffffff',
						width: window.innerWidth,
						height: 300
					},
					current: {
						color: ["#1890FF", "#2FC25B", "#F04864", "#FACC14", "#8543E0", "#13C2C2", "#3436C7"],
						padding: [15, 15, 10, 15],
						enableScroll: true,
						enableMarkLine: false,
						dataPointShape: true,
						dataLabel: false,
						legend: {
							show: true,
							position: 'top',
							float: 'center',
							padding: 5,
							margin: 0,
							backgroundColor: 'rgba(0,0,0,0)',
							borderColor: 'rgba(0,0,0,0)',
							itemGap: 10,
							fontSize: 10,
							lineHeight: 12,
							fontColor: '#333333',
							pageMode: true,
							pageIconColor: '#1890FF',
							pageIconInactiveColor: '#CCCCCC',
							pageIconSize: 8,
							pageIconMargin: 10,
							pageTextStyle: {
								color: '#666666',
								fontSize: 10
							},
							formatter: (name) => {
								if (name.includes(' ')) {
									const parts = name.split(' ');
									return parts[0];
								}
								if (name.length > 10) {
									return name.substring(0, 10) + '...';
								}
								return name;
							},
							filter: (legendItem) => {
								return legendItem.showInLegend !== false;
							}
						},
						xAxis: {
							disableGrid: true,
							scrollShow: true,
							scrollAlign: 'left',
							scrollBackgroundColor: '#F6F6F6',
							scrollColor: '#A8A8A8',
							itemCount: 4,
							labelCount: 4,
							boundaryGap: 'center',
							axisLine: true,
							axisLineColor: '#CCCCCC',
							rotateLabel: false,
							fontSize: 12,
							margin: 15,
							format: (val) => {
								return val;
							}
						},
						yAxis: {
							data: [{
								min: 0,
								max: 5,
								title: '',
								format: (val) => {
									return val.toFixed(1);
								}
							}],
							showTitle: true,
							titleFontSize: 12,
							titleOffsetX: 0,
							titleOffsetY: 0,
							titleAlign: 'center',
							gridType: 'dash',
							dashLength: 4,
							splitNumber: 5
						},
						extra: {
							line: {
								type: "curve",
								width: 2.5,
								activeType: "none",
								linearType: "custom",
								activeOpacity: 0.8,
								lineCap: "round"
							},
							tooltip: {
								showBox: true,
								showArrow: true,
								showCategory: true,
								borderWidth: 0,
								borderRadius: 4,
								borderColor: '#000000',
								borderOpacity: 0.7,
								bgColor: '#000000',
								bgOpacity: 0.7,
								gridType: 'dash',
								dashLength: 4,
								gridColor: '#CCCCCC',
								fontColor: '#FFFFFF',
								horizentalLine: false,
								xAxisLabel: true,
								yAxisLabel: false,
								labelBgColor: '#FFFFFF',
								labelBgOpacity: 0.7,
								labelFontColor: '#666666',
								maxWidth: 300,
								scrollable: true,
								formatter: (item, category, index, opts) => {
									const timePoint = category;
									let tipText = timePoint + '\n';
									const data = opts.series;
									data.forEach(series => {
										const value = series.data[index];
										if (value !== null && value !== undefined) {
											const valueFixed = parseFloat(value).toFixed(2);
											const date = series.date;
											if (date) {
												const dateParts = date.split('-');
												let formattedDate = date;
												if (dateParts.length === 3) {
													const [year, month, day] = dateParts;
													formattedDate =
														`${year}年${parseInt(month)}月${parseInt(day)}日`;
												}
												const baseName = series.name.split(' ')[0];
												tipText += `${baseName} (${formattedDate}): ${valueFixed}A\n`;
											} else {
												tipText += `${series.name}: ${valueFixed}A\n`;
											}
										}
									});
									return tipText;
								}
							},
							area: {
								opacity: 0.2,
								gradient: true
							}
						},
						touchMoveLimit: 120,
						touchMoveThreshold: 0,
						animation: false,
						duration: 800,
						rotateLock: false,
						background: '#ffffff',
						width: window.innerWidth,
						height: 300
					},
					power: {
						color: ["#1890FF", "#2FC25B", "#F04864", "#FACC14", "#8543E0", "#13C2C2", "#3436C7"],
						padding: [15, 15, 10, 15],
						enableScroll: true,
						enableMarkLine: false,
						dataPointShape: true,
						dataLabel: false,
						legend: {
							show: true,
							position: 'top',
							float: 'center',
							padding: 5,
							margin: 0,
							backgroundColor: 'rgba(0,0,0,0)',
							borderColor: 'rgba(0,0,0,0)',
							itemGap: 10,
							fontSize: 10,
							lineHeight: 12,
							fontColor: '#333333',
							pageMode: true,
							pageIconColor: '#1890FF',
							pageIconInactiveColor: '#CCCCCC',
							pageIconSize: 8,
							pageIconMargin: 10,
							pageTextStyle: {
								color: '#666666',
								fontSize: 10
							},
							formatter: (name) => {
								if (name.includes(' ')) {
									const parts = name.split(' ');
									return parts[0];
								}
								if (name.length > 10) {
									return name.substring(0, 10) + '...';
								}
								return name;
							},
							filter: (legendItem) => {
								return legendItem.showInLegend !== false;
							}
						},
						xAxis: {
							disableGrid: true,
							scrollShow: true,
							scrollAlign: 'left',
							scrollBackgroundColor: '#F6F6F6',
							scrollColor: '#A8A8A8',
							itemCount: 4,
							labelCount: 4,
							boundaryGap: 'center',
							axisLine: true,
							axisLineColor: '#CCCCCC',
							rotateLabel: false,
							fontSize: 12,
							margin: 15,
							format: (val) => {
								return val;
							}
						},
						yAxis: {
							data: [{
								min: 0,
								max: 5,
								title: '',
								format: (val) => {
									return val.toFixed(1);
								}
							}],
							showTitle: true,
							titleFontSize: 12,
							titleOffsetX: 0,
							titleOffsetY: 0,
							titleAlign: 'center',
							gridType: 'dash',
							dashLength: 4,
							splitNumber: 5
						},
						extra: {
							line: {
								type: "curve",
								width: 2.5,
								activeType: "none",
								linearType: "custom",
								activeOpacity: 0.8,
								lineCap: "round"
							},
							tooltip: {
								showBox: true,
								showArrow: true,
								showCategory: true,
								borderWidth: 0,
								borderRadius: 4,
								borderColor: '#000000',
								borderOpacity: 0.7,
								bgColor: '#000000',
								bgOpacity: 0.7,
								gridType: 'dash',
								dashLength: 4,
								gridColor: '#CCCCCC',
								fontColor: '#FFFFFF',
								horizentalLine: false,
								xAxisLabel: true,
								yAxisLabel: false,
								labelBgColor: '#FFFFFF',
								labelBgOpacity: 0.7,
								labelFontColor: '#666666',
								maxWidth: 300,
								scrollable: true,
								formatter: (item, category, index, opts) => {
									const timePoint = category;
									let tipText = timePoint + '\n';
									const data = opts.series;
									data.forEach(series => {
										const value = series.data[index];
										if (value !== null && value !== undefined) {
											const valueFixed = parseFloat(value).toFixed(2);
											const date = series.date;
											if (date) {
												const dateParts = date.split('-');
												let formattedDate = date;
												if (dateParts.length === 3) {
													const [year, month, day] = dateParts;
													formattedDate =
														`${year}年${parseInt(month)}月${parseInt(day)}日`;
												}
												const baseName = series.name.split(' ')[0];
												tipText += `${baseName} (${formattedDate}): ${valueFixed}kW\n`;
											} else {
												tipText += `${series.name}: ${valueFixed}kW\n`;
											}
										}
									});
									return tipText;
								}
							},
							area: {
								opacity: 0.2,
								gradient: true
							}
						},
						touchMoveLimit: 120,
						touchMoveThreshold: 0,
						animation: false,
						duration: 800,
						rotateLock: false,
						background: '#ffffff',
						width: window.innerWidth,
						height: 300
					},
					powerFactor: {
						color: ["#1890FF", "#2FC25B", "#F04864", "#FACC14", "#8543E0", "#13C2C2", "#3436C7"],
						padding: [15, 15, 10, 15],
						enableScroll: true,
						enableMarkLine: false,
						dataPointShape: true,
						dataLabel: false,
						legend: {
							show: true,
							position: 'top',
							float: 'center',
							padding: 5,
							margin: 0,
							backgroundColor: 'rgba(0,0,0,0)',
							borderColor: 'rgba(0,0,0,0)',
							itemGap: 10,
							fontSize: 10,
							lineHeight: 12,
							fontColor: '#333333',
							pageMode: true,
							pageIconColor: '#1890FF',
							pageIconInactiveColor: '#CCCCCC',
							pageIconSize: 8,
							pageIconMargin: 10,
							pageTextStyle: {
								color: '#666666',
								fontSize: 10
							},
							formatter: (name) => {
								if (name.includes(' ')) {
									const parts = name.split(' ');
									return parts[0];
								}
								if (name.length > 10) {
									return name.substring(0, 10) + '...';
								}
								return name;
							},
							filter: (legendItem) => {
								return legendItem.showInLegend !== false;
							}
						},
						xAxis: {
							disableGrid: true,
							scrollShow: true,
							scrollAlign: 'left',
							scrollBackgroundColor: '#F6F6F6',
							scrollColor: '#A8A8A8',
							itemCount: 4,
							labelCount: 4,
							boundaryGap: 'center',
							axisLine: true,
							axisLineColor: '#CCCCCC',
							rotateLabel: false,
							fontSize: 12,
							margin: 15,
							format: (val) => {
								return val;
							}
						},
						yAxis: {
							data: [{
								min: 0,
								max: 5,
								title: '',
								format: (val) => {
									return val.toFixed(1);
								}
							}],
							showTitle: true,
							titleFontSize: 12,
							titleOffsetX: 0,
							titleOffsetY: 0,
							titleAlign: 'center',
							gridType: 'dash',
							dashLength: 4,
							splitNumber: 5
						},
						extra: {
							line: {
								type: "curve",
								width: 2.5,
								activeType: "none",
								linearType: "custom",
								activeOpacity: 0.8,
								lineCap: "round"
							},
							tooltip: {
								showBox: true,
								showArrow: true,
								showCategory: true,
								borderWidth: 0,
								borderRadius: 4,
								borderColor: '#000000',
								borderOpacity: 0.7,
								bgColor: '#000000',
								bgOpacity: 0.7,
								gridType: 'dash',
								dashLength: 4,
								gridColor: '#CCCCCC',
								fontColor: '#FFFFFF',
								horizentalLine: false,
								xAxisLabel: true,
								yAxisLabel: false,
								labelBgColor: '#FFFFFF',
								labelBgOpacity: 0.7,
								labelFontColor: '#666666',
								maxWidth: 300,
								scrollable: true,
								formatter: (item, category, index, opts) => {
									const timePoint = category;
									let tipText = timePoint + '\n';
									const data = opts.series;
									data.forEach(series => {
										const value = series.data[index];
										if (value !== null && value !== undefined) {
											const valueFixed = parseFloat(value).toFixed(2);
											const date = series.date;
											if (date) {
												const dateParts = date.split('-');
												let formattedDate = date;
												if (dateParts.length === 3) {
													const [year, month, day] = dateParts;
													formattedDate =
														`${year}年${parseInt(month)}月${parseInt(day)}日`;
												}
												const baseName = series.name.split(' ')[0];
												tipText += `${baseName} (${formattedDate}): ${valueFixed}\n`;
											} else {
												tipText += `${series.name}: ${valueFixed}\n`;
											}
										}
									});
									return tipText;
								}
							},
							area: {
								opacity: 0.2,
								gradient: true
							}
						},
						touchMoveLimit: 120,
						touchMoveThreshold: 0,
						animation: false,
						duration: 800,
						rotateLock: false,
						background: '#ffffff',
						width: window.innerWidth,
						height: 300
					}
				}
			};
		},
		watch: {
			queryParams: {
				handler(newVal, oldVal) {
					console.log('loadData - 查询参数变更:', newVal);
					const isChanged = JSON.stringify(newVal) !== JSON.stringify(oldVal);
					if (isChanged) {
						this.fetchData();
					}
				},
				deep: true
			}
		},
		onLoad() {
			this.fetchData();
		},
		onReady() {
			this.$nextTick(() => {
				this.calcContentHeight();
			});
		},
		mounted() {
			const effectiveParams = this.getEffectiveQueryParams();
			console.log('负荷数据mounted周期：', effectiveParams);
		},
		activated() {
			console.log('负荷数据组件被激活');
			this.getSystemInfo();
			this.calcContentHeight();
			this.fetchData();
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token
				});
			},
			switchViewMode(mode, viewMode) {
				if (this.viewModes[mode] === viewMode) return;
				this.viewModes[mode] = viewMode;
			},
			showChartSwitchToast(message) {
				uni.showToast({
					title: message,
					icon: 'none',
					duration: 800,
					position: 'bottom'
				});
			},
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
				this.screenWidth = systemInfo.screenWidth || 0;
				this.screenHeight = systemInfo.screenHeight || 0;
			},
			calcContentHeight() {
				if (!this.windowHeight) {
					this.getSystemInfo();
				}

				const topHeight = this.statusBarHeight + this.navbarHeight + this.tabNavHeight;

				this.contentHeight = this.windowHeight - topHeight;
			},
			screenRotate() {
				console.log('手动触发屏幕旋转');
				const direction = uni.getStorageSync('direction');
				if(direction) {
					this.direction = direction;
					if(this.direction === '1') {
						console.log('竖屏')
						uni.setStorageSync('direction', '0');
						this.direction = '0'
					}else{
						console.log('横屏')
						uni.setStorageSync('direction', '1');
						this.direction = '1'
					}
					wx.invoke("ext_screenControl_direction", {
							data: {
								orientation:  this.direction
							}
						},
						(res) => {
							console.log(JSON.stringify(res));
							if (res.error_msg === 'ext_screenControl_direction:ok') {
								console.log(res.result);
								this.updateChartForOrientation();
							}
						});
				}else{
					if(this.direction === '1') {
						console.log('竖屏')
						uni.setStorageSync('direction', '0');
						this.direction = '0'
					}else{
						console.log('横屏')
						uni.setStorageSync('direction', '1');
						this.direction = '1'
					}
					wx.invoke("ext_screenControl_direction", {
							data: {
								orientation:  this.direction
							}
						},
						(res) => {
							console.log(JSON.stringify(res));
							if (res.error_msg === 'ext_screenControl_direction:ok') {
								console.log(res.result);
								this.updateChartForOrientation();
							}
						});
				}
				
			},
			updateChartForOrientation() {
				this.chartHeight = this.direction === '0' ? 400 : 320;

				this.calcContentHeight();
			},
			getEffectiveQueryParams() {
				if (this.queryParams && Object.keys(this.queryParams).length > 0) {
					return {
						...this.queryParams
					};
				}

				const savedQueryParams = uni.getStorageSync('userDataQueryParams');
				if (savedQueryParams && Object.keys(savedQueryParams).length > 0) {
					return {
						...savedQueryParams
					};
				}

				return {};
			},
			onRefresh() {
				this.isRefreshing = true;
				this.isChartLoading = true;

				this.fetchData();

				setTimeout(() => {
					this.isRefreshing = false;

					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}, 1500);
			},
			getMockData() {
				const data = {
					ia: [{
						"2025-05-11": [
							0.055, 0.056, 0.055, 0.055, 0.056, 0.055, 0.055, 0.055,
							0.055, 0.055, 0.055, 0.055, 0.055, 0.055, 0.055, 0.055,
							0.054, 0.054, 0.054, 0.054, 0.055, 0.054, 0.054, 0.054,
							0.054, 0.054, 0.054, 0.054, 0.054, 0.054, 0.054, 0.054,
							0.054, 0.054, 0.055, 0.054, 0.057, 0.055, 0.055, 0.054,
							0.054, 0.054, 0.054, 0.055, 0.054, 0.054, 0.054, 0.054,
							0.055, 0.054, 0.054, 0.054, 0.054, 0.054, 0.055, 0.054,
							0.054, 0.054, 0.054, 0.054, 0.054, 0.055, 0.055, 0.055,
							0.055, 0.055, 0.055, 0.054, 0.055, 0.055, 0.055, 0.056,
							0.055, 0.055, 0.056, 0.055, 0.056, 0.056, 0.056, 0.056,
							0.056, 0.056, 0.056, 0.056, 0.056, 0.057, 0.056, 0.056,
							0.056, 0.056, 0.056, 0.056, 0.056, 0.055, 0.056, 0.056,
							0.055
						]
					}],
					ib: [{
						"2025-05-11": [
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000
						]
					}],
					ic: [{
						"2025-05-11": [
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
							0.000
						]
					}],
					ua: [{
						"2025-05-11": [228.9, 229.3, 228.7, 229.3, 228.9, 229.6, 229.9, 230.1,
							229.7, 229.3, 229.8, 230.0, 229.9, 230.1, 230.3, 230.7,
							230.7, 230.5, 230.4, 230.3, 230.6, 229.9, 229.4, 229.9,
							229.4, 230.4, 230.0, 230.7, 230.0, 228.7, 228.2, 228.4,
							228.5, 228.9, 228.2, 227.5, 227.1, 227.5, 227.5, 227.0,
							226.5, 226.1, 226.1, 226.6, 226.6, 226.3, 227.1, 226.6,
							228.1, 227.8, 227.8, 227.3, 227.3, 227.4, 226.7, 226.6,
							226.9, 227.8, 227.6, 227.0, 227.2, 226.7, 227.7, 227.9,
							227.9, 226.4, 225.9, 226.2, 225.7, 226.3, 225.3, 226.1,
							225.5, 226.3, 226.1, 225.7, 225.7, 226.1, 225.8, 226.2,
							225.2, 226.1, 225.9, 225.3, 227.3, 227.8, 227.1, 227.8,
							228.7, 227.1, 227.3, 227.9, 228.5
						]
					}],
					ub: [{
						"2025-05-11": [
							230.5, 230.6, 230.9, 230.1, 231.3, 231.3, 231.5, 231.6,
							230.7, 230.9, 230.9, 231.7, 231.8, 231.8, 230.7, 230.9,
							230.8, 230.7, 230.9, 230.5, 230.8, 230.3, 229.9, 230.0,
							231.1, 230.0, 230.4, 229.8, 229.4, 229.3, 228.3, 228.9,
							228.8, 227.7, 228.2, 227.6, 228.5, 227.8, 226.6, 226.8,
							226.3, 226.3, 226.5, 227.3, 226.9, 226.5, 225.8, 227.0,
							226.3, 225.6, 226.3, 225.6, 225.9, 225.2, 225.0, 225.9,
							226.5, 226.1, 225.8, 225.3, 225.9, 226.3, 224.8, 225.2,
							225.4, 225.4, 225.3, 225.9, 226.7, 226.2, 225.6, 226.3,
							225.0, 226.1, 226.4, 225.7, 225.6, 226.0, 225.6, 226.5,
							226.9, 225.3, 226.0, 226.1, 226.4, 226.6, 227.3, 227.7,
							227.6, 225.4, 225.2, 226.3, 227.1, 227.0, 227.4, 227.8
						]
					}],
					uc: [{
						'2025-05-11': [
							230.2, 229.7, 229.2, 229.7, 229.7, 230.1, 230.6, 230.5,
							229.4, 230.4, 230.3, 229.9, 230.2, 230.3, 231.3, 231.4,
							231.2, 231.1, 231.3, 231.1, 230.9, 231.0, 230.5, 230.5,
							230.5, 230.6, 231.2, 231.1, 231.2, 230.5, 230.8, 230.3,
							229.7, 229.9, 229.6, 229.4, 228.2, 227.7, 228.3, 228.3,
							228.0, 227.8, 228.4, 226.4, 227.4, 227.3, 227.0, 227.6,
							227.7, 227.4, 226.9, 227.4, 226.7, 227.4, 227.9, 227.3,
							226.8, 226.3, 227.3, 227.0, 226.8, 226.3, 225.9, 226.5,
							226.7, 226.6, 226.8, 227.0, 226.5, 226.2, 227.3, 226.7,
							226.3, 226.6, 226.5, 226.0, 227.3, 227.2, 226.2, 227.1,
							226.9, 227.7, 227.7, 227.8, 228.8, 227.8, 228.4, 229.1,
							229.1, 228.2, 227.3, 227.7, 229.0, 228.4, 228.9, 229.1
						]
					}],
					pa: [{
						'2025-05-11': [
							0.0038, 0.0039, 0.0039, 0.0039, 0.0039, 0.0040, 0.0039, 0.0040,
							0.0039, 0.0040, 0.0039, 0.0040, 0.0038, 0.0039, 0.0040, 0.0040,
							0.0040, 0.0040, 0.0039, 0.0039, 0.0040, 0.0041, 0.0039, 0.0040,
							0.0040, 0.0040, 0.0040, 0.0039, 0.0039, 0.0039, 0.0040, 0.0040,
							0.0040, 0.0039, 0.0039, 0.0040, 0.0040, 0.0039, 0.0040, 0.0039,
							0.0040, 0.0040, 0.0039, 0.0039, 0.0038, 0.0040, 0.0039, 0.0039,
							0.0040, 0.0041, 0.0040, 0.0039, 0.0039, 0.0040, 0.0040, 0.0040,
							0.0040, 0.0040, 0.0039, 0.0040, 0.0039, 0.0040, 0.0040, 0.0038,
							0.0040, 0.0039, 0.0039, 0.0038, 0.0040, 0.0040, 0.0039, 0.0039,
							0.0039, 0.0040, 0.0039, 0.0040, 0.0038, 0.0038, 0.0039, 0.0038,
							0.0039, 0.0039, 0.0039, 0.0040, 0.0039, 0.0039, 0.0039, 0.0039,
							0.0040, 0.0040, 0.0040, 0.0039, 0.0040, 0.0040, 0.0040
						]
					}],
					pb: [{
						'2025-05-11': [
							0.0036, 0.0036, 0.0036, 0.0037, 0.0036, 0.0037, 0.0038, 0.0037,
							0.0037, 0.0037, 0.0036, 0.0037, 0.0036, 0.0036, 0.0037, 0.0037,
							0.0037, 0.0037, 0.0036, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037,
							0.0037, 0.0036, 0.0037, 0.0036, 0.0037, 0.0036, 0.0036, 0.0037,
							0.0036, 0.0036, 0.0037, 0.0037, 0.0038, 0.0036, 0.0036, 0.0036,
							0.0036, 0.0036, 0.0036, 0.0036, 0.0036, 0.0037, 0.0036, 0.0037,
							0.0037, 0.0037, 0.0037, 0.0037, 0.0036, 0.0037, 0.0036, 0.0036,
							0.0036, 0.0036, 0.0037, 0.0036, 0.0037, 0.0037, 0.0037, 0.0037,
							0.0036, 0.0037, 0.0036, 0.0036, 0.0036, 0.0037, 0.0036, 0.0037,
							0.0036, 0.0036, 0.0037, 0.0036, 0.0037, 0.0037, 0.0037, 0.0037,
							0.0036, 0.0036, 0.0037, 0.0036, 0.0037, 0.0036, 0.0036, 0.0036,
							0.0037, 0.0036, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037
						]
					}],
					pc: [{
						'2025-05-11': [
							0.0036, 0.0036, 0.0036, 0.0037, 0.0036, 0.0037, 0.0038, 0.0037,
							0.0037, 0.0037, 0.0036, 0.0037, 0.0036, 0.0036, 0.0037, 0.0037,
							0.0037, 0.0037, 0.0036, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037,
							0.0037, 0.0036, 0.0037, 0.0036, 0.0037, 0.0036, 0.0036, 0.0037,
							0.0036, 0.0036, 0.0037, 0.0037, 0.0038, 0.0036, 0.0036, 0.0036,
							0.0036, 0.0036, 0.0036, 0.0036, 0.0036, 0.0037, 0.0036, 0.0037,
							0.0037, 0.0037, 0.0037, 0.0037, 0.0036, 0.0037, 0.0036, 0.0036,
							0.0036, 0.0036, 0.0037, 0.0036, 0.0037, 0.0037, 0.0037, 0.0037,
							0.0036, 0.0037, 0.0036, 0.0036, 0.0036, 0.0037, 0.0036, 0.0037,
							0.0036, 0.0036, 0.0037, 0.0036, 0.0037, 0.0037, 0.0037, 0.0037,
							0.0036, 0.0036, 0.0037, 0.0036, 0.0037, 0.0036, 0.0036, 0.0036,
							0.0037, 0.0036, 0.0037, 0.0037, 0.0037, 0.0037, 0.0037
						]
					}],
					ca: [{
						'2025-05-11': [
							0.282, 0.279, 0.287, 0.288, 0.282, 0.297, 0.297, 0.293,
							0.292, 0.296, 0.288, 0.296, 0.285, 0.286, 0.292, 0.295,
							0.294, 0.291, 0.294, 0.292, 0.294, 0.296, 0.300, 0.299,
							0.298, 0.293, 0.300, 0.290, 0.297, 0.292, 0.297, 0.297,
							0.295, 0.296, 0.295, 0.297, 0.293, 0.289, 0.293, 0.300,
							0.296, 0.296, 0.300, 0.293, 0.292, 0.304, 0.297, 0.299,
							0.296, 0.299, 0.298, 0.298, 0.294, 0.303, 0.291, 0.296,
							0.295, 0.293, 0.298, 0.297, 0.298, 0.295, 0.302, 0.298,
							0.289, 0.296, 0.293, 0.292, 0.293, 0.294, 0.289, 0.291,
							0.289, 0.291, 0.293, 0.291, 0.291, 0.288, 0.288, 0.287,
							0.285, 0.282, 0.290, 0.286, 0.286, 0.284, 0.284, 0.281,
							0.286, 0.287, 0.290, 0.286, 0.289, 0.288, 0.285, 0.292
						]
					}],
					cb: [{
						'2025-05-11': [
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0
						]
					}],
					cc: [{
						'2025-05-11': [
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
							1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0
						]
					}],
				}
				return data;
			},
			fetchData() {
				this.isChartLoading = true;

				// 设置所有图表为加载状态
				this.loadingStatus = {
					voltage: true,
					current: true,
					power: true,
					powerFactor: true
				};

				const requestParams = this.getEffectiveQueryParams();
				console.log('查询参数',requestParams)
				if (this.isMockData) {
					const loadData = this.getMockData();
					if (loadData.ia || loadData.ib || loadData.ic) {
						this.processCurrentData(loadData.ia, loadData.ib, loadData.ic);
					}
					// 处理电压数据
					if (loadData.ua || loadData.ub || loadData.uc) {
						this.processVoltageData(loadData.ua, loadData.ub, loadData.uc);
					}
					// 处理功率数据
					if (loadData.pa || loadData.pb || loadData.pc) {
						this.processPowerData(loadData.pa, loadData.pb, loadData.pc);
					}
					// 处理功率因数数据
					if (loadData.ca || loadData.cb || loadData.cc) {
						this.processPowerFactorData(loadData.ca, loadData.cb, loadData.cc);
					}

				} else {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': this.token
						},
						data: {
							token: this.token,
							method: "PutHuaYun",
							uri: url,
							data: JSON.stringify({
								"bizCode": bizCode,
								"espFlowId": getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": getCurrentTimestamp(),
								"espInformation": {
									"service": "DtsUserController",
									"method": "getUserLoad",
									"data": {
										"meterAssetNo": requestParams.meterAssetNo || "",
										"statDateStart": requestParams.dateRange[0],
										"statDateEnd": requestParams.dateRange[1]
									},
								}
							})
						},
						success: (res) => {
							console.log("负荷数据查询结果:", res);
							if (res && res.data.Tag === 1) {
								const rtnData = res.data.Data.espInformation;
								if (rtnData && rtnData.code == 200) {
									const loadData = rtnData.data;

									// 处理电流数据
									if (loadData.ia || loadData.ib || loadData.ic) {
										this.processCurrentData(loadData.ia, loadData.ib, loadData.ic);
									} else {
										console.warn("未找到电流数据");
										this.loadingStatus.current = false;
									}

									// 处理电压数据
									if (loadData.ua || loadData.ub || loadData.uc) {
										this.processVoltageData(loadData.ua, loadData.ub, loadData.uc);
									} else {
										console.warn("未找到电压数据");
										this.loadingStatus.voltage = false;
									}

									// 处理功率数据
									if (loadData.pa || loadData.pb || loadData.pc) {
										this.processPowerData(loadData.pa, loadData.pb, loadData.pc);
									} else {
										console.warn("未找到功率数据");
										this.loadingStatus.power = false;
									}

									// 处理功率因数数据
									if (loadData.ca || loadData.cb || loadData.cc) {
										this.processPowerFactorData(loadData.ca, loadData.cb, loadData.cc);
									} else {
										console.warn("未找到功率因数数据");
										this.loadingStatus.powerFactor = false;
									}
								}else{
									uni.showToast({
										title: '暂无数据',
										icon: 'none',
										duration: 2000
									});
								}
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}

			},
			resetLoadingStatus() {
				this.isChartLoading = false;
				this.loadingStatus = {
					voltage: false,
					current: false,
					power: false,
					powerFactor: false
				};
			},
			processCurrentData(phaseA, phaseB, phaseC) {
				const categories = [];

				for (let i = 0; i < 96; i++) {
					const hour = Math.floor(i / 4);
					const minute = (i % 4) * 15;
					const timeLabel = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
					categories.push(timeLabel);
				}

				const allDates = new Set();

				[phaseA, phaseB, phaseC].forEach(phaseData => {
					if (phaseData && Array.isArray(phaseData)) {
						phaseData.forEach(dateObj => {
							const date = Object.keys(dateObj)[0];
							allDates.add(date);
						});
					}
				});

				const dateList = Array.from(allDates).sort();
				console.log("可用电流日期列表:", dateList);

				const phaseSeries = [{
						name: 'A相',
						phaseKey: 'A',
						dateData: {},
					},
					{
						name: 'B相',
						phaseKey: 'B',
						dateData: {},
					},
					{
						name: 'C相',
						phaseKey: 'C',
						dateData: {},
					}
				];

				this.processPhaseDataByDate(phaseA, phaseSeries[0].dateData);
				this.processPhaseDataByDate(phaseB, phaseSeries[1].dateData);
				this.processPhaseDataByDate(phaseC, phaseSeries[2].dateData);

				const allSeries = [];

				const seriesA = this.getSeriesForPhase(phaseSeries[0], dateList);
				allSeries.push(...seriesA);

				const seriesB = this.getSeriesForPhase(phaseSeries[1], dateList);
				allSeries.push(...seriesB);

				const seriesC = this.getSeriesForPhase(phaseSeries[2], dateList);
				allSeries.push(...seriesC);

				if (allSeries.length === 0) {
					allSeries.push({
						name: 'A相',
						phaseKey: 'A',
						data: new Array(96).fill(null),
						format: (val) => val ? val.toFixed(2) : '0.00'
					});
				}

				const allValues = allSeries
					.flatMap(series => series.data)
					.filter(val => val !== null && val !== undefined);

				const maxValue = allValues.length > 0 ? Math.max(...allValues) : 5;
				const minValue = allValues.length > 0 ? Math.min(...allValues) : 0;
				// const yAxisMax = Math.ceil(maxValue);
				// const yAxisMin = Math.ceil(minValue);
				this.chartOpts.current.yAxis.data[0].max = maxValue;
				this.chartOpts.current.yAxis.data[0].min = minValue;
				this.chartData.current = {
					categories: categories,
					series: allSeries
				};

				this.dateList.current = dateList;

				// 生成表格数据
				this.generateTableData(categories, allSeries, phaseSeries, 'current');

				if (dateList.length > 0) {
					this.selectedDates.current = dateList[0];
					this.generateMobileTableData(categories, dateList[0], phaseSeries, 'current');
				}

				console.log("处理后的电流图表数据:", this.chartData.current);

				this.loadingStatus.current = false;
			},
			processVoltageData(phaseA, phaseB, phaseC) {
				const categories = [];

				

				const allDates = new Set();

				[phaseA, phaseB, phaseC].forEach(phaseData => {
					if (phaseData && Array.isArray(phaseData)) {
						phaseData.forEach(dateObj => {
							const date = Object.keys(dateObj)[0];
							
							allDates.add(date);
						});
					}
				});

				const dateList = Array.from(allDates).sort();
				console.log("可用电压日期列表:", dateList);
				dateList.forEach((item) => {
					for (let i = 0; i < 96; i++) {
						const hour = Math.floor(i / 4);
						const minute = (i % 4) * 15;
						const timeLabel = `${item} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
						categories.push(timeLabel);
					}
				})
				const phaseSeries = [{
						name: 'A相',
						phaseKey: 'A',
						dateData: {},
					},
					{
						name: 'B相',
						phaseKey: 'B',
						dateData: {},
					},
					{
						name: 'C相',
						phaseKey: 'C',
						dateData: {},
					}
				];

				this.processPhaseDataByDate(phaseA, phaseSeries[0].dateData);
				this.processPhaseDataByDate(phaseB, phaseSeries[1].dateData);
				this.processPhaseDataByDate(phaseC, phaseSeries[2].dateData);

				const allSeries = [];

				const seriesA = this.getSeriesForPhase(phaseSeries[0], dateList);
				allSeries.push(...seriesA);

				const seriesB = this.getSeriesForPhase(phaseSeries[1], dateList);
				allSeries.push(...seriesB);

				const seriesC = this.getSeriesForPhase(phaseSeries[2], dateList);
				allSeries.push(...seriesC);

				if (allSeries.length === 0) {
					allSeries.push({
						name: 'A相',
						phaseKey: 'A',
						data: new Array(96).fill(null),
						format: (val) => val ? val.toFixed(2) : '0.00'
					});
				}

				const allValues = allSeries
					.flatMap(series => series.data)
					.filter(val => val !== null && val !== undefined);
				const maxValue = allValues.length > 0 ? Math.max(...allValues) : 5;
				const minValue = allValues.length > 0 ? Math.min(...allValues) : 0;
				// const yAxisMax = Math.ceil(maxValue);
				// const yAxisMin = Math.ceil(minValue);
				
				this.chartOpts.voltage.yAxis.data[0].max = maxValue;
				this.chartOpts.voltage.yAxis.data[0].min = minValue;
				this.chartData.voltage = {
					categories: categories,
					series: allSeries
				};

				this.dateList.voltage = dateList;

				// 生成表格数据
				this.generateTableData(categories, allSeries, phaseSeries, 'voltage');

				if (dateList.length > 0) {
					this.selectedDates.voltage = dateList[0];
					this.generateMobileTableData(categories, dateList[0], phaseSeries, 'voltage');
				}

				console.log("处理后的电压图表数据:", this.chartData.voltage);

				this.loadingStatus.voltage = false;
			},
			processPowerData(phaseA, phaseB, phaseC) {
				const categories = [];

				for (let i = 0; i < 96; i++) {
					const hour = Math.floor(i / 4);
					const minute = (i % 4) * 15;
					const timeLabel = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
					categories.push(timeLabel);
				}

				const allDates = new Set();

				[phaseA, phaseB, phaseC].forEach(phaseData => {
					if (phaseData && Array.isArray(phaseData)) {
						phaseData.forEach(dateObj => {
							const date = Object.keys(dateObj)[0];
							allDates.add(date);
						});
					}
				});

				const dateList = Array.from(allDates).sort();
				console.log("可用功率日期列表:", dateList);

				const phaseSeries = [{
						name: 'A相',
						phaseKey: 'A',
						dateData: {},
					},
					{
						name: 'B相',
						phaseKey: 'B',
						dateData: {},
					},
					{
						name: 'C相',
						phaseKey: 'C',
						dateData: {},
					}
				];

				this.processPhaseDataByDate(phaseA, phaseSeries[0].dateData);
				this.processPhaseDataByDate(phaseB, phaseSeries[1].dateData);
				this.processPhaseDataByDate(phaseC, phaseSeries[2].dateData);

				const allSeries = [];

				const seriesA = this.getSeriesForPhase(phaseSeries[0], dateList);
				allSeries.push(...seriesA);

				const seriesB = this.getSeriesForPhase(phaseSeries[1], dateList);
				allSeries.push(...seriesB);

				const seriesC = this.getSeriesForPhase(phaseSeries[2], dateList);
				allSeries.push(...seriesC);

				if (allSeries.length === 0) {
					allSeries.push({
						name: 'A相',
						phaseKey: 'A',
						data: new Array(96).fill(null),
						format: (val) => val ? val.toFixed(2) : '0.00'
					});
				}

				const allValues = allSeries
					.flatMap(series => series.data)
					.filter(val => val !== null && val !== undefined);

				const maxValue = allValues.length > 0 ? Math.max(...allValues) : 5;
				const minValue = allValues.length > 0 ? Math.min(...allValues) : 0;
				// const yAxisMax = Math.ceil(maxValue);
				// const yAxisMin = Math.ceil(minValue);
				this.chartOpts.power.yAxis.data[0].max = maxValue;
				this.chartOpts.power.yAxis.data[0].min = minValue;
				this.chartData.power = {
					categories: categories,
					series: allSeries
				};

				this.dateList.power = dateList;

				// 生成表格数据
				this.generateTableData(categories, allSeries, phaseSeries, 'power');

				if (dateList.length > 0) {
					this.selectedDates.power = dateList[0];
					this.generateMobileTableData(categories, dateList[0], phaseSeries, 'power');
				}

				console.log("处理后的功率图表数据:", this.chartData.power);

				this.loadingStatus.power = false;
			},
			processPowerFactorData(phaseA, phaseB, phaseC) {
				const categories = [];

				for (let i = 0; i < 96; i++) {
					const hour = Math.floor(i / 4);
					const minute = (i % 4) * 15;
					const timeLabel = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
					categories.push(timeLabel);
				}

				const allDates = new Set();

				[phaseA, phaseB, phaseC].forEach(phaseData => {
					if (phaseData && Array.isArray(phaseData)) {
						phaseData.forEach(dateObj => {
							const date = Object.keys(dateObj)[0];
							allDates.add(date);
						});
					}
				});

				const dateList = Array.from(allDates).sort();
				console.log("可用功率因数日期列表:", dateList);

				const phaseSeries = [{
						name: 'A相',
						phaseKey: 'A',
						dateData: {},
					},
					{
						name: 'B相',
						phaseKey: 'B',
						dateData: {},
					},
					{
						name: 'C相',
						phaseKey: 'C',
						dateData: {},
					}
				];

				this.processPhaseDataByDate(phaseA, phaseSeries[0].dateData);
				this.processPhaseDataByDate(phaseB, phaseSeries[1].dateData);
				this.processPhaseDataByDate(phaseC, phaseSeries[2].dateData);

				const allSeries = [];

				const seriesA = this.getSeriesForPhase(phaseSeries[0], dateList);
				allSeries.push(...seriesA);

				const seriesB = this.getSeriesForPhase(phaseSeries[1], dateList);
				allSeries.push(...seriesB);

				const seriesC = this.getSeriesForPhase(phaseSeries[2], dateList);
				allSeries.push(...seriesC);

				if (allSeries.length === 0) {
					allSeries.push({
						name: 'A相',
						phaseKey: 'A',
						data: new Array(96).fill(null),
						format: (val) => val ? val.toFixed(2) : '0.00'
					});
				}

				const allValues = allSeries
					.flatMap(series => series.data)
					.filter(val => val !== null && val !== undefined);

				const maxValue = allValues.length > 0 ? Math.max(...allValues) : 5;
				const minValue = allValues.length > 0 ? Math.min(...allValues) : 0;
				this.chartOpts.powerFactor.yAxis.data[0].max = maxValue;
				this.chartOpts.powerFactor.yAxis.data[0].min = minValue;
				this.chartData.powerFactor = {
					categories: categories,
					series: allSeries
				};

				this.dateList.powerFactor = dateList;

				// 生成表格数据
				this.generateTableData(categories, allSeries, phaseSeries, 'powerFactor');

				if (dateList.length > 0) {
					this.selectedDates.powerFactor = dateList[0];
					this.generateMobileTableData(categories, dateList[0], phaseSeries, 'powerFactor');
				}

				console.log("处理后的功率因数图表数据:", this.chartData.powerFactor);

				this.loadingStatus.powerFactor = false;
			},
			processPhaseData(phaseA, phaseB, phaseC) {
				// 将原来的processPhaseData方法重命名为processCurrentData
				// 保留原有代码以供参考
			},
			processPhaseDataByDate(phaseData, dateDataMap) {
				if (!phaseData || !Array.isArray(phaseData)) {
					return;
				}

				phaseData.forEach(dateObj => {
					const date = Object.keys(dateObj)[0];
					const values = dateObj[date];

					if (Array.isArray(values) && values.length > 0) {
						const dataArray = new Array(96).fill(null);

						for (let i = 0; i < Math.min(values.length, 96); i++) {
							dataArray[i] = values[i];
						}

						dateDataMap[date] = dataArray;
					}
				});
			},
			getSeriesForPhase(phaseInfo, dateList) {
				const seriesList = [];
				const {
					name,
					phaseKey,
					dateData
				} = phaseInfo;

				const phaseColorSet = this.phaseColors[name] || [];

				const dates = Object.keys(dateData).sort();
				dates.forEach((date, dateIndex) => {
					const seriesName = name;
					const formattedDate = this.formatDateLabel(date);
					const colorIndex = dateIndex % phaseColorSet.length;
					const shapeIndex = dateIndex % this.dataPointShapes.length;
					const lineStyleIndex = dateIndex % this.lineStyles.length;

					const showInLegend = dateIndex === 0;
					
					
					seriesList.push({
						name: seriesName,
						phaseKey: phaseKey,
						showInLegend: showInLegend,
						date: date,
						dateLabel: formattedDate,
						data: dateData[date],
						format: (val) => val ? val.toFixed(2) : '0.00',
						color: phaseColorSet[colorIndex],
						pointShape: this.dataPointShapes[shapeIndex],
						lineStyle: this.lineStyles[lineStyleIndex]
					});
				});

				return seriesList;
			},
			generateTableData(timePoints, seriesList, phaseSeries, mode) {
			  // 收集所有日期
			  const dateSet = new Set();
			  seriesList.forEach(series => {
			    if (series.date) dateSet.add(series.date);
			  });
			  
			  const dates = Array.from(dateSet).sort();
			  this.dateList[mode] = dates;
			  
			  // 按日期初始化数据结构
			  dates.forEach(date => {
			    if (!this.tableData[mode][date]) {
			      this.tableData[mode][date] = [];
			    }
			  });
			
			  // 处理每个相的数据
			  const phases = ['A相', 'B相', 'C相'];
			  phases.forEach((phase, phaseIndex) => {
			    const phaseData = phaseSeries[phaseIndex].dateData;
			    
			    dates.forEach(date => {
			      const dataForDate = phaseData[date] || new Array(96).fill(null);
			      
			      // 确保每个时间点都有数据
			      for (let i = 0; i < 96; i++) {
			        const timeLabel = this.generateTimeLabel(i);
			        
			        // 初始化或更新行数据
			        if (!this.tableData[mode][date][i]) {
			          this.tableData[mode][date][i] = {
			            time: timeLabel,
			            phaseA: '-',
			            phaseB: '-', 
			            phaseC: '-'
			          };
			        }
			        
			        // 设置当前相的数据
			        this.tableData[mode][date][i][`phase${phase.charAt(0)}`] = 
			          this.formatNumber(dataForDate[i]);
			      }
			    });
			  });
			  // 设置默认选中日期
			  if (dates.length > 0 && !this.selectedDates[mode]) {
			    this.selectedDates[mode] = dates[0];
			  }
			},
			
			// 生成时间标签的辅助方法
			generateTimeLabel(index) {
			  const hour = Math.floor(index / 4);
			  const minute = (index % 4) * 15;
			  return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
			},
			generateMobileTableData(timePoints, date, phaseSeries, mode) {
				if (!date || !phaseSeries) {
					this.mobileTableData = [];
					return;
				}

				const mobileData = [];

				const phaseAData = phaseSeries[0].dateData[date] || new Array(timePoints.length).fill(null);
				const phaseBData = phaseSeries[1].dateData[date] || new Array(timePoints.length).fill(null);
				const phaseCData = phaseSeries[2].dateData[date] || new Array(timePoints.length).fill(null);

				for (let i = 0; i < timePoints.length; i++) {
					mobileData.push({
						time: timePoints[i],
						phaseA: this.formatNumber(phaseAData[i]),
						phaseB: this.formatNumber(phaseBData[i]),
						phaseC: this.formatNumber(phaseCData[i])
					});
				}

				// 根据不同的图表类型存储移动端表格数据
				this.mobileTableData[mode] = mobileData;
			},
			formatNumber(num) {
				if (num === undefined || num === null || isNaN(num)) {
					return '-';
				}
				return parseFloat(num).toFixed(2);
			},
			formatDateLabel(dateStr) {
				const parts = dateStr.split('-');
				if (parts.length === 3) {
					return `${parseInt(parts[0])}年${parseInt(parts[1])}月${parseInt(parts[2])}日`;
				}
				return dateStr;
			},
			selectDateForTable(mode, date) {
				if (this.selectedDates[mode] === date) return;

				this.selectedDates[mode] = date;

				const categories = this.chartData[mode].categories;
				const phaseSeries = [{
						name: 'A相',
						phaseKey: 'A',
						dateData: {},
					},
					{
						name: 'B相',
						phaseKey: 'B',
						dateData: {},
					},
					{
						name: 'C相',
						phaseKey: 'C',
						dateData: {},
					}
				];

				this.chartData[mode].series.forEach(series => {
					if (!series.date) return;

					const phaseIdx = ['A相', 'B相', 'C相'].indexOf(series.name.split(' ')[0]);
					if (phaseIdx === -1) return;

					if (!phaseSeries[phaseIdx].dateData[series.date]) {
						phaseSeries[phaseIdx].dateData[series.date] = [];
					}
					phaseSeries[phaseIdx].dateData[series.date] = series.data;
				});

				this.generateMobileTableData(categories, date, phaseSeries, mode);
			}
		}
	};
</script>

<style lang="scss" scoped>
	page {
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	.load-data-content {
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
		position: relative;
		width: 100%;
		height: 100%;
	}

	.content-section {
		box-sizing: border-box;
		background-color: #f5f5f5;
		padding: 10px 15px;
		-webkit-overflow-scrolling: touch;
		overflow-y: auto;
		width: 100%;
	}

	.chart-item-container {
		background-color: #fff;
		border-radius: 8px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
		padding: 10px 15px;
		margin-bottom: 15px;
	}

	.chart-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px 0;
		margin-bottom: 10px;
		border-bottom: 1px solid #f0f0f0;
	}

	.chart-title {
		font-size: 16px;
		font-weight: bold;
		color: #262626;
		flex: 1;
	}

	.chart-toggle {
		display: flex;
		align-items: center;
		gap: 8px;
		z-index: 9;
	}

	.toggle-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 36rpx;
		height: 36rpx;
		padding: 5rpx;
		border-radius: 4px;
		background-color: #f5f5f5;
		transition: all 0.2s ease;
		cursor: pointer;
		margin-right: 20rpx;
		&.active {
			background-color: #07ac7c;
		}
	}

	.chart-icon {
		font-size: 12px;
		color: #666;

		.active & {
			color: #fff;
		}
	}

	.screen-rotate {
		width: 30rpx;
		height: 30rpx;
		background: url('~@/static/icons/screen.png') no-repeat;
		background-size: 100% 100%;
	}

	.chart-view {
		position: relative;
		transition: opacity 0.3s ease;
	}

	.chart-container {
		width: 100%;
		min-height: 200px;
		position: relative;
		margin-top: 0;
		margin-bottom: 0;
		padding-top: 0;
		transition: opacity 0.3s ease-in-out;
	}

	.data-view {
		width: 100%;
		overflow: hidden;
		position: relative;
		transition: all 0.3s;
	}

	.date-scroll {
		width: 100%;
		white-space: nowrap;
		overflow-x: auto;
		padding: 10px 0;
		// margin-bottom: 10px;
		background: #f9f9f9;
		border-bottom: 1px solid #ebeef5;
	}

	.date-item {
		display: inline-block;
		padding: 10rpx 20rpx;
		background-color: #f0f0f0;
		border-radius: 4px;
		font-size: 28rpx;
		color: #606266;
		transition: all 0.2s;

		&.active {
			background-color: #07ac7c;
			color: #fff;
			box-shadow: 0 2px 4px rgba(7, 172, 124, 0.2);
		}

		&:first-child {
			margin-left: 20rpx;
		}

		&:last-child {
			margin-right: 20rpx;
		}
	}

	.data-table {
		width: 100%;
		border: 1px solid #ebeef5;
		border-radius: 4px;
		overflow: hidden;
	}

	.table-header {
		display: flex;
		background-color: #f5f7fa;
		border-bottom: 1px solid #ebeef5;
		height: 40px;
		font-weight: bold;
	}

	.table-body {
		width: 100%;
		overflow-y: auto;
		background-color: #ffffff;
	}

	.table-row {
		display: flex;
		border-bottom: 1px solid #ebeef5;

		&:last-child {
			border-bottom: none;
		}

		&:nth-child(odd) {
			background-color: #fafafa;
		}
	}

	.table-cell {
		flex: 1;
		min-height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #606266;
		padding: 16rpx 20rpx;
		text-align: center;
		box-sizing: border-box;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		&.time-cell {
			background-color: #f5f7fa;
			font-weight: 500;
			border-right: 1px solid #ebeef5;
			width: 160rpx;
			flex: none;
		}
	}

	.empty-data {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;
	}

	.empty-icon {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background-color: #f5f7fa;
		color: #909399;
		font-size: 24px;
		font-weight: bold;
	}

	.empty-text {
		margin-top: 10px;
		color: #909399;
		font-size: 14px;
	}

	.bottom-space {
		height: 20px;
	}

	.chart-loading {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		display: flex;
		flex-direction: column;
		align-items: center;
		z-index: 10;
		transition: opacity 0.3s ease-in-out;
	}

	.loading-text {
		margin-top: 10px;
		font-size: 14px;
		color: #666;
	}

	@media screen and (max-width: 375px) {
		.chart-title {
			font-size: 14px;
		}

		.table-cell {
			min-width: 140rpx;
			font-size: 24rpx;
			padding: 12rpx 16rpx;
		}

		.time-cell {
			min-width: 160rpx;
		}
	}

	@media screen and (orientation: landscape) {
		.chart-container {
			min-height: 300px;
		}

		.chart-item-container {
			margin-bottom: 20px;
		}

		.chart-header {
			padding: 12px 0;
		}

		.chart-title {
			font-size: 18px;
		}
	}
</style>