<template>
	<view class="query-search">
		<!-- 顶部导航区域 - 固定 -->
		<view class="header-section">
			<custom-navbar title="查询" :showBack="true"></custom-navbar>
		</view>
		
		<!-- 中间表单区域 - 可滚动 -->
		<scroll-view class="form-section" scroll-y :style="{ height: scrollViewHeight + 'px', top: safeAreaTop + 'px' }">
			<view class="form-content">
				<!-- 法律法规类型 -->
				<view class="form-item">
					<view class="form-label">法律法规类型</view>
					<view class="form-content">
						<u-subsection
							:list="caseTypes"
							:current="caseTypeIndex"
							@change="caseTypeChange"
							activeColor="#07ac7c"
							inactiveColor="#333333"
							mode="button"
							:animation="false"
							fontSize="28rpx"
							height="70rpx"
							buttonColor="#f5f5f5"
						></u-subsection>
					</view>
				</view>
				
				<!-- 法律法规名称 -->
				<view class="form-item">
					<view class="form-label">法律法规名称</view>
					<view class="form-content">
						<u-input
							v-model="searchForm.fileName"
							placeholder="请输入法律法规名称"
							border="surround"
							clearable
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
							@blur="handleInputBlur"
							@focus="handleInputFocus"
						></u-input>
					</view>
				</view>
				
				<!-- 法条内容 -->
				<view class="form-item">
					<view class="form-label">法条内容</view>
					<view class="form-content">
						<u-textarea
							v-model="searchForm.fileDescribe"
							placeholder="请输入法条内容"
							count
							maxlength="800"
							height="240rpx"
							fontSize="24rpx"
							:customStyle="{
								backgroundColor: '#FFFFFF',
								borderRadius: '16rpx',
								borderColor: '#f0f0f0'
							}"
						></u-textarea>
					</view>
				</view>
				
				<!-- 发布机关 -->
				<view class="form-item">
					<view class="form-label">发布机关</view>
					<view class="form-content">
						<u-input
							v-model="searchForm.lssuingUnit"
							placeholder="请输入发布机关"
							border="surround"
							clearable
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
							@blur="handleInputBlur"
							@focus="handleInputFocus"
						></u-input>
					</view>
				</view>
				
				<!-- 预留空间，确保小屏幕上也能滚动到底部的所有内容 -->
				<view class="form-space"></view>
			</view>
		</scroll-view>
		
		<!-- 底部按钮区域 - 固定 -->
		<view class="footer-section">
			<view class="btn-group">
				<view class="btn btn-reset" @click="resetForm">
					<text>重置选择</text>
				</view>
				<view class="btn btn-confirm" @click="confirmSearch">
					<text>确定</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				navbarHeight: 44, // 导航栏固定高度，单位px
				headerHeight: 0, // 导航栏总高度，单位px
				footerHeight: 120, // 底部按钮区域高度，单位rpx
				windowHeight: 0,
				scrollViewHeight: 0,
				safeAreaTop: 0, // 顶部安全区域高度，单位px
				isKeyboardShow: false, // 键盘是否显示
				keyboardHeight: 0, // 键盘高度
				platform: '', // 平台类型
				
				// 案例类型选项
				caseTypes: ['国家法律', '行政法规','部门规章'],
				caseTypeIndex: 0,
				
				// 搜索表单
				searchForm: {
					lawType: '01',
					fileName:"",
					fileDescribe:"",
					lssuingUnit:""
				},
				
				// 输入框样式
				inputStyle: {
					height: '80rpx',
					fontSize: '30rpx',
					padding: '0 24rpx',
					borderColor: '#e0e0e0',
					borderWidth: '1rpx',
					borderRadius: '16rpx',
					borderStyle: 'solid',
					color: '#333',
					backgroundColor: '#fff'
				},
			};
		},
		onLoad() {
			// 获取状态栏高度等信息
			this.getStatusBarHeight();
			
			// 获取平台信息
			const systemInfo = uni.getSystemInfoSync();
			this.platform = systemInfo.platform;
			
			// 监听软键盘高度变化
			uni.onKeyboardHeightChange(res => {
				this.isKeyboardShow = res.height > 0;
				this.keyboardHeight = res.height;
				// 键盘高度变化时重新计算滚动区域高度
				this.calcScrollViewHeight();
			});
		},
		onReady() {
			// 计算滚动区域高度
			this.calcScrollViewHeight();
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
				
				// 计算头部总高度（导航栏）
				this.headerHeight = this.statusBarHeight + this.navbarHeight;
				this.safeAreaTop = this.headerHeight;
			},
			
			// 计算滚动区域高度
			calcScrollViewHeight() {
				// 底部区域高度从rpx转为px进行计算
				const footerHeightPx = uni.upx2px(this.footerHeight);
				
				// 考虑键盘高度对滚动区域的影响
				let availableHeight = this.windowHeight - this.headerHeight - footerHeightPx;
				
				// 安卓平台特殊处理 - 当键盘弹出时，窗口高度会变化
				// #ifdef APP-ANDROID
				if (this.isKeyboardShow && this.keyboardHeight > 0) {
					// 安卓端键盘弹出时，可用高度减去键盘高度
					availableHeight = this.windowHeight - this.headerHeight - footerHeightPx - this.keyboardHeight;
				}
				// #endif
				
				this.scrollViewHeight = availableHeight;
			},
			
			// 处理输入框获取焦点
			handleInputFocus() {
				// 安卓端键盘弹出时处理
				// #ifdef APP-ANDROID
				setTimeout(() => {
					this.isKeyboardShow = true;
					this.calcScrollViewHeight();
				}, 300);
				// #endif
			},
			
			// 处理输入框失去焦点
			handleInputBlur() {
				// 安卓端键盘收起时处理
				// #ifdef APP-ANDROID
				setTimeout(() => {
					this.isKeyboardShow = false;
					this.calcScrollViewHeight();
				}, 300);
				// #endif
			},
			
			// 案例类型切换
			caseTypeChange(index) {
				this.caseTypeIndex = index;
				const type = ['01','02','03']
				this.searchForm.lawType = type[index];
			},
			
			// 重置表单
			resetForm() {
				this.caseTypeIndex = 0;
				this.searchForm = {
					lawType: '01',
					fileName:"",
					fileDescribe:"",
					lssuingUnit:""
				};
			},
			
			// 确认搜索
			confirmSearch() {
				// 如果键盘显示，先让键盘收起
				uni.hideKeyboard();
				
				// 构建搜索参数
				const searchParams = {
					...this.searchForm
				};
				
				// 返回列表页面，并传递参数
				uni.navigateBack({
					delta: 1,
					success: () => {
						// 通过事件总线通知列表页面进行搜索
						uni.$emit('lawSearch', searchParams);
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #f5f5f5;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.query-search {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
}

/* 顶部导航区域 */
.header-section {
	background-color: #fff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

/* 中间表单区域 */
.form-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 120rpx; /* 底部按钮区域高度 */
	box-sizing: border-box;
	background-color: #f5f5f5;
	z-index: 99;
}

.form-content {
	padding: 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
	margin-bottom: env(safe-area-inset-bottom); /* 适配底部安全区域 */
}

.form-item {
	margin-bottom: 24rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 30rpx;
	font-weight: 400;
	color: #262626;
	margin-bottom: 12rpx;
	display: block;
}

.form-content {
	width: 100%;
}

.form-input {
	height: 80rpx;
}

/* 底部按钮区域 */
.footer-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	z-index: 100;
	border-top: 1rpx solid #f5f5f5;
	padding-bottom: env(safe-area-inset-bottom);
	/* 兼容安卓底部导航栏 */
	/* #ifdef APP-ANDROID */
	padding-bottom: 0;
	/* #endif */
}

/* 按钮组样式 */
.btn-group {
	display: flex;
	padding: 20rpx 40rpx;
}

.btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: 400;
	transition: all 0.2s;
}

.btn-reset {
	background-color: transparent;
	color: #07ac7c;
	border: none;
	margin-right: 40rpx;
}

.btn-reset:active {
	opacity: 0.8;
}

.btn-confirm {
	background-color: #07ac7c;
	color: #FFFFFF;
}

.btn-confirm:active {
	background-color: #33a085;
}

/* 为小屏幕预留滚动空间 */
.form-space {
	height: 60rpx;
	/* 安卓端键盘弹出时增加额外空间 */
	/* #ifdef APP-ANDROID */
	height: 120rpx;
	/* #endif */
}

/* 修改导航栏样式 */
/deep/ .custom-navbar {
	background-color: #fff !important;
	box-shadow: none !important;
}

/deep/ .navbar-left .svg-icon {
	color: #000000 !important;
}

/deep/ .navbar-title {
	color: #000000 !important;
}

/* 自定义u-input样式 */
/deep/ .u-input {
	height: 80rpx;
}

/deep/ .u-input__input {
	height: 80rpx;
	font-size: 30rpx;
	color: #333;
	padding: 0 24rpx;
}

/deep/ .u-input--border {
	border-color: #e0e0e0;
	border-width: 1rpx;
	border-radius: 16rpx;
	height: 80rpx;
}

/deep/ .u-input__placeholder-style {
	color: #cccccc;
	font-size: 30rpx;
}

/* 自定义u-subsection样式 */
/deep/ .u-subsection {
	margin-top: 10rpx;
}

/* 解决安卓输入框问题 */
/deep/ .u-input__content__field {
	display: flex;
	align-items: center;
	height: 100%;
}
</style>
