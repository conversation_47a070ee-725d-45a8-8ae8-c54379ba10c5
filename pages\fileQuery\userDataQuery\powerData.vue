<template>
	<view class="charts-box">
		<view class="chart-title">电能示值曲线</view>
		<view class="screen-rotate" @click="screenRotate"></view>
		<!-- 图表容器 -->
		<view class="chart-container" :style="{ height: chartHeight + 'px' }">
			<!-- 添加水平居中的电量数据标签 -->
			<!-- <view class="data-label">电量数据</view> -->
			
			<qiun-data-charts type="line" :opts="opts" :chartData="visibleChartData" canvas2d :ontouch="true" :onzoom="true" :disableScroll="true"
				canvasId="powerDataChart" v-if="chartData.series.length"/>
				<!-- 无数据提示 -->
				<view class="no-data" v-else>
					<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
				</view>
		</view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		components: {
			qiunDataCharts: () => import('@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue')
		},
		data() {
			return {
				chartData: {
					categories: [],
					series: []
				},
				token:null,
				contentHeight: 0, // 内容区域高度
				statusBarHeight: 0, // 状态栏高度
				navbarHeight: 44, // 导航栏高度
				tabNavHeight: 50, // 标签导航高度
				screenWidth: 0, // 屏幕宽度
				screenHeight: 0, // 屏幕高度
				chartHeight: 320, // 图表容器高度固定
				allSeries: [], // 存储所有的系列数据
				activeSeries: [], // 当前激活的系列索引
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc", "#3CAEA3", "#F6BD16", "#6395F9", "#975FE5"
					],
					padding: [15, 15, 10, 15],
					enableScroll: true,
					enableMarkLine: false,
					dataPointShape: true,
					dataPointShapeType: 'solid',
					legend: {
						show: false, // 隐藏默认图例，使用自定义图例
					},

					xAxis: {
						disableGrid: true,
						scrollShow: true,
						scrollAlign: 'left',
						scrollBackgroundColor: '#F6F6F6',
						scrollColor: '#A8A8A8',
						itemCount: 4, // 同时显示6个时间点
						labelCount: 4,
						boundaryGap: 'center',
						axisLine: true,
						axisLineColor: '#CCCCCC',
						rotateLabel: false, // 不旋转标签，更美观
						fontSize: 12,
						margin: 15,
						format: (val) => {
							return val; // 直接显示HH:MM格式
						}
					},
					yAxis: {
						data: [{
							min: 0,
							title: '电量数据',
							format: (val) => {
								return val.toFixed(1);
							}
						}],
						showTitle: true,
						titleFontSize: 12,
						titleOffsetX: 0, // 设置为0使其居中
						titleOffsetY: 0,
						titleAlign: 'center', // 设置标题居中对齐
						gridType: 'dash',
						dashLength: 4,
						splitNumber: 5
					},
					extra: {
						line: {
							type: "curve", // 使用曲线而不是直线，更美观
							width: 2.5,
							activeType: "hollow",
							linearType: "custom", // 自定义线性渐变
							activeOpacity: 0.8,
							lineCap: "round"
						},
						tooltip: {
							showBox: true,
							showArrow: true,
							showCategory: true,
							borderWidth: 0,
							borderRadius: 4,
							borderColor: '#000000',
							borderOpacity: 0.7,
							bgColor: '#000000',
							bgOpacity: 0.7,
							gridType: 'dash',
							dashLength: 4,
							gridColor: '#CCCCCC',
							fontColor: '#FFFFFF',
							horizentalLine: false,
							xAxisLabel: true,
							yAxisLabel: false,
							labelBgColor: '#FFFFFF',
							labelBgOpacity: 0.7,
							labelFontColor: '#666666'
						},
						area: {
							opacity: 0.2,
							gradient: true
						}
					},
					touchMoveLimit: 120,
					touchMoveThreshold: 0,
					animation: false,
					duration: 800,
					dataLabel: false, // 不显示数据标签
					rotateLock: false,
					background: '#ffffff',
					width: window.innerWidth,
					height: 300,
					ontap: function(e) {
						console.log('图表点击事件', e);
					}
				},
				isMockData:true,
				direction: '1', // 屏幕方向：1为竖屏，0为横屏
			};
		},
		onLoad() {
			
		},
		onReady() {
			this.fetchData();
		},
		computed: {
			// 根据激活的系列过滤图表数据
			visibleChartData() {
				if (!this.chartData.series || this.chartData.series.length === 0) {
					return this.chartData;
				}

				// 如果没有激活的系列，显示空数据
				if (this.activeSeries.length === 0) {
					return {
						categories: this.chartData.categories,
						series: []
					};
				}

				// 过滤出激活的系列
				const filteredSeries = this.chartData.series.filter((_, index) =>
					this.activeSeries.includes(index)
				);

				return {
					categories: this.chartData.categories,
					series: filteredSeries
				};
			}
		},
		onReady() {
			this.$nextTick(() => {
				this.calcContentHeight();
			});
		},
		// 组件被激活时调用
		activated() {
			console.log('电量数据组件被激活');
			const effectiveParams = this.getEffectiveQueryParams();
			console.log('电量数据activated周期：', effectiveParams)
			this.getSystemInfo();
			this.calcContentHeight();
			this.fetchData();
			// 重新获取数据
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token
				});
			},
			// 获取系列在原始数据中的索引
			getSeriesIndex(series) {
				if (!this.chartData.series || this.chartData.series.length === 0) {
					return 0;
				}

				return this.chartData.series.findIndex(s => s.name === series.name);
			},
			// 获取有效的查询参数（优先使用queryParams对象，其次从本地存储获取）
			getEffectiveQueryParams() {
				// 如果有传入的queryParams，优先使用
				if (this.queryParams && Object.keys(this.queryParams).length > 0) {
					return {
						...this.queryParams
					};
				}

				// 否则，尝试从本地存储获取
				const savedQueryParams = uni.getStorageSync('userDataQueryParams');
				if (savedQueryParams && Object.keys(savedQueryParams).length > 0) {
					return {
						...savedQueryParams
					};
				}

				// 如果都没有，返回空对象
				return {};
			},
			getMockData() {
				return [
					{
					      "2025-05-11": [
					        27107.97, 27107.97, 27107.97, 27107.97,
					        27107.97, 27107.98, 27107.98, 27107.98,
					        27107.98, 27107.98, 27107.98, 27107.98,
					        27107.98, 27107.98, 27107.98, 27107.98,
					        27107.99, 27107.99, 27107.99, 27107.99,
					        27107.99, 27107.99, 27107.99, 27107.99,
					        27107.99, 27107.99, 27107.99, 27107.99,
					        27108.00, 27108.00, 27108.00, 27108.00,
					        27108.00, 27108.00, 27108.00, 27108.00,
					        27108.00, 27108.00, 27108.01, 27108.01,
					        27108.01, 27108.01, 27108.01, 27108.01,
					        27108.01, 27108.01, 27108.01, 27108.01,
					        27108.01, 27108.02, 27108.02, 27108.02,
					        27108.02, 27108.02, 27108.02, 27108.02,
					        27108.02, 27108.02, 27108.02, 27108.02,
					        27108.02, 27108.03, 27108.03, 27108.03,
					        27108.03, 27108.03, 27108.03, 27108.03,
					        27108.03, 27108.03, 27108.03, 27108.03,
					        27108.03, 27108.04, 27108.04, 27108.04,
					        27108.04, 27108.04, 27108.04, 27108.04,
					        27108.04, 27108.04, 27108.04, 27108.04,
					        27108.04, 27108.05, 27108.05, 27108.05,
					        27108.05, 27108.05, 27108.05, 27108.05,
					        27108.05, 27108.05, 27108.05, 27108.06,
					        27108.06, 27108.06, 27108.06, 27108.06
					      ]
					    }
				]
			},
			fetchData() {
				// 获取有效的查询参数
				const requestParams = this.getEffectiveQueryParams();
				if(this.isMockData) {
					let powerDataArray = this.getMockData();
					if(Array.isArray(powerDataArray) && powerDataArray.length > 0) {
						// 为每个日期创建独立的数据系列
						const categories = []; // 时间点标签（小时:分钟）
						const series = []; // 每天一个数据系列
						const allDates = new Set();
						// 首先找出所有时间点，确保所有日期使用相同的时间点
						const timePoints = [];
						powerDataArray.forEach(dateObj => {
							// 获取日期和对应的数据数组
							const date = Object.keys(dateObj)[0];
							console.log(date)
							allDates.add(date);
							const values = dateObj[date];
						
							if (Array.isArray(values) && values.length > 0) {
								// 确保数据点数量匹配
								const seriesData = new Array(96).fill(null);
						
								// 填充实际数据
								for (let i = 0; i < Math.min(values.length, 96); i++) {
									seriesData[i] = values[i];
								}
								
								// 添加到系列中
								series.push({
									name: this.formatDateLabel(date),
									data: seriesData,
									format: (val) => val ? val.toFixed(2) :
										'0.00' // 确保数据格式化为2位小数
								});
							}
						});
						const dateList = Array.from(allDates).sort();
						console.log('日期',dateList)
						dateList.forEach((item) => {
							for (let i = 0; i < 96; i++) {
								const hour = Math.floor(i / 4);
								const minute = (i % 4) * 15;
								const timeLabel =
									`${item} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
								timePoints.push(timeLabel);
								categories.push(timeLabel);
							}
						})
						
						// 确保有数据
						if (series.length > 0) {
							// 找出所有系列中的最大值，用于设置Y轴
							const allValues = series.flatMap(s => s.data.filter(val => val !==
								null));
							const maxValue = allValues.length > 0 ? Math.max(...allValues) : 5;
							const minValue = allValues.length > 0 ? Math.min(...allValues) : 0;
							// const yAxisMax = Math.ceil(maxValue * 1.2); // 最大值的1.2倍，向上取整
						
							// 更新Y轴最大值
							this.opts.yAxis.data[0].max = maxValue;
							this.opts.yAxis.data[0].min = minValue;
						
							// 存储所有系列数据
							this.allSeries = [...series];
						
							// 激活所有系列
							this.activeSeries = Array.from({
								length: series.length
							}, (_, i) => i);
						
							// 构建图表数据
							const chartData = {
								categories: categories,
								series: series
							};
						
							console.log("处理后的图表数据:", chartData);
							this.chartData = chartData;
						} else {
							console.error("没有有效的系列数据");
						}
					}else{
						console.error("电量数据格式不正确，不是有效的数组:", powerDataArray);
					}
					
					
				}else{
					// API请求参数构建
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service": "DtsUserController",
									"method": "getUserPower",
									"data": {
										"meterAssetNo": requestParams.meterAssetNo || "",
										"statDateStart": requestParams.dateRange[0],
										"statDateEnd": requestParams.dateRange[1]
									},
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									 const powerDataArray = rtnData.data.r;
									 if(Array.isArray(powerDataArray) && powerDataArray.length > 0) {
									 	// 为每个日期创建独立的数据系列
									 	const categories = []; // 时间点标签（小时:分钟）
									 	const series = []; // 每天一个数据系列
									 	
									 	// 首先找出所有时间点，确保所有日期使用相同的时间点
									 	const timePoints = [];
									 	for (let i = 0; i < 96; i++) { // 假设每天96个点（15分钟一个点）
									 		const hour = Math.floor(i / 4);
									 		const minute = (i % 4) * 15;
									 		const timeLabel =
									 			`${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
									 		timePoints.push(timeLabel);
									 		categories.push(timeLabel);
									 	}
									 	
									 	// 遍历每个日期对象
									 	powerDataArray.forEach(dateObj => {
									 		// 获取日期和对应的数据数组
									 		const date = Object.keys(dateObj)[0];
									 		const values = dateObj[date];
									 	
									 		if (Array.isArray(values) && values.length > 0) {
									 			// 确保数据点数量匹配
									 			const seriesData = new Array(96).fill(null);
									 	
									 			// 填充实际数据
									 			for (let i = 0; i < Math.min(values.length, 96); i++) {
									 				seriesData[i] = values[i];
									 			}
									 			
									 			// 添加到系列中
									 			series.push({
									 				name: this.formatDateLabel(date),
									 				data: seriesData,
									 				format: (val) => val ? val.toFixed(2) :
									 					'0.00' // 确保数据格式化为2位小数
									 			});
									 		}
									 	});
									 	
									 	// 确保有数据
									 	if (series.length > 0) {
									 		// 找出所有系列中的最大值，用于设置Y轴
									 		const allValues = series.flatMap(s => s.data.filter(val => val !==
									 			null));
									 		// const maxValue = Math.max(...allValues);
									 		// const yAxisMax = Math.ceil(maxValue * 1.2); // 最大值的1.2倍，向上取整
									 	const maxValue = allValues.length > 0 ? Math.max(...allValues) : 5;
									 	const minValue = allValues.length > 0 ? Math.min(...allValues) : 0;
									 		// 更新Y轴最大值
									 		this.opts.yAxis.data[0].max = maxValue;
											this.opts.yAxis.data[0].min = minValue;
									 	
									 		// 存储所有系列数据
									 		this.allSeries = [...series];
									 	
									 		// 激活所有系列
									 		this.activeSeries = Array.from({
									 			length: series.length
									 		}, (_, i) => i);
									 	
									 		// 构建图表数据
									 		const chartData = {
									 			categories: categories,
									 			series: series
									 		};
									 	
									 		console.log("处理后的图表数据:", chartData);
									 		this.chartData = chartData;
									 	} else {
									 		console.error("没有有效的系列数据");
									 	}
									 }else{
									 	console.error("电量数据格式不正确，不是有效的数组:", powerDataArray);
									 }
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
			},
			screenRotate() {
				console.log('手动触发屏幕旋转');
				const vm = this;
				const direction = uni.getStorageSync('direction');
				if(direction) {
					vm.direction = direction;
					if(vm.direction === '1') {
						console.log('竖屏')
						uni.setStorageSync('direction', '0');
						vm.direction = '0'
					}else{
						console.log('横屏')
						uni.setStorageSync('direction', '1');
						vm.direction = '1'
					}
					wx.invoke("ext_screenControl_direction", {
							data: {
								orientation:  vm.direction
							}
						},
						(res) => {
							console.log(JSON.stringify(res));
							if (res.error_msg === 'ext_screenControl_direction:ok') {
								console.log(res.result);
								vm.updateChartForOrientation();
							}
						});
				}else{
					if(vm.direction === '1') {
						vm.direction = '0'
						console.log('横屏')
					}else{
						console.log('竖屏')
						vm.direction = '1'
					}
					wx.invoke("ext_screenControl_direction", {
							data: {
								orientation: vm.direction//0横屏；1竖屏
							}
						},
						function(res) {
							console.log(JSON.stringify(res))
							if (res.error_msg === 'ext_screenControl_direction:ok') {
								//成功处理
								console.log(res.result);
								vm.updateChartForOrientation();
							}
						})
				}
				
			},
			updateChartForOrientation() {
				this.chartHeight = this.direction === '0' ? 400 : 320;
				this.calcContentHeight();
			},
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
				this.screenWidth = systemInfo.screenWidth || 0;
				this.screenHeight = systemInfo.screenHeight || 0;
			},
			calcContentHeight() {
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
			
				const topHeight = this.statusBarHeight + this.navbarHeight + this.tabNavHeight;
			
				this.contentHeight = this.windowHeight - topHeight;
			},
			// 格式化日期标签
			formatDateLabel(dateStr) {
				// 将YYYY-MM-DD格式转换为MM月DD日
				const parts = dateStr.split('-');
				if (parts.length === 3) {
					return `${parseInt(parts[0])}年${parseInt(parts[1])}月${parseInt(parts[2])}日`;
				}
				return dateStr;
			}
		}
	};
</script>

<style scoped>
	.charts-box {
		width: 100%;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 20rpx;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		margin: 10rpx 0;
	}

	.chart-container {
		width: 100%;
		height: 200px;
		overflow-x: auto;
		overflow-y: hidden;
		-webkit-overflow-scrolling: touch;
		position: relative;
	}

	/* 添加水平居中的数据标签样式 */
	.data-label {
		text-align: center;
		color: #666;
		margin-bottom: 5px;
		font-weight: 500;
		font-size: 28rpx;
	}

	.chart-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #262626;
		margin-bottom: 10px;
		padding-left: 5px;
		white-space: nowrap;
	}

	.data-info {
		text-align: center;
		margin-top: 10px;
		padding: 5px;
	}

	.data-info-text {
		font-size: 12px;
		color: #999;
	}

	.no-data {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		color: #999;
		font-size: 14px;
	}

	/* 移动端适配 */
	@media screen and (max-width: 768px) {
		.charts-box {
			padding: 8px;
			margin: 3px 0;
		}

		.chart-container {
			height: 280px;
		}

		.chart-title {
			font-size: 14px;
			margin-bottom: 8px;
		}

		.data-label {
			font-size: 12px;
			margin-bottom: 3px;
		}
	}
	.screen-rotate {
		position: absolute;
		right: 40rpx;
		display: block;
		width: 30rpx;
		height: 30rpx;
		background: url('~@/static/icons/screen.png') no-repeat;
		background-size: 100% 100%;
		z-index: 1;
	}
</style>