<template>
	<view class="law-list-container">
		<!-- 自定义导航栏 -->
		<custom-navbar title="法律法规" :showBack="true">
			<template #right>
				<view class="search-icon" @click="goSearch">
					<u-icon name="search" color="#000000" size="28"></u-icon>
				</view>
			</template>
		</custom-navbar>
		
		<!-- 内容区域 -->
		<scroll-view 
			class="content-section" 
			scroll-y="true"
			refresher-enabled
			:scroll-anchoring = "true"
			:enhanced="true"
			:bounces="true"
			:show-scrollbar="false"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="onLoadMore"
			:style="{ height: contentHeight + 'px' }"
			>
			<!-- 法律法规列表 -->
			<view class="law-list">
				<!-- 法规项 -->
				<view class="law-item" v-for="(item, index) in lawList" :key="index" @click="viewLawDetail(item)">
					<view class="law-tag" :class="{'theft-tag': item.lawType === '01', 'contract-tag': item.lawType === '02','depart-tag': item.lawType === '03'}">
						<text>{{item.lawTypeName}}</text>
					</view>
					<view class="law-title">
						<text>{{item.fileName}}</text>
					</view>
				</view>
				
				<!-- 无数据提示 -->
				<view class="empty-container" v-if="lawList.length === 0 && !isLoading">
					<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
				</view>
				
				<!-- 加载中提示 -->
				<view class="loading-more" v-if="lawList.length>0">
					<text v-if="isLoading && params.pageNum >1">加载中...</text>
					<text v-else-if="!hasMoreData">没有更多数据了</text>
				</view>
				
				<!-- 底部间距 -->
				<view class="bottom-space"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
		import {
		bizCode,
		url,
		getUUID,
		getCurrentTimestamp,
		port} from '@/static/commonJs/util.js'
	export default {
		data() {
			return {
				params: {
					pageNum: 1,
					pageSize: 10
				},
				hasMoreData:true,
				isRefreshing: false,
				isLoading: false,
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				lawList: [],
				// 添加搜索参数对象
				searchForm: {
					lawType: '01',
					fileName:"",
					fileDescribe:"",
					lssuingUnit:""
				},
				isMockData:true,
				token:null
			};
		},
		onLoad() {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
			// 加载法律法规数据
			this.loadLawList(this.searchParams);
			// 添加事件监听器，监听搜索页面传来的参数
			uni.$on('lawSearch', this.lawSearch);
		},
		onReady() {
			// onReady生命周期
		},
		onShow() {
			// 页面不需要每次显示都重新加载数据，避免覆盖搜索结果
			// 如果需要刷新，可以通过下拉刷新或搜索操作触发
		},
		onUnload() {
			// 页面卸载时移除事件监听器
			uni.$off('lawSearch', this.lawSearch);
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
				});
			},
			goSearch() {
				uni.navigateTo({
					url: './lawSearch',
				})
			},
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			lawSearch(params) {
				console.log('111',params)
				// 更新搜索参数
				this.searchParams = {
					...this.searchParams,
					...params
				};
				
				// 根据搜索参数加载数据
				this.loadLawList(this.searchParams);
			},
			
			// 判断是否有搜索条件
			hasSearchConditions(params) {
				if (!params) return false;
				return Boolean(params.title || params.content || params.lssuingUnitName || 
					(params.lawType !== null && params.lawType !== undefined));
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏）
				const topHeight = this.statusBarHeight + this.navbarHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight;
			},
			
			// 加载法律法规列表数据
			loadLawList(params) {
				console.log('获取参数，',params)
				// 如果是下拉刷新，不设置isLoading，避免闪烁
				if (!this.isRefreshing) {
					this.isLoading = true;
				}
				uni.showLoading({
					title:'加载中...'
				})
				if(this.isMockData) {
					this.lawList = [
						{
						    "fileNo": "968123981880852480",
						    "fileName": "国家电网有限公司反窃电管理办法",
						    "lssuingUnit": "03",
						    "lssuingUnitName": "国家电网",
						    "issueTime": "2019-03-01",
						    "issuedNumber": "规章制度编号：国网（营销/3）987-2019",
						    "keyWord": "反窃电管理办法",
						    "lawType": "03",
						    "lawTypeName": "部门规章",
						    "effectiveState": "01",
						    "effectiveStateName": "现行有效",
						    "fileDescribe": "国家电网有限公司反窃电管理办法",
						    "mplementTime": "2019-06-01",
						    "creator": "4684257252382615342",
						    "createTime": "2025-06-22",
						    "createOrg": "上海市电力公司"
						  }
					]
					this.isLoading = false;
					setTimeout(() => {
						this.isRefreshing = false;
					},1000)
					uni.hideLoading();
				}else {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"MobileLawsManagerController",
									"method":"lawsQuery",
									"data": {
										...params
									},
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									const newList = rtnData.data.list || [];
									this.hasMoreData = newList.length >= this.params.pageSize;
									if(this.params.pageNum === 1) {
										this.lawList = [...newList];
									}else{
										this.lawList = [...this.lawList,...newList]
										
									}
									this.isLoading = false;
									if (this.isRefreshing) {
										this.isRefreshing = false;
									}
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
							uni.hideLoading();
						},
						fail: (error) => {
							this.isRefreshing = false;
							this.isLoading = false;
							uni.hideLoading();
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
				
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				this.hasMoreData = true;
				this.lawList = [];
				this.params.pageNum = 1; // 重置为第一页
				// 重新加载数据
				this.loadLawList(this.searchParams);
			},
			onLoadMore() {
				console.log("滚动到底部了")
				if(this.isLoading || !this.hasMoreData) {
					return;
				}
				this.params.pageNum++;
				this.loadLawList(this.searchParams);
				
			},
			
			// 查看法律法规详情
			viewLawDetail(law) {
				console.log(law)
				// 实际项目中应该跳转到法规详情页
				uni.navigateTo({
				    url: `./lawDetail?item=${encodeURIComponent(JSON.stringify(law))}`
				});
			}
		}
	}
</script>

<style lang="scss">
.law-list-container {
	display: flex;
	flex-direction: column;
	background-color: #f8f8f8;
	height: 100vh;
	
	/* 覆盖默认导航栏样式，使其与设计图一致 */
	/deep/ .custom-navbar {
		background-color: #FFFFFF !important;
		
		.navbar-title text {
			color: #333333;
		}
	}
}

/* 内容区域 */
.content-section {
	flex: 1;
	box-sizing: border-box;
	background-color: #f8f8f8;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 法律法规列表 */
.law-list {
	padding: 0 0 env(safe-area-inset-bottom); /* 适配底部安全区域 */
}

/* 法规项 */
.law-item {
	background-color: #FFFFFF;
	margin-bottom: 2rpx;
	padding: 30rpx 40rpx;
	overflow: hidden;
	position: relative;
	border-bottom: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
}

.law-tag {
	height: 60rpx;
	min-width: 120rpx;
	padding: 0 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 30rpx;
	margin-right: 24rpx;
	
	text {
		font-size: 24rpx;
		color: #FFFFFF;
		font-weight: 500;
	}
}

.theft-tag {
	background-color: #07ac7c;
}

.contract-tag {
	background-color: #5193F5;
}
.depart-tag{
	background-color: #f56c6c;
}

.law-title {
	flex: 1;
	
	text {
		font-size: 30rpx;
		color: #333333;
		line-height: 1.5;
	}
}

/* 无数据提示 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
	background-color: #fff;
	margin: 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	margin-top: 24rpx;
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 90rpx 0;
	margin: 20rpx;
	background-color: #fff;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}
.loading-more {
	width:100%;
	height:60rpx;
	display:flex;
	justify-content: center;
	align-items: center;
	padding:20rpx 0;
	color:#999;
	font-size:28rpx;
}
.loading-text {
	font-size: 28rpx;
	color: #8c8c8c;
	margin-top: 24rpx;
	text-align: center;
}

/* 底部间距 */
.bottom-space {
	height: 30rpx;
	/* 安卓端额外空间 */
	/* #ifdef APP-ANDROID */
	height: 60rpx;
	/* #endif */
}

/* 搜索图标样式 */
.search-icon {
	padding: 0 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}
</style>
