<template>
	<!-- 列表 -->
	<view class="mainContainer">
		<indicator-nav title="指标看板" @search-click="handleNavbarSearch" @gear-click="handlegear"></indicator-nav>
		<u-tabs :list="list1" @click="clicktabs" :scrollable="false"
			style="border-bottom: 1px solid lightgrey;"></u-tabs>

		<!-- 搜索弹框 -->
		<u-popup :show="showpopup" :round="10" mode="bottom" @close="close" :closeOnClickOverlay="closeOnClick">
			<view class="popupcontainer">
				<view class="poptitle">
					查询
				</view>
				<u--form :labelStyle="{ fontWeight: 'bold' }" labelPosition="left" :model="uForm" ref="uForm">
					<u-form-item class="formitem" labelWidth="120" label="供电单位" prop="" borderBottom
						style="position: relative;padding-top: 20rpx;" ref="item1">
						<DaTreeVue2 class="DaTreestyle" :style="judge?'background-color:white;height:400rpx':''"
							ref="DaTreeRef" :data="treeData" labelField="name" valueField="mgtOrgCode" expandChecked
							:defaultCheckedKeys="defaultCheckedKeysValue" @change="handleTreeChange"
							@expand="handleExpandChange"></DaTreeVue2>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="统计日期" prop="custNo" borderBottom ref="item1">
						<view class="calendarContainer">
							<uni-datetime-picker v-model="uForm.queryStartDate" type="date" placeholder="开始日期"
								:end="uForm.queryEndDate" @change="handleStartDate"
								style="background-color: white;width: 400rpx;">
							</uni-datetime-picker>
							<view style="margin: 0 10rpx;">-</view>
							<uni-datetime-picker v-model="uForm.queryEndDate" type="date" placeholder="结束日期"
								:start="uForm.queryStartDate" @change="handleEndDate"
								style="background-color: white;width: 400rpx;">
							</uni-datetime-picker>
						</view>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="用户类型" prop="custCls" borderBottom ref="item4">
						<u-radio-group v-model="uForm.custCls" style="display: flex;flex-wrap: wrap;">
							<u-radio name="00" style="width: 50%;">全部</u-radio>
							<u-radio name="01" style="width: 50%;">高压</u-radio>
							<u-radio name="02" style="width: 50%;">低压非居民</u-radio>
							<u-radio name="03" style="width: 50%;">低压居民</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="违窃类型" prop="goalType" borderBottom
						ref="item5">
						<u-radio-group v-model="uForm.goalType" style="display: flex;flex-wrap: wrap;">
							<u-radio name="01" style="width: 50%;">窃电</u-radio>
							<u-radio name="02" style="width: 50%;">违约用电</u-radio>
							<u-radio name="03" style="width: 50%;">无协议用电</u-radio>
							<u-radio name="04" style="width: 100%;">窃电及违约用电</u-radio>
							<u-radio name="05" style="width: 50%;">无违约窃电</u-radio>
							<u-radio name="99" style="width: 50%;">其他</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="工单来源" prop="orderSrc" borderBottom
						ref="item7">
						<u-radio-group v-model="uForm.orderSrc" style="display: flex;flex-wrap: wrap;">
							<u-radio name="00" style="width: 50%;">全部</u-radio>
							<u-radio name="01" style="width: 50%;">反窃电系统</u-radio>
							<u-radio name="02" style="width: 50%;">营销系统</u-radio>
							<u-radio name="03" style="width: 50%;">历史数据</u-radio>
						</u-radio-group>
					</u-form-item>
					<view class="ubutton">
						<u-button type="default" style="border: 1px solid lightgray;width: 300rpx;" text="重置"
							@click="reset"></u-button>
						<u-button type="primary" style="color: #fcfefd;width: 300rpx;" color="#07ac7c" text="查询"
							@click="search"></u-button>
					</view>
				</u--form>
			</view>
		</u-popup>

		<!-- 设置弹框 -->
		<u-popup :show="showpopup2" :round="10" mode="bottom" @close="close2" :closeOnClickOverlay="closeOnClick2">
			<view class="popupcontainer2">
				<view class="poptitle">
					设置
				</view>
				<u--form :labelStyle="{ fontWeight: 'bold' }" labelPosition="left" :model="uForm" ref="uForm">
					<u-form-item class="formitem" labelWidth="120" label="反窃电成效" prop="custCls" borderBottom
						ref="item4">
						<u-checkbox-group v-model="fanArray" style="display: flex;flex-wrap: wrap;width: 440rpx;">
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="00"></u-checkbox>追补总金额
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="01"></u-checkbox>指标
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="02"></u-checkbox>完成率
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="03"></u-checkbox>案例数
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="04"></u-checkbox>追补电量
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="05"></u-checkbox>追补电费
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="06"></u-checkbox>违约金
							</view>
						</u-checkbox-group>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="工单处理情况" prop="goalType" borderBottom
						ref="item5">
						<u-checkbox-group v-model="gondanArray" style="display: flex;flex-wrap: wrap;width: 440rpx;">
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="01"></u-checkbox>线索数
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="02"></u-checkbox>查实工单数
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="03"></u-checkbox>查实率
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="04"></u-checkbox>检查工单数
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="05"></u-checkbox>检查率
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="06"></u-checkbox>处理工单数
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="07"></u-checkbox>处理率
							</view>
						</u-checkbox-group>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="防窃电改造" prop="orderSrc" borderBottom
						ref="item7">
						<u-checkbox-group v-model="gaiArray" style="display: flex;flex-wrap: wrap;width: 440rpx;">
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="00"></u-checkbox>防窃电工单数
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="01"></u-checkbox>改造完成数
							</view>
							<view style="display: flex;width: 200rpx;">
								<u-checkbox name="02"></u-checkbox>完成率
							</view>
						</u-checkbox-group>
					</u-form-item>
					<view class="ubutton">
						<u-button type="default" style="border: 1px solid lightgray;width: 300rpx;" text="重置"
							@click="resetset"></u-button>
						<u-button type="primary" style="color: #fcfefd;width: 300rpx;" color="#07ac7c" text="确定"
							@click="confirm"></u-button>
					</view>
				</u--form>
			</view>
		</u-popup>

		<!-- 本单位 -->
		<view class="" v-if="bendanwei">
			<!-- <picker mode="date">
				<view>选怎日期</view>
			</picker> -->
			<view class="tablecontainer">
				<view class="titletagcontainer">
					<view class="titletag"></view>
				</view>
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletext">
							反窃电成效完成率
						</view>
						<view class="" style="color: #2bb98f;">
							{{benChenWan.rate}}%
						</view>
					</view>
					<view class=""
						style="padding: 24rpx 0 16rpx 0;display: flex;align-items: center;justify-content: space-between;">
						<text style="color: gray;font-size: 24rpx;font-weight: bold;">总金额/目标任务</text>
						<text
							style="color: gray;font-size: 24rpx;font-weight: bold;">{{benChenWan.penalSums}}/{{benChenWan.goalValue}}</text>
					</view>
					<view class="">
						<u-line-progress :percentage="benChenWan.rate" :showText="false" height="8"></u-line-progress>
					</view>
					<view class="" style="padding: 24rpx 0 16rpx 0;font-size: 24rpx;">
						<text style="color:darkgray;">{{exgoalType}}/{{benChenWan.caseNum}}</text>
					</view>
				</view>
			</view>
			<view class="tablecontainer">
				<view class="titletagcontainer">
					<view class="titletag"></view>
				</view>
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletext">
							防窃电改造完成率
						</view>
						<view class="" style="color: #2bb98f;">
							{{benFangWan.rate03}}%
						</view>
					</view>
					<view class=""
						style="padding: 24rpx 0 16rpx 0;display: flex;align-items: center;justify-content: space-between;">
						<text style="color: gray;font-size: 24rpx;font-weight: bold;">已完成数/防窃电改造工单</text>
						<text
							style="color: gray;font-size: 24rpx;font-weight: bold;">{{benFangWan.status03}}/{{benFangWan.all}}</text>
					</view>
					<view class="">
						<u-line-progress :percentage="benFangWan.rate03" :showText="false" height="8"></u-line-progress>
					</view>
					<view class="" style="padding: 24rpx 0 16rpx 0;font-size: 24rpx;">
						<text
							style="color:darkgray;">待填报{{benFangWan.status01}}/待改造{{benFangWan.status04}}/改造中{{benFangWan.status02}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 下级单位 -->
		<view class="" v-if="xiajidanwei">
			<view class="tablecontainer">
				<view class="titletagcontainer">
					<view class="titletag"></view>
				</view>
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletext">
							反窃电成效
						</view>
						<view class="" @click="changescreen">
							<uni-icons type="bars" size="20" color="gray" />
						</view>
					</view>
					<view class=""
						style="padding: 30rpx 0 16rpx 0;display: flex;align-items: center;justify-content: space-between;font-size: 24rpx;">
						<view style="flex:1;text-align: center;">供电单位</view>
						<view style="flex:1;text-align: center;" v-show="show1">追补总金额</view>
						<view style="flex:1;text-align: center;" v-show="show2">指标</view>
						<view style="flex:1;text-align: center;" v-show="show3">完成率</view>
						<view style="flex:1;text-align: center;" v-show="show4">案例数</view>
						<view style="flex:1;text-align: center;" v-show="show5">追补电量</view>
						<view style="flex:1;text-align: center;" v-show="show6">追补电费</view>
						<view style="flex:1;text-align: center;" v-show="show7">违约金</view>
					</view>
					<view v-if="dataShow1"
						style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
						<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
						<view style="color: gainsboro;">
							数据为空
						</view>
					</view>
					<view class=""
						style="font-size: 24rpx;display: flex;align-items: center;justify-content: space-between;"
						v-for="(item,index) in xiaChenArray" :key="index">
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;">
							{{item.mgtOrgName}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show1">
							{{item.penalSums}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show2">
							{{item.goalValue}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show3">
							{{item.rate}}%
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show4">
							{{item.caseNum}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show5">
							{{item.fillPower}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show6">
							{{item.fillCost}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show7">
							{{item.penalSum}}
						</view>
					</view>
				</view>
			</view>
			<!-- 工单处理情况 -->
			<view class="tablecontainer">
				<view class="titletagcontainer">
					<view class="titletag"></view>
				</view>
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletext">
							工单处理情况
						</view>
						<view class="" @click="changescreen">
							<uni-icons type="bars" size="20" color="gray" />
						</view>
					</view>
					<view class=""
						style="padding: 30rpx 0 16rpx 0;display: flex;align-items: center;justify-content: space-between;font-size: 24rpx;">
						<view style="flex:1;text-align: center;">供电单位</view>
						<view style="flex:1;text-align: center;" v-show="show11">线索数</view>
						<view style="flex:1;text-align: center;" v-show="show22">查实工单数</view>
						<view style="flex:1;text-align: center;" v-show="show33">查实率</view>
						<view style="flex:1;text-align: center;" v-show="show44">检查工单数</view>
						<view style="flex:1;text-align: center;" v-show="show55">检查率</view>
						<view style="flex:1;text-align: center;" v-show="show66">处理工单数</view>
						<view style="flex:1;text-align: center;" v-show="show77">处理率</view>
					</view>
					<view v-if="dataShow2"
						style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
						<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
						<view style="color: gainsboro;">
							数据为空
						</view>
					</view>
					<view class=""
						style="font-size: 24rpx;display: flex;align-items: center;justify-content: space-between;"
						v-for="(item,index) in chuliArray" :key="index">
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;">
							{{item.mgtOrgName}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show11">
							{{item.suspTotal}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show22">
							{{item.stealNum}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show33">
							{{item.stealRate}}%
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show44">
							{{item.checkNum}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show55">
							{{item.checkRate}}%
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show66">
							{{item.filedNum}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show77">
							{{item.filedRate}}%
						</view>
					</view>
				</view>
			</view>
			<!-- 防窃电改造 -->
			<view class="tablecontainer">
				<view class="titletagcontainer">
					<view class="titletag"></view>
				</view>
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletext">
							防窃电改造
						</view>
						<view class="" @click="changescreen">
							<uni-icons type="bars" size="20" color="gray" />
						</view>
					</view>
					<view class=""
						style="padding: 30rpx 0 16rpx 0;display: flex;align-items: center;justify-content: space-between;font-size: 24rpx;">
						<view style="flex:1;text-align: center;">供电单位</view>
						<view style="flex:1;text-align: center;" v-show="show111">工单总数</view>
						<view style="flex:1;text-align: center;" v-show="show222">改造完成数</view>
						<view style="flex:1;text-align: center;" v-show="show333">完成率</view>
					</view>
					<view v-if="dataShow3"
						style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
						<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
						<view style="color: gainsboro;">
							数据为空
						</view>
					</view>
					<view class=""
						style="font-size: 24rpx;display: flex;align-items: center;justify-content: space-between;"
						v-for="(item,index) in xiaFangArray" :key="index">
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;">
							{{item.mgtOrgName}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show111">
							{{item.all}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show222">
							{{item.status03}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show333">
							{{item.rate03}}%
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 供服 -->
		<view class="" v-if="gongfu">
			<view class="tablecontainer">
				<view class="titletagcontainer">
					<view class="titletag"></view>
				</view>
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletext">
							反窃电成效
						</view>
						<view class="" @click="changescreen">
							<uni-icons type="bars" size="20" color="gray" />
						</view>
					</view>
					<view class=""
						style="padding: 30rpx 0 16rpx 0;display: flex;align-items: center;justify-content: space-between;font-size: 24rpx;">
						<view style="flex:1;text-align: center;">供电单位</view>
						<view style="flex:1;text-align: center;" v-show="show1">追补总金额</view>
						<view style="flex:1;text-align: center;" v-show="show2">指标</view>
						<view style="flex:1;text-align: center;" v-show="show3">完成率</view>
						<view style="flex:1;text-align: center;" v-show="show4">案例数</view>
						<view style="flex:1;text-align: center;" v-show="show5">追补电量</view>
						<view style="flex:1;text-align: center;" v-show="show6">追补电费</view>
						<view style="flex:1;text-align: center;" v-show="show7">违约金</view>
					</view>
					<view v-if="dataShow1"
						style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
						<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
						<view style="color: gainsboro;">
							数据为空
						</view>
					</view>
					<view class=""
						style="font-size: 24rpx;display: flex;align-items: center;justify-content: space-between;"
						v-for="(item,index) in gongfuArray" :key="index">
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;">
							{{item.mgtOrgName}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show1">
							{{item.penalSums}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show2">
							{{item.goalValue}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show3">
							{{item.rate}}%
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show4">
							{{item.caseNum}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show5">
							{{item.fillPower}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show6">
							{{item.fillCost}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show7">
							{{item.penalSum}}
						</view>
					</view>
				</view>
			</view>
			<!-- 工单处理情况 -->
			<view class="tablecontainer">
				<view class="titletagcontainer">
					<view class="titletag"></view>
				</view>
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletext">
							工单处理情况
						</view>
						<view class="" @click="changescreen">
							<uni-icons type="bars" size="20" color="gray" />
						</view>
					</view>
					<view class=""
						style="padding: 30rpx 0 16rpx 0;display: flex;align-items: center;justify-content: space-between;font-size: 24rpx;">
						<view style="flex:1;text-align: center;">供电单位</view>
						<view style="flex:1;text-align: center;" v-show="show11">线索数</view>
						<view style="flex:1;text-align: center;" v-show="show22">查实工单数</view>
						<view style="flex:1;text-align: center;" v-show="show33">查实率</view>
						<view style="flex:1;text-align: center;" v-show="show44">检查工单数</view>
						<view style="flex:1;text-align: center;" v-show="show55">检查率</view>
						<view style="flex:1;text-align: center;" v-show="show66">处理工单数</view>
						<view style="flex:1;text-align: center;" v-show="show77">处理率</view>
					</view>
					<view v-if="dataShow2"
						style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
						<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
						<view style="color: gainsboro;">
							数据为空
						</view>
					</view>
					<view class=""
						style="font-size: 24rpx;display: flex;align-items: center;justify-content: space-between;"
						v-for="(item,index) in chuligongfuArray" :key="index">
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;">
							{{item.mgtOrgName}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show11">
							{{item.suspTotal}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show22">
							{{item.stealNum}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show33">
							{{item.stealRate}}%
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show44">
							{{item.checkNum}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show55">
							{{item.checkRate}}%
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show66">
							{{item.filedNum}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show77">
							{{item.filedRate}}%
						</view>
					</view>
				</view>
			</view>
			<!-- 防窃电改造 -->
			<view class="tablecontainer">
				<view class="titletagcontainer">
					<view class="titletag"></view>
				</view>
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletext">
							防窃电改造
						</view>
						<view class="" @click="changescreen">
							<uni-icons type="bars" size="20" color="gray" />
						</view>
					</view>
					<view class=""
						style="padding: 30rpx 0 16rpx 0;display: flex;align-items: center;justify-content: space-between;font-size: 24rpx;">
						<view style="flex:1;text-align: center;">供电单位</view>
						<view style="flex:1;text-align: center;" v-show="show111">工单总数</view>
						<view style="flex:1;text-align: center;" v-show="show222">改造完成数</view>
						<view style="flex:1;text-align: center;" v-show="show333">完成率</view>
					</view>
					<view v-if="dataShow3"
						style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
						<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
						<view style="color: gainsboro;">
							数据为空
						</view>
					</view>
					<view class=""
						style="font-size: 24rpx;display: flex;align-items: center;justify-content: space-between;"
						v-for="(item,index) in gongfuFangArray" :key="index">
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;">
							{{item.mgtOrgName}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show111">
							{{item.all}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show222">
							{{item.status03}}
						</view>
						<view style="color:darkgray;flex:1;text-align: center;padding-bottom: 20rpx;" v-show="show333">
							{{item.rate03}}%
						</view>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import DaTreeVue2 from '@/components/da-tree-vue2/index.vue'
	import utils from '@/static/commonJs/util.js'
	import uCheckbox from '@/uni_modules/uview-ui/components/u-checkbox/u-checkbox.vue'
	import uCheckboxGroup from '@/uni_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue'
	export default {
		components: {
			DaTreeVue2,
			uCheckbox,
			uCheckboxGroup
		},
		data() {
			return {
				show1: false,
				show2: false,
				show3: false,
				show4: false,
				show5: false,
				show6: false,
				show7: false,

				show11: false,
				show22: false,
				show33: false,
				show44: false,
				show55: false,
				show66: false,
				show77: false,

				show111: false,
				show222: false,
				show333: false,

				direction: '1',
				gaiArray: ['00', '01', '02'],
				fanArray: ['00', '01', '02'],
				gondanArray: ['01', '02', '03'],
				testdev: true,
				exgoalType: '窃电',
				dataShow3: true,
				dataShow2: true,
				dataShow1: true,
				closeOnClick: true,
				closeOnClick2: true,
				xiaChenArray: [],
				xiaFangArray: [],
				gongfuArray: [],
				chuligongfuArray: [],
				gongfuFangArray: [],
				chuliArray: [],
				benChenWan: {},
				benFangWan: {},
				bendanwei: true,
				xiajidanwei: false,
				gongfu: false,
				dataList: [],
				itemHeight: 120,
				page: 1,
				loading: false,
				noMore: false,
				gender: "1",
				judge: false,
				treeData: [],
				// 单选时默认值为字符串或数值，不能为数组
				defaultCheckedKeysValue: '31',
				dataArray: [],
				uForm: {
					mgtOrgCode: "31102",
					queryStartDate: "2023-12-01",
					queryEndDate: "2024-05-20",
					custCls: "00",
					goalType: "01",
					orderSrc: "00",
				},
				showpopup: false,
				showpopup2: false,
				cardArray: [1, 2, 3, 4, 5],
				curNow: 0,
				list1: [{
						name: '本单位情况',
					},
					{
						name: '下级单位情况',
					},
					{
						name: '供服情况',
					},
				],
				taiqunum: '',
				value: '',
				chartData: {},
			}
		},
		onLoad() {},
		onReady() {
			// this.init();
			// this.getMgtOrgCode();
			this.confirm();
			this.search();
			this.fangSearch();
			this.chuLisearch();
			this.chuLiGongfusearch();
			this.fangGongfuSearch();
		},
		methods: {
			changescreen() {
				if (this.direction === '1') {
					this.direction = '0'
				} else {
					this.direction = '1'
				}
				wx.invoke("ext_screenContril_direction", {
					data: {
						orientation: this.direction
					}
				}, (res) => {
					if (res.error_msg === 'ext_screenControl_direction:ok') {
						// this.updataChartForOrientation();
					}
				})
			},
			resetset() {
				this.gaiArray = ['00', '01', '02'];
				this.fanArray = ['00', '01', '02'];
				this.gondanArray = ['01', '02', '03'];
			},
			confirm() {
				if (this.fanArray.includes("00")) {
					this.show1 = true;
				} else {
					this.show1 = false;
				}
				if (this.fanArray.includes("01")) {
					this.show2 = true;
				} else {
					this.show2 = false;
				}
				if (this.fanArray.includes("02")) {
					this.show3 = true;
				} else {
					this.show3 = false;
				}
				if (this.fanArray.includes("03")) {
					this.show4 = true;
				} else {
					this.show4 = false;
				}
				if (this.fanArray.includes("04")) {
					this.show5 = true;
				} else {
					this.show5 = false;
				}
				if (this.fanArray.includes("05")) {
					this.show6 = true;
				} else {
					this.show6 = false;
				}
				if (this.fanArray.includes("06")) {
					this.show7 = true;
				} else {
					this.show7 = false;
				}

				if (this.gondanArray.includes("01")) {
					this.show11 = true;
				} else {
					this.show11 = false;
				}
				if (this.gondanArray.includes("02")) {
					this.show22 = true;
				} else {
					this.show22 = false;
				}
				if (this.gondanArray.includes("03")) {
					this.show33 = true;
				} else {
					this.show33 = false;
				}
				if (this.gondanArray.includes("04")) {
					this.show44 = true;
				} else {
					this.show44 = false;
				}
				if (this.gondanArray.includes("05")) {
					this.show55 = true;
				} else {
					this.show55 = false;
				}
				if (this.gondanArray.includes("06")) {
					this.show66 = true;
				} else {
					this.show66 = false;
				}
				if (this.gondanArray.includes("07")) {
					this.show77 = true;
				} else {
					this.show77 = false;
				}

				if (this.gaiArray.includes("00")) {
					this.show111 = true;
				} else {
					this.show111 = false;
				}
				if (this.gaiArray.includes("01")) {
					this.show222 = true;
				} else {
					this.show222 = true;
				}
				if (this.gaiArray.includes("02")) {
					this.show333 = true;
				} else {
					this.show333 = false;
				}



				this.showpopup2 = false;
			},
			async init() {
				try {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: 'http://10.131.136.68:17002/fqd2test/WXAPI/RequestSBCF',
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryUserInfoByBody",
									"data": {
										"operatorId": utils.userInfo.USERNAME,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					if (code === 200) {
						this.uForm.mgtOrgCode = data.mgtOrgCode;
					}
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				}
			},
			handleStartDate(value) {
				this.uForm.queryStartDate = value
				if (this.uForm.queryEndDate && new Date(this.uForm.queryEndDate) < new Date(value)) {
					this.uForm.queryEndDate = ''
				}
			},
			handleEndDate(value) {
				this.uForm.queryEndDate = value
			},
			reset() {
				this.uForm.mgtOrgCode = "31102"
				this.uForm.queryStartDate = ""
				this.uForm.queryEndDate = ""
				this.uForm.custCls = "00"
				this.uForm.goalType = "01"
				this.uForm.orderSrc = "00"
			},
			// 工单处理情况搜索
			async chuLisearch() {
				if (this.testdev) {
					this.dataShow2 = false;
					this.chuliArray = [{
							mgtOrgName: '市区供电公司',
							suspTotal: '192',
							stealNum: '125',
							stealRate: '65.10',
							checkNum:'131',
							checkRate:'100',
							filedNum:'215',
							filedRate:'74.5',
						},
						{
							mgtOrgName: '市南供电公司',
							suspTotal: '230',
							stealNum: '223',
							stealRate: '96.95',
							checkNum:'135',
							checkRate:'100',
							filedNum:'218',
							filedRate:'78.5',
						},
						{
							mgtOrgName: '浦东供电公司',
							suspTotal: '230',
							stealNum: '212',
							stealRate: '92.17',
							checkNum:'125',
							checkRate:'100',
							filedNum:'255',
							filedRate:'94.5',
						},
						{
							mgtOrgName: '崇明供电公司',
							suspTotal: '17',
							stealNum: '17',
							stealRate: '100',
							checkNum:'86',
							checkRate:'100',
							filedNum:'275',
							filedRate:'84.5',
						},
						{
							mgtOrgName: '长兴供电公司',
							suspTotal: '4',
							stealNum: '2',
							stealRate: '50',
							checkNum:'17',
							checkRate:'100',
							filedNum:'115',
							filedRate:'92.5',
						},
						{
							mgtOrgName: '市北供电公司',
							suspTotal: '315',
							stealNum: '315',
							stealRate: '100',
							checkNum:'32',
							checkRate:'100',
							filedNum:'145',
							filedRate:'100',
						},
						{
							mgtOrgName: '嘉定供电公司',
							suspTotal: '31',
							stealNum: '31',
							stealRate: '100',
							checkNum:'31',
							checkRate:'100',
							filedNum:'255',
							filedRate:'74.5',
						},
						{
							mgtOrgName: '奉贤供电公司',
							suspTotal: '41',
							stealNum: '40',
							stealRate: '97.56',
							checkNum:'11',
							checkRate:'100',
							filedNum:'295',
							filedRate:'100',
						},
						{
							mgtOrgName: '松江供电公司',
							suspTotal: '39',
							stealNum: '39',
							stealRate: '100',
							checkNum:'135',
							checkRate:'100',
							filedNum:'165',
							filedRate:'93',
						},
						{
							mgtOrgName: '金山供电公司',
							suspTotal: '22',
							stealNum: '17',
							stealRate: '77.27',
							checkNum:'54',
							checkRate:'100',
							filedNum:'185',
							filedRate:'92',
						},
						{
							mgtOrgName: '青浦供电公司',
							suspTotal: '23',
							stealNum: '23',
							stealRate: '100',
							checkNum:'67',
							checkRate:'100',
							filedNum:'215',
							filedRate:'97',
						},
					];
				} else {
					// 调用后端登录接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "AppOrderController",
										"method": "queryCheckedOrderStatus",
										"data": {
											"mgtOrgCode": this.uForm.mgtOrgCode,
											"queryDateEnd": this.uForm.queryEndDate,
											"queryDateStart": this.uForm.queryStartDate
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.dataShow2 = false;
							this.chuliArray = res.data.data.list;
						} else {
							this.dataShow2 = true;
						}
					} catch (error) {
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
						this.dataShow2 = true;
					}
				}
			},
			// 供服工单处理
			async chuLiGongfusearch() {
				if (this.testdev) {
					this.dataShow2 = false;
					this.chuligongfuArray = [{
							mgtOrgName: '周康供电服务中心',
							suspTotal: '192',
							stealNum: '125',
							stealRate: '65.10',
							checkNum:'128',
							checkRate:'100',
							filedNum:'125',
							filedRate:'89',
						},
						{
							mgtOrgName: '张江科技园供电服务中心',
							suspTotal: '230',
							stealNum: '223',
							stealRate: '96.95',
							checkNum:'128',
							checkRate:'100',
							filedNum:'124',
							filedRate:'99',
						},
						{
							mgtOrgName: '陆家嘴供电服务中心',
							suspTotal: '230',
							stealNum: '212',
							stealRate: '92.17',
							checkNum:'128',
							checkRate:'100',
							filedNum:'185',
							filedRate:'78',
						},
						{
							mgtOrgName: '花木供电服务中心',
							suspTotal: '17',
							stealNum: '17',
							stealRate: '100',
							checkNum:'128',
							checkRate:'100',
							filedNum:'127',
							filedRate:'100',
						},
						{
							mgtOrgName: '世博供电服务中心',
							suspTotal: '4',
							stealNum: '2',
							stealRate: '50',
							checkNum:'128',
							checkRate:'100',
							filedNum:'129',
							filedRate:'100',
						},
					];
				} else {
					// 调用后端登录接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "AppOrderController",
										"method": "queryCheckedOrderStatus",
										"data": {
											"mgtOrgCode": this.uForm.mgtOrgCode,
											"queryDateEnd": this.uForm.queryEndDate,
											"queryDateStart": this.uForm.queryStartDate
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.dataShow2 = false;
							this.chuligongfuArray = res.data.data.list;
						} else {
							this.dataShow2 = true;
						}
					} catch (error) {
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
						this.dataShow2 = true;
					}
				}
			},
			// 防窃电改造查询
			async fangSearch() {
				if (this.testdev) {
					this.dataShow3 = false;
					this.benFangWan = {
						rate03: '73.6',
						status03: '407',
						all: '553',
						status01: '146',
						status04: '0',
						status02: '0'
					};
					this.xiaFangArray = [{
							mgtOrgName: '市区供电公司',
							all: '71',
							status03: '47',
							rate03: '66.2'
						},
						{
							mgtOrgName: '市南供电公司',
							all: '89',
							status03: '57',
							rate03: '64.04'
						},
						{
							mgtOrgName: '浦东供电公司',
							all: '116',
							status03: '107',
							rate03: '92.24'
						},
						{
							mgtOrgName: '崇明供电公司',
							all: '3',
							status03: '3',
							rate03: '100'
						},
						{
							mgtOrgName: '长兴供电公司',
							all: '2',
							status03: '0',
							rate03: '0'
						},
						{
							mgtOrgName: '市北供电公司',
							all: '204',
							status03: '152',
							rate03: '74.51'
						},
						{
							mgtOrgName: '嘉定供电公司',
							all: '14',
							status03: '12',
							rate03: '85.71'
						},
						{
							mgtOrgName: '奉贤供电公司',
							all: '22',
							status03: '12',
							rate03: '54.55'
						},
						{
							mgtOrgName: '松江供电公司',
							all: '16',
							status03: '5',
							rate03: '31.25'
						},
						{
							mgtOrgName: '金山供电公司',
							all: '10',
							status03: '10',
							rate03: '100'
						},
						{
							mgtOrgName: '青浦供电公司',
							all: '6',
							status03: '2',
							rate03: '33.33'
						},
					];
				} else {
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "ElectricityAntiRemouldController",
										"method": "recordStatistic",
										"data": {
											"mgtOrgCode": this.uForm.mgtOrgCode,
											"retrofitStartTime": this.uForm.queryStartDate,
											"retrofitEndTime": this.uForm.queryEndDate,
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							if (res.data.data) {
								this.dataShow3 = false;
								let datalength = res.data.data.length;
								this.benFangWan = res.data.data[datalength - 1];
								res.data.data.pop();
								this.xiaFangArray = res.data.data;
							}
						} else {
							this.dataShow3 = true;
						}
					} finally {}
				}
			},
			// 供服防窃电改造
			async fangGongfuSearch() {
				if (this.testdev) {
					this.dataShow3 = false;
					// this.benFangWan = {
					// 	rate03: '73.6',
					// 	status03: '407',
					// 	all: '553',
					// 	status01: '146',
					// 	status04: '0',
					// 	status02: '0'
					// };
					this.gongfuFangArray = [{
							mgtOrgName: '周康供电服务中心',
							all: '71',
							status03: '47',
							rate03: '66.2'
						},
						{
							mgtOrgName: '张江科技园供电服务中心',
							all: '89',
							status03: '57',
							rate03: '64.04'
						},
						{
							mgtOrgName: '陆家嘴供电服务中心',
							all: '116',
							status03: '107',
							rate03: '92.24'
						},
						{
							mgtOrgName: '花木供电服务中心',
							all: '3',
							status03: '3',
							rate03: '100'
						},
						{
							mgtOrgName: '世博供电服务中心',
							all: '2',
							status03: '0',
							rate03: '0'
						},
					];
				} else {
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "ElectricityAntiRemouldController",
										"method": "recordStatistic",
										"data": {
											"mgtOrgCode": this.uForm.mgtOrgCode,
											"retrofitStartTime": this.uForm.queryStartDate,
											"retrofitEndTime": this.uForm.queryEndDate,
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							if (res.data.data) {
								this.dataShow3 = false;
								let datalength = res.data.data.length;
								this.benFangWan = res.data.data[datalength - 1];
								res.data.data.pop();
								this.gongfuFangArray = res.data.data;
							}
						} else {
							this.dataShow3 = true;
						}
					} finally {}
				}
			},
			async search() {
				if (this.testdev) {
					this.dataShow1 = false;
					this.benChenWan = {
						rate: '7.73',
						penalSums: '618.3',
						goalValue: '8000',
						caseNum: '638'
					};
					this.xiaChenArray = [{
							mgtOrgName: '市区供电公司',
							penalSums: '13.98',
							goalValue: '1600',
							rate: '3.52',
							caseNum:'73',
							fillPower:'24.32',
							fillCost:'13.98',
							penalSum:'56.33',
						},
						{
							mgtOrgName: '市南供电公司',
							penalSums: '28.91',
							goalValue: '1550',
							rate: '7.16',
							caseNum:'95',
							fillPower:'56.1',
							fillCost:'28.97',
							penalSum:'111.27',
						},
						{
							mgtOrgName: '浦东供电公司',
							penalSums: '41.1',
							goalValue: '1850',
							rate: '8.7',
							caseNum:'132',
							fillPower:'69.23',
							fillCost:'41.1',
							penalSum:'160.96',
						},
						{
							mgtOrgName: '崇明供电公司',
							penalSums: '0.64',
							goalValue: '75',
							rate: '3.43',
							caseNum:'9',
							fillPower:'1.15',
							fillCost:'0.64',
							penalSum:'2.57',
						},
						{
							mgtOrgName: '长兴供电公司',
							penalSums: '0.1',
							goalValue: '5',
							rate: '7.77',
							caseNum:'1',
							fillPower:'0.15',
							fillCost:'0.1',
							penalSum:'0.39',
						},
						{
							mgtOrgName: '市北供电公司',
							penalSums: '30.15',
							goalValue: '1850',
							rate: '6.51',
							caseNum:'251',
							fillPower:'57.1',
							fillCost:'30.15',
							penalSum:'120.5',
						},
						{
							mgtOrgName: '嘉定供电公司',
							penalSums: '4.98',
							goalValue: '300',
							rate: '6.64',
							caseNum:'15',
							fillPower:'8.31',
							fillCost:'4.98',
							penalSum:'19.93',
						},
						{
							mgtOrgName: '奉贤供电公司',
							penalSums: '21.15',
							goalValue: '250',
							rate: '34.02',
							caseNum:'22',
							fillPower:'27.32',
							fillCost:'21.15',
							penalSum:'85.05',
						},
						{
							mgtOrgName: '松江供电公司',
							penalSums: '4.68',
							goalValue: '170',
							rate: '11.02',
							caseNum:'22',
							fillPower:'8.28',
							fillCost:'4.68',
							penalSum:'18.74',
						},
						{
							mgtOrgName: '金山供电公司',
							penalSums: '8.86',
							goalValue: '150',
							rate: '23.5',
							caseNum:'11',
							fillPower:'12.75',
							fillCost:'8.86',
							penalSum:'35.25',
						},
						{
							mgtOrgName: '青浦供电公司',
							penalSums: '1.89',
							goalValue: '200',
							rate: '3.78',
							caseNum:'8',
							fillPower:'3.21',
							fillCost:'1.89',
							penalSum:'7.56',
						},
					];
					this.showpopup = false
				} else {
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "ResultsStatisticController",
										"method": "statics",
										"data": {
											"mgtOrgCode": this.uForm.mgtOrgCode,
											"queryStartDate": this.uForm.queryStartDate,
											"queryEndDate": this.uForm.queryEndDate,
											"custCls": this.uForm.custCls,
											"goalType": this.uForm.goalType,
											"orderSrc": this.uForm.orderSrc,
											"timeType": "day"
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							if (res.data.data) {
								this.dataShow1 = false;
								let datalength = res.data.data.length;
								this.benChenWan = res.data.data[datalength - 1];
								res.data.data.pop();
								this.xiaChenArray = res.data.data;
								if (this.uForm.goalType == "01") {
									this.exgoalType = '窃电'
								} else if (this.uForm.goalType == "02") {
									this.exgoalType = '违约用电'
								} else if (this.uForm.goalType == "03") {
									this.exgoalType = '无协议用电'
								} else if (this.uForm.goalType == "04") {
									this.exgoalType = '窃电及违约用电'
								} else if (this.uForm.goalType == "05") {
									this.exgoalType = '无违约窃电'
								} else if (this.uForm.goalType == "99") {
									this.exgoalType = '其他'
								}
								this.showpopup = false
							} else {
								this.dataShow1 = true;
								this.showpopup = false
							}
							this.fangSearch();
							this.chuLisearch();
							this.gongfusearch();
							// this.dataList = [...this.dataList, ...res.data.list];
							// this.noMore = res.data.list.length < 20;
							// this.page++
						} else {
							this.dataShow1 = true;
							this.showpopup = false
						}
					} finally {
						// this.loading = false
						this.showpopup = false
					}
				}
			},
			// 供服搜索
			async gongfusearch() {
				if (this.testdev) {
					this.dataShow1 = false;
					// this.benChenWan = {
					// 	rate: '7.73',
					// 	penalSums: '618.3',
					// 	goalValue: '8000',
					// 	caseNum: '638'
					// };
					this.gongfuArray = [
						{
							mgtOrgName: '周康供电服务中心',
							penalSums: '15.47',
							goalValue: '1000',
							rate: '0',
							caseNum:'12',
							fillPower:'6.08',
							fillCost:'3.87',
							penalSum:'11.6',
						},
						{
							mgtOrgName: '张江科技园供电服务中心',
							penalSums: '9.82',
							goalValue: '1000',
							rate: '0',
							caseNum:'43',
							fillPower:'4.78',
							fillCost:'2.45',
							penalSum:'7.36',
						},
						{
							mgtOrgName: '陆家嘴供电服务中心',
							penalSums: '11.29',
							goalValue: '1000',
							rate: '0',
							caseNum:'11',
							fillPower:'5.45',
							fillCost:'2.82',
							penalSum:'8.47',
						},
						{
							mgtOrgName: '花木供电服务中心',
							penalSums: '9.79',
							goalValue: '1000',
							rate: '0',
							caseNum:'19',
							fillPower:'3.29',
							fillCost:'1.7',
							penalSum:'5.09',
						},
						{
							mgtOrgName: '世博供电服务中心',
							penalSums: '20.02',
							goalValue: '1000',
							rate: '0',
							caseNum:'10',
							fillPower:'9.66',
							fillCost:'5',
							penalSum:'15.01',
						},
					];
					this.showpopup = false
				} else {
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "ResultsStatisticController",
										"method": "statics",
										"data": {
											"mgtOrgCode": this.uForm.mgtOrgCode,
											"queryStartDate": this.uForm.queryStartDate,
											"queryEndDate": this.uForm.queryEndDate,
											"custCls": this.uForm.custCls,
											"goalType": this.uForm.goalType,
											"orderSrc": this.uForm.orderSrc,
											"timeType": "day"
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							if (res.data.data) {
								this.dataShow1 = false;
								let datalength = res.data.data.length;
								this.benChenWan = res.data.data[datalength - 1];
								res.data.data.pop();
								this.gongfuArray = res.data.data;
								if (this.uForm.goalType == "01") {
									this.exgoalType = '窃电'
								} else if (this.uForm.goalType == "02") {
									this.exgoalType = '违约用电'
								} else if (this.uForm.goalType == "03") {
									this.exgoalType = '无协议用电'
								} else if (this.uForm.goalType == "04") {
									this.exgoalType = '窃电及违约用电'
								} else if (this.uForm.goalType == "05") {
									this.exgoalType = '无违约窃电'
								} else if (this.uForm.goalType == "99") {
									this.exgoalType = '其他'
								}
								this.showpopup = false
							} else {
								this.dataShow1 = true;
								this.showpopup = false
							}
							this.chuLiGongfusearch();
							this.fangGongfuSearch();
							// this.dataList = [...this.dataList, ...res.data.list];
							// this.noMore = res.data.list.length < 20;
							// this.page++
						} else {
							this.dataShow1 = true;
							this.showpopup = false
						}
					} finally {
						// this.loading = false
						this.showpopup = false
					}
				}
			},
			async getMgtOrgCode() {
				// 调用后端登录接口
				try {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryMgtOrgTree",
									"data": {
										"mgtOrgCode": this.uForm.mgtOrgCode,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					if (code === 200) {
						this.treeData = data;
					} else {
						uni.showToast({
							title: '单位树查询失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
					console.log(error)
				}
			},
			handleNavbarSearch(payload) {
				// 执行父页面的操作（如跳转、弹窗、请求数据等）
				this.showpopup = payload.keyword;
			},
			handlegear(payload) {
				this.showpopup2 = payload.keyword;
			},
			close() {
				this.judge = false;
				this.showpopup = false
			},
			close2() {
				this.showpopup2 = false
			},
			handleTreeChange(values, currentItem) {
				// 支持修改节点数据
				// currentItem.label = `${currentItem.originItem.name}`
				this.uForm.mgtOrgCode = values;
			},
			handleExpandChange(expand, currentItem) {
				// 支持修改节点数据
				// currentItem.label = `${currentItem.originItem.name}`
				if (expand == true) {
					this.judge = true;
				} else {
					this.judge = false;
				}
			},

			expandTree(keys, expand) {
				this.$refs.DaTreeRef?.setExpandedKeys(keys, expand)

				const gek = this.$refs.DaTreeRef?.getExpandedKeys()
			},
			checkedTree(keys, checked) {
				this.$refs.DaTreeRef?.setCheckedKeys(keys, checked)

				const gek = this.$refs.DaTreeRef?.getCheckedKeys()
			},
			clicktabs(item) {
				if (item.name == "本单位情况") {
					this.bendanwei = true;
					this.xiajidanwei = false;
					this.gongfu = false;
				} else if (item.name == "下级单位情况") {
					this.bendanwei = false;
					this.xiajidanwei = true;
					this.gongfu = false;
				} else if (item.name == "供服情况") {
					this.bendanwei = false;
					this.xiajidanwei = false;
					this.gongfu = true;
					this.gongfusearch();
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	page {
		background-color: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	.select-box {
		border: 1rpx solid #ddd;
		width: 100%;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.arrow {
		float: right;
		border: solid #999;
		border-width: 0 2rpx 2rpx 0;
		padding: 6rpx;
		margin-top: 6rpx;
	}

	.down {
		transform: rotate(45deg);
	}

	.up {
		transform: rotate(-135deg);
	}

	.dropdown {
		position: absolute;
		width: 100%;
		border: 1rpx solid #eee;
		background: #fff;
		z-index: 999;
	}

	.dropdown-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.mainContainer {
		.tablecontainer {
			display: flex;
			background-color: white;
			border-radius: 50rpx;
			margin: 20rpx;
			padding: 50rpx 30rpx 30rpx 30rpx;

			.titletagcontainer {
				padding: 12rpx 16rpx 0 0;

				.titletag {
					width: 20rpx;
					height: 20rpx;
					background-color: #07ac7c;
					border-radius: 50%;
				}
			}

			.tabletitle {
				width: 100%;

				.titlecontainer {
					display: flex;
					align-items: center;
					justify-content: space-between;


					.titletext {
						font-size: 32rpx;
						font-weight: bold;
					}
				}
			}
		}

		.list-item {
			display: flex;
			align-items: center;
			padding: 20rpx;
			border-bottom: 1rpx solid #f0f0f0;
		}

		.list-item image {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			margin-right: 20rpx;
		}

		.content {
			flex: 1;
		}

		.title {
			font-size: 32rpx;
			color: #333;
		}

		.desc {
			font-size: 24rpx;
			color: #999;
			margin-top: 8rpx;
		}

		.loading-text {
			text-align: center;
			padding: 20rpx;
			color: #999;
		}

		.popupcontainer {
			::v-deep .u-popup__content .u-popup__content__close {
				display: none !important;
			}

			height: 1500rpx;
			padding: 0 40rpx;

			.poptitle {
				text-align: center;
				font-size: 36rpx;
				font-weight: bold;
				padding: 50rpx 0;
			}

			.formitem {
				.DaTreestyle {
					// border: 1px solid black;
					// background-color: white;
					position: absolute;
					top: 24rpx;
					z-index: 999;
				}

				// font-weight: bold;
				.calendarContainer {
					// display: flex;
					// align-items: center;
				}
			}

			.ubutton {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-top: 400rpx;
			}
		}

		.popupcontainer2 {
			::v-deep .u-popup__content .u-popup__content__close {
				display: none !important;
			}

			height: 1000rpx;
			padding: 0 40rpx;

			.poptitle {
				text-align: center;
				font-size: 36rpx;
				font-weight: bold;
				padding: 50rpx 0;
			}

			.formitem {
				.DaTreestyle {
					// border: 1px solid black;
					// background-color: white;
					position: absolute;
					top: 24rpx;
					z-index: 999;
				}

				// font-weight: bold;
				.calendarContainer {
					// display: flex;
					// align-items: center;
				}
			}

			.ubutton {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-top: 100rpx;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 20rpx;

			.lefttext {
				flex: 1;

				.listtitle {
					font-weight: bold;
					font-size: 36rpx;
					padding: 20rpx 20rpx;
				}

				.datatext {
					padding: 20rpx 0 20rpx 20rpx;

					.datacontainer {
						display: flex;
						align-items: center;
						padding: 6rpx 0;

						.datatitle {
							font-size: 26rpx;
							width: 180rpx;
							color: darkgray;
						}
					}

					// display: flex;
					// flex-direction: column;
					// justify-content: center;
					// align-items: center;
				}
			}

			.rightarrow {
				width: 50rpx;
			}
		}
	}
</style>