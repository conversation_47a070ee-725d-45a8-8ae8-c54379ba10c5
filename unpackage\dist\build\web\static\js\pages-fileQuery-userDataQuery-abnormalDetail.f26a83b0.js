(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-fileQuery-userDataQuery-abnormalDetail"],{2304:function(a,t,e){var i=e("c86c");t=i(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-5bb081a7]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-5bb081a7]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-5bb081a7]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-5bb081a7]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-5bb081a7]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-5bb081a7]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-5bb081a7]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-5bb081a7]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-5bb081a7]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-5bb081a7]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-5bb081a7]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-5bb081a7]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-5bb081a7]::after{border:none}.u-hover-class[data-v-5bb081a7]{opacity:.7}.u-primary-light[data-v-5bb081a7]{color:#ecf5ff}.u-warning-light[data-v-5bb081a7]{color:#fdf6ec}.u-success-light[data-v-5bb081a7]{color:#f5fff0}.u-error-light[data-v-5bb081a7]{color:#fef0f0}.u-info-light[data-v-5bb081a7]{color:#f4f4f5}.u-primary-light-bg[data-v-5bb081a7]{background-color:#ecf5ff}.u-warning-light-bg[data-v-5bb081a7]{background-color:#fdf6ec}.u-success-light-bg[data-v-5bb081a7]{background-color:#f5fff0}.u-error-light-bg[data-v-5bb081a7]{background-color:#fef0f0}.u-info-light-bg[data-v-5bb081a7]{background-color:#f4f4f5}.u-primary-dark[data-v-5bb081a7]{color:#398ade}.u-warning-dark[data-v-5bb081a7]{color:#f1a532}.u-success-dark[data-v-5bb081a7]{color:#53c21d}.u-error-dark[data-v-5bb081a7]{color:#e45656}.u-info-dark[data-v-5bb081a7]{color:#767a82}.u-primary-dark-bg[data-v-5bb081a7]{background-color:#398ade}.u-warning-dark-bg[data-v-5bb081a7]{background-color:#f1a532}.u-success-dark-bg[data-v-5bb081a7]{background-color:#53c21d}.u-error-dark-bg[data-v-5bb081a7]{background-color:#e45656}.u-info-dark-bg[data-v-5bb081a7]{background-color:#767a82}.u-primary-disabled[data-v-5bb081a7]{color:#9acafc}.u-warning-disabled[data-v-5bb081a7]{color:#f9d39b}.u-success-disabled[data-v-5bb081a7]{color:#a9e08f}.u-error-disabled[data-v-5bb081a7]{color:#f7b2b2}.u-info-disabled[data-v-5bb081a7]{color:#c4c6c9}.u-primary[data-v-5bb081a7]{color:#3c9cff}.u-warning[data-v-5bb081a7]{color:#f9ae3d}.u-success[data-v-5bb081a7]{color:#5ac725}.u-error[data-v-5bb081a7]{color:#f56c6c}.u-info[data-v-5bb081a7]{color:#909399}.u-primary-bg[data-v-5bb081a7]{background-color:#3c9cff}.u-warning-bg[data-v-5bb081a7]{background-color:#f9ae3d}.u-success-bg[data-v-5bb081a7]{background-color:#5ac725}.u-error-bg[data-v-5bb081a7]{background-color:#f56c6c}.u-info-bg[data-v-5bb081a7]{background-color:#909399}.u-main-color[data-v-5bb081a7]{color:#303133}.u-content-color[data-v-5bb081a7]{color:#606266}.u-tips-color[data-v-5bb081a7]{color:#909193}.u-light-color[data-v-5bb081a7]{color:#c0c4cc}.u-safe-area-inset-top[data-v-5bb081a7]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-5bb081a7]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-5bb081a7]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-5bb081a7]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-5bb081a7]{z-index:10090}uni-toast .uni-toast[data-v-5bb081a7]{z-index:10090}[data-v-5bb081a7]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-page-body[data-v-5bb081a7]{background-color:#f5f5f5;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}body.?%PAGE?%[data-v-5bb081a7]{background-color:#f5f5f5}.abnormal-detail-page[data-v-5bb081a7]{min-height:100vh;display:flex;flex-direction:column;background-color:#f5f5f5;position:relative}\r\n/* 顶部导航区域 */.header-section[data-v-5bb081a7]{background-color:#fff;position:fixed;top:0;left:0;right:0;z-index:101}.search-icon[data-v-5bb081a7]{width:40px;height:40px;display:flex;align-items:center;justify-content:center}\r\n/* 标签页导航 */.tab-nav[data-v-5bb081a7]{position:fixed;top:44px;\r\n  /* 导航栏高度 */left:0;right:0;height:50px;background-color:#fff;z-index:100;box-shadow:0 1px 2px rgba(0,0,0,.05)}.tab-scroll[data-v-5bb081a7]{height:50px;white-space:nowrap}.tab-items[data-v-5bb081a7]{display:inline-flex;height:50px;padding:0}.tab-item[data-v-5bb081a7]{display:flex;align-items:center;justify-content:center;padding:0 15px;height:50px;color:#262626;font-size:14px;position:relative}.tab-item[data-v-5bb081a7]:first-child{padding-left:20px}.tab-item[data-v-5bb081a7]:last-child{border-right:none}.tab-item.active[data-v-5bb081a7]{color:#07ac7c;font-weight:700}.tab-item.active[data-v-5bb081a7]::after{content:"";position:absolute;bottom:6px;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);width:40%;height:2px;background-color:#07ac7c;border-radius:1px}\r\n/* 内容区域 */.content-section[data-v-5bb081a7]{box-sizing:border-box;background-color:#fff;padding:0 15px;-webkit-overflow-scrolling:touch;\r\n  /* 增强iOS滚动体验 */\r\n  /* 确保内容可以滚动，不设置固定高度 */height:auto;padding-bottom:0;\r\n  /* 移除底部内边距 */overflow-y:auto}\r\n/* 加载中提示 */.loading-container[data-v-5bb081a7]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0;margin-bottom:10px}.loading-text[data-v-5bb081a7]{font-size:14px;color:#8c8c8c;margin-top:10px;text-align:center}\r\n/* 详情内容 */.detail-content[data-v-5bb081a7]{-webkit-animation:fade-in-data-v-5bb081a7 .3s ease;animation:fade-in-data-v-5bb081a7 .3s ease;padding:0;margin-bottom:0;\r\n  /* 添加白色背景和圆角，提高视觉体验 */background-color:#fff;border-radius:8px;\r\n  /* 确保内容底部有足够空间 */padding-bottom:60px}\r\n/* 信息行 */.info-row[data-v-5bb081a7]{display:flex;padding:12px 0;align-items:flex-start;border-bottom:1px solid #f0f0f0;\r\n  /* 使用统一的淡入动画，无延迟 */-webkit-animation:fade-in-data-v-5bb081a7 .3s ease;animation:fade-in-data-v-5bb081a7 .3s ease}.info-row[data-v-5bb081a7]:last-child{border-bottom:none;padding-bottom:0\r\n  /* 移除最后一行的底部内边距 */}.info-label[data-v-5bb081a7]{font-size:14px;color:#8c8c8c;width:120px;text-align:left;font-weight:400}.info-value[data-v-5bb081a7]{font-size:14px;color:#262626;flex:1;text-align:right}\r\n/* 特殊字段样式 */.order-type[data-v-5bb081a7], .check-result[data-v-5bb081a7]{color:#ff4d4f;font-weight:700}\r\n/* 无数据提示 */.empty-container[data-v-5bb081a7]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0;background-color:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,.05);margin-top:20px;-webkit-animation:fade-in-data-v-5bb081a7 .3s ease;animation:fade-in-data-v-5bb081a7 .3s ease}.empty-text[data-v-5bb081a7]{font-size:14px;color:#999;margin-top:10px}\r\n/* 底部按钮 */.bottom-button-container[data-v-5bb081a7]{position:fixed;left:0;right:0;bottom:0;height:40px;background-color:#07ac7c;display:flex;align-items:center;justify-content:center;z-index:100;\r\n  /* 添加顶部阴影，增强视觉分离效果 */box-shadow:0 -2px 4px rgba(0,0,0,.05)}.bottom-button[data-v-5bb081a7]{width:100%;height:40px;color:#fff;display:flex;align-items:center;justify-content:center;font-size:16px;font-weight:700}\r\n/* 底部间距 */.bottom-space[data-v-5bb081a7]{height:50px;\r\n  /* 增加底部间距，确保可以滚动到底部 */width:100%}\r\n/* 动画效果 */@-webkit-keyframes fade-in-data-v-5bb081a7{from{opacity:0}to{opacity:1}}@keyframes fade-in-data-v-5bb081a7{from{opacity:0}to{opacity:1}}@-webkit-keyframes slide-in-data-v-5bb081a7{from{opacity:0;-webkit-transform:translateY(10px);transform:translateY(10px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slide-in-data-v-5bb081a7{from{opacity:0;-webkit-transform:translateY(10px);transform:translateY(10px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}',""]),a.exports=t},3870:function(a,t,e){var i=e("2304");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[a.i,i,""]]),i.locals&&(a.exports=i.locals);var o=e("967d").default;o("0a79f459",i,!0,{sourceMap:!1,shadowMode:!1})},"6ed6":function(a,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){}));var i=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("v-uni-view",{staticClass:"abnormal-detail-page"},[e("v-uni-scroll-view",{staticClass:"content-section",style:{height:a.contentHeight+"px"},attrs:{"scroll-y":!0,"refresher-enabled":!0,"refresher-triggered":a.isRefreshing,"show-scrollbar":!1,enhanced:!0,bounces:!0,"fast-deceleration":!0},on:{refresherrefresh:function(t){arguments[0]=t=a.$handleEvent(t),a.onRefresh.apply(void 0,arguments)}}},[!a.isLoading||a.isRefreshing?e("v-uni-view",{staticClass:"detail-content"},[e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("所属单位：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(a.mergedDetailData.orgName))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("用户编号：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(a.mergedDetailData.custNo))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("电能表资产编号：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(a.mergedDetailData.meterAssetNo))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("用户名称：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(a.mergedDetailData.custName))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("用电地址：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(a.mergedDetailData.elecAddr))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("工单编号：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(a.mergedDetailData.workOrderNo))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("工单生成时间：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(a.mergedDetailData.orderTime))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("工单类型：")]),e("v-uni-text",{staticClass:"info-value order-type"},[a._v(a._s(a.mergedDetailData.orderChildTypeName))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("检查结果：")]),e("v-uni-text",{staticClass:"info-value check-result"},[a._v(a._s(a.mergedDetailData.orderArchiveDes))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("异常问题：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(a.mergedDetailData.abnorDesc))])],1)],1):a._e(),e("v-uni-view",{staticClass:"bottom-space"})],1),e("v-uni-view",{staticClass:"bottom-button-container"},[e("v-uni-view",{staticClass:"bottom-button",on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.goBack.apply(void 0,arguments)}}},[e("v-uni-text",[a._v("返回列表")])],1)],1)],1)},o=[]},9170:function(a,t,e){"use strict";e.r(t);var i=e("6ed6"),o=e("c07f");for(var n in o)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return o[a]}))}(n);e("f16f");var r=e("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"5bb081a7",null,!1,i["a"],void 0);t["default"]=s.exports},c07f:function(a,t,e){"use strict";e.r(t);var i=e("de83"),o=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return i[a]}))}(n);t["default"]=o.a},de83:function(a,t,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(e("9b1b"));e("aa77"),e("bf0f"),e("dc8a"),e("d4b5");var n=e("b3d7"),r={props:{queryParams:{type:Object,default:function(){return{}}},detailData:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!0,isRefreshing:!1,contentHeight:0,statusBarHeight:0,navbarHeight:44,tabNavHeight:50,bottomButtonHeight:40,activeTab:"abnormal",tabItems:[{id:"userFile",name:"用户档案"},{id:"loadData",name:"负荷数据"},{id:"powerData",name:"电量数据"},{id:"historyEvent",name:"历史事件"},{id:"abnormal",name:"用电异常"},{id:"lineLoss",name:"线损数据"}],localDetailData:{},isMockData:!0,token:null}},computed:{pageTitle:function(){var a=this,t=this.tabItems.find((function(t){return t.id===a.activeTab}));return t?t.name:"用电异常详情"},mergedDetailData:function(){return(0,o.default)((0,o.default)({},this.localDetailData),this.detailData)}},watch:{detailData:{handler:function(a){console.log("detailData changed:",a),a&&Object.keys(a).length>0&&this.checkAndCompleteData(a)},deep:!0,immediate:!0}},mounted:function(){this.getSystemInfo(),this.calcContentHeight(),this.checkAndCompleteData(this.detailData)},methods:{init:function(){var a=this;wx.invoke("ext_DataCache_GetInfo",{data:{key:"1002838.userInfo"}},(function(t){var e=JSON.parse(t.result);a.token=null===e||void 0===e?void 0:e.token}))},checkAndCompleteData:function(a){this.loadDetailData(null===a||void 0===a?void 0:a.workOrderNo)},getSystemInfo:function(){var a=uni.getSystemInfoSync();this.statusBarHeight=a.statusBarHeight||0,this.windowHeight=a.windowHeight||0},calcContentHeight:function(){this.contentHeight=this.windowHeight-this.bottomButtonHeight},loadDetailData:function(a){var t=this;this.isRefreshing||(this.isLoading=!0),uni.showLoading({title:"加载中..."}),this.isMockData?(this.localDetailData={orgName:"南翔供电服务中心",workOrderNo:"1868817546379870209",elecAddr:"火车站路151弄3号101室",orderTime:"2024-12-17",orderArchiveDate:"2025-01-03",custNo:"3100030169550",custName:"顾伟弟",meterAssetNo:"110007839864",orderChildType:"01",orderChildTypeName:"窃电",abnorDesc:""},this.isLoading=!1,uni.hideLoading()):uni.request({url:"http://127.0.0.1:".concat(n.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:this.token},data:{token:this.token,method:"PutHuaYun",uri:n.url,data:JSON.stringify({bizCode:n.bizCode,espFlowId:(0,n.getUUID)(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:(0,n.getCurrentTimestamp)(),espInformation:{service:"DtsUserController",method:"getUserElecDetail",data:{workOrderNo:a}}})},success:function(a){if(console.log(a),a&&1===a.data.Tag){var e=a.data.Data.espInformation;e&&200==e.code?(t.localDetailData=e.data,t.isLoading=!1):uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3});uni.hideLoading()},fail:function(a){t.isLoading=!1,t.isRefreshing&&(t.isRefreshing=!1),uni.hideLoading(),uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}})},onRefresh:function(){var a=this;this.isRefreshing=!0,this.loadDetailData(),setTimeout((function(){a.isRefreshing=!1}),1e3)},goBack:function(){this.$emit("backToList")},goToSearch:function(){uni.navigateTo({url:"/pages/fileQuery/userDataQuery/userDataQuery"})},switchTab:function(a){this.activeTab!==a&&(uni.vibrateShort(),"abnormal"!==a&&(this.goBack(),uni.$emit("switchTab",{tabName:a})))}}};t.default=r},f16f:function(a,t,e){"use strict";var i=e("3870"),o=e.n(i);o.a}}]);