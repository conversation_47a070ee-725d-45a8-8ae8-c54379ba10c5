(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-fileQuery-jobMonitor-timeMonitor"],{"013c":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(a("fcf3")),r=i(a("d296")),n={name:"u-subsection",mixins:[uni.$u.mpMixin,uni.$u.mixin,r.default],data:function(){return{itemRect:{width:0,height:0}}},watch:{list:function(t,e){this.init()},current:{immediate:!0,handler:function(t){}}},computed:{wrapperStyle:function(){var t={};return"button"===this.mode&&(t.backgroundColor=this.bgColor),t},barStyle:function(){var t={};return t.width="".concat(this.itemRect.width,"px"),t.height="".concat(this.itemRect.height,"px"),t.transform="translateX(".concat(this.current*this.itemRect.width,"px)"),"subsection"===this.mode&&(t.backgroundColor=this.activeColor),t},itemStyle:function(t){var e=this;return function(t){var a={};return"subsection"===e.mode&&(a.borderColor=e.activeColor,a.borderWidth="1px",a.borderStyle="solid"),a}},textStyle:function(t){var e=this;return function(t){var a={};return a.fontWeight=e.bold&&e.current===t?"bold":"normal",a.fontSize=uni.$u.addUnit(e.fontSize),"subsection"===e.mode?a.color=e.current===t?"#fff":e.inactiveColor:a.color=e.current===t?e.activeColor:e.inactiveColor,a}}},mounted:function(){this.init()},methods:{init:function(){var t=this;uni.$u.sleep().then((function(){return t.getRect()}))},getText:function(t){return"object"===(0,o.default)(t)?t[this.keyName]:t},getRect:function(){var t=this;this.$uGetRect(".u-subsection__item--0").then((function(e){t.itemRect=e}))},clickHandler:function(t){this.$emit("change",t)}}};e.default=n},"0875":function(t,e,a){"use strict";a.r(e);var i=a("7b23"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},1202:function(t,e,a){var i=a("5c14");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("b4ae5ea8",i,!0,{sourceMap:!1,shadowMode:!1})},2603:function(t,e,a){"use strict";(function(t){a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("d4b5"),a("4626"),a("5ac7"),a("aa9c"),a("fd3c"),a("5c47");var o=i(a("fcf3")),r=(i(a("5405")),i(a("81de"))),n=i(a("bda1"));function l(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length,a=new Array(e>1?e-1:0),i=1;i<e;i++)a[i-1]=arguments[i];for(var r in a)for(var n in a[r])a[r].hasOwnProperty(n)&&(t[n]=a[r][n]&&"object"===(0,o.default)(a[r][n])?l(Array.isArray(a[r][n])?[]:{},t[n],a[r][n]):a[r][n]);return t}function s(t){var e=t.getFullYear(),a=t.getMonth()+1,i=t.getDate();a>=1&&a<=9&&(a="0"+a),i>=0&&i<=9&&(i="0"+i);var o=e+"-"+a+"-"+i;return o}var c={name:"qiun-data-charts",mixins:[t.mixinDatacom],props:{type:{type:String,default:null},canvasId:{type:String,default:"uchartsid"},canvas2d:{type:Boolean,default:!1},background:{type:String,default:"rgba(0,0,0,0)"},animation:{type:Boolean,default:!0},chartData:{type:Object,default:function(){return{categories:[],series:[]}}},opts:{type:Object,default:function(){return{}}},eopts:{type:Object,default:function(){return{}}},loadingType:{type:Number,default:2},errorShow:{type:Boolean,default:!0},errorReload:{type:Boolean,default:!0},errorMessage:{type:String,default:null},inScrollView:{type:Boolean,default:!1},reshow:{type:Boolean,default:!1},reload:{type:Boolean,default:!1},disableScroll:{type:Boolean,default:!1},optsWatch:{type:Boolean,default:!0},onzoom:{type:Boolean,default:!1},ontap:{type:Boolean,default:!0},ontouch:{type:Boolean,default:!1},onmouse:{type:Boolean,default:!0},onmovetip:{type:Boolean,default:!1},echartsH5:{type:Boolean,default:!1},echartsApp:{type:Boolean,default:!1},tooltipShow:{type:Boolean,default:!0},tooltipFormat:{type:String,default:void 0},tooltipCustom:{type:Object,default:void 0},startDate:{type:String,default:void 0},endDate:{type:String,default:void 0},textEnum:{type:Array,default:function(){return[]}},groupEnum:{type:Array,default:function(){return[]}},pageScrollTop:{type:Number,default:0},directory:{type:String,default:"/"},tapLegend:{type:Boolean,default:!0},menus:{type:Array,default:function(){return[]}}},data:function(){return{cid:"uchartsid",inWx:!1,inAli:!1,inTt:!1,inBd:!1,inH5:!1,inApp:!1,inWin:!1,type2d:!0,disScroll:!1,openmouse:!1,pixel:1,cWidth:375,cHeight:250,showchart:!1,echarts:!1,echartsResize:{state:!1},uchartsOpts:{},echartsOpts:{},drawData:{},lastDrawTime:null}},created:function(){if(this.cid=this.canvasId,"uchartsid"==this.canvasId||""==this.canvasId){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",e=t.length,a="",i=0;i<32;i++)a+=t.charAt(Math.floor(Math.random()*e));this.cid=a}var o=uni.getSystemInfoSync();"windows"!==o.platform&&"mac"!==o.platform||(this.inWin=!0),this.type2d=!1,this.disScroll=this.disableScroll},mounted:function(){var t=this;this.inH5=!0,!0===this.inWin&&(this.openmouse=this.onmouse),!0===this.echartsH5&&(this.echarts=!0),this.$nextTick((function(){t.beforeInit()}));var e=this.inH5?500:200,a=this;uni.onWindowResize(function(t,e){var a=!1;return function(){var i=arguments,o=this;clearTimeout(a),a&&clearTimeout(a),a=setTimeout((function(){a=!1,t.apply(o,i)}),e)}}((function(t){if(1!=a.mixinDatacomLoading){var e=a.mixinDatacomErrorMessage;null!==e&&"null"!==e&&""!==e||(a.echarts?a.echartsResize.state=!a.echartsResize.state:a.resizeHandler())}}),e))},destroyed:function(){!0===this.echarts?(delete n.default.option[this.cid],delete n.default.instance[this.cid]):(delete r.default.option[this.cid],delete r.default.instance[this.cid]),uni.offWindowResize((function(){}))},watch:{chartDataProps:{handler:function(t,e){"object"===(0,o.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&(this._clearChart(),t.series&&t.series.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this.showchart=!1,this.mixinDatacomErrorMessage=null)):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：chartData数据类型错误")},immediate:!1,deep:!0},localdata:{handler:function(t,e){JSON.stringify(t)!==JSON.stringify(e)&&(t.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage=null))},immediate:!1,deep:!0},optsProps:{handler:function(t,e){"object"===(0,o.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&!1===this.echarts&&1==this.optsWatch&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：opts数据类型错误")},immediate:!1,deep:!0},eoptsProps:{handler:function(t,e){"object"===(0,o.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&!0===this.echarts&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：eopts数据类型错误")},immediate:!1,deep:!0},reshow:function(t,e){var a=this;!0===t&&!1===this.mixinDatacomLoading&&setTimeout((function(){a.mixinDatacomErrorMessage=null,a.echartsResize.state=!a.echartsResize.state,a.checkData(a.drawData)}),200)},reload:function(t,e){!0===t&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())},mixinDatacomErrorMessage:function(t,e){t&&(this.emitMsg({name:"error",params:{type:"error",errorShow:this.errorShow,msg:t,id:this.cid}}),this.errorShow&&console.log("[秋云图表组件]"+t))},errorMessage:function(t,e){t&&this.errorShow&&null!==t&&"null"!==t&&""!==t?(this.showchart=!1,this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=t):(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())}},computed:{optsProps:function(){return JSON.parse(JSON.stringify(this.opts))},eoptsProps:function(){return JSON.parse(JSON.stringify(this.eopts))},chartDataProps:function(){return JSON.parse(JSON.stringify(this.chartData))}},methods:{beforeInit:function(){this.mixinDatacomErrorMessage=null,"object"===(0,o.default)(this.chartData)&&null!=this.chartData&&void 0!==this.chartData.series&&this.chartData.series.length>0?(this.drawData=l({},this.chartData),this.mixinDatacomLoading=!1,this.showchart=!0,this.checkData(this.chartData)):this.localdata.length>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.localdataInit(this.localdata)):""!==this.collection?(this.mixinDatacomLoading=!1,this.getCloudData()):this.mixinDatacomLoading=!0},localdataInit:function(t){if(this.groupEnum.length>0)for(var e=0;e<t.length;e++)for(var a=0;a<this.groupEnum.length;a++)t[e].group===this.groupEnum[a].value&&(t[e].group=this.groupEnum[a].text);if(this.textEnum.length>0)for(var i=0;i<t.length;i++)for(var o=0;o<this.textEnum.length;o++)t[i].text===this.textEnum[o].value&&(t[i].text=this.textEnum[o].text);var c=!1,d={categories:[],series:[]},h=[],u=[];if(c=!0===this.echarts?n.default.categories.includes(this.type):r.default.categories.includes(this.type),!0===c){if(this.chartData&&this.chartData.categories&&this.chartData.categories.length>0)h=this.chartData.categories;else if(this.startDate&&this.endDate){var p=new Date(this.startDate),f=new Date(this.endDate);while(p<=f)h.push(s(p)),p=p.setDate(p.getDate()+1),p=new Date(p)}else{var x={};t.map((function(t,e){void 0==t.text||x[t.text]||(h.push(t.text),x[t.text]=!0)}))}d.categories=h}var g={};if(t.map((function(t,e){void 0==t.group||g[t.group]||(u.push({name:t.group,data:[]}),g[t.group]=!0)})),0==u.length)if(u=[{name:"默认分组",data:[]}],!0===c)for(var v=0;v<h.length;v++){for(var y=0,m=0;m<t.length;m++)t[m].text==h[v]&&(y=t[m].value);u[0].data.push(y)}else for(var b=0;b<t.length;b++)u[0].data.push({name:t[b].text,value:t[b].value});else for(var w=0;w<u.length;w++)if(h.length>0)for(var S=0;S<h.length;S++){for(var C=0,A=0;A<t.length;A++)u[w].name==t[A].group&&t[A].text==h[S]&&(C=t[A].value);u[w].data.push(C)}else for(var T=0;T<t.length;T++)u[w].name==t[T].group&&u[w].data.push(t[T].value);d.series=u,this.drawData=l({},d),this.checkData(d)},reloading:function(){!1!==this.errorReload&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,""!==this.collection?(this.mixinDatacomLoading=!1,this.onMixinDatacomPropsChange(!0)):this.beforeInit())},checkData:function(t){var e=this,a=this.cid;!0===this.echarts?(n.default.option[a]=l({},this.eopts),n.default.option[a].id=a,n.default.option[a].type=this.type):this.type&&r.default.type.includes(this.type)?(r.default.option[a]=l({},r.default[this.type],this.opts),r.default.option[a].canvasId=a):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：props参数中type类型不正确");var i=l({},t);void 0!==i.series&&i.series.length>0&&(this.mixinDatacomErrorMessage=null,!0===this.echarts?(n.default.option[a].chartData=i,this.$nextTick((function(){e.init()}))):(r.default.option[a].categories=i.categories,r.default.option[a].series=i.series,this.$nextTick((function(){e.init()}))))},resizeHandler:function(){var t=this,e=Date.now(),a=this.lastDrawTime?this.lastDrawTime:e-3e3,i=e-a;if(!(i<1e3))uni.createSelectorQuery().in(this).select("#ChartBoxId"+this.cid).boundingClientRect((function(e){t.showchart=!0,e.width>0&&e.height>0&&(e.width===t.cWidth&&e.height===t.cHeight||t.checkData(t.drawData))})).exec()},getCloudData:function(){var t=this;1!=this.mixinDatacomLoading&&(this.mixinDatacomLoading=!0,this.mixinDatacomGet().then((function(e){t.mixinDatacomResData=e.result.data,t.localdataInit(t.mixinDatacomResData)})).catch((function(e){t.mixinDatacomLoading=!1,t.showchart=!1,t.mixinDatacomErrorMessage="请求错误："+e})))},onMixinDatacomPropsChange:function(t,e){1==t&&""!==this.collection&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this._clearChart(),this.getCloudData())},_clearChart:function(){var t=this.cid;if(!0!==this.echarts&&r.default.option[t]&&r.default.option[t].context){var e=r.default.option[t].context;"object"!==(0,o.default)(e)||r.default.option[t].update||(e.clearRect(0,0,this.cWidth*this.pixel,this.cHeight*this.pixel),e.draw())}},init:function(){var t=this,e=this.cid;uni.createSelectorQuery().in(this).select("#ChartBoxId"+e).boundingClientRect((function(a){a.width>0&&a.height>0?(t.mixinDatacomLoading=!1,t.showchart=!0,t.lastDrawTime=Date.now(),t.cWidth=a.width,t.cHeight=a.height,!0!==t.echarts&&(r.default.option[e].background="rgba(0,0,0,0)"==t.background?"#FFFFFF":t.background,r.default.option[e].canvas2d=t.type2d,r.default.option[e].pixelRatio=t.pixel,r.default.option[e].animation=t.animation,r.default.option[e].width=a.width*t.pixel,r.default.option[e].height=a.height*t.pixel,r.default.option[e].onzoom=t.onzoom,r.default.option[e].ontap=t.ontap,r.default.option[e].ontouch=t.ontouch,r.default.option[e].onmouse=t.openmouse,r.default.option[e].onmovetip=t.onmovetip,r.default.option[e].tooltipShow=t.tooltipShow,r.default.option[e].tooltipFormat=t.tooltipFormat,r.default.option[e].tooltipCustom=t.tooltipCustom,r.default.option[e].inScrollView=t.inScrollView,r.default.option[e].lastDrawTime=t.lastDrawTime,r.default.option[e].tapLegend=t.tapLegend),t.inH5||t.inApp?1==t.echarts?(n.default.option[e].ontap=t.ontap,n.default.option[e].onmouse=t.openmouse,n.default.option[e].tooltipShow=t.tooltipShow,n.default.option[e].tooltipFormat=t.tooltipFormat,n.default.option[e].tooltipCustom=t.tooltipCustom,n.default.option[e].lastDrawTime=t.lastDrawTime,t.echartsOpts=l({},n.default.option[e])):(r.default.option[e].rotateLock=r.default.option[e].rotate,t.uchartsOpts=l({},r.default.option[e])):(r.default.option[e]=function t(e,a){for(var i in e)e.hasOwnProperty(i)&&null!==e[i]&&"object"===(0,o.default)(e[i])?t(e[i],a):"format"===i&&"string"===typeof e[i]&&(e["formatter"]=a[e[i]]?a[e[i]]:void 0);return e}(r.default.option[e],r.default.formatter),t.mixinDatacomErrorMessage=null,t.mixinDatacomLoading=!1,t.showchart=!0,t.$nextTick((function(){if(!0===t.type2d){var i=uni.createSelectorQuery().in(t);i.select("#"+e).fields({node:!0,size:!0}).exec((function(i){if(i[0]){var o=i[0].node,n=o.getContext("2d");r.default.option[e].context=n,r.default.option[e].rotateLock=r.default.option[e].rotate,r.default.instance[e]&&r.default.option[e]&&!0===r.default.option[e].update?t._updataUChart(e):(o.width=a.width*t.pixel,o.height=a.height*t.pixel,o._width=a.width*t.pixel,o._height=a.height*t.pixel,setTimeout((function(){r.default.option[e].context.restore(),r.default.option[e].context.save(),t._newChart(e)}),100))}else t.showchart=!1,t.mixinDatacomErrorMessage="参数错误：开启2d模式后，未获取到dom节点，canvas-id:"+e}))}else t.inAli&&(r.default.option[e].rotateLock=r.default.option[e].rotate),r.default.option[e].context=uni.createCanvasContext(e,t),r.default.instance[e]&&r.default.option[e]&&!0===r.default.option[e].update?t._updataUChart(e):setTimeout((function(){r.default.option[e].context.restore(),r.default.option[e].context.save(),t._newChart(e)}),100)})))):(t.mixinDatacomLoading=!1,t.showchart=!1,1==t.reshow&&(t.mixinDatacomErrorMessage="布局错误：未获取到父元素宽高尺寸！canvas-id:"+e))})).exec()},saveImage:function(){var t=this;uni.canvasToTempFilePath({canvasId:this.cid,success:function(e){var a=document.createElement("a");a.href=e.tempFilePath,a.download=t.cid,a.target="_blank",a.click()}},this)},getImage:function(){var t=this;if(0==this.type2d)uni.canvasToTempFilePath({canvasId:this.cid,success:function(e){t.emitMsg({name:"getImage",params:{type:"getImage",base64:e.tempFilePath}})}},this);else{var e=uni.createSelectorQuery().in(this);e.select("#"+this.cid).fields({node:!0,size:!0}).exec((function(e){if(e[0]){var a=e[0].node;t.emitMsg({name:"getImage",params:{type:"getImage",base64:a.toDataURL("image/png")}})}}))}},_error:function(t){this.mixinDatacomErrorMessage=t.detail.errMsg},emitMsg:function(t){this.$emit(t.name,t.params)},getRenderType:function(){!0===this.echarts&&!1===this.mixinDatacomLoading&&this.beforeInit()},toJSON:function(){return this}}};e.default=c}).call(this,a("861b")["uniCloud"])},"2a4c6":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-24c4711e]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-24c4711e]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-24c4711e]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-24c4711e]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-24c4711e]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-24c4711e]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-24c4711e]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-24c4711e]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-24c4711e]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-24c4711e]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-24c4711e]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-24c4711e]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-24c4711e]::after{border:none}.u-hover-class[data-v-24c4711e]{opacity:.7}.u-primary-light[data-v-24c4711e]{color:#ecf5ff}.u-warning-light[data-v-24c4711e]{color:#fdf6ec}.u-success-light[data-v-24c4711e]{color:#f5fff0}.u-error-light[data-v-24c4711e]{color:#fef0f0}.u-info-light[data-v-24c4711e]{color:#f4f4f5}.u-primary-light-bg[data-v-24c4711e]{background-color:#ecf5ff}.u-warning-light-bg[data-v-24c4711e]{background-color:#fdf6ec}.u-success-light-bg[data-v-24c4711e]{background-color:#f5fff0}.u-error-light-bg[data-v-24c4711e]{background-color:#fef0f0}.u-info-light-bg[data-v-24c4711e]{background-color:#f4f4f5}.u-primary-dark[data-v-24c4711e]{color:#398ade}.u-warning-dark[data-v-24c4711e]{color:#f1a532}.u-success-dark[data-v-24c4711e]{color:#53c21d}.u-error-dark[data-v-24c4711e]{color:#e45656}.u-info-dark[data-v-24c4711e]{color:#767a82}.u-primary-dark-bg[data-v-24c4711e]{background-color:#398ade}.u-warning-dark-bg[data-v-24c4711e]{background-color:#f1a532}.u-success-dark-bg[data-v-24c4711e]{background-color:#53c21d}.u-error-dark-bg[data-v-24c4711e]{background-color:#e45656}.u-info-dark-bg[data-v-24c4711e]{background-color:#767a82}.u-primary-disabled[data-v-24c4711e]{color:#9acafc}.u-warning-disabled[data-v-24c4711e]{color:#f9d39b}.u-success-disabled[data-v-24c4711e]{color:#a9e08f}.u-error-disabled[data-v-24c4711e]{color:#f7b2b2}.u-info-disabled[data-v-24c4711e]{color:#c4c6c9}.u-primary[data-v-24c4711e]{color:#3c9cff}.u-warning[data-v-24c4711e]{color:#f9ae3d}.u-success[data-v-24c4711e]{color:#5ac725}.u-error[data-v-24c4711e]{color:#f56c6c}.u-info[data-v-24c4711e]{color:#909399}.u-primary-bg[data-v-24c4711e]{background-color:#3c9cff}.u-warning-bg[data-v-24c4711e]{background-color:#f9ae3d}.u-success-bg[data-v-24c4711e]{background-color:#5ac725}.u-error-bg[data-v-24c4711e]{background-color:#f56c6c}.u-info-bg[data-v-24c4711e]{background-color:#909399}.u-main-color[data-v-24c4711e]{color:#303133}.u-content-color[data-v-24c4711e]{color:#606266}.u-tips-color[data-v-24c4711e]{color:#909193}.u-light-color[data-v-24c4711e]{color:#c0c4cc}.u-safe-area-inset-top[data-v-24c4711e]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-24c4711e]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-24c4711e]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-24c4711e]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-24c4711e]{z-index:10090}uni-toast .uni-toast[data-v-24c4711e]{z-index:10090}[data-v-24c4711e]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */*[data-v-24c4711e]{margin:0;padding:0}uni-page-body[data-v-24c4711e]{background-color:#f8f8f8;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}body.?%PAGE?%[data-v-24c4711e]{background-color:#f8f8f8}.mainContainer[data-v-24c4711e]  .u-popup__content .u-popup__content__close{display:none!important}.mainContainer .chuliContainer .calendarContainer[data-v-24c4711e]{display:flex;align-items:center}.mainContainer .chuliContainer .subsectionContainer[data-v-24c4711e]{padding:%?20?% 0}.mainContainer .chuliContainer .buttonContainer[data-v-24c4711e]{display:flex;justify-content:space-between;align-items:center}.mainContainer .chuliContainer .charts-box[data-v-24c4711e]{background-color:#fff;border-radius:%?20?%;padding:%?20?%;margin:%?40?% %?20?%}.mainContainer .chuliContainer .charts-box .tabletitle[data-v-24c4711e]{display:flex;justify-content:space-between;align-items:center;padding:%?10?% 0 %?20?% %?10?%}.mainContainer .chuliContainer .charts-box .tabletitle .titletag[data-v-24c4711e]{width:%?10?%;height:%?30?%;background-color:#07ac7c;border-radius:%?4?%}.mainContainer .chuliContainer .charts-box .tabletitle .titletext[data-v-24c4711e]{font-weight:700;margin-left:%?20?%}.mainContainer .chuliContainer .charts-box .chartstitle[data-v-24c4711e]{font-weight:700}.mainContainer .chuliContainer .tablecontainer[data-v-24c4711e]{background-color:#fff;border-radius:%?20?%;padding:%?20?%;margin:0 %?20?% %?20?% %?20?%;font-weight:700}.mainContainer .chuliContainer .tablecontainer .tabletitle[data-v-24c4711e]{display:flex;justify-content:left;align-items:center;padding:%?10?% 0 %?20?% %?10?%}.mainContainer .chuliContainer .tablecontainer .tabletitle .titletag[data-v-24c4711e]{width:%?10?%;height:%?30?%;background-color:#07ac7c;border-radius:%?4?%}.mainContainer .chuliContainer .tablecontainer .tabletitle .titletext[data-v-24c4711e]{margin-left:%?20?%}.mainContainer .chuliContainer .tablecontainer .rowcontainer[data-v-24c4711e]{display:flex;justify-content:space-between;align-items:center;font-size:%?30?%}',""]),t.exports=e},"2d31":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uSubsection:a("8fd6").default,uButton:a("6834").default,qiunDataCharts:a("a3e6").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"mainContainer"},[i("custom-navbar",{attrs:{title:"工单时效监控"}}),i("v-uni-view",{staticStyle:{margin:"20rpx 0 0 20rpx","font-weight":"bold"}},[t._v("超时环节")]),i("v-uni-view",{staticClass:"chuliContainer"},[i("v-uni-view",{staticStyle:{padding:"20rpx"}},[i("v-uni-view",{},[i("v-uni-view",{staticClass:"calendarContainer"},[i("uni-datetime-picker",{staticStyle:{"background-color":"white"},attrs:{type:"date",placeholder:"开始日期",end:t.queryDateEnd},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleStartDate.apply(void 0,arguments)}},model:{value:t.queryDateStart,callback:function(e){t.queryDateStart=e},expression:"queryDateStart"}}),i("v-uni-view",{staticStyle:{margin:"0 10rpx"}},[t._v("-")]),i("uni-datetime-picker",{staticStyle:{"background-color":"white"},attrs:{type:"date",placeholder:"结束日期",start:t.queryDateStart},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleEndDate.apply(void 0,arguments)}},model:{value:t.queryDateEnd,callback:function(e){t.queryDateEnd=e},expression:"queryDateEnd"}})],1),i("v-uni-view",{staticClass:"subsectionContainer"},[i("u-subsection",{attrs:{list:t.subsectionlist,current:t.curNow,mode:"button"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.sectionChange.apply(void 0,arguments)}}})],1)],1),i("v-uni-view",{staticClass:"buttonContainer"},[i("u-button",{staticStyle:{color:"#fcfefd",width:"350rpx"},attrs:{type:"primary",color:"#07ac7c",text:"查询"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}}}),i("u-button",{staticStyle:{border:"1px solid lightgray",width:"350rpx"},attrs:{type:"default",text:"重置"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reset.apply(void 0,arguments)}}})],1)],1),i("v-uni-view",{staticClass:"tablecontainer",staticStyle:{"margin-top":"20rpx"}},[i("v-uni-view",{staticClass:"tabletitle"},[i("v-uni-view",{staticClass:"titletag"}),i("v-uni-view",{staticClass:"titletext"},[t._v("本单位时效情况")])],1),i("v-uni-view",{staticClass:"rowcontainer",staticStyle:{padding:"20rpx"}},[i("v-uni-view",{},[i("v-uni-text",[t._v("工单总数：")]),i("v-uni-text",{staticStyle:{color:"#2bb98f"}},[t._v(t._s(t.total))])],1),i("v-uni-view",{staticStyle:{width:"300rpx"}},[i("v-uni-text",[t._v("生成超时：")]),i("v-uni-text",{staticStyle:{color:"red"}},[t._v(t._s(t.createNum))])],1)],1),i("v-uni-view",{staticClass:"rowcontainer",staticStyle:{padding:"20rpx"}},[i("v-uni-view",{},[i("v-uni-text",[t._v("派工超时：")]),i("v-uni-text",{staticStyle:{color:"red"}},[t._v(t._s(t.dispatchNum))])],1),i("v-uni-view",{staticStyle:{width:"300rpx"}},[i("v-uni-text",[t._v("检查超时：")]),i("v-uni-text",{staticStyle:{color:"red"}},[t._v(t._s(t.checkNum))])],1)],1)],1),t.chartMain?i("v-uni-view",{staticClass:"charts-box"},[i("v-uni-view",{staticClass:"tabletitle"},[i("v-uni-view",{staticStyle:{display:"flex","justify-content":"left","align-items":"center"}},[i("v-uni-view",{staticClass:"titletag"}),i("v-uni-view",{staticClass:"titletext"},[t._v("下级单位处理情况")])],1),i("uni-icons",{attrs:{type:"tune",size:"20",color:"gray"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.shownextList.apply(void 0,arguments)}}})],1),t.dataShow?i("v-uni-view",{staticStyle:{height:"500rpx",width:"100%",display:"flex","flex-direction":"column","align-items":"center","justify-content":"center"}},[i("img",{staticStyle:{width:"70%",height:"80%"},attrs:{src:a("80ec"),alt:"nodata"}}),i("v-uni-view",{staticStyle:{color:"gainsboro"}},[t._v("数据为空")])],1):t._e(),t.chartShow?i("qiun-data-charts",{attrs:{type:"column",opts:t.opts2,chartData:t.chartData2,ontouch:!0}}):t._e()],1):t._e(),t.nextshow?i("v-uni-view",{staticStyle:{height:"600rpx","overflow-y":"auto","margin-top":"40rpx"}},t._l(t.shixiaolist,(function(e,a){return i("v-uni-view",{key:a,staticClass:"tablecontainer"},[i("v-uni-view",{staticClass:"tabletitle",staticStyle:{"justify-content":"space-between"}},[i("v-uni-view",{staticStyle:{display:"flex","justify-content":"left","align-items":"center"}},[i("v-uni-view",{staticClass:"titletag"}),i("v-uni-view",{staticClass:"titletext"},[t._v(t._s(e.mgtOrgName)+"时效情况")])],1),i("uni-icons",{attrs:{type:"tune",size:"20",color:"gray"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.shownextList.apply(void 0,arguments)}}})],1),i("v-uni-view",{staticClass:"rowcontainer",staticStyle:{padding:"20rpx"}},[i("v-uni-view",{},[i("v-uni-text",[t._v("工单总数：")]),i("v-uni-text",{staticStyle:{color:"#2bb98f"}},[t._v(t._s(e.total))])],1),i("v-uni-view",{staticStyle:{width:"300rpx"}},[i("v-uni-text",[t._v("生成超时：")]),i("v-uni-text",{staticStyle:{color:"red"}},[t._v(t._s(e.createNum))])],1)],1),i("v-uni-view",{staticClass:"rowcontainer",staticStyle:{padding:"20rpx"}},[i("v-uni-view",{},[i("v-uni-text",[t._v("派工超时：")]),i("v-uni-text",{staticStyle:{color:"red"}},[t._v(t._s(e.dispatchNum))])],1),i("v-uni-view",{staticStyle:{width:"300rpx"}},[i("v-uni-text",[t._v("检查超时：")]),i("v-uni-text",{staticStyle:{color:"red"}},[t._v(t._s(e.checkNum))])],1)],1)],1)})),1):t._e()],1)],1)},r=[]},"3c38":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-4a603381]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-4a603381]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-4a603381]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-4a603381]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-4a603381]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-4a603381]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-4a603381]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-4a603381]::after{border:none}.u-hover-class[data-v-4a603381]{opacity:.7}.u-primary-light[data-v-4a603381]{color:#ecf5ff}.u-warning-light[data-v-4a603381]{color:#fdf6ec}.u-success-light[data-v-4a603381]{color:#f5fff0}.u-error-light[data-v-4a603381]{color:#fef0f0}.u-info-light[data-v-4a603381]{color:#f4f4f5}.u-primary-light-bg[data-v-4a603381]{background-color:#ecf5ff}.u-warning-light-bg[data-v-4a603381]{background-color:#fdf6ec}.u-success-light-bg[data-v-4a603381]{background-color:#f5fff0}.u-error-light-bg[data-v-4a603381]{background-color:#fef0f0}.u-info-light-bg[data-v-4a603381]{background-color:#f4f4f5}.u-primary-dark[data-v-4a603381]{color:#398ade}.u-warning-dark[data-v-4a603381]{color:#f1a532}.u-success-dark[data-v-4a603381]{color:#53c21d}.u-error-dark[data-v-4a603381]{color:#e45656}.u-info-dark[data-v-4a603381]{color:#767a82}.u-primary-dark-bg[data-v-4a603381]{background-color:#398ade}.u-warning-dark-bg[data-v-4a603381]{background-color:#f1a532}.u-success-dark-bg[data-v-4a603381]{background-color:#53c21d}.u-error-dark-bg[data-v-4a603381]{background-color:#e45656}.u-info-dark-bg[data-v-4a603381]{background-color:#767a82}.u-primary-disabled[data-v-4a603381]{color:#9acafc}.u-warning-disabled[data-v-4a603381]{color:#f9d39b}.u-success-disabled[data-v-4a603381]{color:#a9e08f}.u-error-disabled[data-v-4a603381]{color:#f7b2b2}.u-info-disabled[data-v-4a603381]{color:#c4c6c9}.u-primary[data-v-4a603381]{color:#3c9cff}.u-warning[data-v-4a603381]{color:#f9ae3d}.u-success[data-v-4a603381]{color:#5ac725}.u-error[data-v-4a603381]{color:#f56c6c}.u-info[data-v-4a603381]{color:#909399}.u-primary-bg[data-v-4a603381]{background-color:#3c9cff}.u-warning-bg[data-v-4a603381]{background-color:#f9ae3d}.u-success-bg[data-v-4a603381]{background-color:#5ac725}.u-error-bg[data-v-4a603381]{background-color:#f56c6c}.u-info-bg[data-v-4a603381]{background-color:#909399}.u-main-color[data-v-4a603381]{color:#303133}.u-content-color[data-v-4a603381]{color:#606266}.u-tips-color[data-v-4a603381]{color:#909193}.u-light-color[data-v-4a603381]{color:#c0c4cc}.u-safe-area-inset-top[data-v-4a603381]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-4a603381]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-4a603381]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-4a603381]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-4a603381]{z-index:10090}uni-toast .uni-toast[data-v-4a603381]{z-index:10090}[data-v-4a603381]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-view[data-v-4a603381], uni-scroll-view[data-v-4a603381], uni-swiper-item[data-v-4a603381]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-subsection[data-v-4a603381]{display:flex;flex-direction:row;position:relative;overflow:hidden;width:100%;box-sizing:border-box}.u-subsection--button[data-v-4a603381]{height:32px;background-color:#eeeeef;padding:3px;border-radius:3px;align-items:stretch}.u-subsection--button__bar[data-v-4a603381]{background-color:#fff;border-radius:3px!important}.u-subsection--subsection[data-v-4a603381]{height:30px}.u-subsection__bar[data-v-4a603381]{position:absolute;transition-property:color,-webkit-transform;transition-property:transform,color;transition-property:transform,color,-webkit-transform;transition-duration:.3s;transition-timing-function:ease-in-out}.u-subsection__bar--first[data-v-4a603381]{border-top-left-radius:3px;border-bottom-left-radius:3px;border-top-right-radius:0;border-bottom-right-radius:0}.u-subsection__bar--center[data-v-4a603381]{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:0}.u-subsection__bar--last[data-v-4a603381]{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:3px;border-bottom-right-radius:3px}.u-subsection__item[data-v-4a603381]{display:flex;flex-direction:row;flex:1;justify-content:center;align-items:center;position:relative}.u-subsection__item--no-border-right[data-v-4a603381]{border-right-width:0!important}.u-subsection__item--first[data-v-4a603381]{border-top-left-radius:3px;border-bottom-left-radius:3px}.u-subsection__item--last[data-v-4a603381]{border-top-right-radius:3px;border-bottom-right-radius:3px}.u-subsection__item__text[data-v-4a603381]{font-size:12px;line-height:12px;display:flex;flex-direction:row;align-items:center;transition-property:color;transition-duration:.3s}',""]),t.exports=e},"44cb":function(t,e,a){"use strict";a.r(e);var i=a("2603"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},5405:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(a("5de6")),r=i(a("fcf3"));a("7a76"),a("c9b5"),a("bf0f"),a("ab80"),a("2797"),a("aa9c"),a("5c47"),a("a1c1"),a("e966"),a("5ef2"),a("0506"),a("473f"),a("c223"),a("fd3c"),a("8f71"),a("f7a5"),a("dc69"),a("1851"),a("4626"),a("5ac7"),a("3efd");var n={version:"v2.5.0-20230101",yAxisWidth:15,xAxisHeight:22,padding:[10,10,10,10],rotate:!1,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,titleFontSize:20,subtitleFontSize:15,radarLabelTextMargin:13},l=function(t){for(var e=arguments.length,a=new Array(e>1?e-1:0),i=1;i<e;i++)a[i-1]=arguments[i];if(null==t)throw new TypeError("[uCharts] Cannot convert undefined or null to object");if(!a||a.length<=0)return t;function o(t,e){for(var a in e)t[a]=t[a]&&"[object Object]"===t[a].toString()?o(t[a],e[a]):t[a]=e[a];return t}return a.forEach((function(e){t=o(t,e)})),t},s={toFixed:function(t,e){return e=e||2,this.isFloat(t)&&(t=t.toFixed(e)),t},isFloat:function(t){return t%1!==0},approximatelyEqual:function(t,e){return Math.abs(t-e)<1e-10},isSameSign:function(t,e){return Math.abs(t)===t&&Math.abs(e)===e||Math.abs(t)!==t&&Math.abs(e)!==e},isSameXCoordinateArea:function(t,e){return this.isSameSign(t.x,e.x)},isCollision:function(t,e){t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height,e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height;var a=e.start.x>t.end.x||e.end.x<t.start.x||e.end.y>t.start.y||e.start.y<t.end.y;return!a}};function c(t,e){var a=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(t,e,a,i){return e+e+a+a+i+i})),i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a),o=parseInt(i[1],16),r=parseInt(i[2],16),n=parseInt(i[3],16);return"rgba("+o+","+r+","+n+","+e+")"}function d(t,e,a){if(isNaN(t))throw new Error("[uCharts] series数据需为Number格式");a=a||10,e=e||"upper";var i=1;while(a<1)a*=10,i*=10;t="upper"===e?Math.ceil(t*i):Math.floor(t*i);while(t%a!==0)if("upper"===e){if(t==t+1)break;t++}else t--;return t/i}function h(t,e,a,i,o){var r=o.width-o.area[1]-o.area[3],n=a.eachSpacing*(o.chartData.xAxisData.xAxisPoints.length-1);"mount"==o.type&&o.extra&&o.extra.mount&&o.extra.mount.widthRatio&&o.extra.mount.widthRatio>1&&(o.extra.mount.widthRatio>2&&(o.extra.mount.widthRatio=2),n+=(o.extra.mount.widthRatio-1)*a.eachSpacing);var l=e;return e>=0?(l=0,t.uevent.trigger("scrollLeft"),t.scrollOption.position="left",o.xAxis.scrollPosition="left"):Math.abs(e)>=n-r?(l=r-n,t.uevent.trigger("scrollRight"),t.scrollOption.position="right",o.xAxis.scrollPosition="right"):(t.scrollOption.position=e,o.xAxis.scrollPosition=e),l}function u(t,e,a){function i(t){while(t<0)t+=2*Math.PI;while(t>2*Math.PI)t-=2*Math.PI;return t}return t=i(t),e=i(e),a=i(a),e>a&&(a+=2*Math.PI,t<e&&(t+=2*Math.PI)),t>=e&&t<=a}function p(t,e){function a(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].y>=Math.max(t[e-1].y,t[e+1].y)||t[e].y<=Math.min(t[e-1].y,t[e+1].y))}function i(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].x>=Math.max(t[e-1].x,t[e+1].x)||t[e].x<=Math.min(t[e-1].x,t[e+1].x))}var o=.2,r=.2,n=null,l=null,s=null,c=null;if(e<1?(n=t[0].x+(t[1].x-t[0].x)*o,l=t[0].y+(t[1].y-t[0].y)*o):(n=t[e].x+(t[e+1].x-t[e-1].x)*o,l=t[e].y+(t[e+1].y-t[e-1].y)*o),e>t.length-3){var d=t.length-1;s=t[d].x-(t[d].x-t[d-1].x)*r,c=t[d].y-(t[d].y-t[d-1].y)*r}else s=t[e+1].x-(t[e+2].x-t[e].x)*r,c=t[e+1].y-(t[e+2].y-t[e].y)*r;return a(t,e+1)&&(c=t[e+1].y),a(t,e)&&(l=t[e].y),i(t,e+1)&&(s=t[e+1].x),i(t,e)&&(n=t[e].x),(l>=Math.max(t[e].y,t[e+1].y)||l<=Math.min(t[e].y,t[e+1].y))&&(l=t[e].y),(c>=Math.max(t[e].y,t[e+1].y)||c<=Math.min(t[e].y,t[e+1].y))&&(c=t[e+1].y),(n>=Math.max(t[e].x,t[e+1].x)||n<=Math.min(t[e].x,t[e+1].x))&&(n=t[e].x),(s>=Math.max(t[e].x,t[e+1].x)||s<=Math.min(t[e].x,t[e+1].x))&&(s=t[e+1].x),{ctrA:{x:n,y:l},ctrB:{x:s,y:c}}}function f(t,e,a){return{x:a.x+t,y:a.y-e}}function x(t,e){if(e)while(s.isCollision(t,e))t.start.x>0?t.start.y--:t.start.x<0||t.start.y>0?t.start.y++:t.start.y--;return t}function g(t,e,a){for(var i=0,o=0;o<t.length;o++){var r=t[o];if(r.color||(r.color=a.color[i],i=(i+1)%a.color.length),r.linearIndex||(r.linearIndex=o),r.index||(r.index=0),r.type||(r.type=e.type),"undefined"==typeof r.show&&(r.show=!0),r.type||(r.type=e.type),r.pointShape||(r.pointShape="circle"),!r.legendShape)switch(r.type){case"line":r.legendShape="line";break;case"column":case"bar":r.legendShape="rect";break;case"area":case"mount":r.legendShape="triangle";break;default:r.legendShape="circle"}}return t}function v(t,e,a,i){var o=e||[];if("custom"==t&&0==o.length&&(o=i.linearColor),"custom"==t&&o.length<a.length)for(var r=a.length-o.length,n=0;n<r;n++)o.push(i.linearColor[(n+1)%i.linearColor.length]);return o}function y(t,e){var a=0,i=e-t;return a=i>=1e4?1e3:i>=1e3?100:i>=100?10:i>=10?5:i>=1?1:i>=.1?.1:i>=.01?.01:i>=.001?.001:i>=1e-4?1e-4:i>=1e-5?1e-5:1e-6,{minRange:d(t,"lower",a),maxRange:d(e,"upper",a)}}function m(t,e,a){var i=0;if(t=String(t),!1!==a&&void 0!==a&&a.setFontSize&&a.measureText)return a.setFontSize(e),a.measureText(t).width;t=t.split("");for(var o=0;o<t.length;o++){var r=t[o];/[a-zA-Z]/.test(r)?i+=7:/[0-9]/.test(r)?i+=5.5:/\./.test(r)?i+=2.7:/-/.test(r)?i+=3.25:/:/.test(r)?i+=2.5:/[\u4e00-\u9fa5]/.test(r)?i+=10:/\(|\)/.test(r)?i+=3.73:/\s/.test(r)?i+=2.5:/%/.test(r)?i+=8:i+=10}return i*e/10}function b(t){return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data)}),[])}function w(t,e){for(var a=new Array(e),i=0;i<a.length;i++)a[i]=0;for(var o=0;o<t.length;o++)for(i=0;i<a.length;i++)a[i]+=t[o].data[i];return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data).concat(a)}),[])}function S(t,e,a){var i,o;return t.clientX?e.rotate?(o=e.height-t.clientX*e.pix,i=(t.pageY-a.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):(i=t.clientX*e.pix,o=(t.pageY-a.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):e.rotate?(o=e.height-t.x*e.pix,i=t.y*e.pix):(i=t.x*e.pix,o=t.y*e.pix),{x:i,y:o}}function C(t,e,a){var i=[],o=[],r=e.constructor.toString().indexOf("Array")>-1;if(r)for(var n=_(t),l=0;l<a.length;l++)o.push(n[a[l]]);else o=t;for(var s=0;s<o.length;s++){var c=o[s],d=-1;if(d=r?e[s]:e,null!==c.data[d]&&"undefined"!==typeof c.data[d]&&c.show){var h={};h.color=c.color,h.type=c.type,h.style=c.style,h.pointShape=c.pointShape,h.disableLegend=c.disableLegend,h.legendShape=c.legendShape,h.name=c.name,h.show=c.show,h.data=c.formatter?c.formatter(c.data[d]):c.data[d],i.push(h)}}return i}function A(t,e,a){var i=t.map((function(t){return m(t,e,a)}));return Math.max.apply(null,i)}function T(t){for(var e=2*Math.PI/t,a=[],i=0;i<t;i++)a.push(e*i);return a.map((function(t){return-1*t+Math.PI/2}))}function P(t,e,a,i,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},n=e.chartData.calPoints?e.chartData.calPoints:[],l={};if(i.length>0){for(var s=[],c=0;c<i.length;c++)s.push(n[i[c]]);l=s[0][a[0]]}else for(var d=0;d<n.length;d++)if(n[d][a]){l=n[d][a];break}var h=t.map((function(t){var i=null;return e.categories&&e.categories.length>0&&(i=o[a]),{text:r.formatter?r.formatter(t,i,a,e):t.name+": "+t.data,color:t.color,legendShape:"auto"==e.extra.tooltip.legendShape?t.legendShape:e.extra.tooltip.legendShape}})),u={x:Math.round(l.x),y:Math.round(l.y)};return{textList:h,offset:u}}function D(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},r=e.chartData.xAxisPoints[a]+e.chartData.eachSpacing/2,n=t.map((function(t){return{text:o.formatter?o.formatter(t,i[a],a,e):t.name+": "+t.data,color:t.color,disableLegend:!!t.disableLegend,legendShape:"auto"==e.extra.tooltip.legendShape?t.legendShape:e.extra.tooltip.legendShape}}));n=n.filter((function(t){if(!0!==t.disableLegend)return t}));var l={x:Math.round(r),y:0};return{textList:n,offset:l}}function k(t,e,a,i,o,r){var n=a.chartData.calPoints,l=r.color.upFill,s=r.color.downFill,c=[l,l,s,l],d=[];e.map((function(e){0==i?e.data[1]-e.data[0]<0?c[1]=s:c[1]=l:(e.data[0]<t[i-1][1]&&(c[0]=s),e.data[1]<e.data[0]&&(c[1]=s),e.data[2]>t[i-1][1]&&(c[2]=l),e.data[3]<t[i-1][1]&&(c[3]=s));var o={text:"开盘："+e.data[0],color:c[0],legendShape:"auto"==a.extra.tooltip.legendShape?e.legendShape:a.extra.tooltip.legendShape},r={text:"收盘："+e.data[1],color:c[1],legendShape:"auto"==a.extra.tooltip.legendShape?e.legendShape:a.extra.tooltip.legendShape},n={text:"最低："+e.data[2],color:c[2],legendShape:"auto"==a.extra.tooltip.legendShape?e.legendShape:a.extra.tooltip.legendShape},h={text:"最高："+e.data[3],color:c[3],legendShape:"auto"==a.extra.tooltip.legendShape?e.legendShape:a.extra.tooltip.legendShape};d.push(o,r,n,h)}));for(var h=[],u={x:0,y:0},p=0;p<n.length;p++){var f=n[p];"undefined"!==typeof f[i]&&null!==f[i]&&h.push(f[i])}return u.x=Math.round(h[0][0].x),{textList:d,offset:u}}function _(t){for(var e=[],a=0;a<t.length;a++)1==t[a].show&&e.push(t[a]);return e}function F(t,e,a){return t.x<=e.width-e.area[1]+10&&t.x>=e.area[3]-10&&t.y>=e.area[0]&&t.y<=e.height-e.area[2]}function M(t,e,a){return Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)<=Math.pow(a,2)}function L(t,e){var a=[],i=[];return t.forEach((function(t,o){e.connectNulls?null!==t&&i.push(t):null!==t?i.push(t):(i.length&&a.push(i),i=[])})),i.length&&a.push(i),a}function O(t,e,a,i,o){var r={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix},n=e.xAxis.fontSize*e.pix,l=t.map((function(t,a){var i=e.xAxis.formatter?e.xAxis.formatter(t,a,e):t;return m(String(i),n,o)})),s=Math.max.apply(this,l);if(1==e.xAxis.rotateLabel){r.angle=e.xAxis.rotateAngle*Math.PI/180;var c=e.xAxis.marginTop*e.pix*2+Math.abs(s*Math.sin(r.angle));c=c<n+e.xAxis.marginTop*e.pix*2?c+e.xAxis.marginTop*e.pix*2:c,r.xAxisHeight=c}return e.enableScroll&&e.xAxis.scrollShow&&(r.xAxisHeight+=6*e.pix),e.xAxis.disabled&&(r.xAxisHeight=0),r}function I(t,e,a,i){var o=l({},{type:""},e.extra.bar),n={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix};n.ranges=function(t,e,a,i){var o,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;o="stack"==i?w(t,e.categories.length):b(t);var l=[];o=o.filter((function(t){return"object"===(0,r.default)(t)&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t})),o.map((function(t){"object"===(0,r.default)(t)?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){l.push(t)})):l.push(t[0]):l.push(t.value):l.push(t)}));var s=0,c=0;if(l.length>0&&(s=Math.min.apply(this,l),c=Math.max.apply(this,l)),n>-1?("number"===typeof e.xAxis.data[n].min&&(s=Math.min(e.xAxis.data[n].min,s)),"number"===typeof e.xAxis.data[n].max&&(c=Math.max(e.xAxis.data[n].max,c))):("number"===typeof e.xAxis.min&&(s=Math.min(e.xAxis.min,s)),"number"===typeof e.xAxis.max&&(c=Math.max(e.xAxis.max,c))),s===c){var d=c||10;c+=d}for(var h=s,u=c,p=[],f=(u-h)/e.xAxis.splitNumber,x=0;x<=e.xAxis.splitNumber;x++)p.push(h+f*x);return p}(t,e,a,o.type),n.rangesFormat=n.ranges.map((function(t){return t=s.toFixed(t,2),t}));var c=n.ranges.map((function(t){return t=s.toFixed(t,2),t}));n=Object.assign(n,Y(c,e,a));n.eachSpacing,c.map((function(t){return m(t,e.xAxis.fontSize*e.pix,i)}));return!0===e.xAxis.disabled&&(n.xAxisHeight=0),n}function z(t,e,a,i,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=o.extra.radar||{};n.max=n.max||0;for(var l=Math.max(n.max,Math.max.apply(null,b(i))),s=[],c=function(o){var n=i[o],c={};c.color=n.color,c.legendShape=n.legendShape,c.pointShape=n.pointShape,c.data=[],n.data.forEach((function(i,o){var n={};n.angle=t[o],n.proportion=i/l,n.value=i,n.position=f(a*n.proportion*r*Math.cos(n.angle),a*n.proportion*r*Math.sin(n.angle),e),c.data.push(n)})),s.push(c)},d=0;d<i.length;d++)c(d);return s}function E(t,e){for(var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=0,o=0,r=0;r<t.length;r++){var n=t[r];n.data=null===n.data?0:n.data,i+=n.data}for(var l=0;l<t.length;l++){var s=t[l];s.data=null===s.data?0:s.data,s._proportion_=0===i?1/t.length*a:s.data/i*a,s._radius_=e}for(var c=0;c<t.length;c++){var d=t[c];d._start_=o,o+=2*d._proportion_*Math.PI}return t}function R(t,e,a,i){for(var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=0;r<t.length;r++)"funnel"==a.type?t[r].radius=t[r].data/t[0].data*e*o:t[r].radius=i*(t.length-r)/(i*t.length)*e*o,t[r]._proportion_=t[r].data/t[0].data;return t}function N(t,e,a,i){for(var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=0,n=0,l=[],s=0;s<t.length;s++){var c=t[s];c.data=null===c.data?0:c.data,r+=c.data,l.push(c.data)}for(var d=Math.min.apply(null,l),h=Math.max.apply(null,l),u=i-a,p=0;p<t.length;p++){var f=t[p];f.data=null===f.data?0:f.data,0===r?(f._proportion_=1/t.length*o,f._rose_proportion_=1/t.length*o):(f._proportion_=f.data/r*o,f._rose_proportion_="area"==e?1/t.length*o:f.data/r*o),f._radius_=a+u*((f.data-d)/(h-d))||i}for(var x=0;x<t.length;x++){var g=t[x];g._start_=n,n+=2*g._rose_proportion_*Math.PI}return t}function W(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==a&&(a=.999999);for(var i=0;i<t.length;i++){var o=t[i];o.data=null===o.data?0:o.data;var r=void 0;r="circle"==e.type?2:"ccw"==e.direction?e.startAngle<e.endAngle?2+e.startAngle-e.endAngle:e.startAngle-e.endAngle:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,o._proportion_=r*o.data*a+e.startAngle,"ccw"==e.direction&&(o._proportion_=e.startAngle-r*o.data*a),o._proportion_>=2&&(o._proportion_=o._proportion_%2)}return t}function B(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==a&&(a=.999999);for(var i=0;i<t.length;i++){var o=t[i];o.data=null===o.data?0:o.data;var r=void 0;r="circle"==e.type?2:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,o._proportion_=r*o.data*a+e.startAngle,o._proportion_>=2&&(o._proportion_=o._proportion_%2)}return t}function G(t,e,a){var i;i=a<e?2+a-e:e-a;for(var o=e,r=0;r<t.length;r++)t[r].value=null===t[r].value?0:t[r].value,t[r]._startAngle_=o,t[r]._endAngle_=i*t[r].value+e,t[r]._endAngle_>=2&&(t[r]._endAngle_=t[r]._endAngle_%2),o=t[r]._endAngle_;return t}function j(t,e,a){for(var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,o=0;o<t.length;o++){var r=t[o];if(r.data=null===r.data?0:r.data,"auto"==a.pointer.color){for(var n=0;n<e.length;n++)if(r.data<=e[n].value){r.color=e[n].color;break}}else r.color=a.pointer.color;var l=void 0;l=a.endAngle<a.startAngle?2+a.endAngle-a.startAngle:a.startAngle-a.endAngle,r._endAngle_=l*r.data+a.startAngle,r._oldAngle_=a.oldAngle,a.oldAngle<a.endAngle&&(r._oldAngle_+=2),r.data>=a.oldData?r._proportion_=(r._endAngle_-r._oldAngle_)*i+a.oldAngle:r._proportion_=r._oldAngle_-(r._oldAngle_-r._endAngle_)*i,r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return t}function $(t,e,a,i,o,r){return t.map((function(t){if(null===t)return null;var o=0,n=0;return"mix"==r.type?(o=r.extra.mix.column.seriesGap*r.pix||0,n=r.extra.mix.column.categoryGap*r.pix||0):(o=r.extra.column.seriesGap*r.pix||0,n=r.extra.column.categoryGap*r.pix||0),o=Math.min(o,e/a),n=Math.min(n,e/a),t.width=Math.ceil((e-2*n-o*(a-1))/a),r.extra.mix&&r.extra.mix.column.width&&+r.extra.mix.column.width>0&&(t.width=Math.min(t.width,+r.extra.mix.column.width*r.pix)),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),t.width<=0&&(t.width=1),t.x+=(i+.5-a/2)*(t.width+o),t}))}function H(t,e,a,i,o,r){return t.map((function(t){if(null===t)return null;var o=0,n=0;return o=r.extra.bar.seriesGap*r.pix||0,n=r.extra.bar.categoryGap*r.pix||0,o=Math.min(o,e/a),n=Math.min(n,e/a),t.width=Math.ceil((e-2*n-o*(a-1))/a),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(t.width=Math.min(t.width,+r.extra.bar.width*r.pix)),t.width<=0&&(t.width=1),t.y+=(i+.5-a/2)*(t.width+o),t}))}function X(t,e,a,i,o,r,n){var l=r.extra.column.categoryGap*r.pix||0;return t.map((function(t){return null===t?null:(t.width=e-2*l,r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),i>0&&(t.width-=n),t)}))}function q(t,e,a,i,o,r,n){var l=r.extra.column.categoryGap*r.pix||0;return t.map((function(t,a){return null===t?null:(t.width=Math.ceil(e-2*l),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),t.width<=0&&(t.width=1),t)}))}function J(t,e,a,i,o,r,n){var l=r.extra.bar.categoryGap*r.pix||0;return t.map((function(t,a){return null===t?null:(t.width=Math.ceil(e-2*l),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(t.width=Math.min(t.width,+r.extra.bar.width*r.pix)),t.width<=0&&(t.width=1),t)}))}function Y(t,e,a){var i=e.width-e.area[1]-e.area[3],o=e.enableScroll?Math.min(e.xAxis.itemCount,t.length):t.length;("line"==e.type||"area"==e.type||"scatter"==e.type||"bubble"==e.type||"bar"==e.type)&&o>1&&"justify"==e.xAxis.boundaryGap&&(o-=1);var r=0;"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),r=e.extra.mount.widthRatio-1,o+=r);var n=i/o,l=[],s=e.area[3],c=e.width-e.area[1];return t.forEach((function(t,e){l.push(s+r/2*n+e*n)})),"justify"!==e.xAxis.boundaryGap&&(!0===e.enableScroll?l.push(s+r*n+t.length*n):l.push(c)),{xAxisPoints:l,startX:s,endX:c,eachSpacing:n}}function U(t,e,a,i,o,r,n){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,s=[],c=r.height-r.area[0]-r.area[2];return t.forEach((function(t,n){if(null===t)s.push(null);else{var d=[];t.forEach((function(t,s){var h={};h.x=i[n]+Math.round(o/2);var u=t.value||t,p=c*(u-e)/(a-e);p*=l,h.y=r.height-Math.round(p)-r.area[2],d.push(h)})),s.push(d)}})),s}function Q(t,e,a,i,o,n,l){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,c="center";"line"!=n.type&&"area"!=n.type&&"scatter"!=n.type&&"bubble"!=n.type||(c=n.xAxis.boundaryGap);var d=[],h=n.height-n.area[0]-n.area[2],u=n.width-n.area[1]-n.area[3];return t.forEach((function(t,l){if(null===t)d.push(null);else{var p={};p.color=t.color,p.x=i[l];var f,x,g,v=t;if("object"===(0,r.default)(t)&&null!==t)if(t.constructor.toString().indexOf("Array")>-1)f=[].concat(n.chartData.xAxisData.ranges),x=f.shift(),g=f.pop(),v=t[1],p.x=n.area[3]+u*(t[0]-x)/(g-x),"bubble"==n.type&&(p.r=t[2],p.t=t[3]);else v=t.value;"center"==c&&(p.x+=o/2);var y=h*(v-e)/(a-e);y*=s,p.y=n.height-y-n.area[2],d.push(p)}})),d}function V(t,e,a,i,o,n,l,s,c){c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var d=n.xAxis.boundaryGap,h=[],u=n.height-n.area[0]-n.area[2],p=n.width-n.area[1]-n.area[3];return t.forEach((function(t,l){if(null===t)h.push(null);else{var f={};if(f.color=t.color,"vertical"==s.animation){f.x=i[l];var x,g,v,y=t;if("object"===(0,r.default)(t)&&null!==t)if(t.constructor.toString().indexOf("Array")>-1)x=[].concat(n.chartData.xAxisData.ranges),g=x.shift(),v=x.pop(),y=t[1],f.x=n.area[3]+p*(t[0]-g)/(v-g);else y=t.value;"center"==d&&(f.x+=o/2);var m=u*(y-e)/(a-e);m*=c,f.y=n.height-m-n.area[2],h.push(f)}else{f.x=i[0]+o*l*c;y=t;"center"==d&&(f.x+=o/2);m=u*(y-e)/(a-e);f.y=n.height-m-n.area[2],h.push(f)}}})),h}function Z(t,e,a,i,o,n,l,s,c){c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var d=[],h=n.height-n.area[0]-n.area[2],u=n.width-n.area[1]-n.area[3];return t.forEach((function(t,l){if(null===t)d.push(null);else{var s={};s.color=t.color,s.x=i[l];var p,f,x,g=t;if("object"===(0,r.default)(t)&&null!==t)if(t.constructor.toString().indexOf("Array")>-1)p=[].concat(n.chartData.xAxisData.ranges),f=p.shift(),x=p.pop(),g=t[1],s.x=n.area[3]+u*(t[0]-f)/(x-f);else g=t.value;s.x+=o/2;var v=h*(g*c-e)/(a-e);s.y=n.height-v-n.area[2],d.push(s)}})),d}function K(t,e,a,i,o,r,n,l){var s=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1,c=[],d=r.height-r.area[0]-r.area[2],h=(r.width,r.area[1],r.area[3],o*n.widthRatio);return t.forEach((function(t,n){if(null===t)c.push(null);else{var l={};l.color=t.color,l.x=i[n],l.x+=o/2;var u=t.data,p=d*(u*s-e)/(a-e);l.y=r.height-p-r.area[2],l.value=u,l.width=h,c.push(l)}})),c}function tt(t,e,a,i,o,n,l){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,c=[],d=(n.height,n.area[0],n.area[2],n.width-n.area[1]-n.area[3]);return t.forEach((function(t,o){if(null===t)c.push(null);else{var l={};l.color=t.color,l.y=i[o];var h=t;"object"===(0,r.default)(t)&&null!==t&&(h=t.value);var u=d*(h-e)/(a-e);u*=s,l.height=u,l.value=h,l.x=u+n.area[3],c.push(l)}})),c}function et(t,e,a,i,o,n,l,s,c){var d=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,h=[],u=n.height-n.area[0]-n.area[2];return t.forEach((function(t,l){if(null===t)h.push(null);else{var p={};if(p.color=t.color,p.x=i[l]+Math.round(o/2),s>0){for(var f=0,x=0;x<=s;x++)f+=c[x].data[l];var g=f-t,v=u*(f-e)/(a-e),y=u*(g-e)/(a-e)}else{f=t;"object"===(0,r.default)(t)&&null!==t&&(f=t.value);v=u*(f-e)/(a-e),y=0}var m=y;v*=d,m*=d,p.y=n.height-Math.round(v)-n.area[2],p.y0=n.height-Math.round(m)-n.area[2],h.push(p)}})),h}function at(t,e,a,i,o,n,l,s,c){var d=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,h=[],u=n.width-n.area[1]-n.area[3];return t.forEach((function(t,o){if(null===t)h.push(null);else{var l={};if(l.color=t.color,l.y=i[o],s>0){for(var p=0,f=0;f<=s;f++)p+=c[f].data[o];var x=p-t,g=u*(p-e)/(a-e),v=u*(x-e)/(a-e)}else{p=t;"object"===(0,r.default)(t)&&null!==t&&(p=t.value);g=u*(p-e)/(a-e),v=0}var y=v;g*=d,y*=d,l.height=g-y,l.x=n.area[3]+g,l.x0=n.area[3]+y,h.push(l)}})),h}function it(t,e,a,i,o){var n;n="stack"==i?w(t,e.categories.length):b(t);var l=[];n=n.filter((function(t){return"object"===(0,r.default)(t)&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t})),n.map((function(t){"object"===(0,r.default)(t)?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){l.push(t)})):l.push(t[1]):l.push(t.value):l.push(t)}));var s=o.min||0,c=o.max||0;l.length>0&&(s=Math.min.apply(this,l),c=Math.max.apply(this,l)),s===c&&(0==c?c=10:s=0);for(var d=y(s,c),h=void 0===o.min||null===o.min?d.minRange:o.min,u=void 0===o.max||null===o.max?d.maxRange:o.max,p=(u-h)/e.yAxis.splitNumber,f=[],x=0;x<=e.yAxis.splitNumber;x++)f.push(h+p*x);return f.reverse()}function ot(t,e,a,i){var o=l({},{type:""},e.extra.column),r=e.yAxis.data.length,n=new Array(r);if(r>0){for(var c=0;c<r;c++){n[c]=[];for(var d=0;d<t.length;d++)t[d].index==c&&n[c].push(t[d])}for(var h=new Array(r),u=new Array(r),p=new Array(r),f=function(t){var r=e.yAxis.data[t];1==e.yAxis.disabled&&(r.disabled=!0),"categories"===r.type?(r.formatter||(r.formatter=function(t,e,a){return t+(r.unit||"")}),r.categories=r.categories||e.categories,h[t]=r.categories):(r.formatter||(r.formatter=function(t,e,a){return s.toFixed(t,r.tofix||0)+(r.unit||"")}),h[t]=it(n[t],e,a,o.type,r,t));var l=r.fontSize*e.pix||a.fontSize;p[t]={position:r.position?r.position:"left",width:0},u[t]=h[t].map((function(a,o){return a=r.formatter(a,o,e),p[t].width=Math.max(p[t].width,m(a,l,i)+5),a}));var c=r.calibration?4*e.pix:0;p[t].width+=c+3*e.pix,!0===r.disabled&&(p[t].width=0)},x=0;x<r;x++)f(x)}else{h=new Array(1),u=new Array(1),p=new Array(1);"bar"===e.type?(h[0]=e.categories,e.yAxis.formatter||(e.yAxis.formatter=function(t,e,a){return t+(a.yAxis.unit||"")})):(e.yAxis.formatter||(e.yAxis.formatter=function(t,e,a){return t.toFixed(a.yAxis.tofix)+(a.yAxis.unit||"")}),h[0]=it(t,e,a,o.type,{})),p[0]={position:"left",width:0};var g=e.yAxis.fontSize*e.pix||a.fontSize;u[0]=h[0].map((function(t,a){return t=e.yAxis.formatter(t,a,e),p[0].width=Math.max(p[0].width,m(t,g,i)+5),t})),p[0].width+=3*e.pix,!0===e.yAxis.disabled?(p[0]={position:"left",width:0},e.yAxis.data[0]={disabled:!0}):(e.yAxis.data[0]={disabled:!1,position:"left",max:e.yAxis.max,min:e.yAxis.min,formatter:e.yAxis.formatter},"bar"===e.type&&(e.yAxis.data[0].categories=e.categories,e.yAxis.data[0].type="categories"))}return{rangesFormat:u,ranges:h,yAxisWidth:p}}function rt(t,e){!0!==e.rotateLock?(t.translate(e.height,0),t.rotate(90*Math.PI/180)):!0!==e._rotate_&&(t.translate(e.height,0),t.rotate(90*Math.PI/180),e._rotate_=!0)}function nt(t,e,a,i,o){if(i.beginPath(),"hollow"==o.dataPointShapeType?(i.setStrokeStyle(e),i.setFillStyle(o.background),i.setLineWidth(2*o.pix)):(i.setStrokeStyle("#ffffff"),i.setFillStyle(e),i.setLineWidth(1*o.pix)),"diamond"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y),i.lineTo(t.x,t.y****),i.lineTo(t.x****,t.y),i.lineTo(t.x,t.y-4.5))}));else if("circle"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x*****o.pix,t.y),i.arc(t.x,t.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x-3.5,t.y-3.5),i.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y****),i.lineTo(t.x****,t.y****),i.lineTo(t.x,t.y-4.5))}));else if("none"===a)return;i.closePath(),i.fill(),i.stroke()}function lt(t,e,a,i,o,r,n){if(o.tooltip&&!(o.tooltip.group.length>0&&0==o.tooltip.group.includes(n))){var l="number"===typeof o.tooltip.index?o.tooltip.index:o.tooltip.index[o.tooltip.group.indexOf(n)];if(i.beginPath(),"hollow"==r.activeType?(i.setStrokeStyle(e),i.setFillStyle(o.background),i.setLineWidth(2*o.pix)):(i.setStrokeStyle("#ffffff"),i.setFillStyle(e),i.setLineWidth(1*o.pix)),"diamond"===a)t.forEach((function(t,e){null!==t&&l==e&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y),i.lineTo(t.x,t.y****),i.lineTo(t.x****,t.y),i.lineTo(t.x,t.y-4.5))}));else if("circle"===a)t.forEach((function(t,e){null!==t&&l==e&&(i.moveTo(t.x*****o.pix,t.y),i.arc(t.x,t.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===a)t.forEach((function(t,e){null!==t&&l==e&&(i.moveTo(t.x-3.5,t.y-3.5),i.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===a)t.forEach((function(t,e){null!==t&&l==e&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y****),i.lineTo(t.x****,t.y****),i.lineTo(t.x,t.y-4.5))}));else if("none"===a)return;i.closePath(),i.fill(),i.stroke()}}function st(t,e,a,i){var o=t.title.fontSize||e.titleFontSize,r=t.subtitle.fontSize||e.subtitleFontSize,n=t.title.name||"",l=t.subtitle.name||"",s=t.title.color||t.fontColor,c=t.subtitle.color||t.fontColor,d=n?o:0,h=l?r:0;if(l){var u=m(l,r*t.pix,a),p=i.x-u/2+(t.subtitle.offsetX||0)*t.pix,f=i.y+r*t.pix/2+(t.subtitle.offsetY||0)*t.pix;n&&(f+=(d*t.pix+5)/2),a.beginPath(),a.setFontSize(r*t.pix),a.setFillStyle(c),a.fillText(l,p,f),a.closePath(),a.stroke()}if(n){var x=m(n,o*t.pix,a),g=i.x-x/2+(t.title.offsetX||0),v=i.y+o*t.pix/2+(t.title.offsetY||0)*t.pix;l&&(v-=(h*t.pix+5)/2),a.beginPath(),a.setFontSize(o*t.pix),a.setFillStyle(s),a.fillText(n,g,v),a.closePath(),a.stroke()}}function ct(t,e,a,i,o){var n=e.data,l=e.textOffset?e.textOffset:0;t.forEach((function(t,s){if(null!==t){i.beginPath();var c=e.textSize?e.textSize*o.pix:a.fontSize;i.setFontSize(c),i.setFillStyle(e.textColor||o.fontColor);var d=n[s];"object"===(0,r.default)(n[s])&&null!==n[s]&&(d=n[s].constructor.toString().indexOf("Array")>-1?n[s][1]:n[s].value);var h=e.formatter?e.formatter(d,s,e,o):d;i.setTextAlign("center"),i.fillText(String(h),t.x,t.y-4+l*o.pix),i.closePath(),i.stroke(),i.setTextAlign("left")}}))}function dt(t,e,a,i,o){var n=e.data,l=e.textOffset?e.textOffset:0,s=o.extra.column.labelPosition;t.forEach((function(t,c){if(null!==t){i.beginPath();var d=e.textSize?e.textSize*o.pix:a.fontSize;i.setFontSize(d),i.setFillStyle(e.textColor||o.fontColor);var h=n[c];"object"===(0,r.default)(n[c])&&null!==n[c]&&(h=n[c].constructor.toString().indexOf("Array")>-1?n[c][1]:n[c].value);var u=e.formatter?e.formatter(h,c,e,o):h;i.setTextAlign("center");var p=t.y-4*o.pix+l*o.pix;t.y>e.zeroPoints&&(p=t.y+l*o.pix+d),"insideTop"==s&&(p=t.y+d+l*o.pix,t.y>e.zeroPoints&&(p=t.y-l*o.pix-4*o.pix)),"center"==s&&(p=t.y+l*o.pix+(o.height-o.area[2]-t.y+d)/2,e.zeroPoints<o.height-o.area[2]&&(p=t.y+l*o.pix+(e.zeroPoints-t.y+d)/2),t.y>e.zeroPoints&&(p=t.y-l*o.pix-(t.y-e.zeroPoints-d)/2),"stack"==o.extra.column.type&&(p=t.y+l*o.pix+(t.y0-t.y+d)/2)),"bottom"==s&&(p=o.height-o.area[2]+l*o.pix-4*o.pix,e.zeroPoints<o.height-o.area[2]&&(p=e.zeroPoints+l*o.pix-4*o.pix),t.y>e.zeroPoints&&(p=e.zeroPoints-l*o.pix+d+2*o.pix),"stack"==o.extra.column.type&&(p=t.y0+l*o.pix-4*o.pix)),i.fillText(String(u),t.x,p),i.closePath(),i.stroke(),i.setTextAlign("left")}}))}function ht(t,e,a,i,o,r){e.data;var n=e.textOffset?e.textOffset:0;o.extra.mount.labelPosition;t.forEach((function(t,l){if(null!==t){i.beginPath();var s=e[l].textSize?e[l].textSize*o.pix:a.fontSize;i.setFontSize(s),i.setFillStyle(e[l].textColor||o.fontColor);var c=t.value,d=e[l].formatter?e[l].formatter(c,l,e,o):c;i.setTextAlign("center");var h=t.y-4*o.pix+n*o.pix;t.y>r&&(h=t.y+n*o.pix+s),i.fillText(String(d),t.x,h),i.closePath(),i.stroke(),i.setTextAlign("left")}}))}function ut(t,e,a,i,o){var n=e.data;e.textOffset&&e.textOffset;t.forEach((function(t,l){if(null!==t){i.beginPath();var s=e.textSize?e.textSize*o.pix:a.fontSize;i.setFontSize(s),i.setFillStyle(e.textColor||o.fontColor);var c=n[l];"object"===(0,r.default)(n[l])&&null!==n[l]&&(c=n[l].value);var d=e.formatter?e.formatter(c,l,e,o):c;i.setTextAlign("left"),i.fillText(String(d),t.x+4*o.pix,t.y+s/2-3),i.closePath(),i.stroke()}}))}function pt(t,e,a,i,o,r){var n;e-=t.width/2+t.labelOffset*i.pix,e=e<10?10:e,n=t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle;for(var l=n/t.splitLine.splitNumber,s=t.endNumber-t.startNumber,c=s/t.splitLine.splitNumber,d=t.startAngle,h=t.startNumber,u=0;u<t.splitLine.splitNumber+1;u++){var p={x:e*Math.cos(d*Math.PI),y:e*Math.sin(d*Math.PI)},f=t.formatter?t.formatter(h,u,i):h;p.x+=a.x-m(f,o.fontSize,r)/2,p.y+=a.y;var x=p.x,g=p.y;r.beginPath(),r.setFontSize(o.fontSize),r.setFillStyle(t.labelColor||i.fontColor),r.fillText(f,x,g+o.fontSize/2),r.closePath(),r.stroke(),d+=l,d>=2&&(d%=2),h+=c}}function ft(t,e,a,i,o,r){var n=i.extra.radar||{};t.forEach((function(t,l){if(!0===n.labelPointShow&&""!==i.categories[l]){var c={x:e*Math.cos(t),y:e*Math.sin(t)},d=f(c.x,c.y,a);r.setFillStyle(n.labelPointColor),r.beginPath(),r.arc(d.x,d.y,n.labelPointRadius*i.pix,0,2*Math.PI,!1),r.closePath(),r.fill()}if(!0===n.labelShow){var h={x:(e+o.radarLabelTextMargin*i.pix)*Math.cos(t),y:(e+o.radarLabelTextMargin*i.pix)*Math.sin(t)},u=f(h.x,h.y,a),p=u.x,x=u.y;s.approximatelyEqual(h.x,0)?p-=m(i.categories[l]||"",o.fontSize,r)/2:h.x<0&&(p-=m(i.categories[l]||"",o.fontSize,r)),r.beginPath(),r.setFontSize(o.fontSize),r.setFillStyle(n.labelColor||i.fontColor),r.fillText(i.categories[l]||"",p,x+o.fontSize/2),r.closePath(),r.stroke()}}))}function xt(t,e,a,i,o,r){for(var n=a.pieChartLinePadding,l=[],c=null,d=t.map((function(a,i){var o=a.formatter?a.formatter(a,i,t,e):s.toFixed(100*a._proportion_.toFixed(4))+"%";o=a.labelText?a.labelText:o;var r=2*Math.PI-(a._start_+2*Math.PI*a._proportion_/2);a._rose_proportion_&&(r=2*Math.PI-(a._start_+2*Math.PI*a._rose_proportion_/2));var n=a.color,l=a._radius_;return{arc:r,text:o,color:n,radius:l,textColor:a.textColor,textSize:a.textSize,labelShow:a.labelShow}})),h=0;h<d.length;h++){var u=d[h],p=Math.cos(u.arc)*(u.radius+n),g=Math.sin(u.arc)*(u.radius+n),v=Math.cos(u.arc)*u.radius,y=Math.sin(u.arc)*u.radius,b=p>=0?p+a.pieChartTextPadding:p-a.pieChartTextPadding,w=g,S=m(u.text,u.textSize*e.pix||a.fontSize,i),C=w;c&&s.isSameXCoordinateArea(c.start,{x:b})&&(C=b>0?Math.min(w,c.start.y):p<0||w>0?Math.max(w,c.start.y):Math.min(w,c.start.y)),b<0&&(b-=S);var A={lineStart:{x:v,y:y},lineEnd:{x:p,y:g},start:{x:b,y:C},width:S,height:a.fontSize,text:u.text,color:u.color,textColor:u.textColor,textSize:u.textSize};c=x(A,c),l.push(c)}for(var T=0;T<l.length;T++)if(!1!==d[T].labelShow){var P=l[T],D=f(P.lineStart.x,P.lineStart.y,r),k=f(P.lineEnd.x,P.lineEnd.y,r),_=f(P.start.x,P.start.y,r);i.setLineWidth(1*e.pix),i.setFontSize(P.textSize*e.pix||a.fontSize),i.beginPath(),i.setStrokeStyle(P.color),i.setFillStyle(P.color),i.moveTo(D.x,D.y);var F=P.start.x<0?_.x+P.width:_.x,M=P.start.x<0?_.x-5:_.x+5;i.quadraticCurveTo(k.x,k.y,F,_.y),i.moveTo(D.x,D.y),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(_.x+P.width,_.y),i.arc(F,_.y,2*e.pix,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(P.textSize*e.pix||a.fontSize),i.setFillStyle(P.textColor||e.fontColor),i.fillText(P.text,M,_.y+3),i.closePath(),i.stroke(),i.closePath()}}function gt(t,e,a){for(var i=l({},{type:"solid",dashLength:4,data:[]},t.extra.markLine),o=t.area[3],r=t.width-t.area[1],n=function(t,e){for(var a,i,o=e.height-e.area[0]-e.area[2],r=0;r<t.length;r++){t[r].yAxisIndex=t[r].yAxisIndex?t[r].yAxisIndex:0;var n=[].concat(e.chartData.yAxisData.ranges[t[r].yAxisIndex]);a=n.pop(),i=n.shift();var l=o*(t[r].value-a)/(i-a);t[r].y=e.height-Math.round(l)-e.area[2]}return t}(i.data,t),s=0;s<n.length;s++){var d=l({},{lineColor:"#DE4A42",showLabel:!1,labelFontSize:13,labelPadding:6,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,labelAlign:"left",labelOffsetX:0,labelOffsetY:0},n[s]);if("dash"==i.type&&a.setLineDash([i.dashLength,i.dashLength]),a.setStrokeStyle(d.lineColor),a.setLineWidth(1*t.pix),a.beginPath(),a.moveTo(o,d.y),a.lineTo(r,d.y),a.stroke(),a.setLineDash([]),d.showLabel){var h=d.labelFontSize*t.pix,u=d.labelText?d.labelText:d.value;a.setFontSize(h);var p=m(u,h,a),f=p+d.labelPadding*t.pix*2,x="left"==d.labelAlign?t.area[3]-f:t.width-t.area[1];x+=d.labelOffsetX;var g=d.y-.5*h-d.labelPadding*t.pix;g+=d.labelOffsetY;var v=x+d.labelPadding*t.pix;d.y;a.setFillStyle(c(d.labelBgColor,d.labelBgOpacity)),a.setStrokeStyle(d.labelBgColor),a.setLineWidth(1*t.pix),a.beginPath(),a.rect(x,g,f,h+2*d.labelPadding*t.pix),a.closePath(),a.stroke(),a.fill(),a.setFontSize(h),a.setTextAlign("left"),a.setFillStyle(d.labelFontColor),a.fillText(String(u),v,g+h+d.labelPadding*t.pix/2),a.stroke(),a.setTextAlign("left")}}}function vt(t,e,a,i,o){var r=l({},{gridType:"solid",dashLength:4},t.extra.tooltip),n=t.area[3],s=t.width-t.area[1];if("dash"==r.gridType&&a.setLineDash([r.dashLength,r.dashLength]),a.setStrokeStyle(r.gridColor||"#cccccc"),a.setLineWidth(1*t.pix),a.beginPath(),a.moveTo(n,t.tooltip.offset.y),a.lineTo(s,t.tooltip.offset.y),a.stroke(),a.setLineDash([]),r.yAxisLabel)for(var d=r.boxPadding*t.pix,h=function(t,e,a,i,o){for(var r=[].concat(a.chartData.yAxisData.ranges),n=a.height-a.area[0]-a.area[2],l=a.area[0],s=[],c=0;c<r.length;c++){var d=Math.max.apply(this,r[c]),h=Math.min.apply(this,r[c]),u=d-(d-h)*(t-l)/n;u=a.yAxis.data&&a.yAxis.data[c].formatter?a.yAxis.data[c].formatter(u,c,a):u.toFixed(0),s.push(String(u))}return s}(t.tooltip.offset.y,t.series,t),u=t.chartData.yAxisData.yAxisWidth,p=t.area[3],f=t.width-t.area[1],x=0;x<h.length;x++){a.setFontSize(r.fontSize*t.pix);var g,v=m(h[x],r.fontSize*t.pix,a),y=void 0,b=void 0;"left"==u[x].position?(y=p-(v+2*d)-2*t.pix,b=Math.max(y,y+v+2*d)):(y=f+2*t.pix,b=Math.max(y+u[x].width,y+v+2*d)),g=b-y;var w=y+(g-v)/2,S=t.tooltip.offset.y;a.beginPath(),a.setFillStyle(c(r.labelBgColor||e.toolTipBackground,r.labelBgOpacity||e.toolTipOpacity)),a.setStrokeStyle(r.labelBgColor||e.toolTipBackground),a.setLineWidth(1*t.pix),a.rect(y,S-.5*e.fontSize-d,g,e.fontSize+2*d),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(e.fontSize),a.setFillStyle(r.labelFontColor||t.fontColor),a.fillText(h[x],w,S+.5*e.fontSize),a.closePath(),a.stroke(),"left"==u[x].position?p-=u[x].width+t.yAxis.padding*t.pix:f+=u[x].width+t.yAxis.padding*t.pix}}function yt(t,e,a,i,o){var r=l({},{activeBgColor:"#000000",activeBgOpacity:.08,activeWidth:o},e.extra.column);r.activeWidth=r.activeWidth>o?o:r.activeWidth;var n=e.area[0],s=e.height-e.area[2];i.beginPath(),i.setFillStyle(c(r.activeBgColor,r.activeBgOpacity)),i.rect(t-r.activeWidth/2,n,r.activeWidth,s-n),i.closePath(),i.fill(),i.setFillStyle("#FFFFFF")}function mt(t,e,a,i,o){var r=l({},{activeBgColor:"#000000",activeBgOpacity:.08},e.extra.bar),n=e.area[3],s=e.width-e.area[1];i.beginPath(),i.setFillStyle(c(r.activeBgColor,r.activeBgOpacity)),i.rect(n,t-o/2,s-n,o),i.closePath(),i.fill(),i.setFillStyle("#FFFFFF")}function bt(t,e,a,i,o,r,n){var s=l({},{showBox:!0,showArrow:!0,showCategory:!1,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,boxPadding:3,fontColor:"#FFFFFF",fontSize:13,lineHeight:20,legendShow:!0,legendShape:"auto",splitLine:!0},a.extra.tooltip);1==s.showCategory&&a.categories&&t.unshift({text:a.categories[a.tooltip.index],color:null});var d=s.fontSize*a.pix,h=s.lineHeight*a.pix,u=s.boxPadding*a.pix,p=d,f=5*a.pix;0==s.legendShow&&(p=0,f=0);var x=s.showArrow?8*a.pix:0,g=!1;"line"!=a.type&&"mount"!=a.type&&"area"!=a.type&&"candle"!=a.type&&"mix"!=a.type||1==s.splitLine&&function(t,e,a,i){var o=e.extra.tooltip||{};o.gridType=void 0==o.gridType?"solid":o.gridType,o.dashLength=void 0==o.dashLength?4:o.dashLength;var r=e.area[0],n=e.height-e.area[2];if("dash"==o.gridType&&i.setLineDash([o.dashLength,o.dashLength]),i.setStrokeStyle(o.gridColor||"#cccccc"),i.setLineWidth(1*e.pix),i.beginPath(),i.moveTo(t,r),i.lineTo(t,n),i.stroke(),i.setLineDash([]),o.xAxisLabel){var l=e.categories[e.tooltip.index];i.setFontSize(a.fontSize);var s=m(l,a.fontSize,i),d=t-.5*s,h=n+2*e.pix;i.beginPath(),i.setFillStyle(c(o.labelBgColor||a.toolTipBackground,o.labelBgOpacity||a.toolTipOpacity)),i.setStrokeStyle(o.labelBgColor||a.toolTipBackground),i.setLineWidth(1*e.pix),i.rect(d-o.boxPadding*e.pix,h,s+2*o.boxPadding*e.pix,a.fontSize+2*o.boxPadding*e.pix),i.closePath(),i.stroke(),i.fill(),i.beginPath(),i.setFontSize(a.fontSize),i.setFillStyle(o.labelFontColor||e.fontColor),i.fillText(String(l),d,h+o.boxPadding*e.pix+a.fontSize),i.closePath(),i.stroke()}}(a.tooltip.offset.x,a,i,o),e=l({x:0,y:0},e),e.y-=8*a.pix;var v=t.map((function(t){return m(t.text,d,o)})),y=p+f+4*u+Math.max.apply(null,v),b=2*u+t.length*h;if(0!=s.showBox){e.x-Math.abs(a._scrollDistance_||0)+x+y>a.width&&(g=!0),b+e.y>a.height&&(e.y=a.height-b),o.beginPath(),o.setFillStyle(c(s.bgColor,s.bgOpacity)),o.setLineWidth(s.borderWidth*a.pix),o.setStrokeStyle(c(s.borderColor,s.borderOpacity));var w=s.borderRadius;g?(y+x>a.width&&(e.x=a.width+Math.abs(a._scrollDistance_||0)+x+(y-a.width)),y>e.x&&(e.x=a.width+Math.abs(a._scrollDistance_||0)+x+(y-a.width)),s.showArrow&&(o.moveTo(e.x,e.y+10*a.pix),o.lineTo(e.x-x,e.y+10*a.pix+5*a.pix)),o.arc(e.x-x-w,e.y+b-w,w,0,Math.PI/2,!1),o.arc(e.x-x-Math.round(y)+w,e.y+b-w,w,Math.PI/2,Math.PI,!1),o.arc(e.x-x-Math.round(y)+w,e.y+w,w,-Math.PI,-Math.PI/2,!1),o.arc(e.x-x-w,e.y+w,w,-Math.PI/2,0,!1),s.showArrow&&(o.lineTo(e.x-x,e.y+10*a.pix-5*a.pix),o.lineTo(e.x,e.y+10*a.pix))):(s.showArrow&&(o.moveTo(e.x,e.y+10*a.pix),o.lineTo(e.x+x,e.y+10*a.pix-5*a.pix)),o.arc(e.x+x+w,e.y+w,w,-Math.PI,-Math.PI/2,!1),o.arc(e.x+x+Math.round(y)-w,e.y+w,w,-Math.PI/2,0,!1),o.arc(e.x+x+Math.round(y)-w,e.y+b-w,w,0,Math.PI/2,!1),o.arc(e.x+x+w,e.y+b-w,w,Math.PI/2,Math.PI,!1),s.showArrow&&(o.lineTo(e.x+x,e.y+10*a.pix+5*a.pix),o.lineTo(e.x,e.y+10*a.pix))),o.closePath(),o.fill(),s.borderWidth>0&&o.stroke(),s.legendShow&&t.forEach((function(t,i){if(null!==t.color){o.beginPath(),o.setFillStyle(t.color);var r=e.x+x+2*u,n=e.y+(h-d)/2+h*i+u+1;switch(g&&(r=e.x-y-x+2*u),t.legendShape){case"line":o.moveTo(r,n+.5*p-2*a.pix),o.fillRect(r,n+.5*p-2*a.pix,p,4*a.pix);break;case"triangle":o.moveTo(r+7.5*a.pix,n+.5*p-5*a.pix),o.lineTo(r*****a.pix,n+.5*p+5*a.pix),o.lineTo(r+12.5*a.pix,n+.5*p+5*a.pix),o.lineTo(r+7.5*a.pix,n+.5*p-5*a.pix);break;case"diamond":o.moveTo(r+7.5*a.pix,n+.5*p-5*a.pix),o.lineTo(r*****a.pix,n+.5*p),o.lineTo(r+7.5*a.pix,n+.5*p+5*a.pix),o.lineTo(r+12.5*a.pix,n+.5*p),o.lineTo(r+7.5*a.pix,n+.5*p-5*a.pix);break;case"circle":o.moveTo(r+7.5*a.pix,n+.5*p),o.arc(r+7.5*a.pix,n+.5*p,5*a.pix,0,2*Math.PI);break;case"rect":o.moveTo(r,n+.5*p-5*a.pix),o.fillRect(r,n+.5*p-5*a.pix,15*a.pix,10*a.pix);break;case"square":o.moveTo(r+2*a.pix,n+.5*p-5*a.pix),o.fillRect(r+2*a.pix,n+.5*p-5*a.pix,10*a.pix,10*a.pix);break;default:o.moveTo(r,n+.5*p-5*a.pix),o.fillRect(r,n+.5*p-5*a.pix,15*a.pix,10*a.pix)}o.closePath(),o.fill()}})),t.forEach((function(t,a){var i=e.x+x+2*u+p+f;g&&(i=e.x-y-x+2*u+p+f);var r=e.y+h*a+(h-d)/2-1+u+d;o.beginPath(),o.setFontSize(d),o.setTextBaseline("normal"),o.setFillStyle(s.fontColor),o.fillText(t.text,i,r),o.closePath(),o.stroke()}))}}function wt(t,e,a,i,o,r){var n=t.extra.tooltip||{};n.horizentalLine&&t.tooltip&&1===i&&("line"==t.type||"area"==t.type||"column"==t.type||"mount"==t.type||"candle"==t.type||"mix"==t.type)&&vt(t,e,a),a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&a.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===i&&bt(t.tooltip.textList,t.tooltip.offset,t,e,a),a.restore()}function St(t,e,a,i){var o=e.chartData.xAxisData,r=o.xAxisPoints,n=o.startX,l=o.endX,s=o.eachSpacing,c="center";"bar"!=e.type&&"line"!=e.type&&"area"!=e.type&&"scatter"!=e.type&&"bubble"!=e.type||(c=e.xAxis.boundaryGap);var d=e.height-e.area[2],h=e.area[0];if(e.enableScroll&&e.xAxis.scrollShow){var u=e.height-e.area[2]+a.xAxisHeight,p=l-n,f=s*(r.length-1);"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),f+=(e.extra.mount.widthRatio-1)*s);var x=p*p/f,g=0;e._scrollDistance_&&(g=-e._scrollDistance_*p/f),i.beginPath(),i.setLineCap("round"),i.setLineWidth(6*e.pix),i.setStrokeStyle(e.xAxis.scrollBackgroundColor||"#EFEBEF"),i.moveTo(n,u),i.lineTo(l,u),i.stroke(),i.closePath(),i.beginPath(),i.setLineCap("round"),i.setLineWidth(6*e.pix),i.setStrokeStyle(e.xAxis.scrollColor||"#A6A6A6"),i.moveTo(n+g,u),i.lineTo(n+g+x,u),i.stroke(),i.closePath(),i.setLineCap("butt")}if(i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&i.translate(e._scrollDistance_,0),!0===e.xAxis.calibration&&(i.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),i.setLineCap("butt"),i.setLineWidth(1*e.pix),r.forEach((function(t,a){a>0&&(i.beginPath(),i.moveTo(t-s/2,d),i.lineTo(t-s/2,d+3*e.pix),i.closePath(),i.stroke())}))),!0!==e.xAxis.disableGrid&&(i.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),i.setLineCap("butt"),i.setLineWidth(1*e.pix),"dash"==e.xAxis.gridType&&i.setLineDash([e.xAxis.dashLength*e.pix,e.xAxis.dashLength*e.pix]),e.xAxis.gridEval=e.xAxis.gridEval||1,r.forEach((function(t,a){a%e.xAxis.gridEval==0&&(i.beginPath(),i.moveTo(t,d),i.lineTo(t,h),i.stroke())})),i.setLineDash([])),!0!==e.xAxis.disabled){var v=t.length;e.xAxis.labelCount&&(v=e.xAxis.itemCount?Math.ceil(t.length/e.xAxis.itemCount*e.xAxis.labelCount):e.xAxis.labelCount,v-=1);for(var y=Math.ceil(t.length/v),b=[],w=t.length,S=0;S<w;S++)S%y!==0?b.push(""):b.push(t[S]);b[w-1]=t[w-1];var C=e.xAxis.fontSize*e.pix||a.fontSize;0===a._xAxisTextAngle_?b.forEach((function(t,a){var o=e.xAxis.formatter?e.xAxis.formatter(t,a,e):t,n=-m(String(o),C,i)/2;"center"==c&&(n+=s/2);e.xAxis.scrollShow&&e.pix;var l=e._scrollDistance_||0,h="center"==c?r[a]+s/2:r[a];h-Math.abs(l)>=e.area[3]-1&&h-Math.abs(l)<=e.width-e.area[1]+1&&(i.beginPath(),i.setFontSize(C),i.setFillStyle(e.xAxis.fontColor||e.fontColor),i.fillText(String(o),r[a]+n,d+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.fontSize)*e.pix/2+e.xAxis.fontSize*e.pix),i.closePath(),i.stroke())})):b.forEach((function(t,o){var n=e.xAxis.formatter?e.xAxis.formatter(t):t,l=e._scrollDistance_||0,h="center"==c?r[o]+s/2:r[o];if(h-Math.abs(l)>=e.area[3]-1&&h-Math.abs(l)<=e.width-e.area[1]+1){i.save(),i.beginPath(),i.setFontSize(C),i.setFillStyle(e.xAxis.fontColor||e.fontColor);var u=m(String(n),C,i),p=r[o];"center"==c&&(p=r[o]+s/2);e.xAxis.scrollShow&&6*e.pix;var f=d+e.xAxis.marginTop*e.pix+C-C*Math.abs(Math.sin(a._xAxisTextAngle_));e.xAxis.rotateAngle<0?(p-=C/2,u=0):(p+=C/2,u=-u),i.translate(p,f),i.rotate(-1*a._xAxisTextAngle_),i.fillText(String(n),u,0),i.closePath(),i.stroke(),i.restore()}}))}i.restore(),e.xAxis.title&&(i.beginPath(),i.setFontSize(e.xAxis.titleFontSize*e.pix),i.setFillStyle(e.xAxis.titleFontColor),i.fillText(String(e.xAxis.title),e.width-e.area[1]+e.xAxis.titleOffsetX*e.pix,e.height-e.area[2]+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.titleFontSize)*e.pix/2+(e.xAxis.titleFontSize+e.xAxis.titleOffsetY)*e.pix),i.closePath(),i.stroke()),e.xAxis.axisLine&&(i.beginPath(),i.setStrokeStyle(e.xAxis.axisLineColor),i.setLineWidth(1*e.pix),i.moveTo(n,e.height-e.area[2]),i.lineTo(l,e.height-e.area[2]),i.stroke())}function Ct(t,e,a,i){if(!0!==e.yAxis.disableGrid){var o=e.height-e.area[0]-e.area[2],r=o/e.yAxis.splitNumber,n=e.area[3],l=e.chartData.xAxisData.xAxisPoints,s=e.chartData.xAxisData.eachSpacing,c=s*(l.length-1);"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),c+=(e.extra.mount.widthRatio-1)*s);var d=n+c,h=[],u=1;!1===e.xAxis.axisLine&&(u=0);for(var p=u;p<e.yAxis.splitNumber+1;p++)h.push(e.height-e.area[2]-r*p);i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&i.translate(e._scrollDistance_,0),"dash"==e.yAxis.gridType&&i.setLineDash([e.yAxis.dashLength*e.pix,e.yAxis.dashLength*e.pix]),i.setStrokeStyle(e.yAxis.gridColor),i.setLineWidth(1*e.pix),h.forEach((function(t,e){i.beginPath(),i.moveTo(n,t),i.lineTo(d,t),i.stroke()})),i.setLineDash([]),i.restore()}}function At(t,e,a,i){if(!0!==e.yAxis.disabled){var o=e.height-e.area[0]-e.area[2],r=o/e.yAxis.splitNumber,n=e.area[3],l=e.width-e.area[1],s=e.height-e.area[2];i.beginPath(),i.setFillStyle(e.background),1==e.enableScroll&&e.xAxis.scrollPosition&&"left"!==e.xAxis.scrollPosition&&i.fillRect(0,0,n,s+2*e.pix),1==e.enableScroll&&e.xAxis.scrollPosition&&"right"!==e.xAxis.scrollPosition&&i.fillRect(l,0,e.width,s+2*e.pix),i.closePath(),i.stroke();var c=e.area[3],d=e.width-e.area[1],h=e.area[3]+(e.width-e.area[1]-e.area[3])/2;if(e.yAxis.data)for(var u=function(t){var n=e.yAxis.data[t];if(f=[],"categories"===n.type)for(var l=0;l<=n.categories.length;l++)f.push(e.area[0]+o/n.categories.length/2+o/n.categories.length*l);else for(var s=0;s<=e.yAxis.splitNumber;s++)f.push(e.area[0]+r*s);if(!0!==n.disabled){var u=e.chartData.yAxisData.rangesFormat[t],p=n.fontSize?n.fontSize*e.pix:a.fontSize,x=e.chartData.yAxisData.yAxisWidth[t],g=n.textAlign||"right";if(u.forEach((function(t,a){var o=f[a];i.beginPath(),i.setFontSize(p),i.setLineWidth(1*e.pix),i.setStrokeStyle(n.axisLineColor||"#cccccc"),i.setFillStyle(n.fontColor||e.fontColor);var r=0,l=4*e.pix;if("left"==x.position){switch(1==n.calibration&&(i.moveTo(c,o),i.lineTo(c-3*e.pix,o),l+=3*e.pix),g){case"left":i.setTextAlign("left"),r=c-x.width;break;case"right":i.setTextAlign("right"),r=c-l;break;default:i.setTextAlign("center"),r=c-x.width/2}i.fillText(String(t),r,o+p/2-3*e.pix)}else if("right"==x.position){switch(1==n.calibration&&(i.moveTo(d,o),i.lineTo(d+3*e.pix,o),l+=3*e.pix),g){case"left":i.setTextAlign("left"),r=d+l;break;case"right":i.setTextAlign("right"),r=d+x.width;break;default:i.setTextAlign("center"),r=d+x.width/2}i.fillText(String(t),r,o+p/2-3*e.pix)}else if("center"==x.position){switch(1==n.calibration&&(i.moveTo(h,o),i.lineTo(h-3*e.pix,o),l+=3*e.pix),g){case"left":i.setTextAlign("left"),r=h-x.width;break;case"right":i.setTextAlign("right"),r=h-l;break;default:i.setTextAlign("center"),r=h-x.width/2}i.fillText(String(t),r,o+p/2-3*e.pix)}i.closePath(),i.stroke(),i.setTextAlign("left")})),!1!==n.axisLine&&(i.beginPath(),i.setStrokeStyle(n.axisLineColor||"#cccccc"),i.setLineWidth(1*e.pix),"left"==x.position?(i.moveTo(c,e.height-e.area[2]),i.lineTo(c,e.area[0])):"right"==x.position?(i.moveTo(d,e.height-e.area[2]),i.lineTo(d,e.area[0])):"center"==x.position&&(i.moveTo(h,e.height-e.area[2]),i.lineTo(h,e.area[0])),i.stroke()),e.yAxis.showTitle){var v=n.titleFontSize*e.pix||a.fontSize,y=n.title;i.beginPath(),i.setFontSize(v),i.setFillStyle(n.titleFontColor||e.fontColor),"left"==x.position?i.fillText(y,c-m(y,v,i)/2+(n.titleOffsetX||0),e.area[0]-(10-(n.titleOffsetY||0))*e.pix):"right"==x.position?i.fillText(y,d-m(y,v,i)/2+(n.titleOffsetX||0),e.area[0]-(10-(n.titleOffsetY||0))*e.pix):"center"==x.position&&i.fillText(y,h-m(y,v,i)/2+(n.titleOffsetX||0),e.area[0]-(10-(n.titleOffsetY||0))*e.pix),i.closePath(),i.stroke()}"left"==x.position?c-=x.width+e.yAxis.padding*e.pix:d+=x.width+e.yAxis.padding*e.pix}},p=0;p<e.yAxis.data.length;p++){var f;u(p)}}}function Tt(t,e,a,i,o){if(!1!==e.legend.show){var r=o.legendData,n=r.points,l=r.area,s=e.legend.padding*e.pix,c=e.legend.fontSize*e.pix,d=15*e.pix,h=5*e.pix,u=e.legend.itemGap*e.pix,p=Math.max(e.legend.lineHeight*e.pix,c);i.beginPath(),i.setLineWidth(e.legend.borderWidth*e.pix),i.setStrokeStyle(e.legend.borderColor),i.setFillStyle(e.legend.backgroundColor),i.moveTo(l.start.x,l.start.y),i.rect(l.start.x,l.start.y,l.width,l.height),i.closePath(),i.fill(),i.stroke(),n.forEach((function(t,o){var n,f=0;f=r.widthArr[o],n=r.heightArr[o];var x=0,g=0;if("top"==e.legend.position||"bottom"==e.legend.position){switch(e.legend.float){case"left":x=l.start.x+s;break;case"right":x=l.start.x+l.width-f;break;default:x=l.start.x+(l.width-f)/2}g=l.start.y+s+o*p}else f=0==o?0:r.widthArr[o-1],x=l.start.x+s+f,g=l.start.y+s+(l.height-n)/2;i.setFontSize(a.fontSize);for(var v=0;v<t.length;v++){var y=t[v];switch(y.area=[0,0,0,0],y.area[0]=x,y.area[1]=g,y.area[3]=g+p,i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(y.show?y.color:e.legend.hiddenColor),i.setFillStyle(y.show?y.color:e.legend.hiddenColor),y.legendShape){case"line":i.moveTo(x,g+.5*p-2*e.pix),i.fillRect(x,g+.5*p-2*e.pix,15*e.pix,4*e.pix);break;case"triangle":i.moveTo(x+7.5*e.pix,g+.5*p-5*e.pix),i.lineTo(x*****e.pix,g+.5*p+5*e.pix),i.lineTo(x+12.5*e.pix,g+.5*p+5*e.pix),i.lineTo(x+7.5*e.pix,g+.5*p-5*e.pix);break;case"diamond":i.moveTo(x+7.5*e.pix,g+.5*p-5*e.pix),i.lineTo(x*****e.pix,g+.5*p),i.lineTo(x+7.5*e.pix,g+.5*p+5*e.pix),i.lineTo(x+12.5*e.pix,g+.5*p),i.lineTo(x+7.5*e.pix,g+.5*p-5*e.pix);break;case"circle":i.moveTo(x+7.5*e.pix,g+.5*p),i.arc(x+7.5*e.pix,g+.5*p,5*e.pix,0,2*Math.PI);break;case"rect":i.moveTo(x,g+.5*p-5*e.pix),i.fillRect(x,g+.5*p-5*e.pix,15*e.pix,10*e.pix);break;case"square":i.moveTo(x+5*e.pix,g+.5*p-5*e.pix),i.fillRect(x+5*e.pix,g+.5*p-5*e.pix,10*e.pix,10*e.pix);break;case"none":break;default:i.moveTo(x,g+.5*p-5*e.pix),i.fillRect(x,g+.5*p-5*e.pix,15*e.pix,10*e.pix)}i.closePath(),i.fill(),i.stroke(),x+=d+h;var b=.5*p+.5*c-2,w=y.legendText?y.legendText:y.name;i.beginPath(),i.setFontSize(c),i.setFillStyle(y.show?e.legend.fontColor:e.legend.hiddenColor),i.fillText(w,x,g+b),i.closePath(),i.stroke(),"top"==e.legend.position||"bottom"==e.legend.position?(x+=m(w,c,i)+u,y.area[2]=x):(y.area[2]=x+m(w,c,i)+u,x-=d+h,g+=p)}}))}}function Pt(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:!1,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},"pie"==e.type?e.extra.pie:e.extra.ring),n={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2};0==a.pieChartLinePadding&&(a.pieChartLinePadding=r.activeRadius*e.pix);var s=Math.min((e.width-e.area[1]-e.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding);s=s<10?10:s,r.customRadius>0&&(s=r.customRadius*e.pix),t=E(t,s,o);var d=r.activeRadius*e.pix;if(r.customColor=v(r.linearType,r.customColor,t,a),t=t.map((function(t){return t._start_+=r.offsetAngle*Math.PI/180,t})),t.forEach((function(t,a){e.tooltip&&e.tooltip.index==a&&(i.beginPath(),i.setFillStyle(c(t.color,r.activeOpacity||.5)),i.moveTo(n.x,n.y),i.arc(n.x,n.y,t._radius_+d,t._start_,t._start_+2*t._proportion_*Math.PI),i.closePath(),i.fill()),i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.lineJoin="round",i.setStrokeStyle(r.borderColor);var o,l=t.color;"custom"==r.linearType&&(o=i.createCircularGradient?i.createCircularGradient(n.x,n.y,t._radius_):i.createRadialGradient(n.x,n.y,0,n.x,n.y,t._radius_),o.addColorStop(0,c(r.customColor[t.linearIndex],1)),o.addColorStop(1,c(t.color,1)),l=o);i.setFillStyle(l),i.moveTo(n.x,n.y),i.arc(n.x,n.y,t._radius_,t._start_,t._start_+2*t._proportion_*Math.PI),i.closePath(),i.fill(),1==r.border&&i.stroke()})),"ring"===e.type){var h=.6*s;"number"===typeof r.ringWidth&&r.ringWidth>0&&(h=Math.max(0,s-r.ringWidth*e.pix)),i.beginPath(),i.setFillStyle(r.centerColor),i.moveTo(n.x,n.y),i.arc(n.x,n.y,h,0,2*Math.PI),i.closePath(),i.fill()}return!1!==e.dataLabel&&1===o&&xt(t,e,a,i,0,n),1===o&&"ring"===e.type&&st(e,a,i,n),{center:n,radius:s,series:t}}function Dt(t,e){var a=Array(2),i=20037508.34*t/180,o=Math.log(Math.tan((90+e)*Math.PI/360))/(Math.PI/180);return o=20037508.34*o/180,a[0]=i,a[1]=o,a}function kt(t,e,a,i,o,r){return{x:(e-a.xMin)*i+o,y:(a.yMax-t)*i+r}}function _t(t,e,a){if(e[1]==a[1])return!1;if(e[1]>t[1]&&a[1]>t[1])return!1;if(e[1]<t[1]&&a[1]<t[1])return!1;if(e[1]==t[1]&&a[1]>t[1])return!1;if(a[1]==t[1]&&e[1]>t[1])return!1;if(e[0]<t[0]&&a[1]<t[1])return!1;var i=a[0]-(a[0]-e[0])*(a[1]-t[1])/(a[1]-e[1]);return!(i<t[0])}function Ft(t,e,a){for(var i=0,o=0;o<e.length;o++){var r=e[o][0];1==e.length&&(r=e[o][0]);for(var n=0;n<r.length-1;n++){var l=r[n],s=r[n+1];a&&(l=Dt(r[n][0],r[n][1]),s=Dt(r[n+1][0],r[n+1][1])),_t(t,l,s)&&(i+=1)}}return i%2==1}function Mt(t,e,a){a=0==a?1:a;for(var i=[],o=0;o<a;o++)i[o]=Math.random();return Math.floor(i.reduce((function(t,e){return t+e}))/a*(e-t))+t}function Lt(t,e,a,i){for(var o=!1,r=0;r<e.length;r++)if(e[r].area){if(!(t[3]<e[r].area[1]||t[0]>e[r].area[2]||t[1]>e[r].area[3]||t[2]<e[r].area[0])){o=!0;break}if(t[0]<0||t[1]<0||t[2]>a||t[3]>i){o=!0;break}o=!1}return o}function Ot(t,e,a){var i=t.series;switch(e){case"normal":for(var o=0;o<i.length;o++){var r=i[o].name,n=i[o].textSize*t.pix,l=m(r,n,a),s=void 0,c=void 0,d=void 0,h=0;while(1){h++,s=Mt(-t.width/2,t.width/2,5)-l/2,c=Mt(-t.height/2,t.height/2,5)+n/2,d=[s-5+t.width/2,c-5-n+t.height/2,s+l+5+t.width/2,c+5+t.height/2];var u=Lt(d,i,t.width,t.height);if(!u)break;if(1e3==h){d=[-100,-100,-100,-100];break}}i[o].area=d}break;case"vertical":for(var p=function(){return Math.random()>.7},f=0;f<i.length;f++){var x=i[f].name,g=i[f].textSize*t.pix,v=m(x,g,a),y=p(),b=void 0,w=void 0,S=void 0,C=void 0,A=0;while(1){A++;var T=void 0;if(y?(b=Mt(-t.width/2,t.width/2,5)-v/2,w=Mt(-t.height/2,t.height/2,5)+g/2,S=[w-5-v+t.width/2,-b-5+t.height/2,w+5+t.width/2,-b+g+5+t.height/2],C=[t.width-(t.width/2-t.height/2)-(-b+g+5+t.height/2)-5,t.height/2-t.width/2+(w-5-v+t.width/2)-5,t.width-(t.width/2-t.height/2)-(-b+g+5+t.height/2)+g,t.height/2-t.width/2+(w-5-v+t.width/2)+v+5],T=Lt(C,i,t.height,t.width)):(b=Mt(-t.width/2,t.width/2,5)-v/2,w=Mt(-t.height/2,t.height/2,5)+g/2,S=[b-5+t.width/2,w-5-g+t.height/2,b+v+5+t.width/2,w+5+t.height/2],T=Lt(S,i,t.width,t.height)),!T)break;if(1e3==A){S=[-1e3,-1e3,-1e3,-1e3];break}}y?(i[f].area=C,i[f].areav=S):i[f].area=S,i[f].rotate=y}break}return i}function It(t,e,a,i,o,r,n){for(var l=0;l<t.length;l++){var c=t[l];if(!1!==c.labelShow){var d=void 0,h=void 0,u=void 0,p=void 0,f=c.formatter?c.formatter(c,l,t,e):s.toFixed(100*c._proportion_)+"%";f=c.labelText?c.labelText:f,"right"==o&&(d=l==t.length-1?(c.funnelArea[2]+n.x)/2:(c.funnelArea[2]+t[l+1].funnelArea[2])/2,h=d+2*r,u=c.funnelArea[1]+i/2,p=c.textSize*e.pix||e.fontSize*e.pix,a.setLineWidth(1*e.pix),a.setStrokeStyle(c.color),a.setFillStyle(c.color),a.beginPath(),a.moveTo(d,u),a.lineTo(h,u),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(h,u),a.arc(h,u,2*e.pix,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(p),a.setFillStyle(c.textColor||e.fontColor),a.fillText(f,h+5,u+p/2-2),a.closePath(),a.stroke(),a.closePath()),"left"==o&&(d=l==t.length-1?(c.funnelArea[0]+n.x)/2:(c.funnelArea[0]+t[l+1].funnelArea[0])/2,h=d-2*r,u=c.funnelArea[1]+i/2,p=c.textSize*e.pix||e.fontSize*e.pix,a.setLineWidth(1*e.pix),a.setStrokeStyle(c.color),a.setFillStyle(c.color),a.beginPath(),a.moveTo(d,u),a.lineTo(h,u),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(h,u),a.arc(h,u,2,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(p),a.setFillStyle(c.textColor||e.fontColor),a.fillText(f,h-5-m(f,p,a),u+p/2-2),a.closePath(),a.stroke(),a.closePath())}}}function zt(t,e,a,i,o,r,n){for(var l=0;l<t.length;l++){var s=t[l],c=void 0,d=void 0;s.centerText&&(c=s.funnelArea[1]+i/2,d=s.centerTextSize*e.pix||e.fontSize*e.pix,a.beginPath(),a.setFontSize(d),a.setFillStyle(s.centerTextColor||"#FFFFFF"),a.fillText(s.centerText,n.x-m(s.centerText,d,a)/2,c+d/2-2),a.closePath(),a.stroke(),a.closePath())}}function Et(t,e){e.save(),e.translate(0,.5),e.restore(),e.draw()}var Rt={easeIn:function(t){return Math.pow(t,3)},easeOut:function(t){return Math.pow(t-1,3)+1},easeInOut:function(t){return(t/=.5)<1?.5*Math.pow(t,3):.5*(Math.pow(t-2,3)+2)},linear:function(t){return t}};function Nt(t){this.isStop=!1,t.duration="undefined"===typeof t.duration?1e3:t.duration,t.timing=t.timing||"easeInOut";var e=function(){return"undefined"!==typeof setTimeout?function(t,e){setTimeout((function(){var e=+new Date;t(e)}),e)}:"undefined"!==typeof requestAnimationFrame?requestAnimationFrame:function(t){t(null)}}(),a=null,i=function(o){if(null===o||!0===this.isStop)return t.onProcess&&t.onProcess(1),void(t.onAnimationFinish&&t.onAnimationFinish());if(null===a&&(a=o),o-a<t.duration){var r=(o-a)/t.duration,n=Rt[t.timing];r=n(r),t.onProcess&&t.onProcess(r),e(i,17)}else t.onProcess&&t.onProcess(1),t.onAnimationFinish&&t.onAnimationFinish()};i=i.bind(this),e(i,17)}function Wt(t,e,a,i){var r=this,n=this,d=e.series;"pie"!==t&&"ring"!==t&&"mount"!==t&&"rose"!==t&&"funnel"!==t||(d=function(t,e,a){var i=[];if(t.length>0&&t[0].data.constructor.toString().indexOf("Array")>-1){e._pieSeries_=t;for(var o=t[0].data,r=0;r<o.length;r++)o[r].formatter=t[0].formatter,o[r].data=o[r].value,i.push(o[r]);e.series=i}else i=t;return i}(d,e));var h=e.categories;if("mount"===t){h=[];for(var u=0;u<d.length;u++)!1!==d[u].show&&h.push(d[u].name);e.categories=h}d=g(d,e,a);var x=e.animation?e.duration:0;n.animationInstance&&n.animationInstance.stop();var y=null;if("candle"==t){var w=l({},e.extra.candle.average);w.show?(y=function(t,e,a,i){for(var o=[],r=0;r<t.length;r++){for(var n={data:[],name:e[r],color:a[r]},l=0,s=i.length;l<s;l++)if(l<t[r])n.data.push(null);else{for(var c=0,d=0;d<t[r];d++)c+=i[l-d][1];n.data.push(+(c/t[r]).toFixed(3))}o.push(n)}return o}(w.day,w.name,w.color,d[0].data),y=g(y,e,a),e.seriesMA=y):y=e.seriesMA?e.seriesMA=g(e.seriesMA,e,a):d}else y=d;e._series_=d=_(d),e.area=new Array(4);for(var S=0;S<4;S++)e.area[S]=e.padding[S]*e.pix;var C=function(t,e,a,i,o){var r={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(!1===e.legend.show)return i.legendData=r,r;var n=e.legend.padding*e.pix,l=e.legend.margin*e.pix,s=e.legend.fontSize?e.legend.fontSize*e.pix:a.fontSize,c=15*e.pix,d=5*e.pix,h=Math.max(e.legend.lineHeight*e.pix,s);if("top"==e.legend.position||"bottom"==e.legend.position){for(var u=[],p=0,f=[],x=[],g=0;g<t.length;g++){var v=t[g],y=v.legendText?v.legendText:v.name,b=c+d+m(y||"undefined",s,o)+e.legend.itemGap*e.pix;p+b>e.width-e.area[1]-e.area[3]?(u.push(x),f.push(p-e.legend.itemGap*e.pix),p=b,x=[v]):(p+=b,x.push(v))}if(x.length){u.push(x),f.push(p-e.legend.itemGap*e.pix),r.widthArr=f;var w=Math.max.apply(null,f);switch(e.legend.float){case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+w+2*n;break;case"right":r.area.start.x=e.width-e.area[1]-w-2*n,r.area.end.x=e.width-e.area[1];break;default:r.area.start.x=(e.width-w)/2-n,r.area.end.x=(e.width+w)/2+n}r.area.width=w+2*n,r.area.wholeWidth=w+2*n,r.area.height=u.length*h+2*n,r.area.wholeHeight=u.length*h+2*n+2*l,r.points=u}}else{var S=t.length,C=e.height-e.area[0]-e.area[2]-2*l-2*n,A=Math.min(Math.floor(C/h),S);switch(r.area.height=A*h+2*n,r.area.wholeHeight=A*h+2*n,e.legend.float){case"top":r.area.start.y=e.area[0]+l,r.area.end.y=e.area[0]+l+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-l-r.area.height,r.area.end.y=e.height-e.area[2]-l;break;default:r.area.start.y=(e.height-r.area.height)/2,r.area.end.y=(e.height+r.area.height)/2}for(var T=S%A===0?S/A:Math.floor(S/A+1),P=[],D=0;D<T;D++){var k=t.slice(D*A,D*A+A);P.push(k)}if(r.points=P,P.length){for(var _=0;_<P.length;_++){for(var F=P[_],M=0,L=0;L<F.length;L++){var O=c+d+m(F[L].name||"undefined",s,o)+e.legend.itemGap*e.pix;O>M&&(M=O)}r.widthArr.push(M),r.heightArr.push(F.length*h+2*n)}for(var I=0,z=0;z<r.widthArr.length;z++)I+=r.widthArr[z];r.area.width=I-e.legend.itemGap*e.pix+2*n,r.area.wholeWidth=r.area.width+n}}switch(e.legend.position){case"top":r.area.start.y=e.area[0]+l,r.area.end.y=e.area[0]+l+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-r.area.height-l,r.area.end.y=e.height-e.area[2]-l;break;case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+r.area.width;break;case"right":r.area.start.x=e.width-e.area[1]-r.area.width,r.area.end.x=e.width-e.area[1];break}return i.legendData=r,r}(y,e,a,e.chartData,i),P=C.area.wholeHeight,D=C.area.wholeWidth;switch(e.legend.position){case"top":e.area[0]+=P;break;case"bottom":e.area[2]+=P;break;case"left":e.area[3]+=D;break;case"right":e.area[1]+=D;break}var k={},F=0;if("line"===e.type||"column"===e.type||"mount"===e.type||"area"===e.type||"mix"===e.type||"candle"===e.type||"scatter"===e.type||"bubble"===e.type||"bar"===e.type){if(k=ot(d,e,a,i),F=k.yAxisWidth,e.yAxis.showTitle){for(var M=0,it=0;it<e.yAxis.data.length;it++)M=Math.max(M,e.yAxis.data[it].titleFontSize?e.yAxis.data[it].titleFontSize*e.pix:a.fontSize);e.area[0]+=M}for(var vt=0,bt=0,_t=0;_t<F.length;_t++)"left"==F[_t].position?(e.area[3]+=bt>0?F[_t].width+e.yAxis.padding*e.pix:F[_t].width,bt+=1):"right"==F[_t].position&&(e.area[1]+=vt>0?F[_t].width+e.yAxis.padding*e.pix:F[_t].width,vt+=1)}else a.yAxisWidth=F;if(e.chartData.yAxisData=k,e.categories&&e.categories.length&&"radar"!==e.type&&"gauge"!==e.type&&"bar"!==e.type){e.chartData.xAxisData=Y(e.categories,e);var Ft=O(e.categories,e,0,e.chartData.xAxisData.eachSpacing,i),Mt=Ft.xAxisHeight,Lt=Ft.angle;a.xAxisHeight=Mt,a._xAxisTextAngle_=Lt,e.area[2]+=Mt,e.chartData.categoriesData=Ft}else if("line"===e.type||"area"===e.type||"scatter"===e.type||"bubble"===e.type||"bar"===e.type){e.chartData.xAxisData=I(d,e,a,i),h=e.chartData.xAxisData.rangesFormat;var Rt=O(h,e,0,e.chartData.xAxisData.eachSpacing,i),Wt=Rt.xAxisHeight,Bt=Rt.angle;a.xAxisHeight=Wt,a._xAxisTextAngle_=Bt,e.area[2]+=Wt,e.chartData.categoriesData=Rt}else e.chartData.xAxisData={xAxisPoints:[]};if(e.enableScroll&&"right"==e.xAxis.scrollAlign&&void 0===e._scrollDistance_){var Gt,jt=e.chartData.xAxisData.xAxisPoints,$t=e.chartData.xAxisData.startX,Ht=e.chartData.xAxisData.endX,Xt=e.chartData.xAxisData.eachSpacing,qt=Xt*(jt.length-1),Jt=Ht-$t;Gt=Jt-qt,n.scrollOption.currentOffset=Gt,n.scrollOption.startTouchX=Gt,n.scrollOption.distance=0,n.scrollOption.lastMoveTime=0,e._scrollDistance_=Gt}switch("pie"!==t&&"ring"!==t&&"rose"!==t||(a._pieTextMaxLength_=!1===e.dataLabel?0:function(t,e,a,i){t=E(t);for(var o=0,r=0;r<t.length;r++){var n=t[r],l=n.formatter?n.formatter(+n._proportion_.toFixed(2)):s.toFixed(100*n._proportion_)+"%";o=Math.max(o,m(l,n.textSize*i.pix||e.fontSize,a))}return o}(y,a,i,e)),t){case"word":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"normal",autoColors:!0},e.extra.word);e.chartData.wordCloudData||(e.chartData.wordCloudData=Ot(e,r.type,i)),i.beginPath(),i.setFillStyle(e.background),i.rect(0,0,e.width,e.height),i.fill(),i.save();var n=e.chartData.wordCloudData;i.translate(e.width/2,e.height/2);for(var s=0;s<n.length;s++){i.save(),n[s].rotate&&i.rotate(90*Math.PI/180);var c=n[s].name,d=n[s].textSize*e.pix,h=m(c,d,i);i.beginPath(),i.setStrokeStyle(n[s].color),i.setFillStyle(n[s].color),i.setFontSize(d),n[s].rotate?n[s].areav[0]>0&&(e.tooltip&&e.tooltip.index==s?i.strokeText(c,(n[s].areav[0]+5-e.width/2)*o-h*(1-o)/2,(n[s].areav[1]+5+d-e.height/2)*o):i.fillText(c,(n[s].areav[0]+5-e.width/2)*o-h*(1-o)/2,(n[s].areav[1]+5+d-e.height/2)*o)):n[s].area[0]>0&&(e.tooltip&&e.tooltip.index==s?i.strokeText(c,(n[s].area[0]+5-e.width/2)*o-h*(1-o)/2,(n[s].area[1]+5+d-e.height/2)*o):i.fillText(c,(n[s].area[0]+5-e.width/2)*o-h*(1-o)/2,(n[s].area[1]+5+d-e.height/2)*o)),i.stroke(),i.restore()}i.restore()}(d,e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"map":i.clearRect(0,0,e.width,e.height),function(t,e,a,i){var o,r,n=l({},{border:!0,mercator:!1,borderWidth:1,active:!0,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},e.extra.map),s=t,d=function(t){for(var e,a={xMin:180,xMax:0,yMin:90,yMax:0},i=0;i<t.length;i++)for(var o=t[i].geometry.coordinates,r=0;r<o.length;r++){e=o[r],1==e.length&&(e=e[0]);for(var n=0;n<e.length;n++){var l=e[n][0],s=e[n][1],c={x:l,y:s};a.xMin=a.xMin<c.x?a.xMin:c.x,a.xMax=a.xMax>c.x?a.xMax:c.x,a.yMin=a.yMin<c.y?a.yMin:c.y,a.yMax=a.yMax>c.y?a.yMax:c.y}}return a}(s);if(n.mercator){var h=Dt(d.xMax,d.yMax),u=Dt(d.xMin,d.yMin);d.xMax=h[0],d.yMax=h[1],d.xMin=u[0],d.yMin=u[1]}for(var p=e.width/Math.abs(d.xMax-d.xMin),f=e.height/Math.abs(d.yMax-d.yMin),x=p<f?p:f,g=e.width/2-Math.abs(d.xMax-d.xMin)/2*x,v=e.height/2-Math.abs(d.yMax-d.yMin)/2*x,y=0;y<s.length;y++){i.beginPath(),i.setLineWidth(n.borderWidth*e.pix),i.setStrokeStyle(n.borderColor),i.setFillStyle(c(t[y].color,t[y].fillOpacity||n.fillOpacity)),1==n.active&&e.tooltip&&e.tooltip.index==y&&(i.setStrokeStyle(n.activeBorderColor),i.setFillStyle(c(n.activeFillColor,n.activeFillOpacity)));for(var b=s[y].geometry.coordinates,w=0;w<b.length;w++){o=b[w],1==o.length&&(o=o[0]);for(var S=0;S<o.length;S++){var C=Array(2);C=n.mercator?Dt(o[S][0],o[S][1]):o[S],r=kt(C[1],C[0],d,x,g,v),0===S?(i.beginPath(),i.moveTo(r.x,r.y)):i.lineTo(r.x,r.y)}i.fill(),1==n.border&&i.stroke()}}if(1==e.dataLabel)for(y=0;y<s.length;y++){var A=s[y].properties.centroid;if(A){n.mercator&&(A=Dt(s[y].properties.centroid[0],s[y].properties.centroid[1])),r=kt(A[1],A[0],d,x,g,v);var T=s[y].textSize*e.pix||a.fontSize,P=s[y].textColor||e.fontColor;n.active&&n.activeTextColor&&e.tooltip&&e.tooltip.index==y&&(P=n.activeTextColor);var D=s[y].properties.name;i.beginPath(),i.setFontSize(T),i.setFillStyle(P),i.fillText(D,r.x-m(D,T,i)/2,r.y+T/2),i.closePath(),i.stroke()}}e.chartData.mapData={bounds:d,scale:x,xoffset:g,yoffset:v,mercator:n.mercator},wt(e,a,i,1),i.draw()}(d,e,a,i),setTimeout((function(){r.uevent.trigger("renderComplete")}),50);break;case"funnel":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.funnelData=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"funnel",activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,minSize:0,labelAlign:"right",linearType:"none",customColor:[]},e.extra.funnel),n=(e.height-e.area[0]-e.area[2])/t.length,s={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.height-e.area[2]},d=r.activeWidth*e.pix,h=Math.min((e.width-e.area[1]-e.area[3])/2-d,(e.height-e.area[0]-e.area[2])/2-d),u=R(t,h,r,n,o);if(i.save(),i.translate(s.x,s.y),r.customColor=v(r.linearType,r.customColor,t,a),"pyramid"==r.type)for(var p=0;p<u.length;p++){if(p==u.length-1){e.tooltip&&e.tooltip.index==p&&(i.beginPath(),i.setFillStyle(c(u[p].color,r.activeOpacity)),i.moveTo(-d,-n),i.lineTo(-u[p].radius-d,0),i.lineTo(u[p].radius+d,0),i.lineTo(d,-n),i.lineTo(-d,-n),i.closePath(),i.fill()),u[p].funnelArea=[s.x-u[p].radius,s.y-n*(p+1),s.x+u[p].radius,s.y-n*p],i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(r.borderColor);var f=c(u[p].color,r.fillOpacity);if("custom"==r.linearType){var x=i.createLinearGradient(u[p].radius,-n,-u[p].radius,-n);x.addColorStop(0,c(u[p].color,r.fillOpacity)),x.addColorStop(.5,c(r.customColor[u[p].linearIndex],r.fillOpacity)),x.addColorStop(1,c(u[p].color,r.fillOpacity)),f=x}i.setFillStyle(f),i.moveTo(0,-n),i.lineTo(-u[p].radius,0),i.lineTo(u[p].radius,0),i.lineTo(0,-n),i.closePath(),i.fill(),1==r.border&&i.stroke()}else{e.tooltip&&e.tooltip.index==p&&(i.beginPath(),i.setFillStyle(c(u[p].color,r.activeOpacity)),i.moveTo(0,0),i.lineTo(-u[p].radius-d,0),i.lineTo(-u[p+1].radius-d,-n),i.lineTo(u[p+1].radius+d,-n),i.lineTo(u[p].radius+d,0),i.lineTo(0,0),i.closePath(),i.fill()),u[p].funnelArea=[s.x-u[p].radius,s.y-n*(p+1),s.x+u[p].radius,s.y-n*p],i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(r.borderColor);f=c(u[p].color,r.fillOpacity);if("custom"==r.linearType){x=i.createLinearGradient(u[p].radius,-n,-u[p].radius,-n);x.addColorStop(0,c(u[p].color,r.fillOpacity)),x.addColorStop(.5,c(r.customColor[u[p].linearIndex],r.fillOpacity)),x.addColorStop(1,c(u[p].color,r.fillOpacity)),f=x}i.setFillStyle(f),i.moveTo(0,0),i.lineTo(-u[p].radius,0),i.lineTo(-u[p+1].radius,-n),i.lineTo(u[p+1].radius,-n),i.lineTo(u[p].radius,0),i.lineTo(0,0),i.closePath(),i.fill(),1==r.border&&i.stroke()}i.translate(0,-n)}else{i.translate(0,-(u.length-1)*n);for(var g=0;g<u.length;g++){if(g==u.length-1){e.tooltip&&e.tooltip.index==g&&(i.beginPath(),i.setFillStyle(c(u[g].color,r.activeOpacity)),i.moveTo(-d-r.minSize/2,0),i.lineTo(-u[g].radius-d,-n),i.lineTo(u[g].radius+d,-n),i.lineTo(d+r.minSize/2,0),i.lineTo(-d-r.minSize/2,0),i.closePath(),i.fill()),u[g].funnelArea=[s.x-u[g].radius,s.y-n,s.x+u[g].radius,s.y],i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(r.borderColor);f=c(u[g].color,r.fillOpacity);if("custom"==r.linearType){x=i.createLinearGradient(u[g].radius,-n,-u[g].radius,-n);x.addColorStop(0,c(u[g].color,r.fillOpacity)),x.addColorStop(.5,c(r.customColor[u[g].linearIndex],r.fillOpacity)),x.addColorStop(1,c(u[g].color,r.fillOpacity)),f=x}i.setFillStyle(f),i.moveTo(0,0),i.lineTo(-r.minSize/2,0),i.lineTo(-u[g].radius,-n),i.lineTo(u[g].radius,-n),i.lineTo(r.minSize/2,0),i.lineTo(0,0),i.closePath(),i.fill(),1==r.border&&i.stroke()}else{e.tooltip&&e.tooltip.index==g&&(i.beginPath(),i.setFillStyle(c(u[g].color,r.activeOpacity)),i.moveTo(0,0),i.lineTo(-u[g+1].radius-d,0),i.lineTo(-u[g].radius-d,-n),i.lineTo(u[g].radius+d,-n),i.lineTo(u[g+1].radius+d,0),i.lineTo(0,0),i.closePath(),i.fill()),u[g].funnelArea=[s.x-u[g].radius,s.y-n*(u.length-g),s.x+u[g].radius,s.y-n*(u.length-g-1)],i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(r.borderColor);f=c(u[g].color,r.fillOpacity);if("custom"==r.linearType){x=i.createLinearGradient(u[g].radius,-n,-u[g].radius,-n);x.addColorStop(0,c(u[g].color,r.fillOpacity)),x.addColorStop(.5,c(r.customColor[u[g].linearIndex],r.fillOpacity)),x.addColorStop(1,c(u[g].color,r.fillOpacity)),f=x}i.setFillStyle(f),i.moveTo(0,0),i.lineTo(-u[g+1].radius,0),i.lineTo(-u[g].radius,-n),i.lineTo(u[g].radius,-n),i.lineTo(u[g+1].radius,0),i.lineTo(0,0),i.closePath(),i.fill(),1==r.border&&i.stroke()}i.translate(0,n)}}return i.restore(),!1!==e.dataLabel&&1===o&&It(u,e,i,n,r.labelAlign,d,s),1===o&&zt(u,e,i,n,r.labelAlign,d,s),{center:s,radius:h,series:u}}(d,e,a,i,t),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"line":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),Ct(0,e,0,i),St(h,e,a,i);var o=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"straight",width:2,activeType:"none",linearType:"none",onShadow:!1,animation:"vertical"},e.extra.line);r.width*=e.pix;var n=e.chartData.xAxisData,s=n.xAxisPoints,d=n.eachSpacing,h=[];i.save();var u=0,f=e.width+d;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),u=-e._scrollDistance_-2*d+e.area[3],f=u+(e.xAxis.itemCount+4)*d),t.forEach((function(t,n){var l,x,g;i.beginPath(),i.setStrokeStyle(t.color),i.moveTo(-1e4,-1e4),i.lineTo(-10001,-10001),i.stroke(),l=[].concat(e.chartData.yAxisData.ranges[t.index]),x=l.pop(),g=l.shift();var v=t.data,y=V(v,x,g,s,d,e,a,r,o);h.push(y);var m=L(y,t);if("dash"==t.lineType){var b=t.dashLength?t.dashLength:8;b*=e.pix,i.setLineDash([b,b])}i.beginPath();var w=t.color;if("none"!==r.linearType&&t.linearColor&&t.linearColor.length>0){for(var S=i.createLinearGradient(e.chartData.xAxisData.startX,e.height/2,e.chartData.xAxisData.endX,e.height/2),C=0;C<t.linearColor.length;C++)S.addColorStop(t.linearColor[C][0],c(t.linearColor[C][1],1));w=S}i.setStrokeStyle(w),1==r.onShadow&&t.setShadow&&t.setShadow.length>0?i.setShadow(t.setShadow[0],t.setShadow[1],t.setShadow[2],t.setShadow[3]):i.setShadow(0,0,0,"rgba(0,0,0,0)"),i.setLineWidth(r.width),m.forEach((function(t,e){if(1===t.length)i.moveTo(t[0].x,t[0].y);else{i.moveTo(t[0].x,t[0].y);var a=0;if("curve"===r.type)for(var o=0;o<t.length;o++){var n=t[o];if(0==a&&n.x>u&&(i.moveTo(n.x,n.y),a=1),o>0&&n.x>u&&n.x<f){var l=p(t,o-1);i.bezierCurveTo(l.ctrA.x,l.ctrA.y,l.ctrB.x,l.ctrB.y,n.x,n.y)}}if("straight"===r.type)for(var s=0;s<t.length;s++){var c=t[s];0==a&&c.x>u&&(i.moveTo(c.x,c.y),a=1),s>0&&c.x>u&&c.x<f&&i.lineTo(c.x,c.y)}if("step"===r.type)for(var d=0;d<t.length;d++){var h=t[d];0==a&&h.x>u&&(i.moveTo(h.x,h.y),a=1),d>0&&h.x>u&&h.x<f&&(i.lineTo(h.x,t[d-1].y),i.lineTo(h.x,h.y))}i.moveTo(t[0].x,t[0].y)}})),i.stroke(),i.setLineDash([]),!1!==e.dataPointShape&&nt(y,t.color,t.pointShape,i,e),lt(y,t.color,t.pointShape,i,e,r)})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){var n,l,c;n=[].concat(e.chartData.yAxisData.ranges[t.index]),l=n.pop(),c=n.shift();var h=t.data,u=Q(h,l,c,s,d,e,a,o);ct(u,t,a,i,e)})),i.restore(),{xAxisPoints:s,calPoints:h,eachSpacing:d}}(d,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"scatter":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),Ct(0,e,0,i),St(h,e,a,i);var o=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=(l({},{type:"circle"},e.extra.scatter),e.chartData.xAxisData),n=r.xAxisPoints,s=r.eachSpacing,c=[];i.save();return e.width,e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),-e._scrollDistance_-2*s+e.area[3],e.xAxis.itemCount),t.forEach((function(t,r){var l,c,d;l=[].concat(e.chartData.yAxisData.ranges[t.index]),c=l.pop(),d=l.shift();var h=t.data,u=Q(h,c,d,n,s,e,a,o);i.beginPath(),i.setStrokeStyle(t.color),i.setFillStyle(t.color),i.setLineWidth(1*e.pix);var p=t.pointShape;if("diamond"===p)u.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y),i.lineTo(t.x,t.y****),i.lineTo(t.x****,t.y),i.lineTo(t.x,t.y-4.5))}));else if("circle"===p)u.forEach((function(t,a){null!==t&&(i.moveTo(t.x*****e.pix,t.y),i.arc(t.x,t.y,3*e.pix,0,2*Math.PI,!1))}));else if("square"===p)u.forEach((function(t,e){null!==t&&(i.moveTo(t.x-3.5,t.y-3.5),i.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===p)u.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y****),i.lineTo(t.x****,t.y****),i.lineTo(t.x,t.y-4.5))}));else if("triangle"===p)return;i.closePath(),i.fill(),i.stroke()})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){var l,c,d;l=[].concat(e.chartData.yAxisData.ranges[t.index]),c=l.pop(),d=l.shift();var h=t.data,u=Q(h,c,d,n,s,e,a,o);ct(u,t,a,i,e)})),i.restore(),{xAxisPoints:n,calPoints:c,eachSpacing:s}}(d,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"bubble":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),Ct(0,e,0,i),St(h,e,a,i);var o=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{opacity:1,border:2},e.extra.bubble),n=e.chartData.xAxisData,s=n.xAxisPoints,d=n.eachSpacing,h=[];i.save();return e.width,e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),-e._scrollDistance_-2*d+e.area[3],e.xAxis.itemCount),t.forEach((function(t,n){var l,h,u;l=[].concat(e.chartData.yAxisData.ranges[t.index]),h=l.pop(),u=l.shift();var p=t.data,f=Q(p,h,u,s,d,e,a,o);i.beginPath(),i.setStrokeStyle(t.color),i.setLineWidth(r.border*e.pix),i.setFillStyle(c(t.color,r.opacity)),f.forEach((function(t,a){i.moveTo(t.x+t.r,t.y),i.arc(t.x,t.y,t.r*e.pix,0,2*Math.PI,!1)})),i.closePath(),i.fill(),i.stroke(),!1!==e.dataLabel&&1===o&&f.forEach((function(o,r){i.beginPath();var n=t.textSize*e.pix||a.fontSize;i.setFontSize(n),i.setFillStyle(t.textColor||"#FFFFFF"),i.setTextAlign("center"),i.fillText(String(o.t),o.x,o.y+n/2),i.closePath(),i.stroke(),i.setTextAlign("left")}))})),i.restore(),{xAxisPoints:s,calPoints:h,eachSpacing:d}}(d,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"mix":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),Ct(0,e,0,i),St(h,e,a,i);var r=function(t,e,a,i){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,s=n.xAxisPoints,d=n.eachSpacing,h=l({},{width:d/2,barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mix.column),u=l({},{opacity:.2,gradient:!1},e.extra.mix.area),f=l({},{width:2},e.extra.mix.line),x=e.height-e.area[2],g=[],y=0,m=0;t.forEach((function(t,e){"column"==t.type&&(m+=1)})),i.save();var b=-2,w=s.length+2,S=0,C=e.width+d;if(e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),b=Math.floor(-e._scrollDistance_/d)-2,w=b+e.xAxis.itemCount+4,S=-e._scrollDistance_-2*d+e.area[3],C=S+(e.xAxis.itemCount+4)*d),h.customColor=v(h.linearType,h.customColor,t,a),t.forEach((function(t,n){var l,v,A;l=[].concat(e.chartData.yAxisData.ranges[t.index]),v=l.pop(),A=l.shift();var T=t.data,P=Q(T,v,A,s,d,e,a,r);if(g.push(P),"column"==t.type){P=$(P,d,m,y,0,e);for(var D=0;D<P.length;D++){var k=P[D];if(null!==k&&D>b&&D<w){var _=k.x-k.width/2;e.height,k.y,e.area[2];i.beginPath();var F=k.color||t.color,M=k.color||t.color;if("none"!==h.linearType){var O=i.createLinearGradient(_,k.y,_,e.height-e.area[2]);"opacity"==h.linearType?(O.addColorStop(0,c(F,h.linearOpacity)),O.addColorStop(1,c(F,1))):(O.addColorStop(0,c(h.customColor[t.linearIndex],h.linearOpacity)),O.addColorStop(h.colorStop,c(h.customColor[t.linearIndex],h.linearOpacity)),O.addColorStop(1,c(F,1))),F=O}if(h.barBorderRadius&&4===h.barBorderRadius.length||h.barBorderCircle){var I=_,z=k.y,E=k.width,R=e.height-e.area[2]-k.y;h.barBorderCircle&&(h.barBorderRadius=[E/2,E/2,0,0]);var N=(0,o.default)(h.barBorderRadius,4),W=N[0],B=N[1],G=N[2],j=N[3],H=Math.min(E/2,R/2);W=W>H?H:W,B=B>H?H:B,G=G>H?H:G,j=j>H?H:j,W=W<0?0:W,B=B<0?0:B,G=G<0?0:G,j=j<0?0:j,i.arc(I+W,z+W,W,-Math.PI,-Math.PI/2),i.arc(I+E-B,z+B,B,-Math.PI/2,0),i.arc(I+E-G,z+R-G,G,0,Math.PI/2),i.arc(I+j,z+R-j,j,Math.PI/2,Math.PI)}else i.moveTo(_,k.y),i.lineTo(_+k.width,k.y),i.lineTo(_+k.width,e.height-e.area[2]),i.lineTo(_,e.height-e.area[2]),i.lineTo(_,k.y),i.setLineWidth(1),i.setStrokeStyle(M);i.setFillStyle(F),i.closePath(),i.fill()}}y+=1}if("area"==t.type)for(var X=L(P,t),q=0;q<X.length;q++){var J=X[q];if(i.beginPath(),i.setStrokeStyle(t.color),i.setStrokeStyle(c(t.color,u.opacity)),u.gradient){var Y=i.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);Y.addColorStop("0",c(t.color,u.opacity)),Y.addColorStop("1.0",c("#FFFFFF",.1)),i.setFillStyle(Y)}else i.setFillStyle(c(t.color,u.opacity));if(i.setLineWidth(2*e.pix),J.length>1){var U=J[0],V=J[J.length-1];i.moveTo(U.x,U.y);var Z=0;if("curve"===t.style)for(var K=0;K<J.length;K++){var tt=J[K];if(0==Z&&tt.x>S&&(i.moveTo(tt.x,tt.y),Z=1),K>0&&tt.x>S&&tt.x<C){var et=p(J,K-1);i.bezierCurveTo(et.ctrA.x,et.ctrA.y,et.ctrB.x,et.ctrB.y,tt.x,tt.y)}}else for(var at=0;at<J.length;at++){var it=J[at];0==Z&&it.x>S&&(i.moveTo(it.x,it.y),Z=1),at>0&&it.x>S&&it.x<C&&i.lineTo(it.x,it.y)}i.lineTo(V.x,x),i.lineTo(U.x,x),i.lineTo(U.x,U.y)}else{var ot=J[0];i.moveTo(ot.x-d/2,ot.y)}i.closePath(),i.fill()}if("line"==t.type){var rt=L(P,t);rt.forEach((function(a,o){if("dash"==t.lineType){var r=t.dashLength?t.dashLength:8;r*=e.pix,i.setLineDash([r,r])}if(i.beginPath(),i.setStrokeStyle(t.color),i.setLineWidth(f.width*e.pix),1===a.length)i.moveTo(a[0].x,a[0].y);else{i.moveTo(a[0].x,a[0].y);var n=0;if("curve"==t.style)for(var l=0;l<a.length;l++){var s=a[l];if(0==n&&s.x>S&&(i.moveTo(s.x,s.y),n=1),l>0&&s.x>S&&s.x<C){var c=p(a,l-1);i.bezierCurveTo(c.ctrA.x,c.ctrA.y,c.ctrB.x,c.ctrB.y,s.x,s.y)}}else for(var d=0;d<a.length;d++){var h=a[d];0==n&&h.x>S&&(i.moveTo(h.x,h.y),n=1),d>0&&h.x>S&&h.x<C&&i.lineTo(h.x,h.y)}i.moveTo(a[0].x,a[0].y)}i.stroke(),i.setLineDash([])}))}"point"==t.type&&(t.addPoint=!0),1==t.addPoint&&"column"!==t.type&&nt(P,t.color,t.pointShape,i,e)})),!1!==e.dataLabel&&1===r){y=0;t.forEach((function(t,o){var n,l,c;n=[].concat(e.chartData.yAxisData.ranges[t.index]),l=n.pop(),c=n.shift();var h=t.data,u=Q(h,l,c,s,d,e,a,r);"column"!==t.type?ct(u,t,a,i,e):(u=$(u,d,m,y,0,e),ct(u,t,a,i,e),y+=1)}))}return i.restore(),{xAxisPoints:s,calPoints:g,eachSpacing:d}}(d,e,a,i,t),n=r.xAxisPoints,s=r.calPoints,u=r.eachSpacing;e.chartData.xAxisPoints=n,e.chartData.calPoints=s,e.chartData.eachSpacing=u,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"column":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),Ct(0,e,0,i),St(h,e,a,i);var r=function(t,e,a,i){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,s=n.xAxisPoints,d=n.eachSpacing,h=l({},{type:"group",width:d/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0,labelPosition:"outside"},e.extra.column),u=[];i.save();var p=-2,f=s.length+2;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),p=Math.floor(-e._scrollDistance_/d)-2,f=p+e.xAxis.itemCount+4),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===r&&yt(e.tooltip.offset.x,e,0,i,d),h.customColor=v(h.linearType,h.customColor,t,a),t.forEach((function(n,l){var x,g,v;x=[].concat(e.chartData.yAxisData.ranges[n.index]),g=x.pop(),v=x.shift();var y=e.height-e.area[0]-e.area[2],m=y*(0-g)/(v-g),b=e.height-Math.round(m)-e.area[2];n.zeroPoints=b;var w=n.data;switch(h.type){case"group":var S=Z(w,g,v,s,d,e,a,b,r),C=et(w,g,v,s,d,e,a,l,t,r);u.push(C),S=$(S,d,t.length,l,0,e);for(var A=0;A<S.length;A++){var T=S[A];if(null!==T&&A>p&&A<f){var P=T.x-T.width/2,D=e.height-T.y-e.area[2];i.beginPath();var k=T.color||n.color,_=T.color||n.color;if("none"!==h.linearType){var F=i.createLinearGradient(P,T.y,P,b);"opacity"==h.linearType?(F.addColorStop(0,c(k,h.linearOpacity)),F.addColorStop(1,c(k,1))):(F.addColorStop(0,c(h.customColor[n.linearIndex],h.linearOpacity)),F.addColorStop(h.colorStop,c(h.customColor[n.linearIndex],h.linearOpacity)),F.addColorStop(1,c(k,1))),k=F}if(h.barBorderRadius&&4===h.barBorderRadius.length||!0===h.barBorderCircle){var M=P,L=T.y>b?b:T.y,O=T.width,I=Math.abs(b-T.y);h.barBorderCircle&&(h.barBorderRadius=[O/2,O/2,0,0]),T.y>b&&(h.barBorderRadius=[0,0,O/2,O/2]);var z=(0,o.default)(h.barBorderRadius,4),E=z[0],R=z[1],N=z[2],W=z[3],B=Math.min(O/2,I/2);E=E>B?B:E,R=R>B?B:R,N=N>B?B:N,W=W>B?B:W,E=E<0?0:E,R=R<0?0:R,N=N<0?0:N,W=W<0?0:W,i.arc(M+E,L+E,E,-Math.PI,-Math.PI/2),i.arc(M+O-R,L+R,R,-Math.PI/2,0),i.arc(M+O-N,L+I-N,N,0,Math.PI/2),i.arc(M+W,L+I-W,W,Math.PI/2,Math.PI)}else i.moveTo(P,T.y),i.lineTo(P+T.width,T.y),i.lineTo(P+T.width,b),i.lineTo(P,b),i.lineTo(P,T.y),i.setLineWidth(1),i.setStrokeStyle(_);i.setFillStyle(k),i.closePath(),i.fill()}}break;case"stack":S=et(w,g,v,s,d,e,a,l,t,r);u.push(S),S=q(S,d,t.length,0,0,e);for(var G=0;G<S.length;G++){var j=S[G];if(null!==j&&G>p&&G<f){i.beginPath();k=j.color||n.color,P=j.x-j.width/2+1,D=e.height-j.y-e.area[2];var H=e.height-j.y0-e.area[2];l>0&&(D-=H),i.setFillStyle(k),i.moveTo(P,j.y),i.fillRect(P,j.y,j.width,D),i.closePath(),i.fill()}}break;case"meter":S=Q(w,g,v,s,d,e,a,r);u.push(S),S=X(S,d,t.length,l,0,e,h.meterBorder);for(var J=0;J<S.length;J++){var Y=S[J];if(null!==Y&&J>p&&J<f){i.beginPath(),0==l&&h.meterBorder>0&&(i.setStrokeStyle(n.color),i.setLineWidth(h.meterBorder*e.pix)),0==l?i.setFillStyle(h.meterFillColor):i.setFillStyle(Y.color||n.color);P=Y.x-Y.width/2,D=e.height-Y.y-e.area[2];if(h.barBorderRadius&&4===h.barBorderRadius.length||!0===h.barBorderCircle){var U=P,V=Y.y,K=Y.width,tt=b-Y.y;h.barBorderCircle&&(h.barBorderRadius=[K/2,K/2,0,0]);var at=(0,o.default)(h.barBorderRadius,4),it=at[0],ot=at[1],rt=at[2],nt=at[3],lt=Math.min(K/2,tt/2);it=it>lt?lt:it,ot=ot>lt?lt:ot,rt=rt>lt?lt:rt,nt=nt>lt?lt:nt,it=it<0?0:it,ot=ot<0?0:ot,rt=rt<0?0:rt,nt=nt<0?0:nt,i.arc(U+it,V+it,it,-Math.PI,-Math.PI/2),i.arc(U+K-ot,V+ot,ot,-Math.PI/2,0),i.arc(U+K-rt,V+tt-rt,rt,0,Math.PI/2),i.arc(U+nt,V+tt-nt,nt,Math.PI/2,Math.PI),i.fill()}else i.moveTo(P,Y.y),i.lineTo(P+Y.width,Y.y),i.lineTo(P+Y.width,b),i.lineTo(P,b),i.lineTo(P,Y.y),i.fill();0==l&&h.meterBorder>0&&(i.closePath(),i.stroke())}}break}})),!1!==e.dataLabel&&1===r&&t.forEach((function(o,n){var l,c,u;l=[].concat(e.chartData.yAxisData.ranges[o.index]),c=l.pop(),u=l.shift();var p=o.data;switch(h.type){case"group":var f=Z(p,c,u,s,d,e,a,r);f=$(f,d,t.length,n,0,e),dt(f,o,a,i,e);break;case"stack":f=et(p,c,u,s,d,e,a,n,t,r);dt(f,o,a,i,e);break;case"meter":f=Q(p,c,u,s,d,e,a,r);dt(f,o,a,i,e);break}})),i.restore(),{xAxisPoints:s,calPoints:u,eachSpacing:d}}(d,e,a,i,t),n=r.xAxisPoints,s=r.calPoints,u=r.eachSpacing;e.chartData.xAxisPoints=n,e.chartData.calPoints=s,e.chartData.eachSpacing=u,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"mount":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),Ct(0,e,0,i),St(h,e,a,i);var r=function(t,e,a,i){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,s=n.xAxisPoints,d=n.eachSpacing,h=l({},{type:"mount",widthRatio:1,borderWidth:1,barBorderCircle:!1,barBorderRadius:[],linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mount);h.widthRatio=h.widthRatio<=0?0:h.widthRatio,h.widthRatio=h.widthRatio>=2?2:h.widthRatio,i.save();var u,p,f,x=-2,g=s.length+2;e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),x=Math.floor(-e._scrollDistance_/d)-2,g=x+e.xAxis.itemCount+4),h.customColor=v(h.linearType,h.customColor,t,a),u=[].concat(e.chartData.yAxisData.ranges[0]),p=u.pop(),f=u.shift();var y=e.height-e.area[0]-e.area[2],m=y*(0-p)/(f-p),b=e.height-Math.round(m)-e.area[2],w=K(t,p,f,s,d,e,h,b,r);switch(h.type){case"bar":for(var S=0;S<w.length;S++){var C=w[S];if(null!==C&&S>x&&S<g){var A=C.x-d*h.widthRatio/2,T=e.height-C.y-e.area[2];i.beginPath();var P=C.color||t[S].color,D=C.color||t[S].color;if("none"!==h.linearType){var k=i.createLinearGradient(A,C.y,A,b);"opacity"==h.linearType?(k.addColorStop(0,c(P,h.linearOpacity)),k.addColorStop(1,c(P,1))):(k.addColorStop(0,c(h.customColor[t[S].linearIndex],h.linearOpacity)),k.addColorStop(h.colorStop,c(h.customColor[t[S].linearIndex],h.linearOpacity)),k.addColorStop(1,c(P,1))),P=k}if(h.barBorderRadius&&4===h.barBorderRadius.length||!0===h.barBorderCircle){var _=A,F=C.y>b?b:C.y,M=C.width,L=Math.abs(b-C.y);h.barBorderCircle&&(h.barBorderRadius=[M/2,M/2,0,0]),C.y>b&&(h.barBorderRadius=[0,0,M/2,M/2]);var O=(0,o.default)(h.barBorderRadius,4),I=O[0],z=O[1],E=O[2],R=O[3],N=Math.min(M/2,L/2);I=I>N?N:I,z=z>N?N:z,E=E>N?N:E,R=R>N?N:R,I=I<0?0:I,z=z<0?0:z,E=E<0?0:E,R=R<0?0:R,i.arc(_+I,F+I,I,-Math.PI,-Math.PI/2),i.arc(_+M-z,F+z,z,-Math.PI/2,0),i.arc(_+M-E,F+L-E,E,0,Math.PI/2),i.arc(_+R,F+L-R,R,Math.PI/2,Math.PI)}else i.moveTo(A,C.y),i.lineTo(A+C.width,C.y),i.lineTo(A+C.width,b),i.lineTo(A,b),i.lineTo(A,C.y);i.setStrokeStyle(D),i.setFillStyle(P),h.borderWidth>0&&(i.setLineWidth(h.borderWidth*e.pix),i.closePath(),i.stroke()),i.fill()}}break;case"triangle":for(var W=0;W<w.length;W++){var B=w[W];if(null!==B&&W>x&&W<g){A=B.x-d*h.widthRatio/2,T=e.height-B.y-e.area[2];i.beginPath();P=B.color||t[W].color,D=B.color||t[W].color;if("none"!==h.linearType){k=i.createLinearGradient(A,B.y,A,b);"opacity"==h.linearType?(k.addColorStop(0,c(P,h.linearOpacity)),k.addColorStop(1,c(P,1))):(k.addColorStop(0,c(h.customColor[t[W].linearIndex],h.linearOpacity)),k.addColorStop(h.colorStop,c(h.customColor[t[W].linearIndex],h.linearOpacity)),k.addColorStop(1,c(P,1))),P=k}i.moveTo(A,b),i.lineTo(B.x,B.y),i.lineTo(A+B.width,b),i.setStrokeStyle(D),i.setFillStyle(P),h.borderWidth>0&&(i.setLineWidth(h.borderWidth*e.pix),i.stroke()),i.fill()}}break;case"mount":for(var G=0;G<w.length;G++){var j=w[G];if(null!==j&&G>x&&G<g){A=j.x-d*h.widthRatio/2,T=e.height-j.y-e.area[2];i.beginPath();P=j.color||t[G].color,D=j.color||t[G].color;if("none"!==h.linearType){k=i.createLinearGradient(A,j.y,A,b);"opacity"==h.linearType?(k.addColorStop(0,c(P,h.linearOpacity)),k.addColorStop(1,c(P,1))):(k.addColorStop(0,c(h.customColor[t[G].linearIndex],h.linearOpacity)),k.addColorStop(h.colorStop,c(h.customColor[t[G].linearIndex],h.linearOpacity)),k.addColorStop(1,c(P,1))),P=k}i.moveTo(A,b),i.bezierCurveTo(j.x-j.width/4,b,j.x-j.width/4,j.y,j.x,j.y),i.bezierCurveTo(j.x+j.width/4,j.y,j.x+j.width/4,b,A+j.width,b),i.setStrokeStyle(D),i.setFillStyle(P),h.borderWidth>0&&(i.setLineWidth(h.borderWidth*e.pix),i.stroke()),i.fill()}}break;case"sharp":for(var $=0;$<w.length;$++){var H=w[$];if(null!==H&&$>x&&$<g){A=H.x-d*h.widthRatio/2,T=e.height-H.y-e.area[2];i.beginPath();P=H.color||t[$].color,D=H.color||t[$].color;if("none"!==h.linearType){k=i.createLinearGradient(A,H.y,A,b);"opacity"==h.linearType?(k.addColorStop(0,c(P,h.linearOpacity)),k.addColorStop(1,c(P,1))):(k.addColorStop(0,c(h.customColor[t[$].linearIndex],h.linearOpacity)),k.addColorStop(h.colorStop,c(h.customColor[t[$].linearIndex],h.linearOpacity)),k.addColorStop(1,c(P,1))),P=k}i.moveTo(A,b),i.quadraticCurveTo(H.x-0,b-T/4,H.x,H.y),i.quadraticCurveTo(H.x+0,b-T/4,A+H.width,b),i.setStrokeStyle(D),i.setFillStyle(P),h.borderWidth>0&&(i.setLineWidth(h.borderWidth*e.pix),i.stroke()),i.fill()}}break}if(!1!==e.dataLabel&&1===r){var X,q,J;X=[].concat(e.chartData.yAxisData.ranges[0]),q=X.pop(),J=X.shift();w=K(t,q,J,s,d,e,h,b,r);ht(w,t,a,i,e,b)}return i.restore(),{xAxisPoints:s,calPoints:w,eachSpacing:d}}(d,e,a,i,t),n=r.xAxisPoints,s=r.calPoints,u=r.eachSpacing;e.chartData.xAxisPoints=n,e.chartData.calPoints=s,e.chartData.eachSpacing=u,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"bar":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),St(h,e,a,i);var r=function(t,e,a,i){for(var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=[],s=(e.height-e.area[0]-e.area[2])/e.categories.length,d=0;d<e.categories.length;d++)n.push(e.area[0]+s/2+s*d);var h=l({},{type:"group",width:s/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.bar),u=[];i.save();var p=-2,f=n.length+2;return e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===r&&mt(e.tooltip.offset.y,e,0,i,s),h.customColor=v(h.linearType,h.customColor,t,a),t.forEach((function(l,d){var x,g,v;x=[].concat(e.chartData.xAxisData.ranges),v=x.pop(),g=x.shift();var y=l.data;switch(h.type){case"group":var m=tt(y,g,v,n,s,e,a,r),b=at(y,g,v,n,s,e,a,d,t,r);u.push(b),m=H(m,s,t.length,d,0,e);for(var w=0;w<m.length;w++){var S=m[w];if(null!==S&&w>p&&w<f){var C=e.area[3],A=S.y-S.width/2;S.height;i.beginPath();var T=S.color||l.color,P=S.color||l.color;if("none"!==h.linearType){var D=i.createLinearGradient(C,S.y,S.x,S.y);"opacity"==h.linearType?(D.addColorStop(0,c(T,h.linearOpacity)),D.addColorStop(1,c(T,1))):(D.addColorStop(0,c(h.customColor[l.linearIndex],h.linearOpacity)),D.addColorStop(h.colorStop,c(h.customColor[l.linearIndex],h.linearOpacity)),D.addColorStop(1,c(T,1))),T=D}if(h.barBorderRadius&&4===h.barBorderRadius.length||!0===h.barBorderCircle){var k=C,_=S.width,F=S.y-S.width/2,M=S.height;h.barBorderCircle&&(h.barBorderRadius=[_/2,_/2,0,0]);var L=(0,o.default)(h.barBorderRadius,4),O=L[0],I=L[1],z=L[2],E=L[3],R=Math.min(_/2,M/2);O=O>R?R:O,I=I>R?R:I,z=z>R?R:z,E=E>R?R:E,O=O<0?0:O,I=I<0?0:I,z=z<0?0:z,E=E<0?0:E,i.arc(k+E,F+E,E,-Math.PI,-Math.PI/2),i.arc(S.x-O,F+O,O,-Math.PI/2,0),i.arc(S.x-I,F+_-I,I,0,Math.PI/2),i.arc(k+z,F+_-z,z,Math.PI/2,Math.PI)}else i.moveTo(C,A),i.lineTo(S.x,A),i.lineTo(S.x,A+S.width),i.lineTo(C,A+S.width),i.lineTo(C,A),i.setLineWidth(1),i.setStrokeStyle(P);i.setFillStyle(T),i.closePath(),i.fill()}}break;case"stack":m=at(y,g,v,n,s,e,a,d,t,r);u.push(m),m=J(m,s,t.length,0,0,e);for(var N=0;N<m.length;N++){var W=m[N];if(null!==W&&N>p&&N<f){i.beginPath();T=W.color||l.color,C=W.x0;i.setFillStyle(T),i.moveTo(C,W.y-W.width/2),i.fillRect(C,W.y-W.width/2,W.height,W.width),i.closePath(),i.fill()}}break}})),!1!==e.dataLabel&&1===r&&t.forEach((function(o,l){var c,d,u;c=[].concat(e.chartData.xAxisData.ranges),u=c.pop(),d=c.shift();var p=o.data;switch(h.type){case"group":var f=tt(p,d,u,n,s,e,a,r);f=H(f,s,t.length,l,0,e),ut(f,o,a,i,e);break;case"stack":f=at(p,d,u,n,s,e,a,l,t,r);ut(f,o,a,i,e);break}})),{yAxisPoints:n,calPoints:u,eachSpacing:s}}(d,e,a,i,t),n=r.yAxisPoints,s=r.calPoints,u=r.eachSpacing;e.chartData.yAxisPoints=n,e.chartData.xAxisPoints=e.chartData.xAxisData.xAxisPoints,e.chartData.calPoints=s,e.chartData.eachSpacing=u,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"area":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),Ct(0,e,0,i),St(h,e,a,i);var o=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"straight",opacity:.2,addLine:!1,width:2,gradient:!1,activeType:"none"},e.extra.area),n=e.chartData.xAxisData,s=n.xAxisPoints,d=n.eachSpacing,h=e.height-e.area[2],u=[];i.save();var f=0,x=e.width+d;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),f=-e._scrollDistance_-2*d+e.area[3],x=f+(e.xAxis.itemCount+4)*d),t.forEach((function(t,n){var l,g,v;l=[].concat(e.chartData.yAxisData.ranges[t.index]),g=l.pop(),v=l.shift();var y=t.data,m=Q(y,g,v,s,d,e,a,o);u.push(m);for(var b=L(m,t),w=0;w<b.length;w++){var S=b[w];if(i.beginPath(),i.setStrokeStyle(c(t.color,r.opacity)),r.gradient){var C=i.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);C.addColorStop("0",c(t.color,r.opacity)),C.addColorStop("1.0",c("#FFFFFF",.1)),i.setFillStyle(C)}else i.setFillStyle(c(t.color,r.opacity));if(i.setLineWidth(r.width*e.pix),S.length>1){var A=S[0],T=S[S.length-1];i.moveTo(A.x,A.y);var P=0;if("curve"===r.type)for(var D=0;D<S.length;D++){var k=S[D];if(0==P&&k.x>f&&(i.moveTo(k.x,k.y),P=1),D>0&&k.x>f&&k.x<x){var _=p(S,D-1);i.bezierCurveTo(_.ctrA.x,_.ctrA.y,_.ctrB.x,_.ctrB.y,k.x,k.y)}}if("straight"===r.type)for(var F=0;F<S.length;F++){var M=S[F];0==P&&M.x>f&&(i.moveTo(M.x,M.y),P=1),F>0&&M.x>f&&M.x<x&&i.lineTo(M.x,M.y)}if("step"===r.type)for(var O=0;O<S.length;O++){var I=S[O];0==P&&I.x>f&&(i.moveTo(I.x,I.y),P=1),O>0&&I.x>f&&I.x<x&&(i.lineTo(I.x,S[O-1].y),i.lineTo(I.x,I.y))}i.lineTo(T.x,h),i.lineTo(A.x,h),i.lineTo(A.x,A.y)}else{var z=S[0];i.moveTo(z.x-d/2,z.y)}if(i.closePath(),i.fill(),r.addLine){if("dash"==t.lineType){var E=t.dashLength?t.dashLength:8;E*=e.pix,i.setLineDash([E,E])}if(i.beginPath(),i.setStrokeStyle(t.color),i.setLineWidth(r.width*e.pix),1===S.length)i.moveTo(S[0].x,S[0].y);else{i.moveTo(S[0].x,S[0].y);var R=0;if("curve"===r.type)for(var N=0;N<S.length;N++){var W=S[N];if(0==R&&W.x>f&&(i.moveTo(W.x,W.y),R=1),N>0&&W.x>f&&W.x<x){var B=p(S,N-1);i.bezierCurveTo(B.ctrA.x,B.ctrA.y,B.ctrB.x,B.ctrB.y,W.x,W.y)}}if("straight"===r.type)for(var G=0;G<S.length;G++){var j=S[G];0==R&&j.x>f&&(i.moveTo(j.x,j.y),R=1),G>0&&j.x>f&&j.x<x&&i.lineTo(j.x,j.y)}if("step"===r.type)for(var $=0;$<S.length;$++){var H=S[$];0==R&&H.x>f&&(i.moveTo(H.x,H.y),R=1),$>0&&H.x>f&&H.x<x&&(i.lineTo(H.x,S[$-1].y),i.lineTo(H.x,H.y))}i.moveTo(S[0].x,S[0].y)}i.stroke(),i.setLineDash([])}}!1!==e.dataPointShape&&nt(m,t.color,t.pointShape,i,e),lt(m,t.color,t.pointShape,i,e,r,n)})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){var n,l,c;n=[].concat(e.chartData.yAxisData.ranges[t.index]),l=n.pop(),c=n.shift();var h=t.data,u=Q(h,l,c,s,d,e,a,o);ct(u,t,a,i,e)})),i.restore(),{xAxisPoints:s,calPoints:u,eachSpacing:d}}(d,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"ring":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.pieData=Pt(d,e,a,i,t),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"pie":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.pieData=Pt(d,e,a,i,t),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"rose":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.pieData=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"area",activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF",linearType:"none",customColor:[]},e.extra.rose);0==a.pieChartLinePadding&&(a.pieChartLinePadding=r.activeRadius*e.pix);var n={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},s=Math.min((e.width-e.area[1]-e.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding);s=s<10?10:s;var d=r.minRadius||.5*s;s<d&&(s=d+10),t=N(t,r.type,d,s,o);var h=r.activeRadius*e.pix;return r.customColor=v(r.linearType,r.customColor,t,a),t=t.map((function(t){return t._start_+=(r.offsetAngle||0)*Math.PI/180,t})),t.forEach((function(t,a){e.tooltip&&e.tooltip.index==a&&(i.beginPath(),i.setFillStyle(c(t.color,r.activeOpacity||.5)),i.moveTo(n.x,n.y),i.arc(n.x,n.y,h+t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),i.closePath(),i.fill()),i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.lineJoin="round",i.setStrokeStyle(r.borderColor);var o,l=t.color;"custom"==r.linearType&&(o=i.createCircularGradient?i.createCircularGradient(n.x,n.y,t._radius_):i.createRadialGradient(n.x,n.y,0,n.x,n.y,t._radius_),o.addColorStop(0,c(r.customColor[t.linearIndex],1)),o.addColorStop(1,c(t.color,1)),l=o);i.setFillStyle(l),i.moveTo(n.x,n.y),i.arc(n.x,n.y,t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),i.closePath(),i.fill(),1==r.border&&i.stroke()})),!1!==e.dataLabel&&1===o&&xt(t,e,a,i,0,n),{center:n,radius:s,series:t}}(d,e,a,i,t),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"radar":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.radarData=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{gridColor:"#cccccc",gridType:"radar",gridEval:1,axisLabel:!1,axisLabelTofix:0,labelShow:!0,labelColor:"#666666",labelPointShow:!1,labelPointRadius:3,labelPointColor:"#cccccc",opacity:.2,gridCount:3,border:!1,borderWidth:2,linearType:"none",customColor:[]},e.extra.radar),n=T(e.categories.length),s={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},d=(e.width-e.area[1]-e.area[3])/2,h=(e.height-e.area[0]-e.area[2])/2,u=Math.min(d-(A(e.categories,a.fontSize,i)+a.radarLabelTextMargin),h-a.radarLabelTextMargin);u-=a.radarLabelTextMargin*e.pix,u=u<10?10:u,u=r.radius?r.radius:u,i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(r.gridColor),n.forEach((function(t,e){var a=f(u*Math.cos(t),u*Math.sin(t),s);i.moveTo(s.x,s.y),e%r.gridEval==0&&i.lineTo(a.x,a.y)})),i.stroke(),i.closePath();for(var p=function(t){var a={};if(i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(r.gridColor),"radar"==r.gridType)n.forEach((function(e,o){var n=f(u/r.gridCount*t*Math.cos(e),u/r.gridCount*t*Math.sin(e),s);0===o?(a=n,i.moveTo(n.x,n.y)):i.lineTo(n.x,n.y)})),i.lineTo(a.x,a.y);else{var o=f(u/r.gridCount*t*Math.cos(1.5),u/r.gridCount*t*Math.sin(1.5),s);i.arc(s.x,s.y,s.y-o.y,0,2*Math.PI,!1)}i.stroke(),i.closePath()},x=1;x<=r.gridCount;x++)p(x);r.customColor=v(r.linearType,r.customColor,t,a);var g=z(n,s,u,t,e,o);if(g.forEach((function(a,o){i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(a.color);var n,l=c(a.color,r.opacity);"custom"==r.linearType&&(n=i.createCircularGradient?i.createCircularGradient(s.x,s.y,u):i.createRadialGradient(s.x,s.y,0,s.x,s.y,u),n.addColorStop(0,c(r.customColor[t[o].linearIndex],r.opacity)),n.addColorStop(1,c(a.color,r.opacity)),l=n);if(i.setFillStyle(l),a.data.forEach((function(t,e){0===e?i.moveTo(t.position.x,t.position.y):i.lineTo(t.position.x,t.position.y)})),i.closePath(),i.fill(),!0===r.border&&i.stroke(),i.closePath(),!1!==e.dataPointShape){var d=a.data.map((function(t){return t.position}));nt(d,a.color,a.pointShape,i,e)}})),!0===r.axisLabel){var y=Math.max(r.max,Math.max.apply(null,b(t))),m=u/r.gridCount,w=e.fontSize*e.pix;i.setFontSize(w),i.setFillStyle(e.fontColor),i.setTextAlign("left");for(x=0;x<r.gridCount+1;x++){var S=x*y/r.gridCount;S=S.toFixed(r.axisLabelTofix),i.fillText(String(S),s.x+3*e.pix,s.y-x*m+w/2)}}return ft(n,u,s,e,a,i),!1!==e.dataLabel&&1===o&&(g.forEach((function(t,o){i.beginPath();var r=t.textSize*e.pix||a.fontSize;i.setFontSize(r),i.setFillStyle(t.textColor||e.fontColor),t.data.forEach((function(t,e){Math.abs(t.position.x-s.x)<2?t.position.y<s.y?(i.setTextAlign("center"),i.fillText(t.value,t.position.x,t.position.y-4)):(i.setTextAlign("center"),i.fillText(t.value,t.position.x,t.position.y+r+2)):t.position.x<s.x?(i.setTextAlign("right"),i.fillText(t.value,t.position.x-4,t.position.y+r/2-2)):(i.setTextAlign("left"),i.fillText(t.value,t.position.x+4,t.position.y+r/2-2))})),i.closePath(),i.stroke()})),i.setTextAlign("left")),{center:s,radius:u,angleList:n}}(d,e,a,i,t),Tt(e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.arcbarData=function(t,e,a,i){var o,r,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=l({},{startAngle:.75,endAngle:.25,type:"default",direction:"cw",lineCap:"round",width:12,gap:2,linearType:"none",customColor:[]},e.extra.arcbar);t=W(t,s,n),o=s.centerX||s.centerY?{x:s.centerX?s.centerX:e.width/2,y:s.centerY?s.centerY:e.height/2}:{x:e.width/2,y:e.height/2},s.radius?r=s.radius:(r=Math.min(o.x,o.y),r-=5*e.pix,r-=s.width/2),r=r<10?10:r,s.customColor=v(s.linearType,s.customColor,t,a);for(var d=0;d<t.length;d++){var h=t[d];i.setLineWidth(s.width*e.pix),i.setStrokeStyle(s.backgroundColor||"#E9E9E9"),i.setLineCap(s.lineCap),i.beginPath(),"default"==s.type?i.arc(o.x,o.y,r-(s.width*e.pix+s.gap*e.pix)*d,s.startAngle*Math.PI,s.endAngle*Math.PI,"ccw"==s.direction):i.arc(o.x,o.y,r-(s.width*e.pix+s.gap*e.pix)*d,0,2*Math.PI,"ccw"==s.direction),i.stroke();var u=h.color;if("custom"==s.linearType){var p=i.createLinearGradient(o.x-r,o.y,o.x+r,o.y);p.addColorStop(1,c(s.customColor[h.linearIndex],1)),p.addColorStop(0,c(h.color,1)),u=p}i.setLineWidth(s.width*e.pix),i.setStrokeStyle(u),i.setLineCap(s.lineCap),i.beginPath(),i.arc(o.x,o.y,r-(s.width*e.pix+s.gap*e.pix)*d,s.startAngle*Math.PI,h._proportion_*Math.PI,"ccw"==s.direction),i.stroke()}return st(e,a,i,o),{center:o,radius:r,series:t}}(d,e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.gaugeData=function(t,e,a,i,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=l({},{type:"default",startAngle:.75,endAngle:.25,width:15,labelOffset:13,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},a.extra.gauge);void 0==n.oldAngle&&(n.oldAngle=n.startAngle),void 0==n.oldData&&(n.oldData=0),t=G(t,n.startAngle,n.endAngle);var s={x:a.width/2,y:a.height/2},d=Math.min(s.x,s.y);d-=5*a.pix,d-=n.width/2,d=d<10?10:d;var h=d-n.width,u=0;if("progress"==n.type){var p=d-3*n.width;o.beginPath();var f=o.createLinearGradient(s.x,s.y-p,s.x,s.y+p);f.addColorStop("0",c(e[0].color,.3)),f.addColorStop("1.0",c("#FFFFFF",.1)),o.setFillStyle(f),o.arc(s.x,s.y,p,0,2*Math.PI,!1),o.fill(),o.setLineWidth(n.width),o.setStrokeStyle(c(e[0].color,.3)),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,h,n.startAngle*Math.PI,n.endAngle*Math.PI,!1),o.stroke(),u=n.endAngle<n.startAngle?2+n.endAngle-n.startAngle:n.startAngle-n.endAngle;n.splitLine.splitNumber;var x=u/n.splitLine.splitNumber/n.splitLine.childNumber,g=-d-.5*n.width-n.splitLine.fixRadius,v=-d-n.width-n.splitLine.fixRadius+n.splitLine.width;o.save(),o.translate(s.x,s.y),o.rotate((n.startAngle-1)*Math.PI);for(var y=n.splitLine.splitNumber*n.splitLine.childNumber+1,m=e[0].data*r,b=0;b<y;b++)o.beginPath(),m>b/y?o.setStrokeStyle(c(e[0].color,1)):o.setStrokeStyle(c(e[0].color,.3)),o.setLineWidth(3*a.pix),o.moveTo(g,0),o.lineTo(v,0),o.stroke(),o.rotate(x*Math.PI);o.restore(),e=B(e,n,r),o.setLineWidth(n.width),o.setStrokeStyle(e[0].color),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,h,n.startAngle*Math.PI,e[0]._proportion_*Math.PI,!1),o.stroke();var w=d-2.5*n.width;o.save(),o.translate(s.x,s.y),o.rotate((e[0]._proportion_-1)*Math.PI),o.beginPath(),o.setLineWidth(n.width/3);var S=o.createLinearGradient(0,.6*-w,0,.6*w);S.addColorStop("0",c("#FFFFFF",0)),S.addColorStop("0.5",c(e[0].color,1)),S.addColorStop("1.0",c("#FFFFFF",0)),o.setStrokeStyle(S),o.arc(0,0,w,.85*Math.PI,1.15*Math.PI,!1),o.stroke(),o.beginPath(),o.setLineWidth(1),o.setStrokeStyle(e[0].color),o.setFillStyle(e[0].color),o.moveTo(-w-n.width/3/2,-4),o.lineTo(-w-n.width/3/2-4,0),o.lineTo(-w-n.width/3/2,4),o.lineTo(-w-n.width/3/2,-4),o.stroke(),o.fill(),o.restore()}else{o.setLineWidth(n.width),o.setLineCap("butt");for(var C=0;C<t.length;C++){var A=t[C];o.beginPath(),o.setStrokeStyle(A.color),o.arc(s.x,s.y,d,A._startAngle_*Math.PI,A._endAngle_*Math.PI,!1),o.stroke()}o.save(),u=n.endAngle<n.startAngle?2+n.endAngle-n.startAngle:n.startAngle-n.endAngle;var T=u/n.splitLine.splitNumber,P=u/n.splitLine.splitNumber/n.splitLine.childNumber,D=-d-.5*n.width-n.splitLine.fixRadius,k=-d-.5*n.width-n.splitLine.fixRadius+n.splitLine.width,_=-d-.5*n.width-n.splitLine.fixRadius+n.splitLine.childWidth;o.translate(s.x,s.y),o.rotate((n.startAngle-1)*Math.PI);for(var F=0;F<n.splitLine.splitNumber+1;F++)o.beginPath(),o.setStrokeStyle(n.splitLine.color),o.setLineWidth(2*a.pix),o.moveTo(D,0),o.lineTo(k,0),o.stroke(),o.rotate(T*Math.PI);o.restore(),o.save(),o.translate(s.x,s.y),o.rotate((n.startAngle-1)*Math.PI);for(var M=0;M<n.splitLine.splitNumber*n.splitLine.childNumber+1;M++)o.beginPath(),o.setStrokeStyle(n.splitLine.color),o.setLineWidth(1*a.pix),o.moveTo(D,0),o.lineTo(_,0),o.stroke(),o.rotate(P*Math.PI);o.restore(),e=j(e,t,n,r);for(var L=0;L<e.length;L++){var O=e[L];o.save(),o.translate(s.x,s.y),o.rotate((O._proportion_-1)*Math.PI),o.beginPath(),o.setFillStyle(O.color),o.moveTo(n.pointer.width,0),o.lineTo(0,-n.pointer.width/2),o.lineTo(-h,0),o.lineTo(0,n.pointer.width/2),o.lineTo(n.pointer.width,0),o.closePath(),o.fill(),o.beginPath(),o.setFillStyle("#FFFFFF"),o.arc(0,0,n.pointer.width/6,0,2*Math.PI,!1),o.fill(),o.restore()}!1!==a.dataLabel&&pt(n,d,s,a,i,o)}return st(a,i,o,s),1===r&&"gauge"===a.type&&(a.extra.gauge.oldAngle=e[0]._proportion_,a.extra.gauge.oldData=e[0].data),{center:s,radius:d,innerRadius:h,categories:t,totalAngle:u}}(h,d,e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"candle":this.animationInstance=new Nt({timing:e.timing,duration:x,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),Ct(0,e,0,i),St(h,e,a,i);var o=function(t,e,a,i,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=l({},{color:{},average:{}},a.extra.candle);n.color=l({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},n.color),n.average=l({},{show:!1,name:[],day:[],color:i.color},n.average),a.extra.candle=n;var s=a.chartData.xAxisData,c=s.xAxisPoints,d=s.eachSpacing,h=[];o.save();var u=-2,f=c.length+2,x=0,g=a.width+d;return a._scrollDistance_&&0!==a._scrollDistance_&&!0===a.enableScroll&&(o.translate(a._scrollDistance_,0),u=Math.floor(-a._scrollDistance_/d)-2,f=u+a.xAxis.itemCount+4,x=-a._scrollDistance_-2*d+a.area[3],g=x+(a.xAxis.itemCount+4)*d),(n.average.show||e)&&e.forEach((function(t,e){var n,l,s;n=[].concat(a.chartData.yAxisData.ranges[t.index]),l=n.pop(),s=n.shift();for(var h=t.data,u=Q(h,l,s,c,d,a,i,r),f=L(u,t),v=0;v<f.length;v++){var y=f[v];if(o.beginPath(),o.setStrokeStyle(t.color),o.setLineWidth(1),1===y.length)o.moveTo(y[0].x,y[0].y),o.arc(y[0].x,y[0].y,1,0,2*Math.PI);else{o.moveTo(y[0].x,y[0].y);for(var m=0,b=0;b<y.length;b++){var w=y[b];if(0==m&&w.x>x&&(o.moveTo(w.x,w.y),m=1),b>0&&w.x>x&&w.x<g){var S=p(y,b-1);o.bezierCurveTo(S.ctrA.x,S.ctrA.y,S.ctrB.x,S.ctrB.y,w.x,w.y)}}o.moveTo(y[0].x,y[0].y)}o.closePath(),o.stroke()}})),t.forEach((function(t,e){var l,s,p;l=[].concat(a.chartData.yAxisData.ranges[t.index]),s=l.pop(),p=l.shift();var x=t.data,g=U(x,s,p,c,d,a,i,r);h.push(g);for(var v=L(g,t),y=0;y<v[0].length;y++)if(y>u&&y<f){var m=v[0][y];o.beginPath(),x[y][1]-x[y][0]>0?(o.setStrokeStyle(n.color.upLine),o.setFillStyle(n.color.upFill),o.setLineWidth(1*a.pix),o.moveTo(m[3].x,m[3].y),o.lineTo(m[1].x,m[1].y),o.lineTo(m[1].x-d/4,m[1].y),o.lineTo(m[0].x-d/4,m[0].y),o.lineTo(m[0].x,m[0].y),o.lineTo(m[2].x,m[2].y),o.lineTo(m[0].x,m[0].y),o.lineTo(m[0].x+d/4,m[0].y),o.lineTo(m[1].x+d/4,m[1].y),o.lineTo(m[1].x,m[1].y),o.moveTo(m[3].x,m[3].y)):(o.setStrokeStyle(n.color.downLine),o.setFillStyle(n.color.downFill),o.setLineWidth(1*a.pix),o.moveTo(m[3].x,m[3].y),o.lineTo(m[0].x,m[0].y),o.lineTo(m[0].x-d/4,m[0].y),o.lineTo(m[1].x-d/4,m[1].y),o.lineTo(m[1].x,m[1].y),o.lineTo(m[2].x,m[2].y),o.lineTo(m[1].x,m[1].y),o.lineTo(m[1].x+d/4,m[1].y),o.lineTo(m[0].x+d/4,m[0].y),o.lineTo(m[0].x,m[0].y),o.moveTo(m[3].x,m[3].y)),o.closePath(),o.fill(),o.stroke()}})),o.restore(),{xAxisPoints:c,calPoints:h,eachSpacing:d}}(d,y,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,At(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Tt(y?0:e.series,e,a,i,e.chartData),wt(e,a,i,t),Et(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break}}function Bt(){this.events={}}Nt.prototype.stop=function(){this.isStop=!0},Bt.prototype.addEventListener=function(t,e){this.events[t]=this.events[t]||[],this.events[t].push(e)},Bt.prototype.delEventListener=function(t){this.events[t]=[]},Bt.prototype.trigger=function(){for(var t=arguments.length,e=Array(t),a=0;a<t;a++)e[a]=arguments[a];var i=e[0],o=e.slice(1);this.events[i]&&this.events[i].forEach((function(t){try{t.apply(null,o)}catch(e){}}))};var Gt=function(t){t.pix=t.pixelRatio?t.pixelRatio:1,t.fontSize=t.fontSize?t.fontSize:13,t.fontColor=t.fontColor?t.fontColor:n.fontColor,""!=t.background&&"none"!=t.background||(t.background="#FFFFFF"),t.title=l({},t.title),t.subtitle=l({},t.subtitle),t.duration=t.duration?t.duration:1e3,t.yAxis=l({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,gridSet:"number",splitNumber:5,gridType:"solid",dashLength:4*t.pix,gridColor:"#cccccc",padding:10,fontColor:"#666666"},t.yAxis),t.xAxis=l({},{rotateLabel:!1,rotateAngle:45,disabled:!1,disableGrid:!1,splitNumber:5,calibration:!1,fontColor:"#666666",fontSize:13,lineHeight:20,marginTop:0,gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc",titleFontSize:13,titleOffsetY:0,titleOffsetX:0,titleFontColor:"#666666"},t.xAxis),t.xAxis.scrollPosition=t.xAxis.scrollAlign,t.legend=l({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:t.fontSize,lineHeight:t.fontSize,fontColor:t.fontColor,formatter:{},hiddenColor:"#CECECE"},t.legend),t.extra=l({tooltip:{legendShape:"auto"}},t.extra),t.rotate=!!t.rotate,t.animation=!!t.animation,t.rotate=!!t.rotate,t.canvas2d=!!t.canvas2d;var e=l({},n);if(e.color=t.color?t.color:e.color,"pie"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.pie.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"ring"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.ring.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"rose"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.rose.labelWidth*t.pix||e.pieChartLinePadding*t.pix),e.pieChartTextPadding=!1===t.dataLabel?0:e.pieChartTextPadding*t.pix,e.rotate=t.rotate,t.rotate){var a=t.width,i=t.height;t.width=i,t.height=a}if(t.padding=t.padding?t.padding:e.padding,e.yAxisWidth=n.yAxisWidth*t.pix,e.fontSize=t.fontSize*t.pix,e.titleFontSize=n.titleFontSize*t.pix,e.subtitleFontSize=n.subtitleFontSize*t.pix,!t.context)throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！");this.context=t.context,this.context.setTextAlign||(this.context.setStrokeStyle=function(t){return this.strokeStyle=t},this.context.setLineWidth=function(t){return this.lineWidth=t},this.context.setLineCap=function(t){return this.lineCap=t},this.context.setFontSize=function(t){return this.font=t+"px sans-serif"},this.context.setFillStyle=function(t){return this.fillStyle=t},this.context.setTextAlign=function(t){return this.textAlign=t},this.context.setTextBaseline=function(t){return this.textBaseline=t},this.context.setShadow=function(t,e,a,i){this.shadowColor=i,this.shadowOffsetX=t,this.shadowOffsetY=e,this.shadowBlur=a},this.context.draw=function(){}),this.context.setLineDash||(this.context.setLineDash=function(t){}),t.chartData={},this.uevent=new Bt,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=t,this.config=e,Wt.call(this,t.type,t,e,this.context)};Gt.prototype.updateData=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.opts=l({},this.opts,t),this.opts.updateData=!0;var e=t.scrollPosition||"current";switch(e){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":var a=ot(this.opts.series,this.opts,this.config,this.context),i=a.yAxisWidth;this.config.yAxisWidth=i;var o=0,r=Y(this.opts.categories,this.opts,this.config),n=r.xAxisPoints,s=r.startX,c=r.endX,d=r.eachSpacing,h=d*(n.length-1),u=c-s;o=u-h,this.scrollOption={currentOffset:o,startTouchX:o,distance:0,lastMoveTime:0},this.opts._scrollDistance_=o;break}Wt.call(this,this.opts.type,this.opts,this.config,this.context)},Gt.prototype.zoom=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0===this.opts.enableScroll){var e=Math.round(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=t.itemCount;var a=ot(this.opts.series,this.opts,this.config,this.context),i=a.yAxisWidth;this.config.yAxisWidth=i;var o=0,r=Y(this.opts.categories,this.opts,this.config),n=r.xAxisPoints,l=r.startX,s=r.endX,c=r.eachSpacing,d=c*e,u=s-l,p=u-c*(n.length-1);o=u/2-d,o>0&&(o=0),o<p&&(o=p),this.scrollOption={currentOffset:o,startTouchX:0,distance:0,lastMoveTime:0},h(this,o,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=o,Wt.call(this,this.opts.type,this.opts,this.config,this.context)}else console.log("[uCharts] 请启用滚动条后使用")},Gt.prototype.dobuleZoom=function(t){if(!0===this.opts.enableScroll){var e=t.changedTouches;if(!(e.length<2)){for(var a=0;a<e.length;a++)e[a].x=e[a].x?e[a].x:e[a].clientX,e[a].y=e[a].y?e[a].y:e[a].clientY;var i=[S(e[0],this.opts,t),S(e[1],this.opts,t)],o=Math.abs(i[0].x-i[1].x);if(!this.scrollOption.moveCount){var r={changedTouches:[{x:e[0].x,y:this.opts.area[0]/this.opts.pix+2}]},n={changedTouches:[{x:e[1].x,y:this.opts.area[0]/this.opts.pix+2}]};this.opts.rotate&&(r={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[0].y}]},n={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[1].y}]});var l=this.getCurrentDataIndex(r).index,s=this.getCurrentDataIndex(n).index,c=Math.abs(l-s);return this.scrollOption.moveCount=c,this.scrollOption.moveCurrent1=Math.min(l,s),void(this.scrollOption.moveCurrent2=Math.max(l,s))}var d=o/this.scrollOption.moveCount,u=(this.opts.width-this.opts.area[1]-this.opts.area[3])/d;u=u<=2?2:u,u=u>=this.opts.categories.length?this.opts.categories.length:u,this.opts.animation=!1,this.opts.xAxis.itemCount=u;var p=0,f=Y(this.opts.categories,this.opts,this.config),x=f.xAxisPoints,g=f.startX,v=f.endX,y=f.eachSpacing,m=y*this.scrollOption.moveCurrent1,b=v-g,w=b-y*(x.length-1);p=-m+Math.min(i[0].x,i[1].x)-this.opts.area[3]-y,p>0&&(p=0),p<w&&(p=w),this.scrollOption.currentOffset=p,this.scrollOption.startTouchX=0,this.scrollOption.distance=0,h(this,p,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=p,Wt.call(this,this.opts.type,this.opts,this.config,this.context)}}else console.log("[uCharts] 请启用滚动条后使用")},Gt.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},Gt.prototype.addEventListener=function(t,e){this.uevent.addEventListener(t,e)},Gt.prototype.delEventListener=function(t){this.uevent.delEventListener(t)},Gt.prototype.getCurrentDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],e){var a=S(e,this.opts,t);return"pie"===this.opts.type||"ring"===this.opts.type?function(t,e,a){var i=-1,o=E(e.series);if(e&&e.center&&M(t,e.center,e.radius)){var r=Math.atan2(e.center.y-t.y,t.x-e.center.x);r=-r,a.extra.pie&&a.extra.pie.offsetAngle&&(r-=a.extra.pie.offsetAngle*Math.PI/180),a.extra.ring&&a.extra.ring.offsetAngle&&(r-=a.extra.ring.offsetAngle*Math.PI/180);for(var n=0,l=o.length;n<l;n++)if(u(r,o[n]._start_,o[n]._start_+2*o[n]._proportion_*Math.PI)){i=n;break}}return i}({x:a.x,y:a.y},this.opts.chartData.pieData,this.opts):"rose"===this.opts.type?function(t,e,a){var i=-1,o=N(a._series_,a.extra.rose.type,e.radius,e.radius);if(e&&e.center&&M(t,e.center,e.radius)){var r=Math.atan2(e.center.y-t.y,t.x-e.center.x);r=-r,a.extra.rose&&a.extra.rose.offsetAngle&&(r-=a.extra.rose.offsetAngle*Math.PI/180);for(var n=0,l=o.length;n<l;n++)if(u(r,o[n]._start_,o[n]._start_+2*o[n]._rose_proportion_*Math.PI)){i=n;break}}return i}({x:a.x,y:a.y},this.opts.chartData.pieData,this.opts):"radar"===this.opts.type?function(t,e,a){var i=2*Math.PI/a,o=-1;if(M(t,e.center,e.radius)){var r=function(t){return t<0&&(t+=2*Math.PI),t>2*Math.PI&&(t-=2*Math.PI),t},n=Math.atan2(e.center.y-t.y,t.x-e.center.x);n*=-1,n<0&&(n+=2*Math.PI);var l=e.angleList.map((function(t){return t=r(-1*t),t}));l.forEach((function(t,e){var a=r(t-i/2),l=r(t+i/2);l<a&&(l+=2*Math.PI),(n>=a&&n<=l||n+2*Math.PI>=a&&n+2*Math.PI<=l)&&(o=e)}))}return o}({x:a.x,y:a.y},this.opts.chartData.radarData,this.opts.categories.length):"funnel"===this.opts.type?function(t,e){for(var a=-1,i=0,o=e.series.length;i<o;i++){var r=e.series[i];if(t.x>r.funnelArea[0]&&t.x<r.funnelArea[2]&&t.y>r.funnelArea[1]&&t.y<r.funnelArea[3]){a=i;break}}return a}({x:a.x,y:a.y},this.opts.chartData.funnelData):"map"===this.opts.type?function(t,e){for(var a=-1,i=e.chartData.mapData,o=e.series,r=function(t,e,a,i,o,r){return{x:(e-o)/i+a.xMin,y:a.yMax-(t-r)/i}}(t.y,t.x,i.bounds,i.scale,i.xoffset,i.yoffset),n=[r.x,r.y],l=0,s=o.length;l<s;l++){var c=o[l].geometry.coordinates;if(Ft(n,c,e.chartData.mapData.mercator)){a=l;break}}return a}({x:a.x,y:a.y},this.opts):"word"===this.opts.type?function(t,e){for(var a=-1,i=0,o=e.length;i<o;i++){var r=e[i];if(t.x>r.area[0]&&t.x<r.area[2]&&t.y>r.area[1]&&t.y<r.area[3]){a=i;break}}return a}({x:a.x,y:a.y},this.opts.chartData.wordCloudData):"bar"===this.opts.type?function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r={index:-1,group:[]},n=a.chartData.eachSpacing/2,l=a.chartData.yAxisPoints;return e&&e.length>0&&F(t,a,i)&&l.forEach((function(e,a){t.y+o+n>e&&(r.index=a)})),r}({x:a.x,y:a.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset)):function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r={index:-1,group:[]},n=a.chartData.eachSpacing/2,l=[];if(e&&e.length>0){if(a.categories){for(var s=1;s<a.chartData.xAxisPoints.length;s++)l.push(a.chartData.xAxisPoints[s]-n);"line"!=a.type&&"area"!=a.type||"justify"!=a.xAxis.boundaryGap||(l=a.chartData.xAxisPoints)}else n=0;if(F(t,a,i))if(a.categories)l.forEach((function(e,a){t.x+o+n>e&&(r.index=a)}));else{for(var c=Array(e.length),d=0;d<e.length;d++){c[d]=Array(e[d].length);for(var h=0;h<e[d].length;h++)c[d][h]=Math.abs(e[d][h].x-t.x)}for(var u=Array(c.length),p=Array(c.length),f=0;f<c.length;f++)u[f]=Math.min.apply(null,c[f]),p[f]=c[f].indexOf(u[f]);var x=Math.min.apply(null,u);r.index=[];for(var g=0;g<u.length;g++)u[g]==x&&(r.group.push(g),r.index.push(p[g]))}}return r}({x:a.x,y:a.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},Gt.prototype.getLegendDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],e){var a=S(e,this.opts,t);return function(t,e,a){var i=-1;if(function(t,e){return t.x>e.start.x&&t.x<e.end.x&&t.y>e.start.y&&t.y<e.end.y}(t,e.area)){for(var o=e.points,r=-1,n=0,l=o.length;n<l;n++)for(var s=o[n],c=0;c<s.length;c++){r+=1;var d=s[c]["area"];if(d&&t.x>d[0]-0&&t.x<d[2]+0&&t.y>d[1]-0&&t.y<d[3]+0){i=r;break}}return i}return i}({x:a.x,y:a.y},this.opts.chartData.legendData)}return-1},Gt.prototype.touchLegend=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=null;if(a=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],a){S(a,this.opts,t);var i=this.getLegendDataIndex(t);i>=0&&("candle"==this.opts.type?this.opts.seriesMA[i].show=!this.opts.seriesMA[i].show:this.opts.series[i].show=!this.opts.series[i].show,this.opts.animation=!!e.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,Wt.call(this,this.opts.type,this.opts,this.config,this.context))}},Gt.prototype.showToolTip=function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;i=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],i||console.log("[uCharts] 未获取到event坐标信息");var o=S(i,this.opts,t),r=this.scrollOption.currentOffset,n=l({},this.opts,{_scrollDistance_:r,animation:!1});if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type||"scatter"===this.opts.type||"bubble"===this.opts.type){var s=this.getCurrentDataIndex(t),c=void 0==a.index?s.index:a.index;if(c>-1||c.length>0){var d=C(this.opts.series,c,s.group);if(0!==d.length){var h=P(d,this.opts,c,s.group,this.opts.categories,a),u=h.textList,p=h.offset;p.y=o.y,n.tooltip={textList:void 0!==a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c,group:s.group}}}Wt.call(this,n.type,n,this.config,this.context)}if("mount"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(t).index:a.index;if(c>-1){n=l({},this.opts,{animation:!1}),d=l({},n._series_[c]),u=[{text:a.formatter?a.formatter(d,void 0,c,n):d.name+": "+d.data,color:d.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?d.legendShape:this.opts.extra.tooltip.legendShape}],p={x:n.chartData.calPoints[c].x,y:o.y};n.tooltip={textList:a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c}}Wt.call(this,n.type,n,this.config,this.context)}if("bar"===this.opts.type){s=this.getCurrentDataIndex(t),c=void 0==a.index?s.index:a.index;if(c>-1||c.length>0){d=C(this.opts.series,c,s.group);if(0!==d.length){h=P(d,this.opts,c,s.group,this.opts.categories,a),u=h.textList,p=h.offset;p.x=o.x,n.tooltip={textList:void 0!==a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c}}}Wt.call(this,n.type,n,this.config,this.context)}if("mix"===this.opts.type){s=this.getCurrentDataIndex(t),c=void 0==a.index?s.index:a.index;if(c>-1){r=this.scrollOption.currentOffset,n=l({},this.opts,{_scrollDistance_:r,animation:!1}),d=C(this.opts.series,c);if(0!==d.length){var f=D(d,this.opts,c,this.opts.categories,a);u=f.textList,p=f.offset;p.y=o.y,n.tooltip={textList:a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c}}}Wt.call(this,n.type,n,this.config,this.context)}if("candle"===this.opts.type){s=this.getCurrentDataIndex(t),c=void 0==a.index?s.index:a.index;if(c>-1){r=this.scrollOption.currentOffset,n=l({},this.opts,{_scrollDistance_:r,animation:!1}),d=C(this.opts.series,c);if(0!==d.length){h=k(this.opts.series[0].data,d,this.opts,c,this.opts.categories,this.opts.extra.candle,a),u=h.textList,p=h.offset;p.y=o.y,n.tooltip={textList:a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c}}}Wt.call(this,n.type,n,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type||"funnel"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(c>-1){n=l({},this.opts,{animation:!1}),d=l({},n._series_[c]),u=[{text:a.formatter?a.formatter(d,void 0,c,n):d.name+": "+d.data,color:d.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?d.legendShape:this.opts.extra.tooltip.legendShape}],p={x:o.x,y:o.y};n.tooltip={textList:a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c}}Wt.call(this,n.type,n,this.config,this.context)}if("map"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(c>-1){n=l({},this.opts,{animation:!1}),d=l({},this.opts.series[c]);d.name=d.properties.name;u=[{text:a.formatter?a.formatter(d,void 0,c,this.opts):d.name,color:d.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?d.legendShape:this.opts.extra.tooltip.legendShape}],p={x:o.x,y:o.y};n.tooltip={textList:a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c}}n.updateData=!1,Wt.call(this,n.type,n,this.config,this.context)}if("word"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(c>-1){n=l({},this.opts,{animation:!1}),d=l({},this.opts.series[c]),u=[{text:a.formatter?a.formatter(d,void 0,c,this.opts):d.name,color:d.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?d.legendShape:this.opts.extra.tooltip.legendShape}],p={x:o.x,y:o.y};n.tooltip={textList:a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c}}n.updateData=!1,Wt.call(this,n.type,n,this.config,this.context)}if("radar"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(c>-1){n=l({},this.opts,{animation:!1}),d=C(this.opts.series,c);if(0!==d.length){u=d.map((function(t){return{text:a.formatter?a.formatter(t,e.opts.categories[c],c,e.opts):t.name+": "+t.data,color:t.color,legendShape:"auto"==e.opts.extra.tooltip.legendShape?t.legendShape:e.opts.extra.tooltip.legendShape}})),p={x:o.x,y:o.y};n.tooltip={textList:a.textList?a.textList:u,offset:void 0!==a.offset?a.offset:p,option:a,index:c}}}Wt.call(this,n.type,n,this.config,this.context)}},Gt.prototype.translate=function(t){this.scrollOption={currentOffset:t,startTouchX:t,distance:0,lastMoveTime:0};var e=l({},this.opts,{_scrollDistance_:t,animation:!1});Wt.call(this,this.opts.type,e,this.config,this.context)},Gt.prototype.scrollStart=function(t){var e=null;e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0];var a=S(e,this.opts,t);e&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=a.x)},Gt.prototype.scroll=function(t){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());var e=this.opts.touchMoveLimit||60,a=Date.now(),i=a-this.scrollOption.lastMoveTime;if(!(i<Math.floor(1e3/e))&&0!=this.scrollOption.startTouchX){this.scrollOption.lastMoveTime=a;var o=null;if(o=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],o&&!0===this.opts.enableScroll){var r,n=S(o,this.opts,t);r=n.x-this.scrollOption.startTouchX;var s=this.scrollOption.currentOffset,c=h(this,s+r,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=r=c-s;var d=l({},this.opts,{_scrollDistance_:s+r,animation:!1});return this.opts=d,Wt.call(this,d.type,d,this.config,this.context),s+r}}},Gt.prototype.scrollEnd=function(t){if(!0===this.opts.enableScroll){var e=this.scrollOption,a=e.currentOffset,i=e.distance;this.scrollOption.currentOffset=a+i,this.scrollOption.distance=0,this.scrollOption.moveCount=0}};var jt=Gt;e.default=jt},"5c14":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".chartsview[data-v-3dd6a45f]{width:100%;height:100%;display:flex;flex:1;justify-content:center;align-items:center}",""]),t.exports=e},"6d35":function(t,e,a){"use strict";a.r(e);var i=a("2d31"),o=a("0875");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("6f82");var n=a("828b"),l=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"24c4711e",null,!1,i["a"],void 0);e["default"]=l.exports},"6f82":function(t,e,a){"use strict";var i=a("cdca"),o=a.n(i);o.a},"72f8":function(t,e,a){var i=a("3c38");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("2447bae2",i,!0,{sourceMap:!1,shadowMode:!1})},"7b23":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(a("2634")),r=i(a("2fdc"));a("5c47"),a("af8f"),a("d4b5"),a("bf0f"),a("2797"),a("aa9c"),a("64aa");var n=i(a("ec2a")),l=i(a("b3d7")),s={components:{qiunDataCharts:n.default},data:function(){return{nextshow:!1,chartMain:!0,testdev:!0,chartShow:!1,dataShow:!0,checkNum:"",dispatchNum:"",createNum:"",unCharge:"",unHandle:"",unchecked:"",undispatch:"",filed:"",stealType:"00",shixiaolist:[],total:"",stealRate:"",mgtOrgCode:"31102",queryDateStart:"2025-01-01",queryDateEnd:"2026-06-26",subsectionlist:["全部","窃电","违约用电"],curNow:0,chuliContainershow:!1,chashiContainershow:!0,inputdate:"2025-06-13 - 2025-06-29",list1:[{name:"查实"},{name:"处理"}],taiqunum:"",value:"",chartData2:{},opts:{color:["#3CA272","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],legend:{show:!1},padding:[15,15,15,5],touchMoveLimit:24,enableScroll:!0,xAxis:{disableGrid:!0,scrollShow:!0,itemCount:4},yAxis:{data:[{min:0}]},extra:{column:{type:"stack",width:30,activeBgColor:"#000000",activeBgOpacity:.08,labelPosition:"center",labelShow:!1}}},opts2:{color:["#3CA272","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],legend:{show:!1},padding:[15,15,15,5],touchMoveLimit:24,enableScroll:!0,xAxis:{disableGrid:!0,scrollShow:!0,itemCount:4},yAxis:{data:[{min:0}]},extra:{column:{type:"stack",width:30,activeBgColor:"#000000",activeBgOpacity:.08,labelPosition:"center"}}}}},onLoad:function(){},onReady:function(){this.search()},methods:{shownextList:function(){this.nextshow=!this.nextshow,this.chartMain=!this.chartMain},reset:function(){this.queryDateStart="",this.queryDateEnd="",this.stealType="00",this.curNow=0},init:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var a,i,r,n,s;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,uni.request({url:"http://127.0.0.1:".concat(l.default.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:l.default.token},data:{method:"PutHuaYun",token:l.default.token,uri:l.default.url,data:JSON.stringify({bizCode:l.default.bizCode,espFlowId:l.default.getUUID(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:l.default.getCurrentTimestamp(),espInformation:{service:"AseCommonController",method:"queryUserInfoByBody",data:{operatorId:null===(a=l.default.userInfo)||void 0===a?void 0:a.nameCode}}})}});case 3:i=e.sent,r=i.data,n=r.code,r.message,s=r.data,200===n&&(t.mgtOrgCode=s.mgtOrgCode),e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),uni.showToast({title:"网络错误，请稍后再试",icon:"none"});case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))()},search:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var a,i,r,n,s,c,d,h,u,p,f,x,g,v;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.testdev){e.next=16;break}t.chartShow=!0,t.dataShow=!1,t.shixiaolist=[{mgtOrgName:"市区供电公司",total:"125",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"市南供电公司",total:"224",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"浦东供电公司",total:"205",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"崇明供电公司",total:"13",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"长兴供电公司",total:"2",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"市北供电公司",total:"315",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"嘉定供电公司",total:"35",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"奉贤供电公司",total:"43",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"松江供电公司",total:"35",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"金山供电公司",total:"17",createNum:"0",dispatchNum:"0",checkNum:"0"},{mgtOrgName:"青浦供电公司",total:"23",createNum:"0",dispatchNum:"0",checkNum:"0"}],t.total=1037,t.createNum=0,t.dispatchNum=0,t.checkNum=0,a=["市区","市南","浦东","崇明","长兴","市北","嘉定","奉贤","松江","金山","青浦"],i=[0,0,0,0,0,0,0,0,0,0,0],r=[0,0,0,0,0,0,0,0,0,0,0],n=[0,0,0,0,0,0,0,0,0,0,0],s={categories:a,series:[{name:"检查超时",textColor:"#FFFFFF",data:i},{name:"生成超时",textColor:"#FFFFFF",data:r},{name:"派工超时",textColor:"#FFFFFF",data:n}]},t.chartData2=JSON.parse(JSON.stringify(s)),e.next=30;break;case 16:return e.prev=16,e.next=19,uni.request({url:"http://127.0.0.1:".concat(l.default.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:l.default.token},data:{method:"PutHuaYun",token:l.default.token,uri:l.default.url,data:JSON.stringify({bizCode:l.default.bizCode,espFlowId:l.default.getUUID(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:l.default.getCurrentTimestamp(),espInformation:{service:"AppOrderController",method:"queryTimelessStatus",data:{stealType:t.stealType,mgtOrgCode:t.mgtOrgCode,queryDateEnd:t.queryDateEnd,queryDateStart:t.queryDateStart}}})}});case 19:c=e.sent,d=c.data,h=d.code,d.message,u=d.data,200===h?(uni.showToast({title:"查询成功",icon:"success"}),t.chartShow=!0,t.dataShow=!1,t.shixiaolist=u.list,t.total=u.total,t.createNum=u.createNum,t.dispatchNum=u.dispatchNum,t.checkNum=u.checkNum,p=[],f=[],x=[],g=[],u.list.forEach((function(t){p.push(t.mgtOrgName.substring(0,2)),null==t.checkNum?f.push(0):f.push(Number(t.checkNum)),null==t.createNum?x.push(0):x.push(Number(t.createNum)),null==t.dispatchNum?g.push(0):g.push(Number(t.dispatchNum))})),v={categories:p,series:[{name:"检查超时",textColor:"#FFFFFF",data:f},{name:"生成超时",textColor:"#FFFFFF",data:x},{name:"派工超时",textColor:"#FFFFFF",data:g}]},t.chartData2=JSON.parse(JSON.stringify(v))):(t.chartShow=!1,t.dataShow=!0),e.next=30;break;case 24:e.prev=24,e.t0=e["catch"](16),uni.showToast({title:"网络错误，请稍后再试",icon:"none"}),console.log(e.t0),t.chartShow=!1,t.dataShow=!0;case 30:case"end":return e.stop()}}),e,null,[[16,24]])})))()},sectionChange:function(t){this.curNow=t,0==t?this.stealType="00":1==t?this.stealType="01":2==t&&(this.stealType="02")}}};e.default=s},"7d91":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{ref:"u-subsection",staticClass:"u-subsection",class:["u-subsection--"+t.mode],style:[t.$u.addStyle(t.customStyle),t.wrapperStyle]},[a("v-uni-view",{ref:"u-subsection__bar",staticClass:"u-subsection__bar",class:["button"===t.mode&&"u-subsection--button__bar",0===t.current&&"subsection"===t.mode&&"u-subsection__bar--first",t.current>0&&t.current<t.list.length-1&&"subsection"===t.mode&&"u-subsection__bar--center",t.current===t.list.length-1&&"subsection"===t.mode&&"u-subsection__bar--last"],style:[t.barStyle]}),t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,ref:"u-subsection__item--"+i,refInFor:!0,staticClass:"u-subsection__item",class:["u-subsection__item--"+i,i<t.list.length-1&&"u-subsection__item--no-border-right",0===i&&"u-subsection__item--first",i===t.list.length-1&&"u-subsection__item--last"],style:[t.itemStyle(i)],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler(i)}}},[a("v-uni-text",{staticClass:"u-subsection__item__text",style:[t.textStyle(i)]},[t._v(t._s(t.getText(e)))])],1)}))],2)},o=[]},"80ec":function(t,e,a){t.exports=a.p+"static/icons/nodata.jpg"},"81de":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],o={type:["pie","ring","rose","word","funnel","map","arcbar","line","column","mount","bar","area","radar","gauge","candle","mix","tline","tarea","scatter","bubble","demotype"],range:["饼状图","圆环图","玫瑰图","词云图","漏斗图","地图","圆弧进度条","折线图","柱状图","山峰图","条状图","区域图","雷达图","仪表盘","K线图","混合图","时间轴折线","时间轴区域","散点图","气泡图","自定义类型"],categories:["line","column","mount","bar","area","radar","gauge","candle","mix","demotype"],instance:{},option:{},formatter:{yAxisDemo1:function(t,e,a){return t+"元"},yAxisDemo2:function(t,e,a){return t.toFixed(2)},xAxisDemo1:function(t,e,a){return t+"年"},xAxisDemo2:function(t,e,a){return function(t,e){var a=new Date;a.setTime(1e3*t);var i=a.getFullYear(),o=a.getMonth()+1;o=o<10?"0"+o:o;var r=a.getDate();r=r<10?"0"+r:r;var n=a.getHours();n=n<10?"0"+n:n;var l=a.getMinutes(),s=a.getSeconds();return l=l<10?"0"+l:l,s=s<10?"0"+s:s,"full"==e?i+"-"+o+"-"+r+" "+n+":"+l+":"+s:"y-m-d"==e?i+"-"+o+"-"+r:"h:m"==e?n+":"+l:"h:m:s"==e?n+":"+l+":"+s:[i,o,r,n,l,s]}(t,"h:m")},seriesDemo1:function(t,e,a,i){return t+"元"},tooltipDemo1:function(t,e,a,i){return 0==a?"随便用"+t.data+"年":"其他我没改"+t.data+"天"},pieDemo:function(t,e,a,i){if(void 0!==e)return a[e].name+"："+a[e].data+"元"}},demotype:{type:"line",color:i,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"curve",width:2}}},pie:{type:"pie",color:i,padding:[5,5,5,5],extra:{pie:{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},ring:{type:"ring",color:i,padding:[5,5,5,5],rotate:!1,dataLabel:!0,legend:{show:!0,position:"right",lineHeight:25},title:{name:"收益率",fontSize:15,color:"#666666"},subtitle:{name:"70%",fontSize:25,color:"#7cb5ec"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},rose:{type:"rose",color:i,padding:[5,5,5,5],legend:{show:!0,position:"left",lineHeight:25},extra:{rose:{type:"area",minRadius:50,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF"}}},word:{type:"word",color:i,extra:{word:{type:"normal",autoColors:!1}}},funnel:{type:"funnel",color:i,padding:[15,15,0,15],extra:{funnel:{activeOpacity:.3,activeWidth:10,border:!0,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"}}},map:{type:"map",color:i,padding:[0,0,0,0],dataLabel:!0,extra:{map:{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#F04864",activeFillColor:"#FACC14",activeFillOpacity:1}}},arcbar:{type:"arcbar",color:i,title:{name:"百分比",fontSize:25,color:"#00FF00"},subtitle:{name:"默认标题",fontSize:15,color:"#666666"},extra:{arcbar:{type:"default",width:12,backgroundColor:"#E9E9E9",startAngle:.75,endAngle:.25,gap:2}}},line:{type:"line",color:i,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"straight",width:2,activeType:"hollow"}}},tline:{type:"line",color:i,padding:[15,10,0,15],xAxis:{disableGrid:!1,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{line:{type:"curve",width:2,activeType:"hollow"}}},tarea:{type:"area",color:i,padding:[15,10,0,15],xAxis:{disableGrid:!0,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{area:{type:"curve",opacity:.2,addLine:!0,width:2,gradient:!0,activeType:"hollow"}}},column:{type:"column",color:i,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}},mount:{type:"mount",color:i,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{mount:{type:"mount",widthRatio:1.5}}},bar:{type:"bar",color:i,padding:[15,30,0,5],xAxis:{boundaryGap:"justify",disableGrid:!1,min:0,axisLine:!1},yAxis:{},legend:{},extra:{bar:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08}}},area:{type:"area",color:i,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{area:{type:"straight",opacity:.2,addLine:!0,width:2,gradient:!1,activeType:"hollow"}}},radar:{type:"radar",color:i,padding:[5,5,5,5],dataLabel:!1,legend:{show:!0,position:"right",lineHeight:25},extra:{radar:{gridType:"radar",gridColor:"#CCCCCC",gridCount:3,opacity:.2,max:200,labelShow:!0}}},gauge:{type:"gauge",color:i,title:{name:"66Km/H",fontSize:25,color:"#2fc25b",offsetY:50},subtitle:{name:"实时速度",fontSize:15,color:"#1890ff",offsetY:-50},extra:{gauge:{type:"default",width:30,labelColor:"#666666",startAngle:.75,endAngle:.25,startNumber:0,endNumber:100,labelFormat:"",splitLine:{fixRadius:0,splitNumber:10,width:30,color:"#FFFFFF",childNumber:5,childWidth:12},pointer:{width:24,color:"auto"}}}},candle:{type:"candle",color:i,padding:[15,15,0,15],enableScroll:!0,enableMarkLine:!0,dataLabel:!1,xAxis:{labelCount:4,itemCount:40,disableGrid:!0,gridColor:"#CCCCCC",gridType:"solid",dashLength:4,scrollShow:!0,scrollAlign:"left",scrollColor:"#A6A6A6",scrollBackgroundColor:"#EFEBEF"},yAxis:{},legend:{},extra:{candle:{color:{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},average:{show:!0,name:["MA5","MA10","MA30"],day:[5,10,20],color:["#1890ff","#2fc25b","#facc14"]}},markLine:{type:"dash",dashLength:5,data:[{value:2150,lineColor:"#f04864",showLabel:!0},{value:2350,lineColor:"#f04864",showLabel:!0}]}}},mix:{type:"mix",color:i,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{disabled:!1,disableGrid:!1,splitNumber:5,gridType:"dash",dashLength:4,gridColor:"#CCCCCC",padding:10,showTitle:!0,data:[]},legend:{},extra:{mix:{column:{width:20}}}},scatter:{type:"scatter",color:i,padding:[15,15,0,15],dataLabel:!1,xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0},yAxis:{disableGrid:!1,gridType:"dash"},legend:{},extra:{scatter:{}}},bubble:{type:"bubble",color:i,padding:[15,15,0,15],xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0,max:250},yAxis:{disableGrid:!1,gridType:"dash",data:[{min:0,max:150}]},legend:{},extra:{bubble:{border:2,opacity:.5}}}},r=o;e.default=r},"8fd6":function(t,e,a){"use strict";a.r(e);var i=a("7d91"),o=a("d7a9");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("f7a4");var n=a("828b"),l=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"4a603381",null,!1,i["a"],void 0);e["default"]=l.exports},a852:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={qiunLoading:a("aaa0").default,qiunError:a("3ec2").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"chartsview",attrs:{id:"ChartBoxId"+t.cid}},[t.mixinDatacomLoading?a("v-uni-view",[a("qiun-loading",{attrs:{loadingType:t.loadingType}})],1):t._e(),t.mixinDatacomErrorMessage&&t.errorShow?a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reloading.apply(void 0,arguments)}}},[a("qiun-error",{attrs:{errorMessage:t.errorMessage}})],1):t._e(),t.echarts?[a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showchart,expression:"showchart"}],wxsProps:{"change:resize":"echartsResize","change:prop":"echartsOpts"},staticStyle:{width:"100%",height:"100%"},style:{background:t.background},attrs:{"data-directory":t.directory,id:"EC"+t.cid,prop:t.echartsOpts,"change:prop":t.rdcharts.ecinit,resize:t.echartsResize,"change:resize":t.rdcharts.ecresize}})]:[a("v-uni-view",{wxsProps:{"change:prop":"uchartsOpts"},attrs:{id:"UC"+t.cid,prop:t.uchartsOpts,"change:prop":t.rdcharts.ucinit},on:{mousemove:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseMove(e,t.$getComponentDescriptor())},mousedown:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseDown(e,t.$getComponentDescriptor())},mouseup:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseUp(e,t.$getComponentDescriptor())},touchstart:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchStart(e,t.$getComponentDescriptor())},touchmove:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchMove(e,t.$getComponentDescriptor())},touchend:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchEnd(e,t.$getComponentDescriptor())},click:function(e){e=t.$handleWxsEvent(e),t.rdcharts.tap(e,t.$getComponentDescriptor())}}},[a("v-uni-canvas",{directives:[{name:"show",rawName:"v-show",value:t.showchart,expression:"showchart"}],style:{width:t.cWidth+"px",height:t.cHeight+"px",background:t.background},attrs:{id:t.cid,canvasId:t.cid,"disable-scroll":t.disableScroll},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t._error.apply(void 0,arguments)}}})],1)]],2)},r=[]},b8b7:function(t,e,a){"use strict";a.r(e);var i=a("bf2b"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},bda1:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],o={type:["pie","ring","rose","funnel","line","column","area","radar","gauge","candle","demotype"],categories:["line","column","area","radar","gauge","candle","demotype"],instance:{},option:{},formatter:{tooltipDemo1:function(t){var e="";for(var a in t){0==a&&(e+=t[a].axisValueLabel+"年销售额");var i="--";null!==t[a].data&&(i=t[a].data),e+="\n"+t[a].seriesName+"："+i+" 万元"}return e},legendFormat:function(t){return"自定义图例+"+t},yAxisFormatDemo:function(t,e){return t+"元"},seriesFormatDemo:function(t){return t.name+"年"+t.value+"元"}},demotype:{color:i},column:{color:i,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"bar",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},line:{color:i,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},area:{color:i,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],areaStyle:{},label:{show:!0,color:"#666666",position:"top"}}},pie:{color:i,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:"50%",label:{show:!0,color:"#666666",position:"top"}}},ring:{color:i,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#666666",position:"top"},labelLine:{show:!0}}},rose:{color:i,title:{text:""},tooltip:{trigger:"item"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"pie",data:[],radius:"55%",center:["50%","50%"],roseType:"area"}},funnel:{color:i,title:{text:""},tooltip:{trigger:"item",formatter:"{b} : {c}%"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{bordercolor:"#fff",borderwidth:1},emphasis:{label:{fontSize:20}},data:[]}},gauge:{color:i,tooltip:{formatter:"{a} <br/>{b} : {c}%"},seriesTemplate:{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}]}},candle:{xAxis:{data:[]},yAxis:{},color:i,title:{text:""},dataZoom:[{type:"inside",xAxisIndex:[0,1],start:10,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:10,start:10,end:100}],seriesTemplate:{name:"",type:"k",data:[]}}},r=o;e.default=r},bf2b:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d4b5"),a("4626"),a("5ac7"),a("aa9c"),a("3efd");var o=i(a("fcf3")),r=i(a("5405")),n=i(a("81de")),l=i(a("bda1")),s={},c=null;function d(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length,a=new Array(e>1?e-1:0),i=1;i<e;i++)a[i-1]=arguments[i];for(var r in a)for(var n in a[r])a[r].hasOwnProperty(n)&&(t[n]=a[r][n]&&"object"===(0,o.default)(a[r][n])?d(Array.isArray(a[r][n])?[]:{},t[n],a[r][n]):a[r][n]);return t}function h(t,e){for(var a in t)t.hasOwnProperty(a)&&null!==t[a]&&"object"===(0,o.default)(t[a])?h(t[a],e):"format"===a&&"string"===typeof t[a]&&(t["formatter"]=e[t[a]]?e[t[a]]:void 0);return t}var u={data:function(){return{rid:null}},mounted:function(){var t=this;c={top:0,left:0};var e=document.querySelectorAll("uni-main")[0];void 0===e&&(e=document.querySelectorAll("uni-page-wrapper")[0]),c={top:e.offsetTop,left:e.offsetLeft},setTimeout((function(){null===t.rid&&t.$ownerInstance&&t.$ownerInstance.callMethod("getRenderType")}),200)},destroyed:function(){delete n.default.option[this.rid],delete n.default.instance[this.rid],delete l.default.option[this.rid],delete l.default.instance[this.rid]},methods:{ecinit:function(t,e,a,i){var r=JSON.stringify(t.id);this.rid=r,s[r]=this.$ownerInstance||i;var n=JSON.parse(JSON.stringify(t)),c=n.type;c&&l.default.type.includes(c)?l.default.option[r]=d({},l.default[c],n):l.default.option[r]=d({},n);var h=n.chartData;if(h){l.default.option[r].xAxis&&l.default.option[r].xAxis.type&&"category"===l.default.option[r].xAxis.type&&(l.default.option[r].xAxis.data=h.categories),l.default.option[r].yAxis&&l.default.option[r].yAxis.type&&"category"===l.default.option[r].yAxis.type&&(l.default.option[r].yAxis.data=h.categories),l.default.option[r].series=[];for(var u=0;u<h.series.length;u++){l.default.option[r].seriesTemplate=l.default.option[r].seriesTemplate?l.default.option[r].seriesTemplate:{};var p=d({},l.default.option[r].seriesTemplate,h.series[u]);l.default.option[r].series.push(p)}}if("object"===(0,o.default)(window.echarts))this.newEChart();else{var f=document.createElement("script"),x=window.location.origin,g=i.getDataset().directory;f.src=x+g+"uni_modules/qiun-data-charts/static/h5/echarts.min.js",f.onload=this.newEChart,document.head.appendChild(f)}},ecresize:function(t,e,a,i){l.default.instance[this.rid]&&l.default.instance[this.rid].resize()},newEChart:function(){var t=this.rid;void 0===l.default.instance[t]?(l.default.instance[t]=echarts.init(s[t].$el.children[0]),!0===l.default.option[t].ontap&&(l.default.instance[t].on("click",(function(e){var a=JSON.parse(JSON.stringify({x:e.event.offsetX,y:e.event.offsetY}));s[t].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:a,currentIndex:e.dataIndex,value:e.data,seriesName:e.seriesName,id:t}})})),l.default.instance[t].on("highlight",(function(e){s[t].callMethod("emitMsg",{name:"getHighlight",params:{type:"highlight",res:e,id:t}})}))),this.updataEChart(t,l.default.option[t])):this.updataEChart(t,l.default.option[t])},updataEChart:function(t,e){if(e=h(e,l.default.formatter),e.tooltip&&(e.tooltip.show=!!e.tooltipShow,e.tooltip.position=this.tooltipPosition(),"string"===typeof e.tooltipFormat&&l.default.formatter[e.tooltipFormat]&&(e.tooltip.formatter=e.tooltip.formatter?e.tooltip.formatter:l.default.formatter[e.tooltipFormat])),e.series)for(var a in e.series){var i=e.series[a].linearGradient;i&&(e.series[a].color=new echarts.graphic.LinearGradient(i[0],i[1],i[2],i[3],i[4]))}l.default.instance[t].setOption(e,e.notMerge),l.default.instance[t].on("finished",(function(){s[t].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:t}}),l.default.instance[t]&&l.default.instance[t].off("finished")})),"undefined"!=typeof s[t].$el.children[0].clientWidth&&(Math.abs(s[t].$el.children[0].clientWidth-l.default.instance[t].getWidth())>3||Math.abs(s[t].$el.children[0].clientHeight-l.default.instance[t].getHeight())>3)&&this.ecresize()},tooltipPosition:function(){return function(t,e,a,i,o){var r=t[0],n=t[1],l=o.viewSize[0],s=o.viewSize[1],c=o.contentSize[0],d=o.contentSize[1],h=r+30,u=n+30;return h+c>l&&(h=r-c-30),u+d>s&&(u=n-d-30),[h,u]}},ucinit:function(t,e,a,i){var o=this;if(JSON.stringify(t)!=JSON.stringify(e)&&t.canvasId){var r=JSON.parse(JSON.stringify(t.canvasId));this.rid=r,s[r]=this.$ownerInstance||i,n.default.option[r]=JSON.parse(JSON.stringify(t)),n.default.option[r]=h(n.default.option[r],n.default.formatter);var l=document.getElementById(r);l&&l.children[0]&&(n.default.option[r].context=l.children[0].getContext("2d"),n.default.instance[r]&&n.default.option[r]&&!0===n.default.option[r].update?this.updataUChart():setTimeout((function(){n.default.option[r].context.restore(),n.default.option[r].context.save(),o.newUChart()}),100))}},newUChart:function(){var t=this.rid;n.default.instance[t]=new r.default(n.default.option[t]),n.default.instance[t].addEventListener("renderComplete",(function(){s[t].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:t,opts:n.default.instance[t].opts}}),n.default.instance[t].delEventListener("renderComplete")})),n.default.instance[t].addEventListener("scrollLeft",(function(){s[t].callMethod("emitMsg",{name:"scrollLeft",params:{type:"scrollLeft",scrollLeft:!0,id:t,opts:n.default.instance[t].opts}})})),n.default.instance[t].addEventListener("scrollRight",(function(){s[t].callMethod("emitMsg",{name:"scrollRight",params:{type:"scrollRight",scrollRight:!0,id:t,opts:n.default.instance[t].opts}})}))},updataUChart:function(){var t=this.rid;n.default.instance[t].updateData(n.default.option[t])},tooltipDefault:function(t,e,a,i){if(e){var r=t.data;return"object"===(0,o.default)(t.data)&&(r=t.data.value),e+" "+t.name+":"+r}return t.properties&&t.properties.name?t.properties.name:t.name+":"+t.data},showTooltip:function(t,e){var a=this,i=n.default.option[e].tooltipCustom;if(i&&void 0!==i&&null!==i){var o=void 0;i.x>=0&&i.y>=0&&(o={x:i.x,y:i.y+10}),n.default.instance[e].showToolTip(t,{index:i.index,offset:o,textList:i.textList,formatter:function(t,i,o,r){return"string"===typeof n.default.option[e].tooltipFormat&&n.default.formatter[n.default.option[e].tooltipFormat]?n.default.formatter[n.default.option[e].tooltipFormat](t,i,o,r):a.tooltipDefault(t,i,o,r)}})}else n.default.instance[e].showToolTip(t,{formatter:function(t,i,o,r){return"string"===typeof n.default.option[e].tooltipFormat&&n.default.formatter[n.default.option[e].tooltipFormat]?n.default.formatter[n.default.option[e].tooltipFormat](t,i,o,r):a.tooltipDefault(t,i,o,r)}})},tap:function(t){var e=this.rid,a=n.default.option[e].ontap,i=n.default.option[e].tooltipShow,o=n.default.option[e].tapLegend;if(0!=a){var r,l,d=document.getElementById("UC"+e).getBoundingClientRect(),h={};h=t.detail.x?{x:t.detail.x-d.left,y:t.detail.y-d.top+c.top}:{x:t.clientX-d.left,y:t.clientY-d.top+c.top},t.changedTouches=[],t.changedTouches.unshift(h),r=n.default.instance[e].getCurrentDataIndex(t),l=n.default.instance[e].getLegendDataIndex(t),!0===o&&n.default.instance[e].touchLegend(t),1==i&&this.showTooltip(t,e),s[e].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:h,currentIndex:r,legendIndex:l,id:e,opts:n.default.instance[e].opts}})}},touchStart:function(t){var e=this.rid,a=n.default.option[e].ontouch;0!=a&&(!0===n.default.option[e].enableScroll&&1==t.touches.length&&n.default.instance[e].scrollStart(t),s[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"touchStart",event:t.changedTouches[0],id:e,opts:n.default.instance[e].opts}}))},touchMove:function(t){var e=this.rid,a=n.default.option[e].ontouch;if(0!=a){if(!0===n.default.option[e].enableScroll&&1==t.changedTouches.length&&n.default.instance[e].scroll(t),!0===n.default.option[e].ontap&&!1===n.default.option[e].enableScroll&&!0===n.default.option[e].onmovetip){var i=document.getElementById("UC"+e).getBoundingClientRect(),o={x:t.changedTouches[0].clientX-i.left,y:t.changedTouches[0].clientY-i.top+c.top};t.changedTouches.unshift(o),!0===n.default.option[e].tooltipShow&&this.showTooltip(t,e)}!0===a&&!0===n.default.option[e].enableScroll&&!0===n.default.option[e].onzoom&&2==t.changedTouches.length&&n.default.instance[e].dobuleZoom(t),s[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"touchMove",event:t.changedTouches[0],id:e,opts:n.default.instance[e].opts}})}},touchEnd:function(t){var e=this.rid,a=n.default.option[e].ontouch;0!=a&&(!0===n.default.option[e].enableScroll&&0==t.touches.length&&n.default.instance[e].scrollEnd(t),s[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"touchEnd",event:t.changedTouches[0],id:e,opts:n.default.instance[e].opts}}))},mouseDown:function(t){var e=this.rid,a=n.default.option[e].onmouse;if(0!=a){var i,o=document.getElementById("UC"+e).getBoundingClientRect();i={x:t.clientX-o.left,y:t.clientY-o.top+c.top},t.changedTouches=[],t.changedTouches.unshift(i),n.default.instance[e].scrollStart(t),n.default.option[e].mousedown=!0,s[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"mouseDown",event:i,id:e,opts:n.default.instance[e].opts}})}},mouseMove:function(t){var e=this.rid,a=n.default.option[e].onmouse,i=n.default.option[e].tooltipShow;if(0!=a){var o,r=document.getElementById("UC"+e).getBoundingClientRect();o={x:t.clientX-r.left,y:t.clientY-r.top+c.top},t.changedTouches=[],t.changedTouches.unshift(o),n.default.option[e].mousedown?(n.default.instance[e].scroll(t),s[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"mouseMove",event:o,id:e,opts:n.default.instance[e].opts}})):n.default.instance[e]&&1==i&&this.showTooltip(t,e)}},mouseUp:function(t){var e=this.rid,a=n.default.option[e].onmouse;if(0!=a){var i,o=document.getElementById("UC"+e).getBoundingClientRect();i={x:t.clientX-o.left,y:t.clientY-o.top+c.top},t.changedTouches=[],t.changedTouches.unshift(i),n.default.instance[e].scrollEnd(t),n.default.option[e].mousedown=!1,s[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"mouseUp",event:i,id:e,opts:n.default.instance[e].opts}})}}}};e.default=u},c4bf:function(t,e,a){"use strict";var i=a("1202"),o=a.n(i);o.a},cdca:function(t,e,a){var i=a("2a4c6");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("06f42b2c",i,!0,{sourceMap:!1,shadowMode:!1})},d296:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var i={props:{list:{type:Array,default:uni.$u.props.subsection.list},current:{type:[String,Number],default:uni.$u.props.subsection.current},activeColor:{type:String,default:uni.$u.props.subsection.activeColor},inactiveColor:{type:String,default:uni.$u.props.subsection.inactiveColor},mode:{type:String,default:uni.$u.props.subsection.mode},fontSize:{type:[String,Number],default:uni.$u.props.subsection.fontSize},bold:{type:Boolean,default:uni.$u.props.subsection.bold},bgColor:{type:String,default:uni.$u.props.subsection.bgColor},keyName:{type:String,default:uni.$u.props.subsection.keyName}}};e.default=i},d7a9:function(t,e,a){"use strict";a.r(e);var i=a("013c"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},ec2a:function(t,e,a){"use strict";a.r(e);var i=a("a852"),o=a("b8b7");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);var n=a("44cb");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("c4bf");var l=a("828b");o["default"].__module="rdcharts";var s=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"3dd6a45f",null,!1,i["a"],o["default"]);e["default"]=s.exports},f7a4:function(t,e,a){"use strict";var i=a("72f8"),o=a.n(i);o.a}}]);