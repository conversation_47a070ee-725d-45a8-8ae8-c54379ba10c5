<template>
	<view class="line-loss-content">
		<!-- 内容区域 - 线损数据 -->
		<scroll-view 
			class="content-section" 
			scroll-y="true"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			:style="{ height: contentHeight + 'px' }"
		>
			<!-- 加载中提示 -->
			<!-- <view class="loading-container" v-if="isLoading && !isRefreshing">
				<u-loading-icon mode="circle" size="28" color="#07ac7c"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view> -->
			
			<!-- 线损数据内容 -->
			<view class="line-loss-data" v-if="!isLoading || isRefreshing">
				
				<!-- 用户用电量与线损率图表 -->
				<view class="chart-container">
					<view class="chart-header">
						<text class="chart-title">用户用电量与线损率</text>
					</view>
					<view class="chart-content">
						<qiun-data-charts 
							type="mix"
							:opts="chartOpts"
							:chartData="chartData"
							:canvas2d="true"
							:ontouch="true"
							canvasId="lineLossChart"
						/>
					</view>
				</view>
				
				<!-- 数据表格 -->
				<view class="data-table">
					<!-- <view class="table-row">
						<view class="table-cell label">日期</view>
						<view class="table-cell value">{{ date }}</view>
					</view> -->
					<view class="table-row">
						<view class="table-cell label">用户编号</view>
						<view class="table-cell value highlight">{{ lineLossData.custNo }}</view>
					</view>
					<view class="table-row">
						<view class="table-cell label">用户名称</view>
						<view class="table-cell value">{{ lineLossData.custName }}</view>
					</view>
					<view class="table-row">
						<view class="table-cell label">台区编号</view>
						<view class="table-cell value">{{ lineLossData.tgNo }}</view>
					</view>
					<view class="table-row">
						<view class="table-cell label">台区名称</view>
						<view class="table-cell value">{{ lineLossData.tgName }}</view>
					</view>
					<view class="table-row">
						<view class="table-cell label">台区用户数</view>
						<view class="table-cell value">{{ lineLossData.userCount }}</view>
					</view>
					<view class="table-row">
						<view class="table-cell label">台区低压表计数</view>
						<view class="table-cell value">{{ lineLossData.lowUserCount }}</view>
					</view>
					<view class="table-row">
						<view class="table-cell label">台区总容量 (KVA)</view>
						<view class="table-cell value highlight-orange">{{ lineLossData.contractCapCount }}</view>
					</view>
					<view class="table-row">
						<view class="table-cell label">台区供电量</view>
						<view class="table-cell value highlight-orange">{{ lineLossData.ppq }}</view>
					</view>
				</view>
			</view>
			
			<!-- 无数据提示 -->
			<view class="empty-container" v-if="!isLoading && !isRefreshing && !hasData">
				<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
			</view>
			
			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		components: {
			qiunDataCharts: () => import('@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue')
		},
		props: {
			queryParams: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				isRefreshing: false,
				isLoading: true,
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				tabNavHeight: 50,
				hasData: true, // 控制是否有数据
				date:"",
				token:null,
				// 图表配置
				chartOpts: {
					color: ['#1890FF', '#00C389'], // 使用更鲜明的绿色
					padding: [5, 10, 15, 10], // 优化内边距分布
					enableScroll: true, // 启用水平滚动功能
					legend: {
						show: true,
						position: 'top',
						float: 'center',
						padding: 5,
						margin: 5,
						fontSize: 12,
						lineHeight: 16,
						fontColor: '#666666',
						backgroundColor: 'rgba(0,0,0,0)',
						borderColor: 'rgba(0,0,0,0)',
						itemGap: 20 // 增加图例项之间的间距
					},
					xAxis: {
						disableGrid: true,
						scrollShow: true, // 显示滚动条
						itemCount: 3, // 一屏显示的数据点数量
						fontSize: 12,
						fontColor: '#999999' // 设置字体颜色
					},
					yAxis: {
						data: [
							{
								// 左Y轴 - 用户电量
								position: 'left',
								// min: 0,
								// max: 0, // 调整为6，确保能显示所有数据点
								title: '用户电量',
								titleFontSize: 13,
								titleFontColor: '#07ac7c',
								fontSize: 12,
								fontColor: '#999999', // 设置字体颜色
								format: (val) => {
									return val.toFixed(1); // 显示一位小数，更精确
								}
							},
							{
								// 右Y轴 - 台区线损率
								position: 'right',
								// min: 0,
								// max: 0,
								title: '线损率',
								titleFontSize: 13,
								titleFontColor: '#1890FF',
								fontSize: 12,
								fontColor: '#999999', // 设置字体颜色
								format: (val) => {
									return val.toFixed(1); // 保留一位小数
								}
							}
						],
						showTitle: true,
						gridType: 'dash',
						dashLength: 4,
						splitNumber: 5,
						gridColor: '#eeeeee' // 设置网格线颜色
					},
					extra: {
						mix: {
							column: {
								width: 18, // 调整柱状图宽度，使其更协调
								radius: 4, // 添加圆角，增加美感
								linearType: 'none', // 使用纯色，不使用渐变
								barBorderCircle: false, // 柱状图顶部是否为半圆
							}
						},
						line: {
							type: 'straight', // 直线而非曲线
							width: 2.5, // 增加线宽，提高可见性
							activeType: 'hollow', // 激活时显示为空心点
							linePosition: 'top' // 确保折线图在柱状图上方
						},
						tooltip: {
							showBox: true,
							showArrow: true,
							bgColor: '#000000',
							bgOpacity: 0.7,
							borderRadius: 4,
							borderWidth: 0
						}
					},
					background: '#FFFFFF', // 白色背景
					dataLabel: false, // 不显示数据标签
					dataPointShape: true, // 显示数据点形状
					dataPointShapeType: 'solid' // 实心数据点
				},
				
				// 图表数据
				chartData: {
					categories: [],
					series: [
						{
							name: '用户电量',
							data: [],
							type: 'column',
							color: '#07ac7c',
							index: 0
						},
						{
							name: '台区线损率',
							data: [],
							type: 'line',
							color: '#1890FF',
							index: 1,
							yAxisIndex: 1 // 使用右侧Y轴
						}
					]
				},
				
				// 线损数据
				lineLossData: {},
				isMockData:true
			};
		},
		onLoad() {
			this.loadLineLossData();
		},
		mounted() {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
		},
		// 在组件被keep-alive激活时调用
		activated() {
			console.log('线损数据组件被激活');
			// 组件被激活时重新加载数据
			this.loadLineLossData();
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token
				});
			},
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏 + 标签导航）
				const topHeight = this.statusBarHeight + this.navbarHeight + this.tabNavHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight;
			},
			
			// 获取有效的查询参数（优先使用queryParams对象，其次从本地存储获取）
			getEffectiveQueryParams() {
				// 如果有传入的queryParams，优先使用
				console.log('11',this.queryParams)
				
				if (this.queryParams && Object.keys(this.queryParams).length > 0) {
					return { ...this.queryParams };
				}
				
				// 否则，尝试从本地存储获取
				const savedQueryParams = uni.getStorageSync('userDataQueryParams');
				if (savedQueryParams && Object.keys(savedQueryParams).length > 0) {
					return { ...savedQueryParams };
				}
				
				// 如果都没有，返回空对象
				return {};
			},
			
			// 加载线损数据
			loadLineLossData() {
				// 如果是下拉刷新，不设置isLoading，避免闪烁
				if (!this.isRefreshing) {
					this.isLoading = true;
				}
				uni.showLoading({
					title:'加载中...'
				})
				// 获取有效的查询参数
				const requestParams = this.getEffectiveQueryParams();
				console.log('线损参数',requestParams)
				// API请求参数构建
				const params = {
					custNo: requestParams.custNo || '',
					custName: requestParams.custName || '',
					meterAssetNo: requestParams.meterAssetNo || '',
					pageNum: 1,
					pageSize: 10
				};
				
				// 添加日期范围参数（如果有）
				if (requestParams.dateRange && requestParams.dateRange.length > 0) {
					const formatDate = date => {
						if (!date) return '';
						const d = new Date(date);
						const year = d.getFullYear();
						const month = String(d.getMonth() + 1).padStart(2, '0');
						const day = String(d.getDate()).padStart(2, '0');
						return `${year}-${month}-${day}`;
					};
					
					params.startDate = formatDate(requestParams.dateRange[0]);
					params.endDate = formatDate(requestParams.dateRange[requestParams.dateRange.length - 1]);
				}
				const startDate = new Date(requestParams.dateRange[0]);
				const endDate = new Date(requestParams.dateRange[1]);
				const dateList = [];
				let curentDate = new Date(startDate);
				while(curentDate <= endDate) {
					dateList.push(new Date(curentDate).toISOString().split('T')[0]);
					curentDate.setDate(curentDate.getDate()+1)
				}
				console.log(dateList)
				if(this.isMockData) {
					this.lineLossData = {
					  "statDate": "2025/4/22",
					  "orgName": "嘉定营销分中心",
					  "custNo": "3100030169550",
					  "custName": "顾伟弟",
					  "meterAssetNo": "110007839864",
					  "elecAddr": "火车站路151弄3号101室",
					  "tgName": "车站路四号站_配变",
					  "tgNo": "94231_00",
					  "tgId": "10001727465",
					  "contractCapCount": "",
					  "userCount": "1",
					  "lowUserCount": "1"
					}
					this.date = this.lineLossData.statDate;
					let dateList2 = [
					{
					"llRate": "0.95",
					"ppq": "857.6",
					"statDate": "2025-07-12"
					},
					{
					"llRate": "1.03",
					"ppq": "923.2",
					"statDate": "2025-07-13"
					}
					]
					this.chartData.series[0].data = dateList2.map((item) => item.ppq);
					this.chartData.series[1].data = dateList2.map((item) => item.llRate);
					this.lineLossData.ppq = this.chartData.series[0].data.reduce((acc,num) => acc+Number(num),0).toFixed(2);
					this.chartData.categories = dateList2.map((item) => item.statDate);
					this.isLoading = false;
					setTimeout(()=> {
						this.isRefreshing = false;
					},500)
					uni.hideLoading()
				}else{
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"DtsUserController",
									"method":"getUserLoss",
									"data": {
										"custNo":requestParams.custNo||"",
										"tgNo":requestParams.tgNo||"",
										"statDateStart":requestParams.dateRange[0],
										"statDateEnd": requestParams.dateRange[1]
									}
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
								this.lineLossData = rtnData.data;
								const dateTodataMap = new Map();
								if(rtnData?.data?.llRateList.length) {
									rtnData.data.llRateList.forEach((item) => {
										dateTodataMap.set(item.statDate,item)
									})
									this.chartData.series[0].data = dateList.map((date) => {
										const matchData = dateTodataMap.get(date);
										return matchData?matchData.ppq:0
									})
									this.chartData.series[1].data = dateList.map((date) => {
										const matchData = dateTodataMap.get(date);
										return matchData?matchData.llRate:0
									})
									this.lineLossData.ppq = this.chartData.series[0].data.reduce((acc,num) => acc+Number(num),0).toFixed(2);
								}
								this.chartData.categories = dateList;
								this.date = requestParams.dateRangeText;
								console.log(this.chartData.series)
								this.isLoading = false;
								this.isRefreshing = false;
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
							uni.hideLoading()
						},
						fail: (error) => {
							// 停止加载状态
							this.isLoading = false;
							uni.hideLoading()
							// 如果是刷新操作，处理刷新状态
							if (this.isRefreshing) {
								this.isRefreshing = false;
							}
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				// 重新加载数据
				this.loadLineLossData();
			}
		}
	}
</script>

<style lang="scss" scoped>
.line-loss-content {
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
	width: 100%;
	height: 100%;
}

/* 内容区域 */
.content-section {
	box-sizing: border-box;
	background-color: #f5f5f5;
	padding: 10px 15px;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
	overflow-y: auto;
	width: 100%;
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 0;
	margin-bottom: 10px;
	background-color: #fff;
	border-radius: 8px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.loading-text {
	font-size: 14px;
	color: #8c8c8c;
	margin-top: 10px;
	text-align: center;
}

/* 线损数据内容 */
.line-loss-data {
	animation: fade-in 0.3s ease;
}

@keyframes fade-in {
	from {
		opacity: 0;
		transform: translateY(5px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 图表容器 */
.chart-container {
	background-color: #fff;
	border-radius: 12px; /* 增加圆角 */
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); /* 优化阴影效果 */
	overflow: hidden;
	margin-bottom: 15px;
}

.chart-header {
	padding: 15px 18px; /* 增加内边距 */
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.chart-title {
	font-size: 16px; /* 增大字体 */
	color: #333;
	font-weight: 500;
}

.chart-content {
	width: 100%;
	height: 280px; /* 增加图表高度，提供更多空间 */
	position: relative;
}

/* 数据表格 */
.data-table {
	background-color: #fff;
	border-radius: 8px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
	overflow: hidden;
	margin-bottom: 10px;
}

.table-row {
	display: flex;
	border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
	border-bottom: none;
}

.table-cell {
	padding: 10px 15px;
	font-size: 14px;
}

.label {
	// flex: 0 0 100px;
	color: #8c8c8c;
}

.value {
	flex: 1;
	color: #262626;
	text-align: right;
	overflow: hidden;      /* 隐藏超出内容 */
   text-overflow: ellipsis; /* 显示省略号 */
   white-space: nowrap;
}

.highlight {
	color: #07ac7c;
}

.highlight-orange {
	color: #ffa500;
}

/* 无数据提示 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 0;
	background-color: #fff;
	border-radius: 8px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
	animation: fade-in 0.3s ease;
}

.empty-text {
	font-size: 14px;
	color: #999;
	margin-top: 10px;
}

/* 底部间距 */
.bottom-space {
	height: 20px;
}
</style>
