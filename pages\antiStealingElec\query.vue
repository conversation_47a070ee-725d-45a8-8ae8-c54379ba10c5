<template>
	<view class="user-data-query">
		<!-- 顶部导航区域 - 固定 -->
		<view class="header-section">
			<custom-navbar title="查询" :path="path">
			</custom-navbar>
		</view>
		
		<!-- 中间表单区域 - 可滚动 -->
		<scroll-view class="form-section"  scroll-y :style="{ height: scrollViewHeight + 'px', top: safeAreaTop + 'px' }">
			<view class="form-content">
				<!-- 表单项 -->
				<view class="form-item">
					<view class="form-label">供电单位</view>
					<view class="select-wrapper" @touchend.prevent="showOrgPicker">
						<u-input
							v-model="formData.mgtOrgName"
							placeholder="请选择"
							border="surround"
							suffixIcon="arrow-down"
							readonly
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
						></u-input>
					</view>
				</view>
				<view class="form-item">
					<view class="form-label">日期</view>
					<view class="date-input-wrapper">
						<!-- 日期选择器弹窗 -->
						<uni-datetime-picker v-model="dateRange" 
						type="daterange" 
						:start="minDate"
						:end="maxDate"
						rangeSeparator="至"
						@change="confirmDate"
						:border="true"
						:clear-icon="true"
						:placeholder="'请选择日期'"
						@maskClick="calendarShow =false"></uni-datetime-picker>
					</view>
				</view>
				<view class="form-item">
					<view class="form-label">台区编号</view>
					<u-input
						v-model="formData.resrcSuplCode"
						placeholder="请输入台区编号"
						border="surround"
						:clearable="true"
						fontSize="30rpx"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<view class="form-item">
					<view class="form-label">台区名称</view>
					<u-input
						v-model="formData.resrcSuplName"
						placeholder="请输入台区名称"
						border="surround"
						:clearable="true"
						fontSize="30rpx"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<view class="form-item">
					<view class="form-label">用户编号</view>
					<u-input
						v-model="formData.custNo"
						placeholder="请输入用户编号"
						border="surround"
						:clearable="true"
						fontSize="30rpx"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<view class="form-item">
					<view class="form-label">用户名称</view>
					<u-input
						v-model="formData.custName"
						placeholder="请输入用户名称"
						border="surround"
						:clearable="true"
						fontSize="30rpx"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<view class="form-item">
					<view class="form-label">隐患类别</view>
					<view class="select-wrapper" @touchend.prevent="showDangerTypePicker">
						<u-input
							v-model="dangerTypeName"
							placeholder="请选择"
							border="surround"
							suffixIcon="arrow-down"
							readonly
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
						></u-input>
					</view>
				</view>
				
				<view class="form-item">
					<view class="form-label">改造状态</view>
					<view class="select-wrapper" @touchend.prevent="showStatusPicker" >
						<u-input
							v-model="retrofitStatusName"
							placeholder="请选择"
							border="surround"
							suffixIcon="arrow-down"
							readonly
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
						></u-input>
					</view>
				</view>
				
				<!-- <view class="form-item">
					<view class="form-label">工单类型</view>
					<view class="select-wrapper" @touchend.prevent="showWorkTypePicker">
						<u-input
							v-model="orderTypeName"
							placeholder="请选择"
							border="surround"
							suffixIcon="arrow-down"
							readonly
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
						></u-input>
					</view>
				</view> -->
				
				<!-- <view class="form-item">
					<view class="form-label">超时工单</view>
					<view class="radio-group">
						<view class="radio-item" @click="selectTimeoutOption('1')">
							<view class="radio-circle" :class="{ active: formData.timeout === '1' }">
								<view v-if="formData.timeout === '1'" class="radio-inner"></view>
							</view>
							<text class="radio-label">是</text>
						</view>
						<view class="radio-item" @click="selectTimeoutOption('0')">
							<view class="radio-circle" :class="{ active: formData.timeout === '0' }">
								<view v-if="formData.timeout === '0'" class="radio-inner"></view>
							</view>
							<text class="radio-label">否</text>
						</view>
					</view>
				</view> -->
				
				<!-- 预留空间，确保小屏幕上也能滚动到底部的所有内容 -->
				<view class="form-space"></view>
			</view>
		</scroll-view>
		
		<!-- 底部按钮区域 - 固定 -->
		<view class="footer-section">
			<view class="btn-group">
				<view class="btn btn-reset" @click="resetForm">
					<text>重置选择</text>
				</view>
				<view class="btn btn-confirm" @click="submitForm">
					<text>确定</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		data() {
			return {
				statusBarHeight: 0,
				navbarHeight: 44, // 导航栏固定高度，单位px
				headerHeight: 0, // 导航栏总高度，单位px
				footerHeight: 140, // 底部按钮区域高度，单位rpx
				windowHeight: 0,
				scrollViewHeight: 0,
				path:window.location.hash,
				safeAreaTop: 0, // 顶部安全区域高度，单位px
				maxDate: '', // 最大日期（当前日期）
				minDate: '', // 最小日期（当前日期前1个月）
				dateRangeText:"",
				formData: {
					mgtOrgName: '上海市电力公司', // 供电单位
					mgtOrgCode:'31102',
					resrcSuplCode: '', // 台区编号
					resrcSuplName: '', // 台区名称
					custNo: '', // 用户编号
					custName: '', // 用户名称
					dangerType: '01', // 隐患类别
					retrofitStatus: '', // 改造状态
					orderType: '', // 工单类型
					timeout: '', // 超时工单
					retrofitStartTime:"",
					retrofitEndTime:""
				},
				dateRange:[],
				dangerTypeName:"",//隐患分析名字
				retrofitStatusName:"",//改造状态名称
				orderTypeName:"",//工单类型名称
				// 选择器相关数据
				orgOptions: [
					{ text: '浦东供电公司', value: '01' },
					{ text: '市北供电公司', value: '02' },
					{ text: '市南供电公司', value: '03' }
				],
				dangerTypeOptions: [
					{ text: '计量装置', value: '01' },
					{ text: '高低压配电线路及设备', value: '02' }
				],
				statusOptions: [
					{ text: '待填报', value: '01' },
					{ text: '改造中', value: '02' },
					{ text: '已完成', value: '03' }
				],
				workTypeOptions: [
					{ text: '窃电', value: '01' },
					{ text: '违约用电', value: '02' },
					{ text: '无协议用电', value: '03' },
					{ text: '窃电及违约用电', value: '04' },
					{ text: '无违约用电', value: '05' },
					{ text: '其他', value: '99' },
				],
				
				// 弹出选择器相关
				showOrgSelector: false,
				showDangerTypeSelector: false,
				showStatusSelector: false,
				showWorkTypeSelector: false,
				
				// 输入框样式
				inputStyle: {
					height: '80rpx',
				},
				fromPage:'',
				username:"",
				isMockData:true,
				nameCode:'',
				token:null
			};
		},
		onLoad(options) {
			// 获取状态栏高度
			this.getStatusBarHeight();
			
			// 尝试加载之前保存的查询参数
			try {
				const savedParams = uni.getStorageSync('antiStealingElec_queryParams');
				if (savedParams) {
					console.log('加载已保存的查询参数:', savedParams);
					this.formData = {...savedParams};
				}
			} catch (e) {
				console.error('获取查询参数失败:', e);
			}
			this.fromPage = options.fromPage || '';
			this.setupCityEvent();
			if(!this.isMockData) {
				this.init();
			}
			
		},
		onReady() {
			// 计算滚动区域高度
			this.calcScrollViewHeight();
		},
		onUnload() {
			uni.$off('citySelected',this.handleCitySelected)
		},
		methods: {
			init2() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
					vm.nameCode = data?.nameCode;
				});
			},
			init() {
				try {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode": bizCode,
								"espFlowId": getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryUserInfoByBody",
									"data": {
										"operatorId":this.nameCode,
									}
								}
							})
						},
						success: (res) => {
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									this.formData.mgtOrgCode = rtnData.data.mgtOrgCode;
									this.formData.mgtOrgName = rtnData.data.mgtOrgName;
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				}
			},
			// 获取状态栏高度
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
				
				// 计算头部总高度（导航栏）
				this.headerHeight = this.statusBarHeight + this.navbarHeight;
				this.safeAreaTop = this.headerHeight;
			},
			
			// 计算滚动区域高度
			calcScrollViewHeight() {
				// 底部区域高度从rpx转为px进行计算
				const footerHeightPx = uni.upx2px(this.footerHeight);
				this.scrollViewHeight = this.windowHeight - this.headerHeight - footerHeightPx;
				console.log('计算高度:', {
					windowHeight: this.windowHeight,
					headerHeight: this.headerHeight,
					footerHeightPx: footerHeightPx,
					scrollViewHeight: this.scrollViewHeight
				});
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			confirmDate(e) {
				this.updateDateRangeText(e)
			},
			updateDateRangeText(dateRange) {
				if(dateRange && dateRange.length>=2){
					const formatDate = date => {
						if (!date) return '';
						const d = new Date(date);
						const year = d.getFullYear();
						const month = String(d.getMonth() + 1).padStart(2, '0');
						const day = String(d.getDate()).padStart(2, '0');
						return `${year}-${month}-${day}`;
					};
					const startDate = formatDate(dateRange[0]);
					const endDate = formatDate(dateRange[1]);
					this.dateRangeText = `${startDate} 至 ${endDate}`;
				}else{
					this.dateRangeText = '';
				}
			},
			// 选择供电单位
			showOrgPicker() {
				// 实现供电单位选择
				uni.navigateTo({
					url:'/pages/antiStealingElec/city'
				})
			},
			setupCityEvent() {
				uni.$on('citySelected',this.handleCitySelected)
			},
			handleCitySelected(data) {
				console.log(data)
				setTimeout(()=> {
					this.formData.mgtOrgName = data.district.shortName;
					this.formData.mgtOrgCode = data.district.mgtOrgCode;
				},500)
				
			},
			// 选择隐患类别
			showDangerTypePicker() {
				uni.showActionSheet({
					itemList: this.dangerTypeOptions.map(item => item.text),
					success: res => {
						const index = res.tapIndex;
						this.formData.dangerType = this.dangerTypeOptions[index].value;
						this.dangerTypeName = this.dangerTypeOptions[index].text;
					}
				});
			},
			
			// 选择改造状态
			showStatusPicker() {
				uni.showActionSheet({
					itemList: this.statusOptions.map(item => item.text),
					success: res => {
						const index = res.tapIndex;
						this.formData.retrofitStatus = this.statusOptions[index].value;
						this.retrofitStatusName = this.statusOptions[index].text;
					}
				});
			},
			
			// 选择工单类型
			showWorkTypePicker() {
				uni.showActionSheet({
					itemList: this.workTypeOptions.map(item => item.text),
					success: res => {
						const index = res.tapIndex;
						this.formData.orderType = this.workTypeOptions[index].value;
						this.orderTypeName = this.workTypeOptions[index].text;
					}
				});
			},
			
			// 选择超时工单选项
			selectTimeoutOption(value) {
				this.formData.timeout = value;
			},
			
			// 重置表单
			resetForm() {
				this.formData = {
					mgtOrgName: '上海市电力公司',
					mgtOrgCode:"31102",
					resrcSuplCode: '',
					resrcSuplName: '',
					custNo: '',
					custName: '',
					dangerType: '',
					status: '',
					workType: '',
					timeout: '',
					retrofitStartTime:"",
					retrofitEndTime:""
				};
				this.dateRange = [];
				this.dateRangeText = ""
			},
			
			// 提交表单
			submitForm() {
				// 表单验证 - 这里简化处理，允许所有字段为空
				if(!this.dateRange || this.dateRange.length<2) {
					uni.showToast({
						title: '请选择日期',
						icon: 'none'
					});
					return;
				}
				if(!this.formData.mgtOrgCode) {
					uni.showToast({
						title: '请选择管理单位',
						icon: 'none'
					});
					return;
				}
				this.formData.retrofitStartTime = this.dateRange[0];
				this.formData.retrofitEndTime = this.dateRange[1];
				
				// 模拟查询请求
				uni.showLoading({
					title: '查询中...'
				});
				uni.setStorageSync('antiStealingElec_queryParams', this.formData);
				setTimeout(() => {
					uni.hideLoading();
					const storePage =  uni.getStorageSync('antiStealingElec_fromPage') || '';
					const effectivePage = storePage || this.fromPage
					if(effectivePage === 'list') {
						uni.navigateBack({
							delta: 1,
							success: () => {
								// 通知列表页面刷新数据
								const pages = getCurrentPages();
								const prevPage = pages[pages.length - 1]; // 上一个页面已经变成当前页面
								if (prevPage && prevPage.$vm) {
									// 调用上一个页面的刷新方法
									prevPage.$vm.onRefresh && prevPage.$vm.onRefresh();
								}
							}
						});
					}else{
						// const pages = getCurrentPages();
						// console.log(pages)
						// if(pages.length>1) {
						// 	const prevPage = pages[pages.length - 2]; 
						// 	const prevPageUrl = prevPage.route;
						// 	if(prevPageUrl.includes('antiStealingElec/list')) {
						// 		uni.navigateBack({
						// 			delta: 1,
						// 			success: () => {
						// 				// 通知列表页面刷新数据
						// 				const pages = getCurrentPages();
						// 				const prevPage = pages[pages.length - 1]; // 上一个页面已经变成当前页面
						// 				if (prevPage && prevPage.$vm) {
						// 					// 调用上一个页面的刷新方法
						// 					prevPage.$vm.onRefresh && prevPage.$vm.onRefresh();
						// 				}
						// 			}
						// 		});
						// 	}else{
						// 		uni.navigateTo({
						// 			url: `/pages/antiStealingElec/statistics`
						// 		});
						// 	}
						// }else {
							uni.navigateTo({
								url: `/pages/antiStealingElec/statistics`
							});
						// }
					}
					uni.removeStorageSync('antiStealingElec_fromPage')
				}, 1500);
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #fff;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.user-data-query {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	position: relative;
}

/* 顶部导航区域 */
.header-section {
	background-color: #00C389;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

/* 中间表单区域 */
.form-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 180rpx; /* 底部按钮区域高度 */
	box-sizing: border-box;
	background-color: #fff;
	z-index: 99;
}

.form-content {
	padding: 30rpx 40rpx;
	background-color: #fff;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 32rpx;
	color: #262626;
	margin-bottom: 16rpx;
	font-weight: 400;
}

.form-input {
	height: 80rpx;
}

.select-wrapper {
	width: 100%;
	position: relative;
	cursor: pointer;
}

/* 单选按钮组 */
.radio-group {
	display: flex;
	flex-direction: row;
	margin-top: 20rpx;
}

.radio-item {
	display: flex;
	align-items: center;
	margin-right: 60rpx;
}

.radio-circle {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	border: 1rpx solid #ddd;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}

.radio-circle.active {
	border-color: #00C389;
}

.radio-inner {
	width: 18rpx;
	height: 18rpx;
	border-radius: 50%;
	background-color: #00C389;
}

.radio-label {
	font-size: 28rpx;
	color: #333;
}

/* 底部按钮区域 */
.footer-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	z-index: 50;
	border-top: 1rpx solid #f5f5f5;
	padding-bottom: env(safe-area-inset-bottom);
}

/* 按钮组样式 */
.btn-group {
	display: flex;
	padding: 20rpx 40rpx;
}

.btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: 400;
	transition: all 0.2s;
}

.btn-reset {
	background-color: transparent;
	color: #00C389;
	border: none;
	margin-right: 40rpx;
}

.btn-confirm {
	background-color: #00C389;
	color: #FFFFFF;
}

/* 为小屏幕预留滚动空间 */
.form-space {
	height: 40rpx;
}

/* 修改 u-input 样式 */
/deep/ .u-input {
	height: 80rpx;
}

/deep/ .u-input__input {
	height: 92rpx;
	font-size: 30rpx;
	color: #333;
	padding: 0 24rpx;
}

/deep/ .u-input--border {
	border-color: #e0e0e0;
	border-width: 1rpx;
	border-radius: 16rpx;
	height: 88rpx;
}

/deep/ .u-input__placeholder-style {
	color: #c8c9cc;
}

/deep/ .u-icon {
	color: #999;
}

/deep/ .u-input__content__field {
	display: flex;
	align-items: center;
}



/deep/ .navbar-left .svg-icon {
	color: #ffffff !important;
}

/deep/ .uni-datetime-picker--btn {
	background-color: #00C389;
}
/deep/ .uni-calendar-item--multiple .uni-calendar-item--before-checked,
/deep/ .uni-calendar-item--multiple .uni-calendar-item--after-checked,
/deep/ .uni-calendar-item__weeks-box .uni-calendar-item--checked
{
	background-color: #00C389;
}
</style>
