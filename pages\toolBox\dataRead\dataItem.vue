<template>
	<view class="data-item-container">
		<!-- 自定义导航栏 -->
		<custom-navbar title="抄读助手" :showBack="true">
			<template #right>
				<!-- <view class="bluetooth-icon">
					<svg-icon name="bluetooth" size="40"></svg-icon>
				</view> -->
			</template>
		</custom-navbar>
		
		<!-- 内容区域 -->
		<scroll-view 
			class="content-section" 
			scroll-y="true" 
			enable-back-to-top
			:scroll-anchoring = "true"
			:enhanced="true"
			:bounces="true"
			:show-scrollbar="false"
			:style="{ height: contentHeight+ 'px' }"
		>
			<view class="data-item-form">
				<!-- 抄读数据项 -->
				<view class="card-container">
					<view class="card-header">
						<view class="header-indicator"></view>
						<text class="header-title">抄读数据项</text>
					</view>
					
					<view class="form-section">
						<text class="section-title">抄表时间段</text>
						<!-- 日期选择 -->
						<view class="form-item">
							<text class="form-label">开始时间</text>
							<view class="form-value" @click="openDatePicker('startDate')">
								<text>{{ formatDate(formData.startDate) || '请选择' }}</text>
								<u-icon name="arrow-right" color="#262626" size="14"></u-icon>
							</view>
						</view>
						
						<!-- 开始时间 -->
						<view class="form-item">
							<text class="form-label">结束时间</text>
							<view class="form-value" @click="openDatePicker('endDate')">
								<text>{{ formatDate(formData.endDate) || '请选择' }}</text>
								<u-icon name="arrow-right" color="#2626266" size="14"></u-icon>
							</view>
						</view>
						<view class="search-container">
							<view class="search-box">
								<u-search 
								placeholder="请输入数据项名称"
								 :show-action="false"
								 :animation="false"
								 margin="0"
								 shape="round"
								bgColor="#f5f5f5" 
								v-model="formData.searchKeyword"></u-search>
							</view>
						</view>
						<view class="data-item-list">
							<view class="data-item-row" v-for="(item,index) in filterDataItems" :key="index">
								<view class="checkbox-item">
									<text class="checkbox-label">{{item.name}}</text>
									<view class="checkbox-wrapper" @click="toggleCheckbox(item.code)">
										<view class="checkbox-box" :class="{'checked':item.checked}">
											<u-icon v-if="item.checked" name="checkmark" color="#fff" :size="size"></u-icon>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view class="bottom-btn-container" :style="{height:bottomBtnHeight + 'rpx'}">
							<view class="bottom-btn-wrapper">
								<view class="btn-cancel" @click="goBack">取消</view>
								<view class="btn-confirm" @click="confirmSelection">确定</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 日期选择器弹窗 -->
		<u-calendar
			:show="showDatePicker"
			:mode="dateMode"
			:minDate="minDate" 
			:maxDate="maxDate"
			:defaultDate="defaultDate"
			color="#07ac7c"
			@confirm="confirmDate"
			@close="closeDatePicker"
			startText="开始"
			endText="结束"
		></u-calendar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				windowHeight: 0,
				size:"40rpx",
			    bottomBtnHeight:120,
				minDate: new Date('2022-01-01').getTime(), // 固定最小日期为 2023-01-01
				maxDate: new Date().getTime(),
				// 表单数据
				formData: {
					startDate: "",
					endDate: "",
					searchKeyword:'',
					dataItems:[
						{
							name:'实时数据组合有功电能(总及各费率)',
							checked:false,
							code:'0000102000000'
						},
						{
							name:'实时A相正向有功电能(总及各费率)',
							checked:false,
							code:'001110200000'
						},
						{
							name:'实时B相正向有功电能(总及各费率)',
							checked:false,
							code:'001210200000'
						},
						{
							name:'实时C相正向有功电能(总及各费率)',
							checked:false,
							code:'001310200000'
						},
						{
							name:'实时A相反向有功电能(总及各费率)',
							checked:false,
							code:'002110200000'
						},
						{
							name:'实时B相反向有功电能(总及各费率)',
							checked:false,
							code:'002210200000'
						},
						{
							name:'实时C相反向有功电能(总及各费率)',
							checked:false,
							code:'002310200000'
						},
						{
							name:'日冻结正向有功电能(总及各费率)',
							checked:false,
							code:'001010200401'
						},
						{
							name:'日冻结正向有功电能(总)',
							checked:false,
							code:'001010201401'
						},
						{
							name:'日冻结正向有功电能(费率1)',
							checked:false,
							code:'001010202401'
						},
						{
							name:'日冻结正向有功电能(费率2)',
							checked:false,
							code:'001010203401'
						},
						{
							name:'日冻结正向有功电能(费率3)',
							checked:false,
							code:'001010204401'
						},
						{
							name:'日冻结正向有功电能(费率4)',
							checked:false,
							code:'001010205401'
						},
						{
							name:'日冻结反向有功电能(总及各费率)',
							checked:false,
							code:'002010200401'
						},
						{
							name:'日冻结反向有功电能(总)',
							checked:false,
							code:'002010201401'
						},
						{
							name:'日冻结反向有功电能(费率1)',
							checked:false,
							code:'002010202401'
						},
						{
							name:'日冻结反向有功电能(费率2)',
							checked:false,
							code:'002010203401'
						},
						{
							name:'日冻结反向有功电能(费率3)',
							checked:false,
							code:'002010204401'
						},
						{
							name:'日冻结反向有功电能(费率4)',
							checked:false,
							code:'002010205401'
						},
						{
							name:'实时数据正向有功电能(总及各费率)',
							checked:false,
							code:'001010200000'
						},
						{
							name:'实时数据反向有功电能(总及各费率)',
							checked:false,
							code:'002010200000'
						},
						{
							name:'实时数据正向有功电能(总)',
							checked:false,
							code:'001010201000'
						},
						{
							name:'实时数据正向有功电能(费率1)',
							checked:false,
							code:'001010202000'
						},
						{
							name:'实时数据正向有功电能(费率2)',
							checked:false,
							code:'001010203000'
						},
						{
							name:'实时数据正向有功电能(费率3)',
							checked:false,
							code:'001010204000'
						},
						{
							name:'实时数据反向有功电能(总)',
							checked:false,
							code:'002010201000'
						},
						{
							name:'实时数据反向有功电能(费率1)',
							checked:false,
							code:'002010202000'
						},
						{
							name:'实时数据反向有功电能(费率2)',
							checked:false,
							code:'002010203000'
						},
						{
							name:'实时数据反向有功电能(费率3)',
							checked:false,
							code:'002010204000'
						},
						{
							name:'三相电压(三相数据)',
							checked:false,
							code:'200010200000'
						},
						{
							name:'三相电流(三相数据)',
							checked:false,
							code:'200110200000'
						},
						{
							name:'有功功率(数据块)',
							checked:false,
							code:'200410200000'
						},
						{
							name:'无功功率(数据块)',
							checked:false,
							code:'200510200000'
						},
						{
							name:'功率因数(数据块)',
							checked:false,
							code:'200A10200000'
						},
						{
							name:'三相电压(A相)',
							checked:false,
							code:'200010201000'
						},
						{
							name:'三相电压(B相)',
							checked:false,
							code:'200010202000'
						},{
							name:'三相电流(A相)',
							checked:false,
							code:'200110201000'
						},
						{
							name:'三相电流(B相)',
							checked:false,
							code:'200110202000'
						},
						{
							name:'有功功率(总)',
							checked:false,
							code:'200410201000'
						},
						{
							name:'有功功率(A相)',
							checked:false,
							code:'200410202000'
						},
						{
							name:'有功功率(B相)',
							checked:false,
							code:'200410203000'
						},{
							name:'无功功率(总)',
							checked:false,
							code:'200510201000'
						},
						{
							name:'无功功率(A相)',
							checked:false,
							code:'200510202000'
						},
						{
							name:'无功功率(B相)',
							checked:false,
							code:'200510203000'
						},{
							name:'功率因数(总)',
							checked:false,
							code:'200A10201000'
						},{
							name:'功率因数(A相)',
							checked:false,
							code:'200A10202000'
						},{
							name:'功率因数(B相)',
							checked:false,
							code:'200A10203000'
						},{
							name:'电能表开端钮盒事件',
							checked:false,
							code:'301C10200000'
						},{
							name:'电能表开盖事件',
							checked:false,
							code:'301B10200000'
						},{
							name:'终端停/上电事件',
							checked:false,
							code:'310610200000'
						},{
							name:'停电事件',
							checked:false,
							code:'303710200000'
						},{
							name:'上电事件',
							checked:false,
							code:'303810200000'
						},{
							name:'电能表掉电事件',
							checked:false,
							code:'301110200000'
						}
						
					]
				},
				
				// 日期选择器相关
				showDatePicker: false,
				dateMode: 'single', // 单选模式
				currentDateType: '', // 当前编辑的日期类型
				defaultDate: new Date()
			};
		},
		onLoad() {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
		},
		computed:{
			filterDataItems() {
				const keyword = this.formData.searchKeyword.trim();
				if(!keyword) {
					return this.formData.dataItems;
				}
				const filter = this.formData.dataItems.filter((item) => item.name.includes(keyword))
				return filter;
			}
		},
		methods: {
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			confirmSelection() {
				if(!this.formData.startDate || !this.formData.endDate) {
					uni.showToast({
						title: '请选择日期范围',
						icon: 'none',
					});
					return;
				}
				const startDate = new Date(this.formData.startDate);
				const endDate = new Date(this.formData.endDate);
				if(endDate<startDate) {
					uni.showToast({
						title: '结束时间不能晚于开始时间',
						icon: 'none',
					});
					return;
				}
				const hasSelectItem = this.formData.dataItems.some(item =>item.checked);
				if(!hasSelectItem) {
					uni.showToast({
						title: '请至少选择一个数据项',
						icon: 'none',
					});
					return;
				}
				const returnData = []
				this.formData.dataItems.forEach((item) => {
					if(item.checked) {
						returnData.push({
							startDate:this.formData.startDate,
							endDate:this.formData.endDate,
							itemCode:item.code
						})
					}
				})
				
				uni.setStorageSync('readDataItems',JSON.stringify(returnData))
				uni.navigateBack();
			},
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏）
				const topHeight = this.statusBarHeight + this.navbarHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight+160;
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 格式化日期
			formatDate(date) {
				if (!date) return '';
				const d = new Date(date);
				const year = d.getFullYear();
				const month = String(d.getMonth() + 1).padStart(2, '0');
				const day = String(d.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 打开日期选择器
			openDatePicker(type) {
				this.currentDateType = type;
				this.defaultDate = this.formData[type] || new Date();
				this.showDatePicker = true;
			},
			
			// 关闭日期选择器
			closeDatePicker() {
				this.showDatePicker = false;
			},
			
			// 确认日期选择
			confirmDate(e) {
				const selectedDate = Array.isArray(e) ? e[0] : e;
				if (this.currentDateType && selectedDate) {
					this.formData[this.currentDateType] = selectedDate;
				}
				this.closeDatePicker();
			},
			getDataItem() {
				console.log(this.formData)
			},
			// 切换复选框状态
			toggleCheckbox(code) {
				const item = this.formData.dataItems.find(item => item.code === code);
				if(item) {
					item.checked = !item.checked;
				}
			}
		}
	}
</script>

<style lang="scss">
.data-item-container {
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	height: 100vh;
	
	/* 覆盖默认导航栏样式，使其与设计图一致 */
	/deep/ .custom-navbar {
		background-color: #fff !important;
	}
	.setting-icon, .bluetooth-icon {
		height: 42rpx;
		margin-right: 20rpx;
	}
}

/* 导航栏返回按钮 */
.navbar-left {
	display: flex;
	align-items: center;
	height: 100%;
}

.back-btn {
	padding: 0 30rpx;
	height: 100%;
	display: flex;
	align-items: center;
}

/* 内容区域 */
.content-section {
	flex: 1;
	box-sizing: border-box;
	background-color: #f5f5f5;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 数据项表单 */
.data-item-form {
	padding: 30rpx;
}

/* 卡片样式 */
.card-container {
	background-color: #FFFFFF;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.card-header {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.header-indicator {
	width: 6rpx;
	height: 30rpx;
	background-color: #07ac7c;
	margin-right: 20rpx;
	border-radius: 3rpx;
}

.header-title {
	font-size: 30rpx;
	color: #333333;
	font-weight: bold;
}

/* 表单区域 */
.form-section {
	padding: 30rpx;
}

.section-title {
	font-size: 28rpx;
	color: #262626;
	display: block;
}

.form-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1px solid #f0f0f0;
}

.form-label {
	font-size: 28rpx;
	color: #8c8c8c;
}

.form-value {
	display: flex;
	align-items: center;
	
	text {
		font-size: 28rpx;
		color: #262626;
		margin-right: 10rpx;
	}
}

/* 复选框样式 */
.checkbox-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1px solid #f0f0f0;
}

.checkbox-label {
	font-size: 28rpx;
	color: #262626;
}

.checkbox-wrapper {
	padding: 5rpx;
}

.checkbox-box {
	width: 40rpx;
	height: 40rpx;
	border-radius: 8rpx;
	border: 1px solid #d9d9d9;
	display: flex;
	align-items: center;
	justify-content: center;
	
	&.checked {
		background-color: #07ac7c;
		border-color: #07ac7c;
	}
}

/* 子项样式 */
.checkbox-subitem {
	padding: 0 0 15rpx 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.subitem-row {
	padding: 10rpx 0;
}

.subitem-label {
	font-size: 26rpx;
	color: #8c8c8c;
}
.bottom-btn-container {
	width:100%;
	background: #fff;
	position: fixed;
	bottom:0;
	left:0;
	z-index:10;
}
.bottom-btn-wrapper {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	height:100%;
	box-sizing: border-box;
}
.btn-cancel,.btn-confirm{
	flex:1;
	height:80rpx;
	border-radius: 40rpx;
	display:flex;
	align-items: center;
	justify-content: center;
	font-size:30rpx;
	font-weight: 500;
}
.btn-cancel {
	background-color: #f5f5f5;
	color:#666;
	margin-right: 20rpx;
}
.btn-confirm {
	background-color: #07ac7c;
	color:#fff;
}
.data-item-list {
	padding-bottom: 40rpx;
}
.search-box {
	margin-top: 20rpx;
}
</style>
