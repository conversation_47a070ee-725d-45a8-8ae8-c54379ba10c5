(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-toolBox-tool"],{"3e6c":function(a,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t("c223");var r={data:function(){return{iconSize:80,path:window.location.hash,pageRoutes:{caseLibrary:"/pages/toolBox/caseLibrary/caseList",expertLibrary:"/pages/toolBox/expertLibrary/expertList",lawLibrary:"/pages/toolBox/lawLibrary/lawList",dataRead:"/pages/toolBox/dataRead/dataList"}}},methods:{navigateTo:function(a){var e=this.pageRoutes[a]||"/pages/toolBox/".concat(a,"/").concat(a);uni.navigateTo({url:e,fail:function(a){console.error("页面跳转失败:",a),uni.showToast({title:"该功能正在开发中",icon:"none",duration:2e3})}})}}};e.default=r},"5da1":function(a,e,t){"use strict";var r=t("9325"),o=t.n(r);o.a},"8b9d":function(a,e,t){var r=t("c86c");e=r(!1),e.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-ec55a0de]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-ec55a0de]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-ec55a0de]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-ec55a0de]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-ec55a0de]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-ec55a0de]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-ec55a0de]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-ec55a0de]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-ec55a0de]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-ec55a0de]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-ec55a0de]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-ec55a0de]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-ec55a0de]::after{border:none}.u-hover-class[data-v-ec55a0de]{opacity:.7}.u-primary-light[data-v-ec55a0de]{color:#ecf5ff}.u-warning-light[data-v-ec55a0de]{color:#fdf6ec}.u-success-light[data-v-ec55a0de]{color:#f5fff0}.u-error-light[data-v-ec55a0de]{color:#fef0f0}.u-info-light[data-v-ec55a0de]{color:#f4f4f5}.u-primary-light-bg[data-v-ec55a0de]{background-color:#ecf5ff}.u-warning-light-bg[data-v-ec55a0de]{background-color:#fdf6ec}.u-success-light-bg[data-v-ec55a0de]{background-color:#f5fff0}.u-error-light-bg[data-v-ec55a0de]{background-color:#fef0f0}.u-info-light-bg[data-v-ec55a0de]{background-color:#f4f4f5}.u-primary-dark[data-v-ec55a0de]{color:#398ade}.u-warning-dark[data-v-ec55a0de]{color:#f1a532}.u-success-dark[data-v-ec55a0de]{color:#53c21d}.u-error-dark[data-v-ec55a0de]{color:#e45656}.u-info-dark[data-v-ec55a0de]{color:#767a82}.u-primary-dark-bg[data-v-ec55a0de]{background-color:#398ade}.u-warning-dark-bg[data-v-ec55a0de]{background-color:#f1a532}.u-success-dark-bg[data-v-ec55a0de]{background-color:#53c21d}.u-error-dark-bg[data-v-ec55a0de]{background-color:#e45656}.u-info-dark-bg[data-v-ec55a0de]{background-color:#767a82}.u-primary-disabled[data-v-ec55a0de]{color:#9acafc}.u-warning-disabled[data-v-ec55a0de]{color:#f9d39b}.u-success-disabled[data-v-ec55a0de]{color:#a9e08f}.u-error-disabled[data-v-ec55a0de]{color:#f7b2b2}.u-info-disabled[data-v-ec55a0de]{color:#c4c6c9}.u-primary[data-v-ec55a0de]{color:#3c9cff}.u-warning[data-v-ec55a0de]{color:#f9ae3d}.u-success[data-v-ec55a0de]{color:#5ac725}.u-error[data-v-ec55a0de]{color:#f56c6c}.u-info[data-v-ec55a0de]{color:#909399}.u-primary-bg[data-v-ec55a0de]{background-color:#3c9cff}.u-warning-bg[data-v-ec55a0de]{background-color:#f9ae3d}.u-success-bg[data-v-ec55a0de]{background-color:#5ac725}.u-error-bg[data-v-ec55a0de]{background-color:#f56c6c}.u-info-bg[data-v-ec55a0de]{background-color:#909399}.u-main-color[data-v-ec55a0de]{color:#303133}.u-content-color[data-v-ec55a0de]{color:#606266}.u-tips-color[data-v-ec55a0de]{color:#909193}.u-light-color[data-v-ec55a0de]{color:#c0c4cc}.u-safe-area-inset-top[data-v-ec55a0de]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-ec55a0de]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-ec55a0de]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-ec55a0de]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-ec55a0de]{z-index:10090}uni-toast .uni-toast[data-v-ec55a0de]{z-index:10090}[data-v-ec55a0de]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-page-body[data-v-ec55a0de]{background-color:#f8f8f8;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}body.?%PAGE?%[data-v-ec55a0de]{background-color:#f8f8f8}.file-query-container[data-v-ec55a0de]{min-height:100vh;display:flex;flex-direction:column;background:#f8f8f8}\r\n/* 内容区域 */.content-area[data-v-ec55a0de]{flex:1;overflow-y:auto;background:#f8f8f8}\r\n/* 卡片网格 */.card-grid[data-v-ec55a0de]{padding:15px 16px}.card-row[data-v-ec55a0de]{display:flex;justify-content:space-between;margin-bottom:15px}.card-item[data-v-ec55a0de]{width:48%;border-radius:12px;overflow:hidden;box-shadow:0 2px 10px rgba(0,0,0,.03);background-color:#fff}.card-hover[data-v-ec55a0de]{-webkit-transform:scale(.98);transform:scale(.98);transition:all .2s;opacity:.9}.card-inner[data-v-ec55a0de]{height:140px;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:15px 10px;background-color:#fff}\r\n/* 渐变背景 - 完全匹配原型图 */.orange-green-gradient[data-v-ec55a0de]{background:linear-gradient(180deg,#f68d04,#6dbd4e)}.icon-box[data-v-ec55a0de]{display:flex;align-items:center;justify-content:center;width:80px;height:80px;border-radius:50%;background-color:#f5f5f5;overflow:hidden;box-sizing:border-box}.icon-green[data-v-ec55a0de]{background-color:#dcf7eb}.icon-orange[data-v-ec55a0de]{background-color:#feead1}.icon-orange-dark[data-v-ec55a0de]{background-color:#feead1}.icon-blue[data-v-ec55a0de]{background-color:#e1eefb}.icon-purple[data-v-ec55a0de]{background-color:#e9e1ff}.icon-cyan[data-v-ec55a0de]{background-color:#d7f6f5}.icon-red[data-v-ec55a0de]{background-color:#ffe5e5}.card-text[data-v-ec55a0de]{font-size:16px;color:#1d2129;font-weight:400;text-align:center;line-height:1.4;margin-top:12px;width:100%;padding:0 5px;box-sizing:border-box;word-break:break-word}.small-text[data-v-ec55a0de]{font-size:15px;letter-spacing:-.3px}\r\n/* 媒体查询 - 适配不同设备 */\r\n/* 小屏幕设备，如iPhone 4 (320px宽度) */@media screen and (max-width:320px){.card-grid[data-v-ec55a0de]{padding:10px 8px}.card-row[data-v-ec55a0de]{margin-bottom:10px}.card-inner[data-v-ec55a0de]{height:120px;padding:10px 5px}.icon-box[data-v-ec55a0de]{width:60px;height:60px}.card-text[data-v-ec55a0de]{font-size:13px;margin-top:8px;\r\n    /* 确保文字不会被截断 */width:100%;white-space:normal;word-break:break-word}.small-text[data-v-ec55a0de]{font-size:12px;letter-spacing:-.5px}\r\n  /* 调整图标大小 */.svg-icon[data-v-ec55a0de]{-webkit-transform:scale(.8);transform:scale(.8)}}@media screen and (min-width:375px){.card-inner[data-v-ec55a0de]{height:140px}.icon-box[data-v-ec55a0de]{width:80px;height:80px}.card-text[data-v-ec55a0de]{font-size:16px;margin-top:12px}}@media screen and (min-width:414px){.card-grid[data-v-ec55a0de]{padding:15px 20px}.card-row[data-v-ec55a0de]{margin-bottom:15px}.card-inner[data-v-ec55a0de]{height:150px}.icon-box[data-v-ec55a0de]{width:85px;height:85px}.card-text[data-v-ec55a0de]{font-size:16px}}\r\n/* 平板和大屏幕设备 */@media screen and (min-width:768px){.card-grid[data-v-ec55a0de]{padding:25px;max-width:1200px;margin:0 auto}.card-row[data-v-ec55a0de]{margin-bottom:25px}.card-inner[data-v-ec55a0de]{height:170px;padding:20px}.icon-box[data-v-ec55a0de]{width:90px;height:90px}.card-text[data-v-ec55a0de]{font-size:18px;margin-top:10px}}\r\n/* 功能服务卡片样式 */.function-card[data-v-ec55a0de]{background-color:#fff;border-radius:12px;margin:15px;padding:15px;box-shadow:0 2px 12px rgba(0,0,0,.06)}',""]),a.exports=e},9325:function(a,e,t){var r=t("8b9d");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[a.i,r,""]]),r.locals&&(a.exports=r.locals);var o=t("967d").default;o("e0d4f40c",r,!0,{sourceMap:!1,shadowMode:!1})},"9bcb":function(a,e,t){"use strict";t.r(e);var r=t("3e6c"),o=t.n(r);for(var d in r)["default"].indexOf(d)<0&&function(a){t.d(e,a,(function(){return r[a]}))}(d);e["default"]=o.a},b966:function(a,e,t){"use strict";t.d(e,"b",(function(){return r})),t.d(e,"c",(function(){return o})),t.d(e,"a",(function(){}));var r=function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("v-uni-view",{staticClass:"file-query-container"},[t("custom-navbar",{attrs:{title:"工具箱",path:a.path}}),t("v-uni-view",{staticClass:"content-area"},[t("v-uni-view",{staticClass:"card-grid"},[t("v-uni-view",{staticClass:"card-row"},[t("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(e){arguments[0]=e=a.$handleEvent(e),a.navigateTo("caseLibrary")}}},[t("v-uni-view",{staticClass:"card-inner"},[t("v-uni-view",{staticClass:"icon-box icon-green"},[t("svg-icon",{attrs:{name:"case",color:"#3CC792",size:a.iconSize}})],1),t("v-uni-text",{staticClass:"card-text"},[a._v("案例库")])],1)],1),t("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(e){arguments[0]=e=a.$handleEvent(e),a.navigateTo("expertLibrary")}}},[t("v-uni-view",{staticClass:"card-inner"},[t("v-uni-view",{staticClass:"icon-box icon-orange"},[t("svg-icon",{attrs:{name:"taiqushitu",color:"#F49B2C",size:a.iconSize}})],1),t("v-uni-text",{staticClass:"card-text"},[a._v("专家库")])],1)],1)],1),t("v-uni-view",{staticClass:"card-row"},[t("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(e){arguments[0]=e=a.$handleEvent(e),a.navigateTo("lawLibrary")}}},[t("v-uni-view",{staticClass:"card-inner"},[t("v-uni-view",{staticClass:"icon-box icon-red"},[t("svg-icon",{attrs:{name:"zhongdiantaiqu",color:"#FF5A5F",size:a.iconSize}})],1),t("v-uni-text",{staticClass:"card-text"},[a._v("法律法规")])],1)],1),t("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(e){arguments[0]=e=a.$handleEvent(e),a.navigateTo("dataRead")}}},[t("v-uni-view",{staticClass:"card-inner"},[t("v-uni-view",{staticClass:"icon-box icon-cyan"},[t("svg-icon",{attrs:{name:"gongzuoxiangqing",color:"#1CBBB4",size:a.iconSize}})],1),t("v-uni-text",{staticClass:"card-text small-text"},[a._v("数据抄读")])],1)],1)],1)],1)],1)],1)},o=[]},ff53:function(a,e,t){"use strict";t.r(e);var r=t("b966"),o=t("9bcb");for(var d in o)["default"].indexOf(d)<0&&function(a){t.d(e,a,(function(){return o[a]}))}(d);t("5da1");var i=t("828b"),n=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"ec55a0de",null,!1,r["a"],void 0);e["default"]=n.exports}}]);