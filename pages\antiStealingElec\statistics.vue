<template>
	<view class="statistics-container">
		<!-- 自定义导航栏 -->
		<view class="header-section">
			<custom-navbar title="防窃电改造">
				<template #right>
					<view class="search-icon" @click="goToSearch">
						<u-icon name="search" color="#000000" size="28"></u-icon>
					</view>
				</template>
			</custom-navbar>
		</view>

		<!-- 页面内容区域 -->
		<view class="content-container" :style="{ paddingTop: safeAreaTop }">
			<!-- 饼图统计区域 -->
			<view class="chart-section">
				<view class="section-header">
					<view class="section-title">
						<view class="title-indicator"></view>
						<text>反窃电改造统计图</text>
					</view>
				</view>
				<view class="chart-container">
					<qiun-data-charts type="pie" :opts="opts" :chartData="chartData" />
				</view>
			</view>

			<!-- 表格统计区域 -->
			<view class="table-section">
				<view class="section-header">
					<view class="section-title">
						<view class="title-indicator"></view>
						<text>反窃电改造统计数据</text>
					</view>
				</view>
				<view class="table-container">
					<!-- 表格头部 -->
					<view class="table-header">
						<view class="table-cell table-cell-orgname">管理单位</view>
						<view class="table-cell table-cell-number">总数</view>
						<view class="table-cell table-cell-number">待填报</view>
						<view class="table-cell table-cell-number">待改造</view>
						<view class="table-cell table-cell-number">改造中</view>
						<view class="table-cell table-cell-number">已完成</view>
					</view>

					<!-- 表格内容 -->
					<view class="table-body" v-if="!isLoading && statisticsData.length > 0">
						<view class="table-row" v-for="(item, index) in statisticsData" :key="index"
							@click="onRowClick(item)">
							<view class="table-cell table-cell-orgname">{{ item.mgtOrgName }}</view>
							<view class="table-cell table-cell-number">{{ item.all }}</view>
							<view class="table-cell table-cell-number">{{ item.status01 }}</view>
							<view class="table-cell table-cell-number">{{ item.status04 }}</view>
							<view class="table-cell table-cell-number">{{ item.status02 }}</view>
							<view class="table-cell table-cell-number">{{ item.status03 }}</view>
						</view>
					</view>

					<!-- 空状态提示 -->
					<view class="table-empty" v-else-if="!isLoading && statisticsData.length === 0">
						<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
					</view>

					<!-- 加载中状态 -->
					<view class="table-loading" v-else>
						<text class="loading-text">数据加载中...</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		bizCode,
		url,
		getUUID,
		getCurrentTimestamp,
		port
	} from '@/static/commonJs/util.js'
	export default {
		data() {
			return {
				statusBarHeight: 0,
				navbarHeight: 44,
				// 统计数据
				statisticsData: [],
				isLoading: true, // 添加加载状态

				// 饼图数据
				chartData: {},

				// 饼图配置
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [5, 5, 5, 5],
					enableScroll: false,
					legend: {
						show: true,
						itemGap: 5
					},
					extra: {
						pie: {
							activeOpacity: 0.5,
							activeRadius: 10,
							offsetAngle: 0,
							labelWidth: 15,
							border: true,
							borderWidth: 3,
							borderColor: "#FFFFFF"
						}
					}
				},
				isMockData: true,
				token:null
			};
		},

		computed: {
			// 计算顶部安全区域高度（导航栏+状态栏）
			safeAreaTop() {
				return this.statusBarHeight + this.navbarHeight + 'px';
			},

			// 总数
			totalCount() {
				return this.statisticsData.reduce((sum, item) => sum + (parseInt(item.total) || 0), 0);
			}
		},

		onLoad() {
			// 获取状态栏高度
			this.getStatusBarHeight();

			// 设置图表加载状态
			this.isLoading = true;
			this.getStatisticsData();
		},
		onShow() {
			const queryParams = uni.getStorageSync('antiStealingElec_queryParams');
			if (queryParams) {
				console.log('统计页查询参数', queryParams)
				this.queryParams = queryParams;
			} else {
				console.log('没有有查询参数', queryParams)
			}
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
				});
			},
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},

			// 跳转到搜索页面
			goToSearch() {
				uni.navigateTo({
					url: './query'
				});
			},

			// 获取状态栏高度
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
			},

			// 获取统计数据
			getStatisticsData() {
				const vm = this;
				if (this.isMockData) {
					this.statisticsData = [
						{
							"mgtOrgCode": "31102",
							"mgtOrgName": "上海市电力公司",
							"disLv": "02",
							"ynFlag": "1",
							"all": 14405,
							"status01": 1170,
							"status02": 9,
							"status03": 13223,
							"status04": 3,
							"dangerType01": 10293,
							"dangerType02": 55,
							"rate03": 91.79
						},
						{
							"mgtOrgCode": "31401",
							"mgtOrgName": "市区供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 1666,
							"status01": 136,
							"status02": 9,
							"status03": 1518,
							"status04": 3,
							"dangerType01": 1144,
							"dangerType02": 4,
							"rate03": 91.12
						},
						{
							"mgtOrgCode": "31402",
							"mgtOrgName": "市南供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 2137,
							"status01": 188,
							"status02": 0,
							"status03": 1949,
							"status04": 0,
							"dangerType01": 1552,
							"dangerType02": 0,
							"rate03": 91.2
						},
						{
							"mgtOrgCode": "31403",
							"mgtOrgName": "浦东供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 2250,
							"status01": 218,
							"status02": 0,
							"status03": 2032,
							"status04": 0,
							"dangerType01": 1391,
							"dangerType02": 4,
							"rate03": 90.31
						},
						{
							"mgtOrgCode": "31404",
							"mgtOrgName": "崇明供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 122,
							"status01": 2,
							"status02": 0,
							"status03": 120,
							"status04": 0,
							"dangerType01": 73,
							"dangerType02": 4,
							"rate03": 98.36
						},
						{
							"mgtOrgCode": "31405",
							"mgtOrgName": "长兴供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 13,
							"status01": 1,
							"status02": 0,
							"status03": 12,
							"status04": 0,
							"dangerType01": 1,
							"dangerType02": 10,
							"rate03": 92.31
						},
						{
							"mgtOrgCode": "31406",
							"mgtOrgName": "市北供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 6181,
							"status01": 485,
							"status02": 0,
							"status03": 5696,
							"status04": 0,
							"dangerType01": 4522,
							"dangerType02": 0,
							"rate03": 92.15
						},
						{
							"mgtOrgCode": "31409",
							"mgtOrgName": "嘉定供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 553,
							"status01": 36,
							"status02": 0,
							"status03": 517,
							"status04": 0,
							"dangerType01": 452,
							"dangerType02": 0,
							"rate03": 93.49
						},
						{
							"mgtOrgCode": "31415",
							"mgtOrgName": "奉贤供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 638,
							"status01": 41,
							"status02": 0,
							"status03": 597,
							"status04": 0,
							"dangerType01": 484,
							"dangerType02": 32,
							"rate03": 93.57
						},
						{
							"mgtOrgCode": "31416",
							"mgtOrgName": "松江供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 424,
							"status01": 34,
							"status02": 0,
							"status03": 390,
							"status04": 0,
							"dangerType01": 372,
							"dangerType02": 1,
							"rate03": 91.98
						},
						{
							"mgtOrgCode": "31417",
							"mgtOrgName": "金山供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 177,
							"status01": 6,
							"status02": 0,
							"status03": 171,
							"status04": 0,
							"dangerType01": 132,
							"dangerType02": 0,
							"rate03": 96.61
						},
						{
							"mgtOrgCode": "31418",
							"mgtOrgName": "青浦供电公司",
							"disLv": "03",
							"ynFlag": "1",
							"all": 244,
							"status01": 23,
							"status02": 0,
							"status03": 221,
							"status04": 0,
							"dangerType01": 170,
							"dangerType02": 0,
							"rate03": 90.57
						}
					]
					let item = this.statisticsData[0];
					let data = {
						series: [{
							data: [{
									"name": "待填报",
									"value": Number(item.status01),
									"labelText": "待填报"
								},
								{
									"name": "待改造",
									"value": Number(item.status04),
									"labelText": "待改造"
								},
								{
									"name": "改造中",
									"value": Number(item.status02),
									"labelText": "改造中"
								},
								{
									"name": "改造完成",
									"value": Number(item.status03),
									"labelText": "改造完成"
								},
							]
						}]
					};
					this.chartData = JSON.parse(JSON.stringify(data));
					this.isLoading = false;
				} else {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': this.token
						},
						data: {
							token: this.token,
							method: "PutHuaYun",
							uri: url,
							data: JSON.stringify({
								"bizCode": bizCode,
								"espFlowId": getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": getCurrentTimestamp(),
								"espInformation": {
									"service": "MobileElectricityAntiRemouldController",
									"method": "recordStatistic",
									"data": {
										...this.queryParams
									}
								}
							})

						},
						success: (res) => {
							if (res && res.data.Tag === 1) {
								const rtnData = res.data.Data.espInformation;
								this.statisticsData = rtnData.data;
								let item = this.statisticsData[0];
								let data = {
									series: [{
										data: [{
												"name": "待填报",
												"value": Number(item.status01),
												"labelText": "待填报"
											},
											{
												"name": "待改造",
												"value": Number(item.status04),
												"labelText": "待改造"
											},
											{
												"name": "改造中",
												"value": Number(item.status02),
												"labelText": "改造中"
											},
											{
												"name": "改造完成",
												"value": Number(item.status03),
												"labelText": "改造完成"
											},
										]
									}]
								};
								this.chartData = JSON.parse(JSON.stringify(data));
								this.isLoading = false;
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							this.isLoading = false;
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}

			},

			// 点击表格行
			onRowClick(item) {
				// 构建查询参数对象
				const queryParams = {
					mgtOrgCode: item.mgtOrgCode, // 传递供电单位名称作为查询条件
					mgtOrgName: item.mgtOrgName,
					retrofitStartTime: this.queryParams?.retrofitStartTime||'',
					retrofitEndTime: this.queryParams?.retrofitEndTime||''
				};
				console.log('跳转到列表页，携带参数：', queryParams);
				// 将参数存储到本地缓存，供list页面使用
				uni.setStorageSync('antiStealingElec_queryParams', queryParams);
				// 跳转到列表页
				uni.navigateTo({
					url: `./list?item=${encodeURIComponent(JSON.stringify(queryParams))}`
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.statistics-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		position: relative;
	}

	/* 顶部导航区域 */
	.header-section {
		background-color: #00C389;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 101;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.back-icon {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 导航栏搜索框样式 */
	.nav-search-container {
		display: flex;
		align-items: center;
		height: 100%;
	}

	.search-box {
		display: flex;
		align-items: center;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		padding: 6rpx 20rpx;
		width: 230rpx;
		margin-right: 10rpx;
	}

	.search-input {
		flex: 1;
		height: 56rpx;
		line-height: 56rpx;
		font-size: 26rpx;
		color: #ffffff;
		margin-left: 10rpx;
	}

	/* 内容区域 */
	.content-container {
		box-sizing: border-box;
		position: relative;
		padding: 20rpx;
		width: 100%;
	}

	/* 段落标题样式 */
	.section-header {
		padding: 15rpx;
		margin-bottom: 5rpx;
		text-align: left;
	}

	.section-title {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		font-weight: 500;
		color: #333;
	}

	.title-indicator {
		width: 6rpx;
		height: 32rpx;
		background-color: #00C389;
		margin-right: 16rpx;
		border-radius: 3rpx;
	}

	/* 图表区域 */
	.chart-section {
		background-color: #fff;
		margin-bottom: 20rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.chart-container {
		height: 500rpx;
		padding: 20rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	/* 加载中样式 */
	.loading-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: rgba(255, 255, 255, 0.8);
		z-index: 10;
	}

	.loading-text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #666;
	}

	/* 饼图样式 */
	.pie-chart {
		width: 100%;
		height: 400rpx;
		margin: 0 auto;
	}

	/* 饼图图例 */
	.chart-legend {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		margin: 30rpx 0;
		padding: 0 20rpx;
		width: 100%;
	}

	.legend-item {
		display: flex;
		align-items: center;
		margin: 10rpx 20rpx;
		min-width: 120rpx;
	}

	.legend-color {
		width: 24rpx;
		height: 24rpx;
		border-radius: 4rpx;
		margin-right: 8rpx;
	}

	.legend-pending {
		background-color: #6C5CE7;
	}

	.legend-to-be-reformed {
		background-color: #5696F7;
	}

	.legend-in-progress {
		background-color: #EB5E95;
	}

	.legend-completed {
		background-color: #F05454;
	}

	.legend-text {
		font-size: 28rpx;
		color: #666;
	}

	/* 表格区域 - 优化后的样式 */
	.table-section {
		background-color: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 30rpx;
		padding: 0;
	}

	.table-container {
		padding: 0;
		overflow-x: auto;
		border-radius: 8rpx;
		border: 1rpx solid #f0f0f0;
		// margin: 0 15rpx 15rpx;
	}

	/* 统一表格头部样式 */
	.table-header {
		display: flex;
		background-color: #f8f8f8;
		border-bottom: 1rpx solid #e8e8e8;
		height: 70rpx;
		padding: 0;
	}

	/* 表头单元格基础样式 */
	.table-header .table-cell {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 500;
		color: #333;
		font-size: 24rpx;
		text-align: center;
	}

	/* 表头管理单位列特殊样式 */
	.table-header .table-cell-orgname {
		flex: 2;
		justify-content: flex-start;
		padding-left: 15rpx;
		text-align: left;
	}

	/* 表格内容区域 */
	.table-body {
		width: 100%;
	}

	/* 表格行样式 */
	.table-row {
		display: flex;
		height: 70rpx;
		border-bottom: 1rpx solid #f0f0f0;
		transition: all 0.2s;
		padding: 0;

		&:active,
		&:hover {
			background-color: #f9f9f9;
		}

		&:last-child {
			border-bottom: none;
		}
	}

	/* 表格单元格基础样式 */
	.table-body .table-cell {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #333;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	/* 管理单位列特殊样式 */
	.table-body .table-cell-orgname {
		flex: 2;
		justify-content: flex-start;
		padding-left: 15rpx;
		text-align: left;
	}

	/* 数字列居中样式 */
	.table-cell-number {
		text-align: center;
	}

	/* 隔行变色 */
	.table-row:nth-child(even) {
		background-color: #fafafa;
	}

	/* 空状态提示 */
	.table-empty {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 20rpx;
	}

	.empty-text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #666;
	}

	/* 加载中状态 */
	.table-loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 20rpx;
	}

	.loading-text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #666;
	}
</style>