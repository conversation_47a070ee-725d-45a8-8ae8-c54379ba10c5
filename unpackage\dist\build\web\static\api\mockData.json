{"DtsUserController": {"queryUserList": {"code": 200, "message": "成功", "data": {"total": 4, "records": [{"userId": "3100137001029", "userName": "张一三", "meterCode": "110006324463"}, {"userId": "3101172412468", "userName": "李一四", "meterCode": "110021713945"}, {"userId": "3100060266767", "userName": "王一五", "meterCode": "2002706289"}, {"userId": "3101251476170", "userName": "张一三", "meterCode": "110004475655"}]}}, "getUserProfile": {"code": 200, "message": "成功", "data": {"userProfile": {"custNo": "3100137001029", "custName": "张一三", "phoneNo": "***********", "address": "上海市浦东新区金桥路100号", "custType": "居民", "supplyUnit": "浦东供电公司", "serviceState": "正常", "businessType": "商铺"}, "meterInfo": {"assetCode": "32332323", "commAddress": "上海浦东新区北蔡镇北蔡大街108号", "connectionMode": "三相三线", "installDate": "2025-01-10", "comprehensiveRatio": "1000", "specification": "DL/t645_2007", "ratedVoltage": "220V", "ratedCurrent": "10A"}}}, "getUserArc": {"code": 200, "message": "成功", "data": {"ua": [220.5, 220.8, 221.2, 221.5, 221.8, 222.0, 222.3, 222.5, 222.1, 221.8, 221.5, 221.2, 220.9, 220.6, 220.4, 220.2, 220.0, 219.8, 219.5, 219.3, 219.0, 218.8, 218.5, 218.3, 218.0, 217.8, 217.5, 217.3, 217.0, 216.8, 216.5, 216.3, 216.0, 215.8, 215.5, 215.3, 215.5, 215.8, 216.0, 216.3, 216.5, 216.8, 217.0, 217.3, 217.5, 217.8, 218.0, 218.3, 218.5, 218.8, 219.0, 219.3, 219.5, 219.8, 220.0, 220.2, 220.4, 220.6, 220.9, 221.2, 221.5, 221.8, 222.0, 222.3, 222.5, 222.8, 223.0, 223.3, 223.5, 223.8, 224.0, 224.3, 224.5, 224.8, 225.0, 224.8, 224.5, 224.3, 224.0, 223.8, 223.5, 223.3, 223.0, 222.8, 222.5, 222.3, 222.0, 221.8, 221.5, 221.2, 220.9, 220.6, 220.4, 220.2, 220.0, 219.8], "ub": [219.0, 219.3, 219.6, 219.9, 220.2, 220.5, 220.8, 221.1, 220.8, 220.5, 220.2, 219.9, 219.6, 219.3, 219.0, 218.7, 218.4, 218.1, 217.8, 217.5, 217.2, 216.9, 216.6, 216.3, 216.0, 215.7, 215.4, 215.1, 214.8, 214.5, 214.2, 213.9, 213.6, 213.3, 213.0, 212.7, 213.0, 213.3, 213.6, 213.9, 214.2, 214.5, 214.8, 215.1, 215.4, 215.7, 216.0, 216.3, 216.6, 216.9, 217.2, 217.5, 217.8, 218.1, 218.4, 218.7, 219.0, 219.3, 219.6, 219.9, 220.2, 220.5, 220.8, 221.1, 221.4, 221.7, 222.0, 222.3, 222.6, 222.9, 223.2, 223.5, 223.8, 224.1, 224.4, 224.1, 223.8, 223.5, 223.2, 222.9, 222.6, 222.3, 222.0, 221.7, 221.4, 221.1, 220.8, 220.5, 220.2, 219.9, 219.6, 219.3, 219.0, 218.7, 218.4, 218.1], "uc": [221.5, 221.8, 222.1, 222.4, 222.7, 223.0, 223.3, 223.6, 223.3, 223.0, 222.7, 222.4, 222.1, 221.8, 221.5, 221.2, 220.9, 220.6, 220.3, 220.0, 219.7, 219.4, 219.1, 218.8, 218.5, 218.2, 217.9, 217.6, 217.3, 217.0, 216.7, 216.4, 216.1, 215.8, 215.5, 215.2, 215.5, 215.8, 216.1, 216.4, 216.7, 217.0, 217.3, 217.6, 217.9, 218.2, 218.5, 218.8, 219.1, 219.4, 219.7, 220.0, 220.3, 220.6, 220.9, 221.2, 221.5, 221.8, 222.1, 222.4, 222.7, 223.0, 223.3, 223.6, 223.9, 224.2, 224.5, 224.8, 225.1, 225.4, 225.7, 226.0, 226.3, 226.6, 226.9, 226.6, 226.3, 226.0, 225.7, 225.4, 225.1, 224.8, 224.5, 224.2, 223.9, 223.6, 223.3, 223.0, 222.7, 222.4, 222.1, 221.8, 221.5, 221.2, 220.9, 220.6], "ia": [1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4.0, 3.9, 3.8, 3.7, 3.6, 3.5, 3.4, 3.3, 3.2, 3.1, 3.0, 2.9, 2.8, 2.7, 2.6, 2.5, 2.4, 2.3, 2.2, 2.1, 2.0, 1.9, 1.8, 1.7, 1.6, 1.5, 1.4, 1.3, 1.2, 1.1, 1.0, 0.9, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4.0, 3.9, 3.8, 3.7], "ib": [1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 3.8, 3.7, 3.6, 3.5, 3.4, 3.3, 3.2, 3.1, 3.0, 2.9, 2.8, 2.7, 2.6, 2.5, 2.4, 2.3, 2.2, 2.1, 2.0, 1.9, 1.8, 1.7, 1.6, 1.5, 1.4, 1.3, 1.2, 1.1, 1.0, 0.9, 0.8, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 3.8, 3.7, 3.6], "ic": [1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4.0, 4.1, 4.0, 3.9, 3.8, 3.7, 3.6, 3.5, 3.4, 3.3, 3.2, 3.1, 3.0, 2.9, 2.8, 2.7, 2.6, 2.5, 2.4, 2.3, 2.2, 2.1, 2.0, 1.9, 1.8, 1.7, 1.6, 1.5, 1.4, 1.3, 1.2, 1.1, 1.0, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4.0, 4.1, 4.0, 3.9, 3.8], "pa": [0.85, 0.86, 0.87, 0.88, 0.89, 0.9, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1.0, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.11, 1.12, 1.13, 1.12, 1.11, 1.1, 1.09, 1.08, 1.07, 1.06, 1.05, 1.04, 1.03, 1.02, 1.01, 1.0, 0.99, 0.98, 0.97, 0.96, 0.95, 0.94, 0.93, 0.92, 0.91, 0.9, 0.89, 0.88, 0.87, 0.86, 0.85, 0.84, 0.83, 0.82, 0.81, 0.82, 0.83, 0.84, 0.85, 0.86, 0.87, 0.88, 0.89, 0.9, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1.0, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.11, 1.12, 1.13, 1.12, 1.11, 1.1], "pb": [0.84, 0.85, 0.86, 0.87, 0.88, 0.89, 0.9, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1.0, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.11, 1.12, 1.11, 1.1, 1.09, 1.08, 1.07, 1.06, 1.05, 1.04, 1.03, 1.02, 1.01, 1.0, 0.99, 0.98, 0.97, 0.96, 0.95, 0.94, 0.93, 0.92, 0.91, 0.9, 0.89, 0.88, 0.87, 0.86, 0.85, 0.84, 0.83, 0.82, 0.81, 0.8, 0.81, 0.82, 0.83, 0.84, 0.85, 0.86, 0.87, 0.88, 0.89, 0.9, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1.0, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.11, 1.12, 1.11, 1.1, 1.09], "pc": [0.86, 0.87, 0.88, 0.89, 0.9, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1.0, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.11, 1.12, 1.13, 1.14, 1.13, 1.12, 1.11, 1.1, 1.09, 1.08, 1.07, 1.06, 1.05, 1.04, 1.03, 1.02, 1.01, 1.0, 0.99, 0.98, 0.97, 0.96, 0.95, 0.94, 0.93, 0.92, 0.91, 0.9, 0.89, 0.88, 0.87, 0.86, 0.85, 0.84, 0.83, 0.82, 0.83, 0.84, 0.85, 0.86, 0.87, 0.88, 0.89, 0.9, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1.0, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.11, 1.12, 1.13, 1.14, 1.13, 1.12, 1.11], "ca": [0.92, 0.92, 0.92, 0.93, 0.93, 0.93, 0.94, 0.94, 0.94, 0.95, 0.95, 0.95, 0.96, 0.96, 0.96, 0.97, 0.97, 0.97, 0.98, 0.98, 0.98, 0.99, 0.99, 0.99, 0.98, 0.98, 0.98, 0.97, 0.97, 0.97, 0.96, 0.96, 0.96, 0.95, 0.95, 0.95, 0.94, 0.94, 0.94, 0.93, 0.93, 0.93, 0.92, 0.92, 0.92, 0.91, 0.91, 0.91, 0.9, 0.9, 0.9, 0.91, 0.91, 0.91, 0.92, 0.92, 0.92, 0.93, 0.93, 0.93, 0.94, 0.94, 0.94, 0.95, 0.95, 0.95, 0.96, 0.96, 0.96, 0.97, 0.97, 0.97, 0.98, 0.98, 0.98, 0.99, 0.99, 0.99, 0.98, 0.98, 0.98, 0.97, 0.97, 0.97, 0.96, 0.96, 0.96, 0.95, 0.95, 0.95, 0.94, 0.94, 0.94, 0.93, 0.93, 0.93], "cb": [0.91, 0.91, 0.91, 0.92, 0.92, 0.92, 0.93, 0.93, 0.93, 0.94, 0.94, 0.94, 0.95, 0.95, 0.95, 0.96, 0.96, 0.96, 0.97, 0.97, 0.97, 0.98, 0.98, 0.98, 0.97, 0.97, 0.97, 0.96, 0.96, 0.96, 0.95, 0.95, 0.95, 0.94, 0.94, 0.94, 0.93, 0.93, 0.93, 0.92, 0.92, 0.92, 0.91, 0.91, 0.91, 0.9, 0.9, 0.9, 0.89, 0.89, 0.89, 0.9, 0.9, 0.9, 0.91, 0.91, 0.91, 0.92, 0.92, 0.92, 0.93, 0.93, 0.93, 0.94, 0.94, 0.94, 0.95, 0.95, 0.95, 0.96, 0.96, 0.96, 0.97, 0.97, 0.97, 0.98, 0.98, 0.98, 0.97, 0.97, 0.97, 0.96, 0.96, 0.96, 0.95, 0.95, 0.95, 0.94, 0.94, 0.94, 0.93, 0.93, 0.93, 0.92, 0.92, 0.92], "cc": [0.93, 0.93, 0.93, 0.94, 0.94, 0.94, 0.95, 0.95, 0.95, 0.96, 0.96, 0.96, 0.97, 0.97, 0.97, 0.98, 0.98, 0.98, 0.99, 0.99, 0.99, 0.98, 0.98, 0.98, 0.97, 0.97, 0.97, 0.96, 0.96, 0.96, 0.95, 0.95, 0.95, 0.94, 0.94, 0.94, 0.93, 0.93, 0.93, 0.92, 0.92, 0.92, 0.91, 0.91, 0.91, 0.9, 0.9, 0.9, 0.91, 0.91, 0.91, 0.92, 0.92, 0.92, 0.93, 0.93, 0.93, 0.94, 0.94, 0.94, 0.95, 0.95, 0.95, 0.96, 0.96, 0.96, 0.97, 0.97, 0.97, 0.98, 0.98, 0.98, 0.99, 0.99, 0.99, 0.98, 0.98, 0.98, 0.97, 0.97, 0.97, 0.96, 0.96, 0.96, 0.95, 0.95, 0.95, 0.94, 0.94, 0.94, 0.93, 0.93, 0.93, 0.92, 0.92, 0.92]}}, "getPowerData": {"code": 200, "message": "成功", "data": {"powerData": [{"date": "2023-01", "value": 210}, {"date": "2023-02", "value": 190}, {"date": "2023-03", "value": 205}, {"date": "2023-04", "value": 230}, {"date": "2023-05", "value": 280}, {"date": "2023-06", "value": 320}]}}, "getHistoryEvents": {"code": 200, "message": "成功", "data": {"events": [{"eventId": "E2023060001", "eventType": "停电", "eventTime": "2023-06-15 10:30:00", "duration": "120", "status": "已恢复"}, {"eventId": "E2023050002", "eventType": "过载", "eventTime": "2023-05-20 14:15:00", "duration": "45", "status": "已处理"}, {"eventId": "E2023040003", "eventType": "异常电压", "eventTime": "2023-04-05 08:20:00", "duration": "30", "status": "已处理"}]}}, "getAbnormalData": {"code": 200, "message": "成功", "data": {"abnormals": [{"orderId": "WO202306001", "orderType": "电表异常", "userAddress": "上海市浦东新区金桥路100号", "checkResult": "零火线不一致", "orderTime": "2023-06-10 09:15:00", "status": "待处理"}, {"orderId": "WO202305002", "orderType": "用电异常", "userAddress": "上海市浦东新区张江路500号", "checkResult": "功率因数过低", "orderTime": "2023-05-18 14:30:00", "status": "已处理"}]}}, "getLineLossData": {"code": 200, "message": "成功", "data": {"lineLoss": [{"date": "2023-01", "technicalLoss": 3.2, "managementLoss": 1.5, "totalLoss": 4.7}, {"date": "2023-02", "technicalLoss": 3.0, "managementLoss": 1.3, "totalLoss": 4.3}, {"date": "2023-03", "technicalLoss": 3.1, "managementLoss": 1.4, "totalLoss": 4.5}, {"date": "2023-04", "technicalLoss": 3.3, "managementLoss": 1.6, "totalLoss": 4.9}, {"date": "2023-05", "technicalLoss": 3.5, "managementLoss": 1.7, "totalLoss": 5.2}, {"date": "2023-06", "technicalLoss": 3.4, "managementLoss": 1.6, "totalLoss": 5.0}]}}}}