<template>
	<view class="mainContainer">
		<second-navbar title="工单明细"
			@search-click="handleNavbarSearch"></second-navbar>

		<!-- 搜索弹框 -->
		<u-popup :show="showpopup" :round="10" mode="bottom" @close="close" @open="open"
			:closeOnClickOverlay="closeOnClick">
			<view class="popupcontainer">
				<view class="poptitle">
					查询
				</view>
				<u--form :labelStyle="{ fontWeight: 'bold' }" labelPosition="left" :model="uForm" ref="uForm">
					<u-form-item class="formitem" labelWidth="120" label="供电单位" prop="" borderBottom
						style="position: relative;padding-top: 20rpx;" ref="item1">
						<DaTreeVue2 class="DaTreestyle" :style="judge?'background-color:white;height:400rpx':''"
							ref="DaTreeRef" :data="treeData" labelField="name" valueField="mgtOrgCode" expandChecked
							:defaultCheckedKeys="defaultCheckedKeysValue" @change="handleTreeChange"
							@expand="handleExpandChange"></DaTreeVue2>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="用户编号" prop="custNo" borderBottom ref="item1">
						<u--input placeholder="请输入用户编号" border="surround" v-model="uForm.custNo"></u--input>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="用户名称" prop="custName" borderBottom
						ref="item2">
						<u--input placeholder="请输入用户名称" border="surround" v-model="uForm.custName"></u--input>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="用电地址" prop="ecAddr" borderBottom ref="item3">
						<u--input placeholder="请输入用电地址" border="surround" v-model="uForm.ecAddr"></u--input>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="用户类型" prop="consType" borderBottom
						ref="item4">
						<u-radio-group v-model="uForm.consType" style="display: flex;flex-wrap: wrap;">
							<u-radio name="00" style="width: 50%;">全部</u-radio>
							<u-radio name="01" style="width: 50%;">高压</u-radio>
							<u-radio name="02" style="width: 50%;">低压非居民</u-radio>
							<u-radio name="03" style="width: 50%;">低压居民</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="工单来源" prop="orderSrc" borderBottom
						ref="item5">
						<u-radio-group v-model="uForm.orderSrc" style="display: flex;flex-wrap: wrap;">
							<u-radio name="00" style="width: 50%;">全部</u-radio>
							<u-radio name="01" style="width: 50%;">反窃电系统</u-radio>
							<u-radio name="02" style="width: 50%;">营销系统</u-radio>
							<u-radio name="03" style="width: 50%;">历史数据</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="工单状态" prop="orderStatus" borderBottom
						ref="item6">
						<u-radio-group v-model="uForm.orderStatus" style="display: flex;flex-wrap: wrap;">
							<u-radio name="00" style="width: 50%;">全部</u-radio>
							<u-radio name="01" style="width: 50%;">待派工</u-radio>
							<u-radio name="02" style="width: 50%;">待检查</u-radio>
							<u-radio name="03" style="width: 50%;">待处理</u-radio>
							<u-radio name="07" style="width: 50%;">待收费</u-radio>
							<u-radio name="08" style="width: 50%;">已归档</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="工单类型" prop="stealType" borderBottom
						ref="item7">
						<u-radio-group v-model="uForm.stealType" style="display: flex;flex-wrap: wrap;">
							<u-radio name="00" style="width: 50%;">全部</u-radio>
							<u-radio name="01" style="width: 50%;">窃电</u-radio>
							<u-radio name="02" style="width: 50%;">违约用电</u-radio>
							<u-radio name="03" style="width: 50%;">无协议用电</u-radio>
							<u-radio name="04" style="width: 100%;">窃电及违约用电</u-radio>
							<u-radio name="05" style="width: 50%;">无违约用电</u-radio>
						</u-radio-group>
					</u-form-item>
					<view class="ubutton">
						<u-button type="default" style="border: 1px solid lightgray;width: 300rpx;" text="重置"
							@click="reset"></u-button>
						<u-button type="primary" style="color: #fcfefd;width: 300rpx;" color="#07ac7c" text="查询"
							@click="search"></u-button>
					</view>
				</u--form>
			</view>
		</u-popup>


		<!-- 列表 -->
		<view v-if="dataShow"
			style="padding: 20rpx 40rpx 20rpx 40rpx;margin: 20rpx;height: 500rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;">
			<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
			<view style="color: gainsboro;">
				数据为空
			</view>
		</view>
		<view class="listcontainer" v-for="(item,index) in dataArray" :key="index" @click="gotoFullDetail(item)">
			<view class="lefttext">
				<view class="listtitle">
					用户编号：<text style="color: blue;">{{item.custNo}}</text>
				</view>
				<view class="datatext">
					<view class="datacontainer">
						<view class="datatitle">工单编号：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.appNo}}</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">供电单位：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.mgtOrgName}}</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">台区名称：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.resrcSuplName}}
						</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">用电地址：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.ecAddr}}</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">用户名称：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.custName}}</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">工单状态：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.orderStatus}}</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">日期：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.checkDate}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// import uvVirtualList from '@/components/uv-ui/uv-virtual-list/uv-virtual-list.vue'
	import DaTreeVue2 from '@/components/da-tree-vue2/index.vue'
	import utils from '@/static/commonJs/util.js'
	export default {
		components: {
			DaTreeVue2,
			// uvVirtualList
		},
		data() {
			return {
				testdev: true,
				dataShow: true,
				dataList: [],
				itemHeight: 120,
				loading: false,
				noMore: false,
				judge: false,
				treeData: [],
				// 单选时默认值为字符串或数值，不能为数组
				defaultCheckedKeysValue: '31',
				date: '2025-04-22',
				dataArray: [],
				uForm: {
					mgtOrgCode: "31102",
					custNo: "", //用户编号
					custName: "", //用户名称
					ecAddr: "", //用电地址
					consType: "00", //用户类型
					orderSrc: "00", //工单来源
					orderStatus: "00", //工单状态
					stealType: "00", //工单类型
					pageNum: 1,
					pageSize: 5,
					totalCount: 0,
				},
				closeOnClick: true,
				showpopup: false,
			}
		},
		onLoad() {},
		onReady() {
			// this.init();
			// this.getMgtOrgCode();
			this.search();
		},
		onReachBottom() {
			if (this.dataArray.length < this.uForm.totalCount) {
				this.uForm.pageNum++
				this.search()
			} else {
				uni.showToast({
					title: '没有更多了',
					icon: 'none'
				})
			}
		},
		onPullDownRefresh() {
			this.uForm.pageNum = 1
			this.search().finally(() => {
				uni.stopPullDownRefresh()
			})
		},
		methods: {
			reset() {
				this.uForm.mgtOrgCode = "31102"
				this.uForm.custNo = ""
				this.uForm.custName = ""
				this.uForm.ecAddr = ""
				this.uForm.consType = ""
				this.uForm.orderSrc = ""
				this.uForm.orderStatus = ""
				this.uForm.stealType = ""
				this.uForm.pageNum = 1
				this.uForm.pageSize = 5
			},
			async init() {
				try {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: 'http://10.131.136.68:17002/fqd2test/WXAPI/RequestSBCF',
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryUserInfoByBody",
									"data": {
										"operatorId": utils.userInfo.USERNAME,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					if (code === 200) {
						this.uForm.mgtOrgCode = data.mgtOrgCode;
					}
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				}
			},
			async search() {
				if (this.testdev) {
					uni.hideLoading()
					this.dataShow = false;
					this.dataArray = [
						{
							custNo:'3100060729412',
							appNo:'3125063000547940',
							mgtOrgName:'市南供电公司',
							resrcSuplName:'浦电4所变',
							ecAddr:'华林路228弄4号501室',
							custName:'邱源',
							orderStatus:'已归档',
							checkDate:'2025/7/2 9:08:59',
							orderCreator:'虞琰文',
							consType:'低压居民',
							orderSrc:'反窃电系统',
							workOrderType:'窃电',
							checkRslt:'窃电',
							stepStateTime:'2025/7/2',
							overdue:'否',
							abnorDesc:'本部-异常',
							// zhi:"",
							// fu:"",
						},
						{
							custNo:'3100000321637',
							appNo:'3125063000551310',
							mgtOrgName:'市南供电公司',
							resrcSuplName:'市南4街坊4号配变',
							ecAddr:'七宝七莘路3198弄38号1204室',
							custName:'姚志群',
							orderStatus:'已归档',
							checkDate:'2025/7/2 9:08:36',
							orderCreator:'虞琰文',
							consType:'低压居民',
							orderSrc:'反窃电系统',
							workOrderType:'无违约窃电',
							checkRslt:'无违约',
							stepStateTime:'2025/7/2',
							overdue:'否',
							abnorDesc:'本部-异常'
						},
					],
					this.uForm.totalCount = 2
					this.showpopup = false
				} else {
					if (this.loading) return

					this.loading = true;
					uni.showLoading({

					})
					// 调用后端接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "WorkOrderController",
										"method": "detail",
										"data": {
											"mgtOrgCode": this.uForm.mgtOrgCode,
											"custNo": this.uForm.custNo,
											"custName": this.uForm.custName,
											"ecAddr": this.uForm.ecAddr,
											"consType": this.uForm.consType,
											"orderSrc": this.uForm.orderSrc,
											"orderStatus": this.uForm.orderStatus,
											"stealType": this.uForm.stealType,
											"app":true,
											"pageNum": this.uForm.pageNum,
											"pageSize": this.uForm.pageSize
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.hideLoading()
							this.dataShow = false;
							this.dataArray = this.uForm.pageNum === 1 ? data.list : [...this.dataArray, ...data.list],
								this.uForm.totalCount = data.totalCount
							this.showpopup = false
						} else {
							uni.hideLoading()
							this.dataShow = true;
							this.loading = false
							this.showpopup = false
						}
					} finally {
						this.loading = false
						this.showpopup = false
					}
				}
			},
			async getMgtOrgCode() {
				// 调用后端登录接口
				try {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryMgtOrgTree",
									"data": {
										"mgtOrgCode": this.uForm.mgtOrgCode,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					if (code === 200) {
						this.treeData = data;
					} else {
						uni.showToast({
							title: '查询失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
					console.log(error)
				}
			},
			gotoFullDetail(item) {
				uni.navigateTo({
					url: '/pages/fileQuery/jobMonitor/orderFullDetail?data=' + encodeURIComponent(JSON.stringify(
						item))
				})
			},
			handleNavbarSearch(payload) {
				console.log('收到搜索事件，参数:', payload.keyword);
				// 执行父页面的操作（如跳转、弹窗、请求数据等）
				this.showpopup = payload.keyword;
			},
			open() {
				// console.log('open');
			},
			close() {
				this.judge = false;
				this.showpopup = false
			},
			handleTreeChange(values, currentItem) {
				// 支持修改节点数据
				// currentItem.label = `${currentItem.originItem.name}`
				this.uForm.mgtOrgCode = values;
				console.log('handleTreeChange ==>', values, currentItem)
			},
			handleExpandChange(expand, currentItem) {
				// 支持修改节点数据
				// currentItem.label = `${currentItem.originItem.name}`
				if (expand == true) {
					this.judge = true;
				} else {
					this.judge = false;
				}
				console.log('handleExpandChange ==>', expand);
				console.log('handleExpandChange ==>', currentItem);
			},

			expandTree(keys, expand) {
				console.log('expandTree ==>', keys, expand)
				this.$refs.DaTreeRef?.setExpandedKeys(keys, expand)

				const gek = this.$refs.DaTreeRef?.getExpandedKeys()
				console.log('当前已展开的KEY ==>', gek)
			},
			checkedTree(keys, checked) {
				console.log('checkedTree ==>', keys, checked)
				this.$refs.DaTreeRef?.setCheckedKeys(keys, checked)

				const gek = this.$refs.DaTreeRef?.getCheckedKeys()
				console.log('当前已选中的KEY ==>', gek)
			},
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
		// font-family: 'SimHei',sans-serif;
	}

	.select-box {
		border: 1rpx solid #ddd;
		width: 100%;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.arrow {
		float: right;
		border: solid #999;
		border-width: 0 2rpx 2rpx 0;
		padding: 6rpx;
		margin-top: 6rpx;
	}

	.down {
		transform: rotate(45deg);
	}

	.up {
		transform: rotate(-135deg);
	}

	.dropdown {
		position: absolute;
		width: 100%;
		border: 1rpx solid #eee;
		background: #fff;
		z-index: 999;
	}

	.dropdown-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.mainContainer {
		.list-item {
			display: flex;
			align-items: center;
			padding: 20rpx;
			border-bottom: 1rpx solid #f0f0f0;
		}

		.list-item image {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			margin-right: 20rpx;
		}

		.content {
			flex: 1;
		}

		.title {
			font-size: 32rpx;
			color: #333;
		}

		.desc {
			font-size: 24rpx;
			color: #999;
			margin-top: 8rpx;
		}

		.loading-text {
			text-align: center;
			padding: 20rpx;
			color: #999;
		}

		.popupcontainer {
			::v-deep .u-popup__content .u-popup__content__close {
				display: none !important;
			}

			height: 1500rpx;
			padding: 0 40rpx;

			.poptitle {
				text-align: center;
				font-size: 36rpx;
				font-weight: bold;
				padding: 50rpx 0;
			}

			.formitem {
				.DaTreestyle {
					// border: 1px solid black;
					// background-color: white;
					position: absolute;
					top: 24rpx;
					z-index: 999;
				}

				// font-weight: bold;
				.calendarContainer {
					width: 100%;

					.calInput {
						width: 100%;
						// margin-right: 30rpx;
					}
				}
			}

			.ubutton {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-top: 100rpx;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 20rpx;

			.lefttext {
				flex: 1;

				.listtitle {
					font-weight: bold;
					font-size: 36rpx;
					padding: 20rpx 20rpx;
				}

				.datatext {
					padding: 20rpx 0 20rpx 20rpx;

					.datacontainer {
						display: flex;
						align-items: center;
						padding: 6rpx 0;

						.datatitle {
							font-size: 26rpx;
							width: 180rpx;
							color: darkgray;
						}
					}

					// display: flex;
					// flex-direction: column;
					// justify-content: center;
					// align-items: center;
				}
			}
		}
	}
</style>