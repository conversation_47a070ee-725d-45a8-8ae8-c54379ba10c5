<template>
	<view>
		<view class="photo-item" v-for="(item,index) in photoList" :key="index">
			<view class="top-text" :style="{ backgroundColor: item.color }">
				<u-row justify="space-between">
					<u-col span="9">
						<text style="font-size: 14px;"
							:style="{color: item.color == '#fff'?'#000':(item.color == '#f00'?'#fff':'')}"><text
								:style="{color:item.color=='#fff'?'red':(item.color == '#009d85' ?'red':'')}">*</text>{{item.name}}</text>
		
					</u-col>
					<u-col span="2">
						<u-row justify="space-between">
							<u-icon
								:name="item.color =='#fff'?'checkmark-circle-fill':(item.color =='#009d85'? 'checkmark-circle':'checkmark-circle-fill')"
								:color="item.color =='#fff'?'#009d85':(item.color == '#009d85' ?'#fff':'#009d85')"
								size="28" @click="okClick('0',index,item)"></u-icon>
							<u-icon
								:name="item.color =='#fff'?'close-circle-fill':(item.color =='#f00'? 'close-circle':'close-circle-fill')"
								:color="item.color =='#fff'?'#f00':(item.color == '#f00' ?'#fff':'#f00')"
								size="28" @click="okClick('1',index,item)"></u-icon>
						</u-row>
		
					</u-col>
				</u-row>
			</view>
			<view class="upload-wrap">
				<!-- <u-icon name="warning-fill" color="#fe9d23" size="25" @click="showTip1 = true"></u-icon> -->
				<view :class="imagesList ? 'openImgList' : 'closeImgList'"
					v-for="(it, inx) in item.imgList" :key="String(inx)">
					<u--image class="images" :showLoading="true" :src="it.img" width="60px"
						height="60px"></u--image>
					<u-icon name="close-circle-fill" class="right-close"
						@click="delImg(inx,it)"></u-icon>
				</view>
				<u--image :class="images ? 'openImg' : 'closeImg'" :showLoading="true"
					style="display: inline-block;" :src="src" width="60px" height="60px"
					@click="imageSrc(item)"></u--image>
				<u-popup :show="showTip1" mode="center" @close="close" @open="open">
					<view>
						<text>人生若只如初见，何事秋风悲画扇</text>
					</view>
				</u-popup>
			</view>
		
		</view>
	</view>
</template>

<script>
	export default {
		name:"checkItem",
		props: {
			photoList: {
			  type: Array,
			  default: [],
			},
		},
		data() {
			return {
				showTip1: false,
				indexImg: '',
				images: true,
				imagesList: true,
				src: '../../static/img/shangchuan.png',
				chooseImg: [],
				iconNameGreen: 'checkmark-circle-fill',
				iconNameRed: 'close-circle-fill',
			};
		},
		methods: {
			delImg(inx,it) {
				for (let i=0;i<this.photoList.length;i++) {
					let item = this.photoList[i];
					let filterItem = item.imgList.filter((item) => item.img == it.img);
					if(filterItem.length) {
						item.imgList.splice(inx,1)
					}
				}
			},
			okClick(it, index,item) {
				if (it == '0') {
					this.photoList[index].color = '#009d85';
					item.passFlag = "01";
				} else {
					this.photoList[index].color = '#f00';
					item.passFlag = "02";
				}
			
			},
			open() {},
			close() {
				this.showTip1 = false
			},
			imageSrc(item) {
				console.log(item)
				var vm = this;
				var acceptFileType = /^jpg?|jpeg|png$/i;
				if (item.files?.length >= 3) {
					uni.showToast({
						title: '最多上传3张照片',
						icon: 'none'
					});
					return;
				}
				wx.chooseImage({
					count: 1, // 默认9
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['camera'], // 可以指定来源是相册还是相机，默认二者都有
					quality: 0.1, //压缩质量，范围0～1，数值越小，质量越低，压缩率越高（仅对jpg有效）
					success: function(res) {
						var localIds = res.localIds; // 返回选定照片的本地ID列表
						console.log(localIds[0]);
						let splitArr = localIds[0].split('/');
						let fileType = splitArr[splitArr.length - 1].split('.')[1];
						console.log('文件类型：', fileType);
						if (!acceptFileType.test(fileType)) {
							uni.showToast({
								title: '支持格式: .jpg .png .jpeg',
								icon: 'none'
							});
							return;
						}
						item.files?.length && item.files.push(localIds[0]);
						console.log(vm.photoList);
						vm.chooseImg.push(localIds[0]);
						vm.compres(localIds[0], item);
						console.log(vm.chooseImg);
						vm.images = false;
						vm.imagesList = true;
						wx.getLocalImgData({
							localId: localIds[0], // 图片的localID
							success: function(ress) {
								if ('getLocalImgData:ok' === ress.errMsg) {
									// vm.uploadImg(vm.imgList)
									// vm.imgList.push(res.localData)
									vm.images = false;
									vm.imagesList = true;
									var localData = ress.localData;
									console.log('1压缩前长度', localData.length);
									// vm.dealImage(item, localData, 600,vm.useImg, imgSrc);
								}
							}
						});
					}
				});
			},
			compres(localIds, type) {
				var vm = this;
				// var localIds = localIds.split("//")[1]
				wx.invoke('getLocalResPath', {
					localId: localIds
				}, function(res) {
					console.log("pathtrans", res.localResPath);
					var arr = res.localResPath.split('/');
					var storagePath = arr.splice(0, arr.length - 1).join('/');
					wx.invoke(
						'ext_Compressor_Quality', {
							data: {
								quality: '50', // 0-100的字符串，例如：50
								imgPath: localIds.split('//')[1], // 图片的地址
								storagePath: storagePath // 压缩后图片的存储地址
							}
						},
						function(res) {
							console.log(JSON.stringify(res));
							if (res.err_msg === 'ext_Compressor_Quality:ok') {
								//成功处理
								console.log(res.result);
								console.log('img://' + res.path)
								wx.getLocalImgData({
									localId: 'img://' + res.path, // 图片的localID
									success: function(ress) {
										if ('getLocalImgData:ok' === ress.errMsg) {
											var itemss = vm.imgList.filter(v => v.type ==
												type); //[{}]
											console.log(itemss);
											var localData = ress.localData;
											itemss[0].img.push({
												appendixName: localIds,
												base64: localData
											});
											console.log('压缩后长度', localData.length);
											// vm.imgList.filter((v) => v.type == image.name)
											// vm.dealImage(item, localData, 600,vm.useImg, imgSrc);
										}
									}
								});
								// res.path
							}
							if (res.err_msg === 'ext_Compressor_Quality:fail') {
								//失败处理
								console.log(res.result);
							}
							if (res.err_msg === 'ext_Compressor_Quality:cancel') {
								//取消处理
								console.log(res.result);
							}
						}
					);
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
.photo-item {
		border-radius: 20rpx;
		background: #f5f5f5;
		padding: 16rpx;

		.top-text,
		.upload-wrap {
			border-radius: 20rpx;
			padding: 18rpx;
			background: #fff;
			margin-bottom: 20rpx;
		}

		.upload-wrap {
			margin-bottom: 0;
		}
	}
	.openImg {
		margin-left: 10rpx;
		margin-bottom: 20rpx;
	}
	
	.closeImg {
		display: none;
	}
	
	.openImgList {
		display: inline-block;
		position: relative;
		margin: 0 4px 10px;
	
		.right-close {
			position: absolute;
			top: -10px;
			right: -10px;
			z-index: 1;
		}
	}
	
	.closeImgList {
		display: none;
	}
	
	.upload-wrap {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex-wrap: wrap;
	}
</style>