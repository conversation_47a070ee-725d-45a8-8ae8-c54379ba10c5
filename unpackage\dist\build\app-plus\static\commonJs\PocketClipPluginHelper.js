import backClipUtil from "@/static/commonJs/backclip_util.js";
import ResultEntryBuilder from "@/static/commonJs/ResultEntryBuilder.js";

var _baseParam = {
	"userCode": "",
	"gatewayType": "uniform_XJ",
	"routeUrl": "",
	"header": {
		"Content-Type": "application/json;charset=utf-8",
		'token': "",
		"serviceCode": "igwsafeAuthUniformRequest"
	},
	"body": {
		"serviceCode": "igwsafeAuthUniformRequest"
	},
	"closeVpn": false
};
var _meterParam = {
	"meterAgreement": "",
	"meterAddress": "",
	"meterType": "",
	"commType": "",
	"baudRate": "1200"
};
var _taskParam = {
	"appType": "",
	"orderNo": "",
	"taskType": "",
	"taskData": {},
	"data": ""
};
var PocketClipPluginHelper = {
	/**
	 * @param {String} userCode 用户账号
	 * @param {String} routeUrl 网关的路由转发地址全路径
	 * @param {String} serviceUrl 安全中心内网网址
	 * @param {Boolean} closeVpn 是否使用I型网关 true:使用 false：不使用； 可为空
	 */
	setBaseParam: function(userCode, routeUrl, token, closeVpn) {
		console.log("setBaseParam")
		_baseParam.userCode = userCode;
		_baseParam.routeUrl = routeUrl;
		_baseParam.header.token = token;
		if (closeVpn !== null) {
			_baseParam.closeVpn = closeVpn
		}
		return this;
	},
	/**
	 * @param {String} meterAgreement 计量设备通信规约
	 * @param {String} meterAddress 表地址
	 * @param {String} meterType 电表类型
	 * @param {String} commType 通讯方式
	 * @param {String} baudRate 波特率 可为空
	 */
	setMeterParam: function(meterAgreement, meterAddress, meterType, commType, baudRate) {
		console.log("setMeterParam")
		_meterParam.meterAgreement = meterAgreement;
		_meterParam.meterAddress = meterAddress;
		_meterParam.meterType = meterType;
		_meterParam.commType = commType;
		if (baudRate !== null && baudRate !== '' && baudRate !== undefined) {
			_meterParam.baudRate = baudRate;
		}
		return this;
	},
	/**
	 * @param {String} appType 业务类型 0001-抄读 0002-电价调整 0004-拉合闸 0005-设置结算日  0006-校时  0008-充值开户  0009-统一设参  
	 * @param {String} orderNo 工单编号
	 * @param {String} taskType 任务类型 01-校时 03-跳闸，04-合闸，06-允许合闸，07-保电解除，08-保电 05-抄读 10-充值 11-开户 20-设置结算日 21-电价调整 30-统一设参
	 * @param {Object} taskData 任务格式数据（获取权限）
	 * @param {Array} data 功能执行任务数据，根据功能不同进行改变（如抄表任务，为readDates）
	 */
	setTaskParam: function(appType, orderNo, taskType, taskData, data) {
		console.log("setTaskParam")
		_taskParam.appType = appType;
		_taskParam.orderNo = orderNo;
		_taskParam.taskType = taskType;
		_taskParam.taskData = taskData;
		_taskParam.data = data;
		return this;
	},
	executePocketClipTask: function executePocketClipTask(callBack) {
		console.log("_baseParam", JSON.stringify(_baseParam));
		console.log("_meterParam", JSON.stringify(_meterParam));
		console.log("_taskParam", JSON.stringify(_taskParam));

		new Promise((resolve) => {
			//STEP1:安全插件初始化 init
			callBack(ResultEntryBuilder.buildProcessEntry("1.安全插件初始化中..."));
			backClipUtil.init(null, function(res) {
				console.log("1.res: " + JSON.stringify(res));
				if (res.ret === 1) {
					callBack(ResultEntryBuilder.buildSuccessEntry("安全插件初始化成功", res))
					console.log("1.安全插件初始化" + res)
					resolve(res.ret)
				} else {
					callBack(ResultEntryBuilder.buildErrorEntry("安全插件初始化失败:" + res.errorMsg));
				}
			})
		}).then(ret => {
			callBack(ResultEntryBuilder.buildProcessEntry("2.安全插件安全认证中..."));
			return new Promise((resolve) => {
				//STEP2:安全插件安全认证 esamAuth
				backClipUtil.esamAuth(_baseParam, function(res) {
					console.log("2.res: " + JSON.stringify(res));
					if (typeof(res) == 'string') {
						res = JSON.parse(res)
					}
					if (res.ret === 1) {
						callBack(ResultEntryBuilder.buildSuccessEntry("安全插件安全认证成功",
							res))
						console.log("2.安全插件安全认证" + res)
						resolve(res.ret)
					} else {
						callBack(ResultEntryBuilder.buildErrorEntry("安全插件安全认证失败:" +
							res.errorMsg));
					}
				})
			})
		}).then(ret => {
			callBack(ResultEntryBuilder.buildProcessEntry("3.安全插件获取权限中..."));
			return new Promise((resolve) => {
				//STEP3:安全插件获取权限 getAuth
				var param = {
					"appType": _taskParam.appType,
					"orderNo": _taskParam.orderNo,
					"meterAgreement": _meterParam.meterAgreement,
					"meterAddress": _meterParam.meterAddress,
					"taskType": _taskParam.taskType,
					"taskData": _taskParam.taskData
				};
				Object.assign(param, _baseParam);
				backClipUtil.getAuth(param, function(res) {
					console.log("3.res: " + JSON.stringify(res));
					if (typeof(res) == 'string') {
						res = JSON.parse(res)
					}
					if (res.ret === 1) {
						callBack(ResultEntryBuilder.buildSuccessEntry("安全插件获取权限成功",
							res))
						console.log("3.安全插件获取权限" + res)
						resolve(res.ret)
					} else {
						callBack(ResultEntryBuilder.buildErrorEntry("安全插件获取权限失败:" +
							res.errorMsg));
					}
				})
			})
		}).then(ret => {
			callBack(ResultEntryBuilder.buildProcessEntry("4.安全插件任务执行中..."));
			return new Promise((resolve) => {
				console.log("4.安全插件任务执行")
				//STEP4:安全插件任务执行
				var param = "";
				var result = "";
				if (_taskParam.appType === "0001") {
					callBack(ResultEntryBuilder.buildProcessEntry("抄表任务开始执行..."));
					param = {
						"orderNo": _taskParam.orderNo,
						"readDatas": _taskParam.data
					};
					Object.assign(param, _baseParam, _meterParam);
					backClipUtil.readData(param, function(res) {
						console.log("res: " + JSON.stringify(res));
						if (typeof(res) == 'string') {
							res = JSON.parse(res)
						}
						console.log("typeof(res.ret)=====>" + typeof(res.ret));
						if (res.ret == 1) {
							console.log("抄表任务执行成功" + res);
							resolve(ResultEntryBuilder.buildSuccessEntry("抄表任务执行成功！",
								res));
						} else {
							callBack(ResultEntryBuilder.buildErrorEntry("抄表任务执行失败:" +
								res.errorMsg));
						}
					})
				} else if (_taskParam.appType === "0002") {
					callBack(ResultEntryBuilder.buildProcessEntry("电价调整任务开始执行..."));
					//电价调整
					param = {
						"orderNo": _taskParam.orderNo,
						"taskData": _taskParam.data
					};
					Object.assign(param, _baseParam, _meterParam);
					backClipUtil.adjustPrice(param, function(res) {
						if (typeof(res) == 'string') {
							res = JSON.parse(res)
						}
						if (res.ret == 1) {
							console.log("电价调整任务执行成功" + res);
							resolve(ResultEntryBuilder.buildSuccessEntry("电价调整任务执行成功！",
								res));
						} else {
							callBack(ResultEntryBuilder.buildErrorEntry("电价调整任务执行失败:" +
								res.errorMsg));
						}
					})
				} else if (_taskParam.appType === "0004") {
					callBack(ResultEntryBuilder.buildProcessEntry("停复电任务开始执行..."));
					//拉合闸
					param = {
						"orderNo": _taskParam.orderNo,
						"taskType": _taskParam.taskType
					};
					Object.assign(param, _baseParam, _meterParam);
					backClipUtil.switchPower(param, function(res) {
						if (typeof(res) == 'string') {
							res = JSON.parse(res)
						}
						if (res.ret == 1) {
							console.log("停复电任务执行成功" + res);
							resolve(ResultEntryBuilder.buildSuccessEntry("停复电任务执行成功！",
								res));
						} else {
							callBack(ResultEntryBuilder.buildErrorEntry("停复电任务执行失败:" +
								res.errorMsg));
						}
					})
				} else if (_taskParam.appType === "0005") {
					callBack(ResultEntryBuilder.buildProcessEntry("设置结算日任务开始执行..."));
					//设置结算日
					param = {
						"orderNo": _taskParam.orderNo,
						"taskData": _taskParam.taskData
					};
					Object.assign(param, _baseParam, _meterParam);
					backClipUtil.setSettlementDay(param, function(res) {
						if (typeof(res) == 'string') {
							res = JSON.parse(res)
						}
						if (res.ret == 1) {
							console.log("设置结算日任务执行成功" + res);
							resolve(ResultEntryBuilder.buildSuccessEntry("设置结算日任务执行成功！",
								res));
						} else {
							callBack(ResultEntryBuilder.buildErrorEntry("设置结算日任务执行失败:" +
								res.errorMsg));
						}
					})
				} else if (_taskParam.appType === "0006") {
					callBack(ResultEntryBuilder.buildProcessEntry("校时任务开始执行..."));
					//校时
					param = {
						"orderNo": _taskParam.orderNo
					};
					Object.assign(param, _baseParam, _meterParam);
					backClipUtil.setMeterTime(param, function(res) {
						if (typeof(res) == 'string') {
							res = JSON.parse(res)
						}
						if (res.ret == 1) {
							console.log("校时任务执行成功" + res);
							resolve(ResultEntryBuilder.buildSuccessEntry("校时任务执行成功！",
								res));
						} else {
							callBack(ResultEntryBuilder.buildErrorEntry("校时任务执行失败:" +
								res.errorMsg));
						}
					})
				} else if (_taskParam.appType === "0008") {
					callBack(ResultEntryBuilder.buildProcessEntry("充值/开户准备执行..."));
					//充值开户
					param = {
						"orderNo": _taskParam.orderNo
					};
					Object.assign(param, _baseParam, _meterParam);
					if (_taskParam.taskType === "10") {
						callBack(ResultEntryBuilder.buildProcessEntry("充值任务开始执行..."));
						//充值
						backClipUtil.fillMoney(param, function(res) {
							if (typeof(res) == 'string') {
								res = JSON.parse(res)
							}
							if (res.ret == 1) {
								console.log("充值任务执行成功" + res);
								resolve(ResultEntryBuilder.buildSuccessEntry(
									"充值任务执行成功！", res));
							} else {
								callBack(ResultEntryBuilder.buildErrorEntry(
									"充值任务执行失败:" + res.errorMsg));
							}
						})
					} else {
						callBack(ResultEntryBuilder.buildProcessEntry("开户任务开始执行..."));
						//开户
						backClipUtil.openAccount(param, function(res) {
							if (typeof(res) == 'string') {
								res = JSON.parse(res)
							}
							if (res.ret == 1) {
								console.log("开户任务执行成功" + res);
								resolve(ResultEntryBuilder.buildSuccessEntry(
									"开户任务执行成功！", res));
							} else {
								callBack(ResultEntryBuilder.buildErrorEntry(
									"开户任务执行失败:" + res.errorMsg));
							}
						})
					}

				} else if (_taskParam.appType === "0009") {
					callBack(ResultEntryBuilder.buildProcessEntry("统一设参任务开始执行..."));
					//统一设参
					param = {
						"orderNo": _taskParam.orderNo,
						"setParams": _taskParam.data
					};
					Object.assign(param, _baseParam, _meterParam);
					backClipUtil.setParams(param, function(res) {
						if (typeof(res) == 'string') {
							res = JSON.parse(res)
						}
						if (res.ret == 1) {
							console.log("统一设参任务执行成功" + res);
							resolve(ResultEntryBuilder.buildSuccessEntry("统一设参任务执行成功！",
								res));
						} else {
							callBack(ResultEntryBuilder.buildErrorEntry("统一设参任务执行失败:" +
								res.errorMsg));
						}
					})
				} else {
					callBack(ResultEntryBuilder.buildErrorEntry("业务类型错误"));
				}
			})
		}).then(res => {
			callBack(res)
		})
	}
}
module.exports = PocketClipPluginHelper
