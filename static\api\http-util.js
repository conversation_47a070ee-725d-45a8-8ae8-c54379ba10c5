import Axios from 'axios'
import SM from "../commonJs/SM-method.js";
const apiUrl = ''
const token = ''
//const apiUrl = 'http://127.0.0.1:${`从缓存中获取的端口号`}/publicInterface/outerNetGateway/index'
/** **** 创建axios实例 ***** */
const axios = Axios.create({
  baseURL: apiUrl,
  timeout: 30000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json; charset=utf-8',
	'token': token
  }
})

// 添加请求拦截器
axios.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    const { data } = config
    return config
  },
  (error) => {
    // 对请求错误做些什么
    console.log('对请求错误做些什么', error)
    return Promise.reject(error)
  }
)

// 添加响应拦截器
axios.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
	console.log("axios响应：",response)
	let res = response.data.data;
	// console.log("解密",SM.decrypt(res))
	return SM.decrypt(res);
	// return res;
  },
  (error) => {
    // 对响应错误做点什么
    return error;
  }
)

const postAxios = (url, data, config) => {
  return axios.post(url, data, config)
}

const getAxios = (url, data) => {
  return axios.get(url, {
    params: data
  })
}




const request = (url, data, type, config) => {
	console.log("=================================axios入参=============================");
	console.log("url:",url);
	console.log("data:",data);
	// console.log(SM.decrypt(data))
	if (type === 'post') {
		console.log("11111")
		return postAxios(url, data, config);
	}
	return getAxios(url, data);
}

const requestHy = (url, data, token, config) => {
	console.log("=================================axios入参=============================");
	console.log("url:",url);
	console.log("data:",data);
	console.log("token:",token);
	
	return axios({
			"method": "post",
			"url": url,//`http://127.0.0.1:${url}/publicInterface/outerNetGateway/index`,
			"headers": {
				"Content-Type": "application/json;charset=utf-8",
				'token': token
			},
			"data": {
				"data":data
			}
	});
}

export default {
	request,
	requestHy
}

