<template>
	<view class="wrap">
		<view class="header-section">
			<custom-navbar :title="gzRecord.status == '03'?'防窃电改造详情':'防窃电改造填报'">
			</custom-navbar>
		</view>
		
		<!-- 添加一个内容容器，设置margin-top确保不被导航栏遮挡 -->
		<view class="content-container" :style="{ paddingTop: safeAreaTop }">
			<!-- 状态显示区域 -->
			<view class="status-bar" :class="{'status-pending': gzRecord.status == '01', 'status-inprogress': gzRecord.status == '02', 'status-completed': gzRecord.status == '03'}">
				<text>改造状态</text>
				<text class="status-text">{{ status }}</text>
			</view>
			
			<!-- 工单信息区域 -->
			<view class="info-section">
				<view class="info-title">工单信息</view>
				<view class="info-content">
					<view class="info-item">
						<text class="label">工单编号：</text>
						<text class="value">{{ gzRecord.orderNo }}</text>
					</view>
					<view class="info-item">
						<text class="label">供电单位：</text>
						<text class="value">{{ gzRecord.mgtOrgName }}</text>
					</view>
					<view class="info-item">
						<text class="label">台区名称：</text>
						<text class="value">{{ gzRecord.resrcSuplName }}</text>
					</view>
					<view class="info-item">
						<text class="label">台区编号：</text>
						<text class="value">{{ gzRecord.resrcSuplCode }}</text>
					</view>
					<view class="info-item">
						<text class="label">用户名称：</text>
						<text class="value">{{ gzRecord.custName }}</text>
					</view>
					<view class="info-item">
						<text class="label">检查结果：</text>
						<text class="value">{{ gzRecord.checkRslt || '窃电'}}</text>
					</view>
				</view>
			</view>
			<!-- 隐患分类 -->
			<view class="danger-category-section" v-if="gzRecord.status == '02'|| gzRecord.status == '03'">
				<view class="danger-category-header">
					<text>所属隐患类别</text>
					<text class="category-value">{{gzRecord.dangerType}}</text>
				</view>
			</view>
			
			<!-- 隐患分析区域 -->
			<view class="danger-section" v-if="gzRecord.status == '02' || gzRecord.status == '03'">
				<view class="section-title">隐患分析</view>
				<view class="section-content">
					<view class="text-content">
						{{ gzRecord.dangerAnls}}
					</view>
					<view class="image-container" v-if="img1 && img1.length > 0">
						<u-album :urls="img1" keyName="baseImg" rowCount="3" space="5" class="album-view"></u-album>
					</view>
				</view>
			</view>
			<!-- 底部按钮 -->
			<view class="bottom-button-section" v-if="gzRecord.status != '03'">
				<button class="submit-button" @click="goForm">{{ buttonText }}</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		bizCode,
		url,
		getUUID,
		getCurrentTimestamp,
		port
	} from '@/static/commonJs/util.js'
export default {
	data() {
		return {
			detailData: {},
			gzRecord: {},
			imgsData: [],
			userInfo: {},
			color: 'black',
			statusBarHeight: 0,
			navbarHeight: 0,
			windowHeight: 0,
			// 添加模拟图片数据
			demoImages: [
				{ FILE_REMARK: 'dangerAnls', baseImg: '/static/icons/fqd1.png' },
				{ FILE_REMARK: 'dangerAnls', baseImg: '/static/icons/fqd1.png' },
				{ FILE_REMARK: 'dangerAnls', baseImg: '/static/icons/fqd1.png' },
			],
			img1:[],
			token:null
		};
	},
	onLoad: function(option) {
		// 获取状态栏高度
		this.getStatusBarHeight();
		if(option && option.item) {
			var item = JSON.parse(decodeURIComponent(option.item));
			console.log('上个页面传递的参数：',item)
			this.gzRecord = item
		}
		// this.handleImgPreview();
		
	},
	computed: {
		buttonText() {
			let obj = {
				'01': '填报',
				'02': '完善',
				'03': '已完成'
			}
			if(this.gzRecord && this.gzRecord.status) {
				return obj[this.gzRecord.status]
			}
			return '填报'
		},
		status() {
			switch (this.gzRecord.status){
				case '01':
					this.color = 'red';
					return '待填报'
					break;
				case '02':
					this.color = 'blue';
					return '改造中'
					break;	
				case '03':
					this.color = 'grey';
					return '已完成'
					break;	
				case '04':
					this.color = 'blue';
					return '待改造'
					break;	
				default:
					return '--'
					break;
			}
		},
		// img1() {
		// 	return this.imgsData.filter((item) => item.FILE_REMARK == 'dangerAnls')
		// },
		// 计算顶部安全区域高度（导航栏+状态栏）
		safeAreaTop() {
			return (this.statusBarHeight + this.navbarHeight) + 'px';
		}
	},
	created() {
	},
	onReady() {
		// 完成后计算内容区域高度
		console.log('系统信息:', {
			windowHeight: this.windowHeight,
			statusBarHeight: this.statusBarHeight,
			navbarHeight: this.navbarHeight,
			safeAreaTop: this.safeAreaTop
		});
	},
	methods: {
		init() {
			let vm = this;
			wx.invoke("ext_DataCache_GetInfo", {
			  data: { key: '1002838.userInfo' }
			}, (res) => {
				let data = JSON.parse(res.result);
				vm.token = data?.token;
			});
		},
		handleImgPreview() {
			const uploadImgs = uni.getStorageSync("uploadImgs");
			if(uploadImgs) {
				const imgsArr = JSON.parse(uploadImgs);
				imgsArr.forEach((item,index) => {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "downLoadFile",
									"data": {
										"storeFileId":item.storeFileId
									}
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
								this.img1.push(rtnData.data.base64)
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							console.error("用户档案查询错误:", error);
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
					// uni.request({
					// 	// url: url,
					// 	url: '/eas-master-app/interactive/handle',
					// 	method: 'POST',
					// 	header: {
					// 		'Content-Type': 'application/json',
					// 	},
					// 	data: {
					// 		"service": "AseCommonController",
					// 		"method": "downLoadFile",
					// 		"data": {
					// 			"storeFileId":item.storeFileId
					// 		}
					// 	},
					// 	success: (res) => {
					// 		console.log('预览:',res)
					// 		this.img1.push(res.data.data.base64)
					// 	},
					// 	fail: (error) => {
					// 		uni.showToast({
					// 			title: '上传失败',
					// 			icon: 'none',
					// 			duration: 2000
					// 		});
					// 	}
					// });
				})
			}
			
		},
		getStatusBarHeight() {
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 0;
			this.windowHeight = systemInfo.windowHeight || 0;
			
			// 增加额外的安全边距，确保内容不被遮挡
			this.navbarHeight = 40; // 导航栏高度设置为更大的值
		},
		goForm() {
			uni.navigateTo({
				url: './submit?param='+ encodeURIComponent(JSON.stringify(this.gzRecord))
			});
			
		},
		goBack() {
			uni.navigateBack({
				delta: 1
			});
		},
	}
};
</script>

<style lang="scss" scoped>
.wrap {
	padding-bottom: 90rpx; /* 减小底部padding */
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 顶部导航区域 */
.header-section {
	background-color: #fff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
	/* 高度会由导航组件自行处理，包含状态栏高度 */
}

/* 状态显示区域 */
.status-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 30rpx;
	background-color: #fff;
	border-top: 1rpx solid #eee;
	border-bottom: 1rpx solid #eee;
	margin-top: 0; /* 移除顶部margin，由content-container的padding控制 */
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}

.status-text {
	font-weight: bold;
}

.status-pending {
	.status-text {
		color: #f10606;
	}
}

.status-inprogress {
	.status-text {
		color: #00C389;
	}
}

.status-completed {
	.status-text {
		color: #999;
	}
}

/* 内容容器 */
.content-container {
	width: 100%;
	box-sizing: border-box;
	position: relative;
	padding-bottom: 90rpx; /* 减小底部padding */
	overflow: auto;
}

/* 工单信息区域 */
.info-section {
	background-color: #fff;
	margin-bottom: 20rpx;
	padding: 0; /* 移除padding */
}

.info-title {
	font-size: 32rpx;
	font-weight: 500;
	padding: 20rpx 30rpx; /* 左右padding减少为30rpx */
	color: #333;
	font-weight: bold;
}

.info-content {
	padding: 20rpx 30rpx; /* 左右padding减少为30rpx */
}

.info-item {
	display: flex;
	margin-bottom: 20rpx;
	line-height: 1.5;
	font-size: 28rpx;
}

.info-item .label {
	color: #8c8c8c;
	width: 160rpx; /* 减小标签宽度 */
	flex-shrink: 0;
}

.info-item .value {
	color: #262626;
	flex: 1;
}

/* 隐患分类 */
.danger-category-section {
	background-color: #fff;
	margin-bottom: 20rpx;
	padding: 20rpx 30rpx; /* 左右padding减少为30rpx */
}

.danger-category-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
	color: #262626;
	font-weight: bold;
}

.category-value {
	color: #ccc;
	font-weight: normal;
}

/* 通用区域样式 */
.danger-section, .measure-section, .record-section {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	padding: 20rpx 30rpx; /* 调整padding */
	border-bottom: 1rpx solid #eee;
	color: #262626;
	font-weight: bold;
}

.section-content {
	padding: 20rpx 30rpx; /* 调整padding */
}

.text-content {
	font-size: 28rpx;
	line-height: 1.6;
	color: #333;
	margin-bottom: 20rpx; /* 减少底部margin */
	text-align: justify;
}

.image-container {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
}

/* 改造记录样式 */
.record-list {
	padding: 0 30rpx; /* 调整padding */
}

.record-item {
	position: relative;
	padding: 20rpx 0 40rpx;
	border-bottom: 1rpx solid #eee;
}

.record-item:last-child {
	border-bottom: none;
}

.record-header {
	margin-bottom: 20rpx;
}

.record-status {
	color: #00C389;
	font-size: 30rpx;
	font-weight: 500;
}

.record-content {
	font-size: 28rpx;
	line-height: 1.6;
	color: #333;
	margin-bottom: 20rpx;
	text-align: justify;
}

.record-images {
	margin-bottom: 20rpx;
}

.record-footer {
	display: flex;
	justify-content: space-between;
	font-size: 24rpx;
	color: #999;
}

/* 底部按钮 */
.bottom-button-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 0; /* 移除内边距 */
	background-color: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	z-index: 10;
}

.submit-button {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	background-color: #00C389;
	color: #fff;
	font-size: 32rpx;
	border-radius: 0;
	margin: 0;
}

/* 相册样式 */
.album-view {
	width: 100%;
	/deep/ .u-album__row__wrapper {
		width: 31%;
		margin-right: 3%;
		margin-bottom: 20rpx;
	}
	/deep/ .u-album__row__wrapper:nth-child(3n) {
		margin-right: 0;
	}
	/deep/ .u-album__row__wrapper uni-image {
		width: 100% !important;
		height: 160rpx !important;
		border-radius: 4rpx;
	}
}

.album-view-small {
	/deep/ .u-album__row__wrapper {
		width: 20%;
		margin-right: 2%;
		margin-bottom: 10rpx;
	}
	/deep/ .u-album__row__wrapper:nth-child(4n) {
		margin-right: 0;
	}
	/deep/ .u-album__row__wrapper uni-image {
		width: 100% !important;
		height: 140rpx !important;
		border-radius: 8rpx;
	}
}
</style>
