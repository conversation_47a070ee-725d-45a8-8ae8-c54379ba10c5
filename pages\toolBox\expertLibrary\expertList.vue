<template>
	<view class="expert-list-container">
		<!-- 自定义导航栏 -->
		<custom-navbar title="专家库" :showBack="true">
			<template #right>
				<view class="search-icon" @click="goSearch">
					<u-icon name="search" color="#000000" size="28"></u-icon>
				</view>
			</template>
		</custom-navbar>
		
		<!-- 内容区域 -->
		<scroll-view 
			class="content-section" 
			scroll-y="true"
			refresher-enabled
			:scroll-anchoring = "true"
			:enhanced="true"
			:bounces="true"
			:show-scrollbar="false"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="onLoadMore"
			:style="{ height: contentHeight + 'px' }"
			>
			<!-- 加载中提示 -->
			<!-- <view class="loading-container" v-if="isLoading && !isRefreshing">
				<u-loading-icon mode="circle" size="28" color="#07ac7c"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view> -->
			
			<!-- 专家列表 -->
			<view class="expert-list">
				<!-- 专家项 -->
				<view class="expert-item" v-for="(item, index) in expertList" :key="index">
					<!-- 专家头像和基本信息 -->
					<view class="expert-header">
						<view class="expert-avatar">
							<image src="/static/icons/default-avatar.png" mode="aspectFill"></image>
						</view>
						<view class="expert-info">
							<view class="expert-name-title">
								<text class="expert-name">{{item.expertName}}</text>
								<text class="expert-title" v-if="item.expertType">{{item.expertType == '01'?'技术专家':(item.expertType == '02'?'法律专家':'管理专家')}}</text>
								<text class="expert-level" v-if="item.title">{{item.title}}</text>
							</view>
							<view class="expert-company">
								<text style="margin-right:20rpx" v-if="item.expertOrg">{{item.expertOrg}}</text>
								<text v-if="item.department">{{item.department}}</text>
							</view>
							<view class="expert-more" @click="toggleExpertDetail(index)">
								<text>{{!item.showDetail ?'更多':'收起'}}</text>
								<uni-icons type="arrow-down" size="18" color="#8c8c8c" v-if="!item.showDetail"></uni-icons>
								<uni-icons type="arrow-up" size="18" color="#8c8c8c" v-else></uni-icons>
							</view>
						</view>
						<view class="contact-btn" @click="contactExpert(item)">
							<u-icon name="phone" color="#00BF6B" size="16"></u-icon>
							<text>联系</text>
						</view>
					</view>
					
					<!-- 专家详情信息 -->
					<view class="expert-detail" v-if="item.showDetail && item.brirfIntroduction">
						<view class="detail-content" v-if="item.brirfIntroduction">
							<text>{{item.brirfIntroduction}}</text>
						</view>
						<view class="detail-toggle" v-if="item.detail && item.detail.length > 100" @click="toggleDetailCollapse(index)">
							<text class="toggle-text">{{item.isCollapsed ? '展开' : '收起'}}</text>
						</view>
					</view>
				</view>
				
				<!-- 无数据提示 -->
				<view class="empty-container" v-if="expertList.length === 0 && !isLoading">
					<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
				</view>
				
				<!-- 加载中提示 -->
				<view class="loading-more" v-if="expertList.length>0">
					<text v-if="isLoading && params.pageNum >1">加载中...</text>
					<text v-else-if="!hasMoreData">没有更多数据了</text>
				</view>
				
				<!-- 底部间距 -->
				<view class="bottom-space"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		data() {
			return {
				token:null,
				isRefreshing: false,
				isLoading: false,
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				expertList: [],
				moreText:'︾',
				params: {
					pageNum: 1,
					pageSize: 10
				},
				hasMoreData:true,
				isMockData:true
			};
		},
		onLoad() {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
			// 加载专家数据
			this.loadExpertListParams();
		},
		onReady() {
			// onReady生命周期
		},
		onShow() {
			console.log("显示")
			// 页面显示时重新加载数据
			// this.loadExpertList();
			const searchParam = uni.getStorageSync('expert_search_params');
			if(searchParam){
				this.loadExpertListParams(searchParam);
				uni.removeStorageSync('expert_search_params');
			}
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
				});
			},
			goSearch(){
				uni.navigateTo({
					url: './expertSearch',
				})
			},
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏）
				const topHeight = this.statusBarHeight + this.navbarHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight;
			},
			loadExpertListParams(params) {
				if(params) this.searchParam = params;
				if (!this.isRefreshing && this.isLoading) {
					return;
				}
				this.isLoading = true;
				this.expertList =[]
				uni.showLoading({
					title:'加载中...'
				})
				if(this.isMockData) {
					this.expertList = [
						{
						    "expertNo": "1834417580638023682",
						    "expertName": "卢娜",
						    "expertType": "01",
						    "expertLevel": "02",
						    "expertSource": "01",
						    "bithTime": "1993-04-03",
						    "expertOrg": "国网上海松江供电公司",
						    "department": "营销部",
						    "title": "助理工程师",
						    "sexType": "女",
						    "brirfIntroduction": "",
						    "contactInfo": "18019787472"
						  }
					]
					this.isLoading = false;
					// 如果是下拉刷新，需要结束刷新状态
					setTimeout(()=>{
						this.isRefreshing = false
					},1000)
					uni.hideLoading();
				}else{
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"MobileExpertManageController",
									"method":"detail",
									"data": {
										"expertName":params&&params.expertName||'',//专家姓名
										"experOrg":params&&params.experOrg||'',//专家单位
										"title":params&&params.title||'',//专家职称
										"pageNum":this.params.pageNum,
										"pageSize":this.params.pageSize
									},
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									 const newList = rtnData.data.list || [];
									 this.hasMoreData = newList.length >= this.params.pageSize;
									 if(this.params.pageNum === 1) {
									 	this.expertList = [...newList];
									 }else{
									 	this.expertList = [...this.expertList,...newList]
									 	
									 }
									 this.isLoading = false;
									 // 如果是下拉刷新，需要结束刷新状态
									 if (this.isRefreshing) {
									 	this.isRefreshing = false;
									 }
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
							uni.hideLoading();
						},
						fail: (error) => {
							this.isRefreshing = false;
							this.isLoading = false;
							uni.hideLoading();
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
			},
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				this.hasMoreData = true;
				this.expertList  = [];
				this.params.pageNum = 1; // 重置为第一页
				// 重新加载数据
				if(this.searchParam) {
					this.loadExpertListParams(this.searchParam);
				}else{
					this.loadExpertListParams();
				}
				
			},
			onLoadMore() {
				console.log("滚动到底部了")
				if(this.isLoading || !this.hasMoreData) {
					return;
				}
				this.params.pageNum++;
				this.loadExpertListParams(this.searchParam);
				
			},
			// 展开/收起专家详情
			toggleExpertDetail(index) {
				this.$set(this.expertList[index], 'showDetail', !this.expertList[index].showDetail);
			},
			
			// 展开/收起详情文本
			toggleDetailCollapse(index) {
				this.$set(this.expertList[index], 'isCollapsed', !this.expertList[index].isCollapsed);
			},
			
			// 联系专家
			contactExpert(expert) {
				// 实际项目中应该跳转到联系页面或拨打电话
				uni.makePhoneCall({
				    phoneNumber: expert.contactInfo || ''
				});
			}
		}
	}
</script>

<style lang="scss">
.expert-list-container {
	display: flex;
	flex-direction: column;
	background-color: #f8f8f8;
	height: 100vh;
	
	/* 覆盖默认导航栏样式，使其与设计图一致 */
	/deep/ .custom-navbar {
		background-color: #FFFFFF !important;
		
		.navbar-title text {
			color: #333333;
		}
	}
}

/* 内容区域 */
.content-section {
	flex: 1;
	box-sizing: border-box;
	background-color: #f8f8f8;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 专家列表 */
.expert-list {
	padding: 0;
}

/* 专家项 */
.expert-item {
	background-color: #FFFFFF;
	margin-bottom: 2rpx;
	padding: 30rpx;
	overflow: hidden;
	position: relative;
	border-bottom: 1rpx solid #f0f0f0;
}

/* 专家头像和基本信息 */
.expert-header {
	display: flex;
	position: relative;
}

.expert-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	overflow: hidden;
	margin-right: 20rpx;
	background-color: #f2f2f2;
	
	image {
		width: 100%;
		height: 100%;
	}
}

.expert-info {
	flex: 1;
	padding-right: 130rpx; /* 为联系按钮留出空间 */
}

.expert-name-title {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 8rpx;
	align-items: center;
}

.expert-name {
	font-size: 32rpx;
	color: #333333;
	font-weight: bold;
	margin-right: 16rpx;
}

.expert-title {
	font-size: 26rpx;
	color: #666666;
	margin-right: 16rpx;
}

.expert-level {
	font-size: 26rpx;
	color: #666666;
}

.expert-company {
	font-size: 24rpx;
	color: #888888;
	margin-bottom: 10rpx;
}

.expert-more {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #888888;
	
	.more-icon {
		margin-left: 8rpx;
		color: #aaaaaa;
	}
}
.loading-more {
	width:100%;
	height:60rpx;
	display:flex;
	justify-content: center;
	align-items: center;
	padding:20rpx 0;
	color:#999;
	font-size:28rpx;
}

.contact-btn {
	position: absolute;
	right: 0;
	top: 0;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	width: 130rpx;
	height: 60rpx;
	border-radius: 10rpx;
	border: 1rpx solid #00BF6B;
	
	text {
		font-size: 22rpx;
		color: #00BF6B;
		margin-left: 4rpx;
	}
}

/* 专家详情信息 */
.expert-detail {
	margin-top: 20rpx;
	padding-left: 120rpx;
	position: relative;
}

.detail-content {
	font-size: 26rpx;
	color: #888888;
	line-height: 1.5;
}

.detail-toggle {
	margin-top: 10rpx;
	display: flex;
	justify-content: flex-end;
	
	.toggle-text {
		font-size: 24rpx;
		color: #00BF6B;
	}
}

/* 无数据提示 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
	background-color: #fff;
	margin: 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	margin-top: 24rpx;
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 90rpx 0;
	margin: 20rpx;
	background-color: #fff;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.loading-text {
	font-size: 28rpx;
	color: #8c8c8c;
	margin-top: 24rpx;
	text-align: center;
}

/* 底部间距 */
.bottom-space {
	height: 30rpx;
}
</style>
