<template>
	<view class="case-list-content">
		<!-- 自定义导航栏 -->
		<custom-navbar title="案例库" :showBack="true">
			<template #right>
				<view class="search-icon" @click="goToSearch">
					<u-icon name="search" color="#000000" size="28"></u-icon>
				</view>
				<!-- <view class="icon-box icon-green">
					<svg-icon name="sort" color="#000000" size="44"></svg-icon>
				</view> -->
			</template>
		</custom-navbar>
		
		<!-- 内容区域 -->
		<scroll-view 
			class="content-section" 
			scroll-y="true"
			enable-back-to-top
			:scroll-anchoring = "true"
			:enhanced="true"
			:bounces="true"
			:show-scrollbar="false"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="onLoadMore"
			:style="{ height: contentHeight + 'px' }"
		>
			<!-- 加载中提示 -->
			<!-- <view class="loading-container" v-if="isLoading && !isRefreshing">
				<u-loading-icon mode="circle" size="28" color="#07ac7c"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view> -->
			
			<!-- 案例列表 -->
			<view class="case-list">
				<!-- 案例项 - 司法类案例 -->
				<view class="case-item" v-for="(item, index) in caseList" :key="'case'+String(index)" @click="goToDetail(item)">
					<!-- 案例标签和标题 -->
					<view class="case-header">
						<view class="case-tag" :style="getTagStyle(item.caseStatus)">
							<text>{{item.caseStatusName}}</text>
						</view>
						<view class="case-title">
							<text>{{ item.caseName }}</text>
						</view>
					</view>
					
					<!-- 案例信息 -->
					<view class="case-info">
						<view class="info-row">
							<text class="info-label">案件地点：</text>
							<text class="info-value">{{ item.caseLocal }}</text>
						</view>
						<view class="info-row" v-if="item.caseStatus == '01'">
							<text class="info-label">案件类型：</text>
							<text class="info-value">{{ item.caseTypeName }}</text>
						</view>
						<view class="info-row" v-if="item.caseStatus == '02'">
							<text class="info-label" >违窃性质：</text>
							<text class="info-value">{{ item.stealTypeName }}</text>
						</view>
						<view class="info-row" v-if="item.caseStatus == '01'">
							<text class="info-label" >审结时间：</text>
							<text class="info-value">{{ item.caseTime }}</text>
						</view>
						<view class="info-row" v-if="item.caseStatus == '02'">
							<text class="info-label">案例时间：</text>
							<text class="info-value">{{ item.caseTime }}</text>
						</view>
						<view class="info-row" v-if="item.caseStatus == '02'">
							<text class="info-label">违窃类别：</text>
							<text class="info-value">{{ item.caseClassName}}</text>
						</view>
					</view>
					
					<!-- 右侧箭头 -->
					<view class="case-arrow">
						<u-icon name="arrow-right" color="#262626" size="18"></u-icon>
					</view>
				</view>
			</view>
			
			<!-- 无数据提示 -->
			<view class="empty-container" v-if="!isLoading && !isRefreshing && caseList.length === 0">
				<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
			</view>
			<view class="loading-more" v-if="caseList.length>0">
				<text v-if="isLoading && params.pageNum >1">加载中...</text>
				<text v-else-if="!hasMoreData">没有更多数据了</text>
			</view>
			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		data() {
			return {
				params: {
					pageNum: 1,
					pageSize: 10
				},
				token:null,
				hasMoreData:true,
				isRefreshing: false,
				isLoading: false,
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				caseList: [],
				searchForm: {
					caseStatus: '',
					caseName: '',
					createOrg: '',
					creator: '',
					caseStartTime:"",
					caseEndTime:""
				},
				isMockData:true
				
			};
		},
		onLoad() {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
			// 加载案例数据
			// this.init();
			this.params.pageNum = 1;
			this.loadCaseList(this.searchForm)
			uni.$on('caseSearch', this.caseSearch);
		},
		onReady() {
			// onReady生命周期
		},
		onShow() {
			// 页面显示时重新加载数据
		},
		onUnload() {
			// 页面卸载时移除事件监听器
			uni.$off('caseSearch', this.caseSearch);
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
				});
			},
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏）
				const topHeight = this.statusBarHeight + this.navbarHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight;
			},
			//获取标签样式
			getTagStyle(type){
				let gradits = {
					'01':'#05d69b',
					'02':'#3a7aff',
					'03':'#ff4d4f',
					'99':'#9254de',
					
				}
				return {
					background: gradits[type]
				}
			},
			onLoadMore() {
				console.log("滚动到底部了")
				if(this.isLoading || !this.hasMoreData) {
					return;
				}
				this.params.pageNum++;
				this.loadCaseList(this.searchParams);
				
			},
			caseSearch(params) {
				this.searchParams = {
					...this.searchParams,
					...params,
				};
				console.log('案例搜索')
				// 根据搜索参数加载数据
				this.loadCaseList(this.searchParams);
				
			},
			// 加载案例列表数据
			loadCaseList(params) {
				// 如果是下拉刷新，不设置isLoading，避免闪烁
				if (this.isLoading && !this.isRefreshing) {
					return;
				}
				this.isLoading = true
				uni.showLoading({
					title:'加载中...'
				})
				if(this.isMockData) {
					this.caseList = [
					  {
						"caseStatus": "02",
						"caseStatusName": "窃电",
						"theftCaseNo": "966007594567827456",
						"caseName": "浦东公司“职业焊接二次电流”窃电案",
						"caseClass": "12",
						"caseClassName": "故意使供电企业的用电计量装置计量不准或者失效",
						"caseLevel": "03",
						"caseLevelName": "市级",
						"caseState": "03",
						"caseStateName": "已发布",
						"keyWord": "窃电点极其隐蔽，接触二次导线",
						"caseLocal": "上海市浦东新区曹路镇张唐村",
						"caseTime": "2025-06-16",
						"stealType": "01",
						"stealTypeName": "窃电",
						"asQty": "1188",
						"sumExp": "2440.92",
						"creator": "4684257252382615342",
						"createOrg": "上海市电力公司",
						"createTime": "2025-06-16",
						"caseType": "",
						"caseTypeName": "",
						"litigCont": "职业窃电焊接二次电流窃电，窃电点极其隐蔽，检查人员一旦接触二次导线，电表恢复正常，易造成误判",
						"notifAnnexId": "",
						"asExp": "",
						"breachCtrtExp": "1830.69",
						"caseFeature": "职业窃电焊接二次电流窃电，窃电点极其隐蔽，检查人员一旦接触二次导线，电表恢复正常，易造成误判"
					  }
					]
					this.isLoading = false;
					setTimeout(() => {
						this.isRefreshing = false;
					},1000)
					uni.hideLoading();
				}else{
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						// url:"/eas-master-app/interactive/handle",
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						// data:{
						// 	"service":"MobileCaseManagerController",
						// 	"method":"stealCaseQuery",
						// 	"data":{
						// 		...params,
						// 		"pageNum":this.params.pageNum,
						// 		"pageSize":this.params.pageSize
						// 	}
						// },
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"MobileCaseManagerController",
									"method":"stealCaseQuery",
									"data": {
										// "createOrg":mgtOrgCode,
										...params,
										"pageNum":this.params.pageNum,
										"pageSize":this.params.pageSize
									},
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									 const newList = rtnData.data.list || [];
									 this.hasMoreData = newList.length >= this.params.pageSize;
									 if(this.params.pageNum === 1) {
									 	this.caseList = [...newList];
									 }else{
									 	this.caseList = [...this.caseList,...newList]
									 	
									 }
									 console.log(this.caseList)
									 this.isLoading = false;
									 // 如果是下拉刷新，需要结束刷新状态
									 if (this.isRefreshing) {
									 	this.isRefreshing = false;
									 }
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
							uni.hideLoading();
						},
						fail: (error) => {
							this.isRefreshing = false;
							this.isLoading = false;
							uni.hideLoading();
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				this.hasMoreData = true;
				this.caseList  = [];
				try{
				const queryParams = uni.getStorageSync('caseSearchParams');
				if(queryParams) {
					this.searchParams = queryParams;
				}
				} catch (e) {
					console.log('获取查询参数失败',e)
				}
				this.params.pageNum = 1; // 重置为第一页
				// 重新加载数据
				this.loadCaseList(this.searchParams);
				
			},
			
			// 跳转到案例详情页
			goToDetail(item) {
				uni.navigateTo({
					url: `/pages/toolBox/caseLibrary/caseDetail?item=${encodeURIComponent(JSON.stringify(item))}`
				});
			},
			
			// 跳转到搜索页面
			goToSearch() {
				uni.navigateTo({
					url: '/pages/toolBox/caseLibrary/caseSearch'
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
.case-list-content {
	display: flex;
	flex-direction: column;
	background-color: #f8f9fc;
	position: relative;
	width: 100%;
	height: 100%;
}

/* 导航栏图标 */
.search-icon, .icon-box {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 6rpx;
}

.icon-green {
	color: #3CC792;
}

/* 内容区域 */
.content-section {
	box-sizing: border-box;
	background-color: #f8f9fc;
	padding: 24rpx 30rpx;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
	overflow-y: auto;
	width: 100%;
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 90rpx 0;
	margin-bottom: 24rpx;
	background-color: #fff;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.loading-text {
	font-size: 28rpx;
	color: #8c8c8c;
	margin-top: 24rpx;
	text-align: center;
}

/* 案例列表 */
.case-list {
	animation: fade-in 0.4s ease;
}

@keyframes fade-in {
	from {
		opacity: 0;
		transform: translateY(16rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 案例项 */
.case-item {
	position: relative;
	background-color: #fff;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	padding: 20rpx 30rpx;
	margin-bottom: 24rpx;
	overflow: hidden;
	transition: all 0.2s ease;
	&:active {
		transform: scale(0.985);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.04);
	}
}

/* 案例标签和标题 */
.case-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 24rpx;
	padding-right: 48rpx; /* 为右侧箭头留出空间 */
}

.case-tag {
	min-width: 80rpx;
	height: 48rpx;
	line-height: 48rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	flex-shrink: 0;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.06);
	
	text {
		font-size: 24rpx;
		color: #fff;
		font-weight: 600;
	}
}

.case-tag-legal {
	background: linear-gradient(135deg, #07ac7c, #05d69b); /* 渐变背景 */
}

.case-tag-violation {
	background: linear-gradient(135deg, #3a7aff, #4080ff); /* 渐变背景 */
}

.case-title {
	flex: 1;
	
	text {
		font-size: 32rpx;
		color: #262626;
		font-weight: 600;
		line-height: 1.5;
	}
}

/* 案例信息 */
.case-info {
	padding-right: 60rpx; /* 为右侧箭头留出空间 */
	padding-left: 6rpx;
}

.info-row {
	display: flex;
	margin-bottom: 14rpx;
	align-items: start;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 28rpx;
	color: #8c8c8c;
	flex-shrink: 0;
	width: 150rpx;
}

.info-value {
	font-size: 28rpx;
	color: #262626;
	flex: 1;
	font-weight: 500;
}

/* 右侧箭头 */
.case-arrow {
	position: absolute;
	right: 36rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 无数据提示 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
	background-color: #fff;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	animation: fade-in 0.4s ease;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	margin-top: 24rpx;
}

/* 悬浮搜索按钮 */
.search-btn {
	position: fixed;
	right: 40rpx;
	bottom: 120rpx;
	width: 110rpx;
	height: 110rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #07ac7c, #05d69b);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 6rpx 20rpx rgba(7, 172, 124, 0.3);
	z-index: 99;
	transition: all 0.2s ease;
	
	&:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 10rpx rgba(7, 172, 124, 0.2);
	}
}
.loading-more {
	width:100%;
	height:60rpx;
	display:flex;
	justify-content: center;
	align-items: center;
	padding:20rpx 0;
	color:#999;
	font-size:28rpx;
}

/* 底部间距 */
.bottom-space {
	height: 120rpx;
}
</style>
