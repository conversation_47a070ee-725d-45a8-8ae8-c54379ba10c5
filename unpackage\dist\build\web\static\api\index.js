import {iscpConnet, getUserInfo} from './wxjs'
import http from './http-util'

const jsApiList = [
    'multiWindows_close',
    'ext_ISCP_Init',
    'ext_ISCP_ConnectService',
    'ext_ISCP_GetLocalPort',
    'ext_ISCP_Status',
    'scanQRCode',
    'chooseImage',
    'ext_DeviceInfo_GetInfo',
    'getLocation',
    'ext_VoiceRecord_Start',
    'ext_VoiceRecord_Stop'

]
window.onload = function () {
    try {
        wx.config({
            beta: true, // 调用wx.invoke形式的接口值时，该值必须为true。
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: 'ww445f8033443a14aa', // 必填，政务微信的cropID
            timestamp: '1608692903', // 必填，生成签名的时间戳
            nonceStr: 'XKIvykvwOuAeIbl', // 必填，生成签名的随机串
            signature: 'ad0c077e6b5fbfe0c9e5394703d8262a3bc21fc3', // 必填，签名，见附录1
            jsApiList // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
        })
    } catch (e) {
        console.log(e);
    }
};

const hyDock = {
    get: () => {
        //获取网络状态接口
        return new Promise((resolve) => {
            wx.getNetworkType({
                success: function (res) {
                    // var networkType = res.networkType; // 返回网络类型2g，3g，4g，wifi
                    resolve(res)
                }
            });
        })
    },
    //开始录音
    startRecordVoice: (params) => {
        return new Promise((resolve, reject) => {
            wx.invoke("ext_VoiceRecord_Start", {
                    data: Object.assign({
                        recordFilePath: "文件路径以'/'结尾",
                        recordFileName: "文件名称 以.mp3后缀结尾",
                        recordDuration: "15",
                        channelId: "xxxxxxxx"
                    }, params)
                },
                function (res) {
                    console.log(JSON.stringify(res))
                    if (res.error_msg === 'ext_VoiceRecord_Start:ok') {
                        resolve(res)
                    }
                    if (res.error_msg === 'ext_VoiceRecord_Start:fail') {
                        reject(res)
                    }
                    if (res.error_msg === 'ext_VoiceRecord_Start:cancel') {
                        //取消处理
                        resolve(res)
                    }
                })
        })

    },
    //停止录音
    stopRecordVoice: (params) => {
        return new Promise((resolve, reject) => {
            wx.invoke("ext_VoiceRecord_Stop", {
                    data: Object.assign({
                        recordFilePath: "文件路径以'/'结尾",
                        recordFileName: "文件名称以.mp3后缀结尾"
                    }, params)
                },
                function (res) {
                    console.log(JSON.stringify(res))
                    if (res.error_msg === 'ext_VoiceRecord_Stop:ok') {
                        //成功处理
                        resolve(res)
                    }
                    if (res.error_msg === 'ext_VoiceRecord_Stop:fail') {
                        //失败处理
                        reject(res)
                    }
                    if (res.error_msg === 'ext_VoiceRecord_Stop:cancel') {
                        //取消处理
                        resolve(res)
                    }
                })
        })

    },
    //获取设备信息
    getDeviceInfo: (params = {}) => {
        if (process.env.VUE_APP_IS_LOCALHOST) return Promise.resolve({imei: '1234567890'})
        return new Promise((resolve, reject) => {
            wx.invoke(
                'ext_DeviceInfo_GetInfo',
                {
                    data: {
                        ...params
                    }
                },
                (res) => {
                    console.log('设备信息：', JSON.stringify(res))
                    if (res.err_msg === 'ext_DeviceInfo_GetInfo:ok')
                        resolve({
                            imei: res.deviceUUID,
                            ...res
                        })
                    else reject(res)
                }
            )
        })
    },
    /** 退出应用 */
    closeWindow: () => {
        return new Promise((resolve) => {
            wx.invoke('multiWindows_close', {}, function (res) {
                resolve(res)
            })
        })
    },
    /** iscp初始化 */
    iscpInit: (data = {iscpIP: ''}) => {
        return new Promise((resolve, reject) => {
            wx.invoke(
                'ext_ISCP_Init',
                {
                    data
                },
                (res) => {
                    if (res.err_msg === 'ext_ISCP_Init:ok') resolve(res)
                    else reject(res)
                }
            )
        })
    },
    /** iscp连接 */
    iscpConnectService: (data = {ip: '', port: '', user: '', appid: '', iscpIP: ''}) => {
        return new Promise((resolve, reject) => {
            wx.invoke('ext_ISCP_ConnectService', {data}, (res) => {
                if (res.err_msg === 'ext_ISCP_ConnectService:ok') resolve(res)
                else reject(res)
            })
        })
    },
    /** 获取动态端口  */
    iscpGetLocalPort: (data = {ip: '', port: '', iscpIP: ''}) => {
        return new Promise((resolve, reject) => {
            wx.invoke('ext_ISCP_GetLocalPort', {data}, (res) => {
                if (res.err_msg === 'ext_ISCP_GetLocalPort:ok') resolve(res)
                else reject(res)
            })
        })
    },
    /** 获取isxp连接状态 */
    iscpStatus: (data = {iscpIP: ''}) => {
        return new Promise((resolve, reject) => {
            wx.invoke('ext_ISCP_Status', {data}, (res) => {
                if (res.err_msg === 'ext_ISCP_Status:ok') resolve(res)
                else reject(res)
            })
        })
    },
    /**
     * 请求接口
     * @param type
     * @param data
     * @param url
     * @param config
     * @returns {Promise<AxiosResponse<T>> | Promise<AxiosResponse<T>>}
     */
    request: (
        type = 'post',
        data = {code: '', body: {}, api: {}},
        url = process.env.VUE_APP_URL,
        config
    ) => {
        // const {api} = data.body;
        const api = data.body;
        console.log('bridge-index-进入网络-187', data, api);
        let params = {
            requestBody: {
                ...data.body
            }
        }
        if (api.type === 'notRequestBody') {
            params = {
                ...data.body
            }
        } else if (api.type === 'webservice') {
            params = {
                dataXmlStr: [
                    {
                        ...data.body
                    }
                ]
            }
        } else if (api.type === 'extend') {
            params = {}
            params[api.extend] = {
                ...data.body
            }
        }

        console.log('查看环境变量内容', process.env);
        const body = {
            appName: process.env.VUE_APP_APPNAME.replace(/\//g, '$'),
            authToken: sessionStorage.getItem('authToken'),
            operationType: data.code,
            resource: process.env.VUE_APP_RESOURCE,
            data: params
        }
        console.log('查看最后请求前的入参', body);
        if (process.env.VUE_APP_IS_LOCALHOST) return http.request(url, body, type, config)
        return iscpConnet().then(async (res1) => {
            return http.request(`http://127.0.0.1:${res1}${url}`, body, type, config)
        })
    },

    /**
     * 获取用户信息
     * @returns {Promise<any | {}>}
     */
    getUserInfo: () => {
        return getUserInfo()
    },

    /** 二维码扫描 企业微信扫一扫 */
    scan: () => {
        console.log('进入扫码桥接了吗')
        return new Promise((resolve, reject) => {
            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
                success(res) {
                    // 回调

                    const data = {
                        result: res.resultStr
                    }
                    console.log('扫描成功：', res)
                    console.log('扫描返回：', res)
                    resolve(data)
                },
                error(res) {
                    console.log('扫描失败：', res)
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                    reject(res)
                }
            })
        })
    },
    /** 拍照 */
    takePhotos: () => {
        return new Promise((resolve, reject) => {
            wx.chooseImage({
                count: 1, // 默认9
                sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                quality: 0.8, // 压缩质量，范围0～1，数值越小，质量越低，压缩率越高（仅对jpg有效）
                async success(res) {
                    console.log('拍照地址', res)
                    const url = res.localIds[0]
                    resolve({
                        files: [
                            {
                                filePath: url
                            }
                        ]
                    })
                },
                error(res) {
                    reject(res)
                }
            })
        })
    },
    /** 获取位置信息 */
    getLocationInfo: (callback) => {
        wx.getLocation({
            type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
            success(res) {
                console.log('定位信息：', res)
                callback(res)
                /* var latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
                var longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
                var speed = res.speed; // 速度，以米/每秒计
                var accuracy = res.accuracy; // 位置精度
                var gps_status =  res.gps_status; //gps状态，-1：应用未获取GPS权限；
                                                  // 0：已获取GPS权限，GPS信号异常；
                                                  // 1：已获取GPS权限，GPS信号正常，AGPS信号异常；
                                                  // 2：已获取GPS权限，GPS信号异常，AGPS信号正常；
                                                  // 3：已获取GPS权限，GPS/AGPS信号正常 */
            }
        })
    },
    // /** 获取设备信息 */
    // getDeviceInfo: (params = {}) => {
    //   // return Promise.resolve({ imei: '1234567890' })
    //    return new Promise((resolve,reject) => {
    //     wx.invoke(
    //       'ext_DeviceInfo_GetInfo',
    //       {
    //         data: {
    //           ...params
    //         }
    //       },
    //       (res) => {
    //         console.log(JSON.stringify('设备信息：',res))
    //         if (res.err_msg === 'ext_DeviceInfo_GetInfo:ok') resolve({
    //           imei:res.deviceUUID,
    //           ...res
    //         })
    //         else reject(res)
    //       }
    //     )
    //   })
    // },
}

window.Vegeta = hyDock
export default hyDock
