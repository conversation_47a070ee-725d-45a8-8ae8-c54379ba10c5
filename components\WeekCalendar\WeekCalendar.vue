<template>
	<view class="calendar-container">
		<view class="week-header">
			<view class="week-day" v-for="(day, index) in daysOfWeek" :key="index">
				{{ day }}
			</view>
		</view>
		<view class="week-days">
			<view class="day-item" v-for="(date, index) in dates" :key="index"
				:class="{ selected: selectedDate === date.day }" @click="selectDate(date.day)">
				<text>{{ date.day }}</text>
				<view v-if="isWeekday(index)" class="dot" :class="{ selectDot: selectedDate === date.day }"><span
						style="opacity: 0;">1</span></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				daysOfWeek: ["一", "二", "三", "四", "五", "六", "日"],
				dates: [], // 日期列表，包含当前周的日期
				selectedDate: new Date().getDate() // 默认选中今天
			};
		},
		created() {
			this.getWeekDates();
		},
		methods: {
			// 获取当前周的日期
			getWeekDates() {
				const today = new Date();
				const dayOfWeek = today.getDay();
				const startOfWeek = new Date(today);
				startOfWeek.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));

				for (let i = 0; i < 7; i++) {
					const date = new Date(startOfWeek);
					date.setDate(startOfWeek.getDate() + i);
					this.dates.push({
						day: date.getDate()
					});
				}
			},
			// 判断是否是周一到周五
			isWeekday(index) {
				const isWeekday = index < 5; // 周一到周五
				return isWeekday;
			},
			// 选择日期
			selectDate(day) {
				this.selectedDate = day;
				this.$emit("dateSelected", day); // 向父组件传递选中的日期
			}
		}
	};
</script>

<style lang="scss" scoped>
	.calendar-container {
		background-color: white;
		padding: 10px;
		border-radius: 12px;
		display: flex;
		flex-direction: column;
	}

	.week-header {
		display: flex;
		justify-content: space-around;
		font-size: 14px;
		color: #666;
	}

	.week-day {
		text-align: center;
		flex: 1;
	}

	.week-days {
		display: flex;
		justify-content: space-around;
		margin-top: 5px;
	}

	.day-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 16px;
		color: #333;
		padding: 5px;
		border-radius: 50%;
		width: 76rpx;
		height: 76rpx;

		.dot {
			display: inline-flex;
			justify-content: center;
			align-items: center;
			width: 8rpx;
			height: 8rpx;
			background-color: #55bb8a;
			border-radius: 50%;
			margin-top: 4rpx;
		}

		.dot.selectDot {
			background-color: white;
		}
	}

	.day-item.selected {
		background-color: #55bb8a;
		color: #fff;
	}
</style>