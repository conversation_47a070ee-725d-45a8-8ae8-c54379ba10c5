<template>
	<view class="ele-wrap">
		<view class="header-section">
			<custom-navbar title="防窃电改造">
				<!-- 自定义右侧搜索按钮 -->
				<template #right>
					<view class="search-icon" @click="goToSearch">
						<uni-icons type="search" color="#000000" size="28"></uni-icons>
					</view>
				</template>
			</custom-navbar>
		</view>
		<!-- 使用scroll-view包装列表区域，添加下拉刷新功能 -->
		<scroll-view class="scroll-content" scroll-y :style="{ top: safeAreaTop }" enable-back-to-top
			:scroll-anchoring="true" :enhanced="true" :bounces="true" :show-scrollbar="false" refresher-enabled
			:refresher-triggered="isRefreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore">
			<view class="item" v-for="item in workList" :key="item.id" @click="goDetail(item)">
				<view class="item-header">
					<text class="bold">工单编号：{{ item.orderNo }}</text>
					<view class="right-flag"
						:class="item.status == '01' ? 'red' : item.status == '02' ? 'blue' : 'grey'"
						v-if="item.statusName">
						{{item.statusName}}
					</view>
				</view>
				<u-cell-group :border="false">
					<u-cell isLink :border="false" :disabled="true">
						<view slot="title" class="u-slot-title">
							<view class="text" style="display: flex;">
								<text class="left-text">供电单位：</text>
								<text class="ellipse">{{ item.mgtOrgName }}</text>
							</view>
							<view class="text" style="display: flex;">
								<text class="left-text">台区名称：</text>
								<text class="ellipse">{{ item.resrcSuplName }}</text>
							</view>
							<view class="text" style="display: flex;">
								<text class="left-text">台区编号：</text>
								<text class="ellipse">{{ item.resrcSuplCode }}</text>
							</view>
							<view class="text">
								<text class="left-text">用户名称：</text>
								{{ item.custName }}
							</view>
							<!-- <view class="text">
								<text class="left-text">检查结果：</text>
								{{ item.checkRslt }}
							</view> -->
						</view>
					</u-cell>
				</u-cell-group>
			</view>
			<!-- 空数据提示 -->
			<view v-if="workList.length === 0" class="empty-data">
				<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
			</view>
			<view class="loading-more" v-if="workList.length>0">
				<text v-if="isLoading && params.pageNum >1">加载中...</text>
				<text v-else-if="!hasMoreData">没有更多数据了</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		bizCode,
		url,
		getUUID,
		getCurrentTimestamp,
		port
	} from '@/static/commonJs/util.js'
	export default {
		data() {
			return {
				params: {
					pageNum: 1,
					pageSize: 10
				},
				workList: [],
				isLoading: false,
				statusBarHeight: 0,
				navbarHeight: 44,
				windowHeight: 0,
				isRefreshing: false,
				queryParams: null,
				hasMoreData: true,
				isMockData: true,
				token:null

			};
		},

		computed: {
			// 计算顶部安全区域高度（导航栏+状态栏）
			safeAreaTop() {
				return this.statusBarHeight + this.navbarHeight + 'px';
			},
		},

		onLoad(option) {
			// 获取状态栏高度
			this.getStatusBarHeight();
			if (option && option.item) {
				var item = JSON.parse(decodeURIComponent(option.item));
				console.log('统计页面跳转的参数：', item)
				this.queryParams = item;
				this.params.pageNum = 1;
				this.workList = [];
				this.showInfo();
			} else {
				this.showInfo();
			}
		},
		onShow() {
			const queryParams = uni.getStorageSync('antiStealingElec_queryParams');
			if (queryParams) {
				console.log('有查询参数', queryParams)
				this.queryParams = queryParams;
				this.params.pageNum = 1;
				this.workList = [];
				this.showInfo();
			} else {
				console.log('没有有查询参数', queryParams)
			}


		},
		onReady() {
			// 计算滚动区域高度
			this.calcScrollViewHeight();
			console.log(this.gzStatus)
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
				});
			},
			goToSearch() {
				uni.setStorageSync('antiStealingElec_fromPage', 'list')
				uni.navigateTo({
					url: './query?fromPage=list'
				});
			},
			goDetail(obj) {
				console.log(obj);
				uni.navigateTo({
					url: `./detail?item=${encodeURIComponent(JSON.stringify(obj))}`
				});
			},
			filterData() {
				let data = [
					{
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "90121_01",
					    "resrcSuplName": "九城9号配电站_1号配变",
					    "custNo": "3100062239228",
					    "custName": "唐梅君",
					    "consType": "低压居民",
					    "orderNo": "971773708564398080",
					    "tbId": "1940311087826124801",
					    "statusName": "待填报",
					    "status": "01",
					    "inspectors": "p00023628",
					    "dangerType": "",
					    "statDate": "2025-07-02",
					    "retrofitStartTime": "2025-07-02 15:26:27",
					    "retrofitEndTime": "",
					    "dangerAnls": "",
					    "annexId": ""
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "60843_04",
					    "resrcSuplName": "新水桥4号箱变站_新水桥4号箱变",
					    "custNo": "3100062262677",
					    "custName": "105室.车库",
					    "consType": "低压非居民",
					    "orderNo": "971773705699688448",
					    "tbId": "1940297573925437441",
					    "statusName": "待填报",
					    "status": "01",
					    "inspectors": "P00023649",
					    "dangerType": "",
					    "statDate": "2025-07-02",
					    "retrofitStartTime": "2025-07-02 14:32:45",
					    "retrofitEndTime": "",
					    "dangerAnls": "",
					    "annexId": ""
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "60843_04",
					    "resrcSuplName": "新水桥4号箱变站_新水桥4号箱变",
					    "custNo": "3100062262702",
					    "custName": "101室.车库",
					    "consType": "低压非居民",
					    "orderNo": "971773702864338944",
					    "tbId": "1940297637649498113",
					    "statusName": "待填报",
					    "status": "01",
					    "inspectors": "P00023649",
					    "dangerType": "",
					    "statDate": "2025-07-02",
					    "retrofitStartTime": "2025-07-02 14:33:00",
					    "retrofitEndTime": "",
					    "dangerAnls": "",
					    "annexId": ""
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "90449_01",
					    "resrcSuplName": "礼品诚1号配电站_1号配变",
					    "custNo": "3101332326729",
					    "custName": "1601室.商铺",
					    "consType": "低压非居民",
					    "orderNo": "971772091387576320",
					    "tbId": "1940298670404583425",
					    "statusName": "待填报",
					    "status": "01",
					    "inspectors": "p00023746",
					    "dangerType": "",
					    "statDate": "2025-07-02",
					    "retrofitStartTime": "2025-07-02 14:37:06",
					    "retrofitEndTime": "",
					    "dangerAnls": "",
					    "annexId": ""
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "90449_01",
					    "resrcSuplName": "礼品诚1号配电站_1号配变",
					    "custNo": "3101332326774",
					    "custName": "1606室.商铺",
					    "consType": "低压非居民",
					    "orderNo": "971772088426397696",
					    "tbId": "1940302066364760066",
					    "statusName": "待填报",
					    "status": "01",
					    "inspectors": "p00023746",
					    "dangerType": "",
					    "statDate": "2025-07-02",
					    "retrofitStartTime": "2025-07-02 14:50:36",
					    "retrofitEndTime": "",
					    "dangerAnls": "",
					    "annexId": ""
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "0000482051",
					    "resrcSuplName": "辰塔路灯六",
					    "custNo": "3105006569997",
					    "custName": "上海市公安局松江分局",
					    "consType": "低压非居民",
					    "orderNo": "971772056172199936",
					    "tbId": "1940304909515669506",
					    "statusName": "待填报",
					    "status": "01",
					    "inspectors": "p00023785",
					    "dangerType": "",
					    "statDate": "2025-07-02",
					    "retrofitStartTime": "2025-07-02 15:01:54",
					    "retrofitEndTime": "",
					    "dangerAnls": "",
					    "annexId": ""
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "90295_02",
					    "resrcSuplName": "丽水馨亭2号开关站_2号配变",
					    "custNo": "3101139346410",
					    "custName": "王莉萍",
					    "consType": "低压居民",
					    "orderNo": "971773686506553344",
					    "tbId": "1940295125982822401",
					    "statusName": "待填报",
					    "status": "01",
					    "inspectors": "p00023628",
					    "dangerType": "",
					    "statDate": "2025-07-09",
					    "retrofitStartTime": "2025-07-02 14:23:01",
					    "retrofitEndTime": "",
					    "dangerAnls": "",
					    "annexId": ""
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "90910_01",
					    "resrcSuplName": "新松嘉园开关站_1号配变",
					    "custNo": "3101334965661",
					    "custName": "赵日芳",
					    "consType": "低压居民",
					    "orderNo": "971772093967073280",
					    "tbId": "1940295287471915010",
					    "statusName": "待填报",
					    "status": "01",
					    "inspectors": "P00023630",
					    "dangerType": "",
					    "statDate": "2025-07-03",
					    "retrofitStartTime": "2025-07-02 14:23:40",
					    "retrofitEndTime": "",
					    "dangerAnls": "",
					    "annexId": ""
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "90787_02",
					    "resrcSuplName": "韵意14号配电站_2号配变",
					    "custNo": "3101331938912",
					    "custName": "杨培磊",
					    "consType": "低压居民",
					    "orderNo": "971772076615237632",
					    "tbId": "1940299496619556866",
					    "statusName": "改造完成",
					    "status": "03",
					    "inspectors": "P00023718",
					    "dangerType": "计量装置",
					    "statDate": "2025-07-07",
					    "retrofitStartTime": "2025-07-02 14:40:23",
					    "retrofitEndTime": "2025-07-11 10:13:34",
					    "dangerAnls": "现场检查发现该户存在窃电行为，表板后跨接",
					    "annexId": "975004622349885440"
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "90038_02",
					    "resrcSuplName": "新凯6号配电站_2号配变",
					    "custNo": "3100061546412",
					    "custName": "王思南",
					    "consType": "低压居民",
					    "orderNo": "971772082671812608",
					    "tbId": "1940295201924890626",
					    "statusName": "改造完成",
					    "status": "03",
					    "inspectors": "P00023718",
					    "dangerType": "计量装置",
					    "statDate": "2025-07-07",
					    "retrofitStartTime": "2025-07-02 14:23:19",
					    "retrofitEndTime": "2025-07-11 10:15:27",
					    "dangerAnls": "现场检查发现该户存在窃电行为，表板后跨接",
					    "annexId": "975005103142952960"
					  },
					  {
					    "mgtOrgCode": "31416",
					    "mgtOrgName": "松江供电公司",
					    "resrcSuplCode": "90361_01",
					    "resrcSuplName": "新凯三期2号开关站_1号配变",
					    "custNo": "3101280440379",
					    "custName": "金幼倩",
					    "consType": "低压居民",
					    "orderNo": "971772085800763392",
					    "tbId": "1940309300243120130",
					    "statusName": "改造完成",
					    "status": "03",
					    "inspectors": "P00023666",
					    "dangerType": "计量装置",
					    "statDate": "2025-07-04",
					    "retrofitStartTime": "2025-07-02 15:19:21",
					    "retrofitEndTime": "2025-07-11 10:18:02",
					    "dangerAnls": "现场检查发现该户存在窃电行为， 采用量电流变二次回路短路方式。",
					    "annexId": "975005746746318848"
					  }
				]
				return data;
			},
			showInfo() {
				var vm = this;

				// 如果正在加载则不再请求
				if (this.isLoading && !this.isRefreshing) {
					return;
				}
				this.isLoading = true;
				uni.showLoading({
					title: '加载中...'
				})
				if (this.isMockData) {
					this.$nextTick(() => {
						this.workList = this.filterData().filter((item)=>item.mgtOrgCode===this.queryParams.mgtOrgCode)
					})
					this.isLoading = false;
					this.hasMoreData = false;
					setTimeout(() => {
						this.isRefreshing = false;
					}, 1000)
					uni.hideLoading();
				} else {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': this.token
						},
						data: {
							token: this.token,
							method: "PutHuaYun",
							uri: url,
							data: JSON.stringify({
								"bizCode": bizCode,
								"espFlowId": getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": getCurrentTimestamp(),
								"espInformation": {
									"service": "MobileElectricityAntiRemouldController",
									"method": "mobileStealingElectricityReform",
									"data": {
										...this.queryParams,
										"pageNum": this.params.pageNum,
										"pageSize": this.params.pageSize
									},
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								const rtnData = res.data.Data.espInformation;
								if (rtnData && rtnData.code == 200) {
									const newList = rtnData.data.list || [];
									this.hasMoreData = newList.length >= this.params.pageSize;
									if (this.params.pageNum === 1) {
										this.workList = [...newList];
									} else {
										this.workList = [...this.workList, ...newList]
									}
									this.isLoading = false;
									if (this.isRefreshing) {
										this.isRefreshing = false;
									}
								}else{
									uni.showToast({
										title: '暂无数据',
										icon: 'none',
										duration: 2000
									});
								}
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
							uni.hideLoading();
						},
						fail: (error) => {
							this.isRefreshing = false;
							uni.hideLoading();
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}

			},
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;

				// uniapp环境中不使用document，改为直接使用computed属性
			},
			//滚动底部分页+1
			onLoadMore() {
				console.log("滚动到底部了")
				if (this.isLoading || !this.hasMoreData) {
					return;
				}
				this.params.pageNum++;
				this.showInfo();
			},
			calcScrollViewHeight() {
				// 只记录日志用于调试
				console.log('系统信息:', {
					windowHeight: this.windowHeight,
					statusBarHeight: this.statusBarHeight,
					navbarHeight: this.navbarHeight
				});
			},
			onRefresh() {
				console.log('下拉刷新触发');
				this.isRefreshing = true;
				this.workList = [];
				this.hasMoreData = true;
				// 尝试获取查询参数
				try {
					const queryParams = uni.getStorageSync('antiStealingElec_queryParams');
					if (queryParams) {
						console.log('获取到查询参数:', queryParams);
						// 设置查询参数，这里只是保存，真实场景可能需要格式转换
						this.queryParams = queryParams;
					}
				} catch (e) {
					console.error('获取查询参数失败:', e);
				}

				// 重新加载数据
				this.params.pageNum = 1; // 重置为第一页
				this.showInfo();
			},
		},
	};
</script>

<style lang="scss">
	.ele-wrap {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
		position: relative;
	}

	/* 顶部导航区域 */
	.header-section {
		background-color: #fff;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 101;
		/* 高度会由导航组件自行处理，包含状态栏高度 */
	}

	.search-icon {
		width: 40px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.scroll-content {
		margin-top: 0;
		/* 重置margin-top */
		box-sizing: border-box;
		background-color: #f5f5f5;
		padding: 10rpx 20rpx;
		-webkit-overflow-scrolling: touch;
		/* 增强iOS滚动体验 */
		position: fixed;
		/* top值通过绑定safeAreaTop动态设置 */
		left: 0;
		right: 0;
		bottom: 0;
	}

	.loading-more {
		width: 100%;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		color: #999;
		font-size: 28rpx;
	}

	.empty-data {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 0;
	}

	.ele-wrap .item {
		border-radius: 20rpx;
		padding: 30rpx 30rpx 0;
		position: relative;
		background-color: #fff;
		margin-bottom: 20rpx;
		transform: translateZ(0);
		/* 启用GPU加速 */
		will-change: transform;
		/* 提示浏览器该元素会变化 */
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.04);
		/* 简化阴影 */

		/* 减少嵌套层级，提高渲染性能 */
		&::after {
			content: '';
			position: absolute;
			left: 0;
			right: 0;
			bottom: 0;
			height: 1rpx;
			background-color: transparent;
		}

		.item-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			position: relative;
		}

		.right-flag {
			color: #fff;
			padding: 12rpx 30rpx;
			font-size: 28rpx;
			border-radius: 0 20rpx 0 20rpx;
			line-height: 36rpx;
			position: absolute;
			right: -30rpx;
			top: -30rpx;

			&.red {
				background: #ff5a5f;
			}

			&.blue {
				background: #07ac7c;
			}

			&.grey {
				background: #cccccc;
			}
		}

		.bold {
			font-weight: bold;
			font-size: 32rpx;
			color: #262626;
			margin-bottom: 10rpx;
			display: block;
			padding-right: 120rpx;
			/* 为状态标签留出空间 */
		}

		.u-slot-title {
			.text {
				line-height: 48rpx;
				font-size: 28rpx;
				color: #8c8c8c;
				margin-bottom: 8rpx;

				.ellipse {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 70%;
				}
			}

			.left-text {
				color: #262626;
				width: 180rpx;
				display: inline-block;
			}
		}

		/deep/ .u-cell__body {
			padding: 20rpx 0;
		}

		/deep/ .u-cell--clickable {
			background-color: transparent !important;
		}
	}
</style>