<template>
	<view class="file-query-container">
		<!-- 使用自定义导航栏组件 -->
		<custom-navbar title="工单监控"></custom-navbar>
		
		<!-- 功能卡片区域 -->
		<view class="content-area">
			<view class="card-grid">
				<!-- 第一行卡片 -->
				<view class="card-row">
					<view class="card-item" hover-class="card-hover" @click="navigateTo('mainMonitor')">
						<view class="card-inner">
							<view class="icon-box icon-green">
								<svg-icon name="jiankong" color="#3CC792" :size="iconSize"></svg-icon>
							</view>
							<text class="card-text">工单整体监控</text>
						</view>
					</view>
					<view class="card-item" hover-class="card-hover" @click="navigateTo('timeMonitor')">
						<view class="card-inner">
							<view class="icon-box icon-orange">
								<svg-icon name="jiankong" color="#F49B2C" :size="iconSize"></svg-icon>
							</view>
							<text class="card-text">工单时效监控</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import svgIcon from '@/components/svg-icon.vue';
	
	export default {
		components: {
			svgIcon
		},
		data() {
			return {
				iconSize: 40,
			};
		},
		methods: {
			// 页面导航
			navigateTo(page) {
				uni.navigateTo({
					url: `/pages/fileQuery/jobMonitor/${page}`
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #f8f8f8;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.file-query-container {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background: #f8f8f8;
}

/* 内容区域 */
.content-area {
	flex: 1;
	overflow-y: auto;
	background: #f8f8f8;
}

/* 卡片网格 */
.card-grid {
	padding: 15px 16px;
}

.card-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15px;
}

.card-item {
	width: 48%;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
	background-color: #FFFFFF;
}

.card-hover {
	transform: scale(0.98);
	transition: all 0.2s;
	opacity: 0.9;
}

.card-inner {
	height: 140px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 15px 10px;
	background-color: #FFFFFF;
}

/* 渐变背景 - 完全匹配原型图 */
.orange-green-gradient {
	background: linear-gradient(to bottom, #f68d04, #6dbd4e);
}

.icon-box {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 80px;
	height: 80px;
	border-radius: 50%;
	background-color: #f5f5f5;
	overflow: hidden;
	box-sizing: border-box;
}

.icon-green {
	background-color: #3CC792;
}

.icon-orange {
	background-color: #F49B2C;
}

.icon-orange-dark {
	background-color: #feead1;
}

.icon-blue {
	background-color: #e1eefb;
}

.icon-purple {
	background-color: #e9e1ff;
}

.icon-cyan {
	background-color: #d7f6f5;
}

.icon-red {
	background-color: #ffe5e5;
}

.card-text {
	font-size: 16px;
	color: #1d2129;
	font-weight: normal;
	text-align: center;
	line-height: 1.4;
	margin-top: 12px;
	width: 100%;
	padding: 0 5px;
	box-sizing: border-box;
	word-break: break-word;
}

.small-text {
	font-size: 15px;
	letter-spacing: -0.3px;
}

/* 媒体查询 - 适配不同设备 */
/* 小屏幕设备，如iPhone 4 (320px宽度) */
@media screen and (max-width: 320px) {
	.card-grid {
		padding: 10px 8px;
	}
	
	.card-row {
		margin-bottom: 10px;
	}
	
	.card-inner {
		height: 120px;
		padding: 10px 5px;
	}
	
	.icon-box {
		width: 60px;
		height: 60px;
	}
	
	.card-text {
		font-size: 13px;
		margin-top: 8px;
		/* 确保文字不会被截断 */
		width: 100%;
		white-space: normal;
		word-break: break-word;
	}
	
	.small-text {
		font-size: 12px;
		letter-spacing: -0.5px;
	}
	
	/* 调整图标大小 */
	.svg-icon {
		transform: scale(0.8);
	}
}

@media screen and (min-width: 375px) {
	.card-inner {
		height: 140px;
	}
	
	.icon-box {
		width: 80px;
		height: 80px;
	}
	
	.card-text {
		font-size: 16px;
		margin-top: 12px;
	}
}

@media screen and (min-width: 414px) {
	.card-grid {
		padding: 15px 20px;
	}
	
	.card-row {
		margin-bottom: 15px;
	}
	
	.card-inner {
		height: 150px;
	}
	
	.icon-box {
		width: 85px;
		height: 85px;
	}
	
	.card-text {
		font-size: 16px;
	}
}

/* 平板和大屏幕设备 */
@media screen and (min-width: 768px) {
	.card-grid {
		padding: 25px;
		max-width: 1200px;
		margin: 0 auto;
	}
	
	.card-row {
		margin-bottom: 25px;
	}
	
	.card-inner {
		height: 170px;
		padding: 20px;
	}
	
	.icon-box {
		width: 90px;
		height: 90px;
	}
	
	.card-text {
		font-size: 18px;
		margin-top: 10px;
	}
}

/* 功能服务卡片样式 */
.function-card {
	background-color: #FFFFFF;
	border-radius: 12px;
	margin: 15px;
	padding: 15px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}
</style>