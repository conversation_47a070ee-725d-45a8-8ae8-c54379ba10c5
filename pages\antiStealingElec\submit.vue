<template>
	<view class="wrap">
		<!-- 固定导航栏 -->
		<view class="header-section">
			<custom-navbar title="防窃电改造填报">
			</custom-navbar>
		</view>
		
		<!-- 内容区域容器 -->
		<view class="content-container" :style="{ marginTop: safeAreaTop }">
			<!-- 改造状态 -->
			<view class="white d-flex custom-style border-bottom">
				<text class="status-title">改造状态</text>
				<select-lay
					class="status-select"
					style="width:240rpx"
					:value="status"
					name="name"
					:zindex="3"
					:showplaceholder="false"
					placeholder="请选择"
					:options="filterStatusList"
					:customSytle="{ fontSize: '28rpx' }"
					@selectitem="selectitem"
				></select-lay>
			</view>
			
			<!-- 工单信息 -->
			<view class="info-section white">
				<text class="section-title">工单信息</text>
				<view class="info-item">
					<text class="info-label">工单编号：</text>
					<text class="info-value">{{gzData.orderNo}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">供电单位：</text>
					<text class="info-value">{{gzData.mgtOrgName}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">台区名称：</text>
					<text class="info-value">{{gzData.resrcSuplName}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">台区编号：</text>
					<text class="info-value">{{gzData.resrcSuplCode}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">用户名称：</text>
					<text class="info-value">{{gzData.custName}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">检查结果：</text>
					<text class="info-value">{{gzData.checkRslt||'窃电'}}</text>
				</view>
			</view>
			
			<!-- 所属隐患类别 -->
			<view class="white custom-style">
				<view class="category-section d-flex">
					<text class="section-title no-margin">所属隐患类别</text>
					<select-lay
						class="category-select"
						:value="dangerCategory"
						name="category"
						:zindex="10"
						:showplaceholder="false"
						placeholder="计量装置"
						:options="categoryList"
						:customSytle="{ fontSize: '28rpx' }"
						@selectitem="selectCategory"
					></select-lay>
				</view>
			</view>
			
			<!-- 隐患分析 -->
			<view class="white custom-style">
				<text class="section-title">隐患分析</text>
				<u--textarea v-model="dangerAnls" placeholder="请描述具体的隐患分析。" class="textarea"></u--textarea>
				<view class="image-upload-area">
					<view :class="imagesList ? 'openImgList' : 'closeImgList'" v-for="(item, index) in imgList1[0] && imgList1[0].img" :key="String(index)">
						<u--image class="images" :showLoading="true" :src="item.base64" width="120rpx" height="120rpx" @click="delImg('dangerAnls', index)"></u--image>
						<u-icon name="close-circle-fill" class="right-close" @click="delImg('dangerAnls', index)"></u-icon>
					</view>
					<u--image
						:class="imgList1[0] && imgList1[0].img.length < 3 ? 'upload-box' : 'closeImg'"
						:showLoading="true"
						:src="src"
						width="120rpx"
						height="120rpx"
						@click="imageSrc('dangerAnls')"
					></u--image>
				</view>
				<view>
					<u-modal style="text-align: center;" confirmColor="#07ac7c" :show="showImg" :title="titleImg" :content="content" showCancelButton @confirm="confirmImg" @cancel="cancelImg"></u-modal>
				</view>
				<text class="upload-tip">支持类型:jpg、jpeg、png，单个文件不超过10M</text>
			</view>
			<!-- 保存按钮 -->
			<view class="save-btn-container">
				<button class="save-btn" @click="saveData">保存</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	import Compressor from 'compressorjs';
export default {
	data() {
		return {
			dangerAnls: '',
			src: '../../static/icons/shangchuan.png',
			datalist: [
				{
					label: '待填报',
					value: '01'
				},
				{
					label: '改造中',
					value: '02'
				},
				{
					label: '已完成',
					value: '03'
				},
				{
					label: '待改造',
					value: '04'
				}
			],
			chooseImg: [],
			imgList: [
				{
					type: 'dangerAnls',
					img: []
				}
			],
			imageValue: [], // 存储上传图片的文件名和base64值，用于传给后台
			imageStyles: {
				width:70,
				height:70
			},
			indexImg: '',
			images: true,
			showImg: false,
			titleImg: '提示',
			content: '是否删除照片',
			imagesList: true,
			status: '01',
			type: '',
			gzData: {},
			gzRecord: {},
			statusBarHeight: 0,
			navbarHeight: 44,
			windowHeight: 0,
			dangerCategory: '01',
			categoryList: [
				{
					label: '计量装置',
					value: '01'
				},
				{
					label: '高低压配电线路及设备',
					value: '02'
				}
			],
			token: '',
			nameCode:'',
			isMockData:true,
			uploadImgs:[]
		};
	},
	onLoad(option) {
		// 获取状态栏高度
		this.getStatusBarHeight();
		console.log('接收到的参数:',option);
		if (option && option.param) {
			var item = JSON.parse(decodeURIComponent(option.param));
			console.log('解析参数:',item);
			
			// 设置页面状态和标题
			this.status = item.status || '01';
			this.gzData = item;
			
			// 回显隐患分析数据
			if(item.dangerAnls) {
				this.dangerAnls = item.dangerAnls;
			}
			if(this.isMockData) {
				
			}else{
				this.getImageBase64();
				this.updateImageDisplayStatus();
			}
			
		}
	},
	onReady() {
		// 不需要再次初始化datalist，已在data中定义
	},
	computed: {
		uuids() {
			var s = [];
			var hexDigits = '0123456789abcdef';
			for (var i = 0; i < 32; i++) {
				s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
			}
			s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
			s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
			s[8] = s[13] = s[18] = s[23];
			var uuid = s.join('');
			return uuid;
		},
		imgList1() {
			return this.imgList.filter(item => item.type == 'dangerAnls');
		},
		// 计算顶部安全区域高度（导航栏+状态栏）
		safeAreaTop() {
			// 使用px单位
			return (this.statusBarHeight + this.navbarHeight) + 'px';
		},
		filterStatusList() {
			if(this.gzData.status == '01') {
				this.status = '02';
				return this.datalist.filter((item) => item.value != '01')
			}else if(this.gzData.status == '02') {
				this.status = '02';
				return this.datalist.filter((item) => item.value != '01' && item.value != '04')
			}
		}
	},
	methods: {
		init() {
			let vm = this;
			wx.invoke("ext_DataCache_GetInfo", {
			  data: { key: '1002838.userInfo' }
			}, (res) => {
				let data = JSON.parse(res.result);
				vm.token = data?.token;
				vm.nameCode = data?.nameCode;
			});
		},
		getImageBase64() {
			console.log('this.imgList',this.imgList);
			uni.request({
				url: url,
				method: 'POST',
				header: {
					'Content-Type': 'application/json',
				},
				data: {
					method: "GetOrderZip", // 获取图片base64的方法
					token: this.token,
					data: JSON.stringify({
						"APP_NO":this.gzData.tbId,//应用编号
					})
				},
				success: (res) => {
					console.log('获取图片base64成功:', res);
					if (res && res.data && res.data.Data) {
						const imgData = res.data.Data;
						
						// 清空现有的图片列表，准备添加新图片
						const targetList = this.imgList.find(list => list.type === 'dangerAnls');
						if (targetList) {
							targetList.img = [];
							
							// 遍历返回的图片数据，添加到imgList中
							if (Array.isArray(imgData)) {
								imgData.forEach(dv => {
									if (dv && dv.Base64Data) {
										// 确保只添加最多3张图片
										if (targetList.img.length < 3) {
											targetList.img.push({
												base64: dv.Base64Data,
												fileName: dv.FileName || '图片.png'
											});
										}
									}
								});
								
								// 更新UI显示状态
								this.updateImageDisplayStatus();
								
								console.log('已加载图片数量:', targetList.img.length);
							} else {
								console.error('返回的图片数据不是数组格式');
							}
						}
					} else {
						console.error('获取图片base64失败:', res);
					}
				},
				fail: (error) => {
					console.error('获取图片base64请求失败:', error);
					uni.showToast({
						title: '获取图片失败',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		handleImgUpload() {
			if(this.imageValue.length > 0) {
				this.imageValue.forEach((item,index) => {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "uploadFile",
									"data": {
										"base64":item.base64
									}
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
								this.uploadImgs.push(rtnData.data)
								uni.setStorageSync('uploadImgs',JSON.stringify(this.uploadImgs));
								uni.showToast({
									title: '上传成功',
									icon: 'none',
								});
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							uni.showToast({
								title: '上传失败',
								icon: 'none',
								duration: 2000
							});
						}
					});
					// uni.request({
					// 	// url: url,
					// 	url: '/eas-master-app/interactive/handle',
					// 	method: 'POST',
					// 	header: {
					// 		'Content-Type': 'application/json',
					// 	},
					// 	data: {
					// 		"service": "AseCommonController",
					// 		"method": "uploadFile",
					// 		"data": {
					// 			"base64":item.base64
					// 		}
					// 	},
					// 	success: (res) => {
					// 		console.log('上传成功:',res)
					// 		this.uploadImgs.push(res.data.data)
					// 		uni.setStorageSync('uploadImgs',JSON.stringify(this.uploadImgs));
					// 		uni.showToast({
					// 			title: '上传成功',
					// 			icon: 'none',
					// 		});
					// 	},
					// 	fail: (error) => {
					// 		uni.showToast({
					// 			title: '上传失败',
					// 			icon: 'none',
					// 			duration: 2000
					// 		});
					// 	}
					// });
				})
			}
			
		},
		imageSrc(item) {
			var vm = this;
			var acceptFileType = /^jpg?|jpeg|png$/i;
			var item2 = vm.imgList.filter(v => v.type == item);
			if (item2[0].img.length >= 3) {
				uni.showToast({
					title: '最多上传3张照片',
					icon: 'none'
				});
				return;
			}
			
			// 使用uni.chooseImage替代wx.chooseImage
			uni.chooseImage({
				count: 1, // 最多可以选择的图片张数
				sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
				success: async function(res) {
					console.log(res);
					// 获取选择的图片临时路径
					const tempFilePath = res.tempFilePaths[0];
					console.log(tempFilePath);
					
					// 从res.tempFiles中获取文件类型
					const fileType = res.tempFiles && res.tempFiles[0] && res.tempFiles[0].type 
						? res.tempFiles[0].type.split('/')[1] 
						: 'png'; // 默认为png类型
					
					// 使用文件名
					const fileName = res.tempFiles[0].name;
					
					console.log('文件类型:', fileType);
					console.log('生成的文件名:', fileName);
					const originalSize = res.tempFiles[0].size;
					 console.log('原始图片大小：',`${(originalSize/ 1024).toFixed(2)}kb`)
					console.log(`原始图片字节: ${(originalSize)}`);
					new Compressor(res.tempFiles[0], {
					  quality: 0.6, // 压缩质量0-97.72%，0.2-83.9%，0.4-76.18%，0.6-67.99%，0.8-46.41%
					  convertSize:false,
					  mimeType: res.tempFiles[0].type,
					  success: (result) => {
					    //这里是Bold流转化为新的File
						if(res.tempFiles[0].type ===  result.type) {
							const fileA = new File([result], result.name, { type: result.type })
							console.log(fileA)
							console.log('压缩后的图片大小：',`${(fileA.size/ 1024).toFixed(2)}kb`)
							console.log(`压缩后图片字节: ${(fileA.size)}`);
							vm.getBase64Data(fileA,item,fileName)
						}else{
							const fileA = new File([result], result.name, { type: res.tempFiles[0].type })
							console.log(fileA)
							console.log('压缩后的图片大小：',`${(fileA.size/ 1024).toFixed(2)}kb`)
							console.log(`压缩后图片字节: ${(fileA.size)}`);
							vm.getBase64Data(fileA,item,fileName)
						}
					    
					  },
					  error: (error) => {
					    console.error('图片压缩失败', error);
					  },
					});
					 // 执行压缩
					if (!acceptFileType.test(fileType)) {
						uni.showToast({
							title: '支持格式: .jpg .png .jpeg',
							icon: 'none'
						});
						return;
					}
					
					// 使用FileReader API将文件转换为base64 - 只保留H5实现
					// 获取文件对象
					// const file = res.tempFiles[0];
					// this.getBase64Data(file)
				}
			});
		},
		getBase64Data(file,item,fileName) {
			const vm = this;
			if (file) {
				const reader = new FileReader();
				reader.onload = (e) => {
					// 获取base64数据
					const base64Data = e.target.result;
					// 添加到imgList用于显示
					const targetList = vm.imgList.find(list => list.type === item);
					if(targetList) {
						targetList.img.push({
							base64: base64Data
						});
					}
					
					// 添加到imageValue用于上传
					vm.imageValue.push({
						fileName: fileName,
						base64: base64Data
					});
					// 更新UI显示状态
					vm.imagesList = true;
					vm.updateImageDisplayStatus();
					
					console.log('已添加图片:', fileName);
				};
				
				reader.onerror = () => {
					console.error('FileReader读取失败');
					uni.showToast({
						title: '图片处理失败',
						icon: 'none'
					});
				};
				
				// 读取文件为DataURL (base64格式)
				reader.readAsDataURL(file);
			} else {
				console.error('无法获取文件对象');
				uni.showToast({
					title: '图片处理失败',
					icon: 'none'
				});
			}
		},
		delImg(item, index) {
			console.log(index);
			this.type = item;
			this.indexImg = index;
			this.showImg = true;
		},
		confirmImg() {
			// 获取要删除的图片
			const deletedImg = this.imgList.filter(item => item.type == this.type)[0].img[this.indexImg];
			
			// 从imgList中删除图片
			this.imgList.filter(item => item.type == this.type)[0].img.splice(this.indexImg, 1);
			
			// 从imageValue中删除对应的图片（使用base64值匹配）
			if (deletedImg && deletedImg.base64) {
				const index = this.imageValue.findIndex(item => item.base64 === deletedImg.base64);
				if (index !== -1) {
					this.imageValue.splice(index, 1);
				}
			}
			
			this.indexImg = '';
			this.showImg = false;
			
			// 更新图片显示状态
			const targetList = this.imgList.find(list => list.type === this.type);
			if(targetList && targetList.img.length === 0) {
				this.images = true;
				this.imagesList = false;
			}
		},
		cancelImg() {
			console.log('3333');
			this.indexImg = '';
			this.showImg = false;
		},
		
		saveData() {
			var vm = this;
			console.log('上传数据:', vm.imgList);
			console.log('上传图片:',this.imageValue);
			console.log(this.gzData);
			const { id, orderNo, status } = this.gzData;
			console.log(status);
			if (!this.dangerAnls) {
				uni.showToast({
					title: '请输入隐患分析',
					icon: 'none'
				});
				return;
			}
			uni.showLoading({
				title:'提交中...'
			})
			if(this.isMockData) {
				uni.showToast({
					title: '提交成功',
					icon: 'none',
					duration: 1500
				});
				setTimeout(()=> {
					uni.navigateTo({
						url: './list'
					});
				},1500)
				this.isLoading = false;
				uni.hideLoading();
			}else{
				this.handleImgUpload();//上传图片
				uni.request({
					url: `http://127.0.0.1:${port}/xczs/forward/for`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json',
						'token':this.token
					},
					data: {
						token: this.token,
						method:"PutHuaYun",
						uri:url,
						data:JSON.stringify({
							"bizCode":bizCode,
							"espFlowId":getUUID(),
							"espRsvField1":"",
							"espRsvField2":"",
							"espRsvField3":"",
							"espSign":"",
							"espTimestamp":getCurrentTimestamp(),
							"espInformation": {
								"service":"MobileElectricityAntiRemouldController",
								"method": status == '01'?'addTb':'changeStatus',
								"data": {
									"fileInfos":this.imageValue,
									"orderNo":this.gzData.orderNo,
									"dangerAnls":this.dangerAnls,
									"dangerType":this.dangerCategory,
									"retrofitStatus":this.status,
									"userId":this.nameCode,
									"tbId":this.gzData.tbId,//改造id
									"progressDesc":""//进展描述
								},
							}
						})
					},
					success: (res) => {
						console.log(res)
						if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
								 setTimeout(()=> {
								 	uni.navigateTo({
								 		url: './list'
								 	});
								 },1500)
								 this.isLoading = false;
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
						}else{
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
						uni.hideLoading();
					},
					fail: (error) => {
						uni.hideLoading();
						uni.showToast({
							title: '提交失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			}
			
		},
		selectitem(index, item) {
			if (index >= 0) {
				this.status = item.value;
			} else {
				this.status = '';
			}
		},
		selectCategory(index, item) {
			this.dangerCategory = item.value;
		},
		// 获取状态栏高度
		getStatusBarHeight() {
			var systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
			this.navbarHeight = systemInfo.platform === 'android' ? 48 : 44;
			this.windowHeight = systemInfo.windowHeight;
		},
		updateImageDisplayStatus() {
			if (this.imgList.some(list => list.img.length > 0)) {
				this.images = false;
				this.imagesList = true;
			} else {
				this.images = true;
				this.imagesList = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.wrap {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 100rpx;
}

/* 顶部导航区域 */
.header-section {
	background-color: #00C389;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

.content-container {
	box-sizing: border-box;
	position: relative;
}
/deep/ .uni-select-lay .uni-select-lay-select .uni-select-lay-input {
	color: #333;
}
.white {
	background-color: #fff;
	margin-bottom: 2rpx;
}

.custom-style {
	padding: 4rpx 30rpx;
	margin: 10rpx 0;
}


/* 改造状态区域 */
.status-title {
	font-size: 30rpx;
	font-weight: bold;
	flex: 1;
}


/* 工单信息区域 */
.info-section {
	padding: 30rpx;
}

.section-title {
	display: block;
	font-size: 30rpx;
	font-weight: bold;
	margin: 20rpx 0;
}

.no-margin {
	margin-bottom: 0;
}

.info-item {
	display: flex;
	margin-bottom: 20rpx;
	line-height: 40rpx;
}

.info-label {
	color: #999;
	font-size: 28rpx;
	width: 160rpx;
}

.info-value {
	color: #333;
	font-size: 28rpx;
	flex: 1;
}

.category-select {
	width: 400rpx;
}

/* 文本框区域 */
.textarea {
	border-radius: 8rpx;
	padding: 20rpx;
	margin-top: 15rpx;
	margin-bottom: 10rpx;
	height: 200rpx;
	border: 1px solid #ddd;
}
/deep/ .u-textarea__field {
	font-size: 24rpx;
}
/* 图片上传区域 */
.image-upload-area {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	margin: 20rpx 0;
}

.upload-box {
	display: flex;
	width: 120rpx;
	height: 120rpx;
	border: 1px solid #ddd;
	border-radius: 10rpx;
	background-color: #f9f9f9;
	align-items: center;
	justify-content: center;
	margin: 10rpx;
}

.openImg {
	margin: 10rpx;
}

.closeImg {
	display: none;
}

.openImgList {
	display: inline-block;
	position: relative;
	margin: 10rpx;
	.right-close {
		position: absolute;
		top: -20rpx;
		right: -20rpx;
		z-index: 1;
	}
}

.closeImgList {
	display: none;
}

.upload-tip {
	color: #999;
	font-size: 24rpx;
	display: block;
	margin: 10rpx 0 30rpx;
}

/* 保存按钮区域 */
.save-btn-container {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99;
}

.save-btn {
	background-color: #07ac7c;
	color: #fff;
	border: none;
	width: 100%;
	font-size: 32rpx;
	font-weight: normal;
	height: 90rpx;
	line-height: 90rpx;
	border-radius: 0;
}

.d-flex {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
</style>
