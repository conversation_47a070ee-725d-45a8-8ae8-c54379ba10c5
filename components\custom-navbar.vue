<template>
	<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
		<!-- 左侧按钮区域 -->
		<view class="navbar-left" @click="handleLeftClick">
			<slot name="left">
				<svg-icon v-if="showBack" name="arrow-left" color="#000000" :size="finalIconSize"></svg-icon>
			</slot>
		</view>

		<!-- 标题区域 - 修改以确保居中 -->
		<view class="navbar-center">
			<view class="navbar-title">
				<slot name="title">
					<text>{{ title }}</text>
				</slot>
			</view>
		</view>

		<!-- 右侧按钮区域 - 修改为可容纳多个图标 -->
		<view class="navbar-right">
			<slot name="right"></slot>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CustomNavbar',
		props: {
			// 标题文字
			title: {
				type: String,
				default: ''
			},
			// 是否显示返回按钮
			showBack: {
				type: Boolean,
				default: true
			},
			// 左侧图标大小（可选，如果不提供则自动计算）
			leftIconSize: {
				type: Number,
				default: null
			},
			// 自定义返回事件处理
			customBack: {
				type: Function,
				default: null
			},
			path: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				statusBarHeight: 0,
				calculatedIconSize: 24,
			};
		},
		created() {
			// 获取状态栏高度
			this.getStatusBarHeight();
			// 设置图标大小
			this.setIconSize();
		},
		computed: {
			// 计算最终使用的图标大小
			finalIconSize() {
				// 如果提供了leftIconSize属性，则使用它，否则使用自动计算的大小
				return this.leftIconSize !== null ? this.leftIconSize : this.calculatedIconSize;
			}
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				// 设置状态栏高度
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
			},
			// 设置图标大小
			setIconSize() {
				const systemInfo = uni.getSystemInfoSync();
				const screenWidth = systemInfo.screenWidth;
				// 根据屏幕宽度设置图标大小
				if (screenWidth <= 320) {
					this.calculatedIconSize = 22;
				} else if (screenWidth <= 375) {
					this.calculatedIconSize = 24;
				} else {
					this.calculatedIconSize = 26;
				}
			},
			// 处理左侧按钮点击
			handleLeftClick() {
				if (this.customBack) {
					this.customBack();
				} else if (this.showBack) {
					const url = window.location.href
					const indexPath = this.path.substring(1);
					const route = url.split('#')[1]
					if (route == indexPath) {
						console.log('返回首页')
						 wx.invoke("multiWindows_close", {},
						        function (res) {
						          console.log('multiWindows_close', res)
						        }
						      )
					} else {
						console.log('返回上一页面')
						// 返回上一页并清空缓存
						uni.navigateBack({
						    success: () => {
						        // 清空缓存
						        uni.removeStorageSync('activeUserDataTab');
						    }
						});
						// uni.navigateBack({
						// 	delta: 1
						// });
					}

				}
				this.$emit('leftClick');
			}
		}
	}
</script>

<style lang="scss">
	/* 自定义导航栏 */
	.custom-navbar {
		width: 100%;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
		height: 88rpx;
		/* 44px -> 88rpx */
		position: relative;
		z-index: 999;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
		/* 1px 6px -> 2rpx 12rpx */

		.navbar-left {
			width: 88rpx;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			flex-shrink: 0;
			&:active {
				opacity: 0.7;
			}
		}

		.navbar-center {
			flex: 1;
			display: flex;
			justify-content: center;
			height: 100%;
			z-index: -1;
		}

		.navbar-title {
			max-width: 60%;
			text-align: center;
			color: #1d2129;
			font-size: 36rpx;
			/* 18px -> 36rpx */
			font-weight: 500;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			padding: 0 20rpx;
			/* 10px -> 20rpx */
			letter-spacing: 1rpx;
			/* 0.5px -> 1rpx */
			display: flex;
			align-items: center;
		}

		.navbar-right {
			min-width: 88rpx;
			/* 44px -> 88rpx, 改为 min-width 以支持多个图标 */
			height: 88rpx;
			/* 44px -> 88rpx */
			display: flex;
			align-items: center;
			justify-content: flex-end;
			position: relative;
			padding-right: 20rpx;
			/* 添加右侧内边距 */
			flex-shrink: 0;
			/* 防止压缩 */

			&:active {
				opacity: 0.7;
			}
		}
	}

	/* 替换媒体查询规则，改为使用uni-app条件编译和SCSS变量
   uni-app会自动将rpx单位在不同设备上做适当转换，无需使用px为单位的媒体查询 */
	// 小型设备 (如iPhone SE)
	.custom-navbar {
		@media screen and (max-width: 640rpx) {
			/* 320px -> 640rpx */
			height: 80rpx;

			.navbar-left {
				width: 80rpx;
				height: 80rpx;
			}

			.navbar-right {
				min-width: 80rpx;
				height: 80rpx;
			}

			.navbar-title {
				font-size: 32rpx;
			}
		}
	}

	// 大型设备 (如iPhone Plus, iPad等)
	.custom-navbar {
		@media screen and (min-width: 828rpx) {
			/* 414px -> 828rpx */
			height: 96rpx;

			.navbar-left {
				width: 96rpx;
				height: 96rpx;
			}

			.navbar-right {
				min-width: 96rpx;
				height: 96rpx;
			}

			.navbar-title {
				font-size: 38rpx;
			}
		}
	}
</style>