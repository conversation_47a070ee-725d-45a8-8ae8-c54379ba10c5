<template>
	<view class="user-file-query">
		<!-- 顶部导航栏 -->
		<view class="header-section">
			<custom-navbar :title="pageTitle">
				<!-- 自定义右侧搜索按钮 -->
				<template #right>
					<view class="search-icon" @click="goToSearch">
						<uni-icons type="search" color="#000000" size="28"></uni-icons>
					</view>
				</template>
			</custom-navbar>
		</view>
		
		<!-- 标签页导航 -->
		<view class="tab-nav">
			<scroll-view scroll-x class="tab-scroll" show-scrollbar="false">
				<view class="container-box">
				<view class="tab-items">
					<view 
						class="tab-item" 
						:class="{ active: activeTab === item.id }"
						@click="switchTab(item.id)"
						v-for="(item, index) in tabItems"
						:key="'tab-' + index"
					>
						<text>{{ item.name }}</text>
					</view>
				</view>
				<view class="screen-rotate" @click="screenRotate"></view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 内容区域 - 用户档案 -->
		<scroll-view 
			v-if="activeTab === 'userFile'"
			class="content-section" 
			scroll-y 
			:style="{ height: scrollViewHeight + 'px' }"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="onLoadMore"
		>
			<!-- 加载中提示 -->
			<!-- <view class="loading-container" v-if="isLoading">
				<u-loading-icon mode="circle" size="28" color="#07ac7c"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view> -->
			
			<!-- 用户资料卡片 - 实际内容 -->
			<view 
				class="info-card" 
				:class="{'card-animation': !isLoading}" 
				v-if="!isLoading" 
				v-for="(card, cardIndex) in cards" 
				:key="'card-' + cardIndex"
			>
				<view class="card-title">
					<text>{{ card.title }}</text>
				</view>
				<view class="info-content">
					<view class="info-row" v-for="(field, index) in card.fields" :key="card.type + '-field-' + index">
						<text class="info-label">{{ field.label }}：</text>
						<text class="info-value">{{ card.data[field.key] || '-' }}</text>
					</view>
				</view>
			</view>
			
			<!-- 无数据提示 -->
			<view class="no-data" v-if="cards.length === 0">
				<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
			</view>
			
			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
		
		<!-- 负荷数据页面 -->
		<view v-if="activeTab === 'loadData'" class="tab-content-container" :style="{ height: scrollViewHeight + 'px', marginTop: '188rpx' }">
			<keep-alive>
				<load-data-component 
					:queryParams="getQueryParams()"
				></load-data-component>
			</keep-alive>
		</view>
		
		<!-- 电量数据页面 -->
		<view v-if="activeTab === 'powerData'" class="tab-content-container" :style="{ height: scrollViewHeight + 'px', marginTop: '188rpx' }">
			<keep-alive>
				<power-data-component :queryParams="getQueryParams()"></power-data-component>
			</keep-alive>
		</view>
		
		<!-- 历史事件页面 -->
		<view v-if="activeTab === 'historyEvent'" class="tab-content-container" :style="{ height: scrollViewHeight + 'px', marginTop: '188rpx' }">
			<keep-alive>
				<history-events-component :queryParams="getQueryParams()"></history-events-component>
			</keep-alive>
		</view>
		
		<!-- 用电异常页面 -->
		<view v-if="activeTab === 'abnormal'" class="tab-content-container" :style="{ height: scrollViewHeight + 'px', marginTop: '188rpx' }">
			<keep-alive>
				<component 
					:is="isDetail ? 'abnormal-detail-component' : 'abnormal-component'" 
					:queryParams="getQueryParams()"
					:detail-data="currentDetailData"
					@showAbnormalDetail="showAbnormalDetail"
					@backToList="backToList"
				></component>
			</keep-alive>
		</view>
		
		<!-- 线损数据页面 -->
		<view v-if="activeTab === 'lineLoss'" class="tab-content-container" :style="{ height: scrollViewHeight + 'px', marginTop: '188rpx' }">
			<keep-alive>
				<line-loss-component :queryParams="getQueryParams()"></line-loss-component>
			</keep-alive>
		</view>
		
		<!-- 其他标签页内容 -->
		<view v-if="activeTab !== 'userFile' && activeTab !== 'loadData' && activeTab !== 'powerData' && activeTab !== 'historyEvent' && activeTab !== 'abnormal' && activeTab !== 'lineLoss'" class="other-tab-content" :style="{ height: scrollViewHeight + 'px' }">
			<view class="tab-placeholder">
				<u-icon name="info-circle" size="50" color="#c8c9cc"></u-icon>
				<text class="placeholder-text">{{ getTabPlaceholderText() }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import LoadDataComponent from './loadData.vue';
	import PowerDataComponent from './powerData.vue';
	import HistoryEventsComponent from './historyEvents.vue';
	import AbnormalComponent from './abnormal.vue';
	import AbnormalDetailComponent from './abnormalDetail.vue';
	import LineLossComponent from './lineLoss.vue';
		import {
		bizCode,
		url,
		getUUID,
		getCurrentTimestamp,
		port} from '@/static/commonJs/util.js'
	export default {
		components: {
			LoadDataComponent,
			PowerDataComponent,
			HistoryEventsComponent,
			AbnormalComponent,
			AbnormalDetailComponent,
			LineLossComponent
		},
		data() {
			return {
				token:null,
				statusBarHeight: 0,
				navbarHeight: 44, // 44px 转为 88rpx
				tabNavHeight: 50, // 50px 转为 100rpx
				windowHeight: 0,
				scrollViewHeight: 0,
				custNo: '', // 修改为custNo
				custName: '', // 修改为custName
				isDetail: false,
				currentDetailData: {}, // 当前查看的异常详情数据
				meterAssetNo: '', // 修改为meterAssetNo
				dateRange: [],
				dateRangeText: '',
				isLoading: true,
				isRefreshing: false,
				activeTab: 'userFile',
				// 用户资料字段配置
				userFileFields: [],
				// 电能表字段配置
				meterFields: [],
				// 接口返回的用户数据
				userData: {},
				// 接口返回的电表数据
				meterData: {},
				
				// 所有卡片数据
				cards: [],
				
				// 标签页数据
				tabItems: [
					{ id: 'userFile', name: '用户档案' },
					{ id: 'loadData', name: '负荷数据' },
					{ id: 'powerData', name: '电量数据' },
					{ id: 'historyEvent', name: '历史事件' },
					{ id: 'abnormal', name: '用电异常' },
					{ id: 'lineLoss', name: '线损数据' }
				],
				queryResultParams:{},
				isMockData: true,
				direction:'0'
			};
		},
		computed: {
			// 动态页面标题
			pageTitle() {
				const activeTabItem = this.tabItems.find(item => item.id === this.activeTab);
				return activeTabItem ? activeTabItem.name : '用户档案查询';
			}
		},
		watch: {
			// 监听activeTab变化
			activeTab(newVal, oldVal) {
				if (newVal !== oldVal) {
					console.log(`Tab切换：从 ${oldVal} 到 ${newVal}`);
					
					// 记录当前活动标签到本地存储
					uni.setStorageSync('activeUserDataTab', newVal);
					
					// 根据当前活动标签加载对应数据
					this.onTabChange();
					
					// 使用延迟触发，确保子组件完全挂载
					setTimeout(() => {
						// 获取当前查询参数
						const currentParams = this.getQueryParams();
						console.log('Tab切换后传递参数:', currentParams);
						
						// 特别处理日期范围
						if (this.dateRange && this.dateRange.length > 0) {
							// 如果是用户档案标签，清除日期范围
							if (newVal === 'userFile') {
								this.dateRange = [];
								this.dateRangeText = '';
							}
						}
					}, 50);
				}
			}
		},
		onLoad(options) {
			// 获取状态栏高度
			this.getStatusBarHeight();
			
			// 获取传递的参数
			if (options && options.item) {
				const item = JSON.parse(decodeURIComponent(options.item))
				console.log("上个页面传过来参数",item);
				uni.setStorageSync('activeUserDataTab', this.activeTab);
				this.queryResultParams = {...item};
				const {custNo, custName,meterAssetNo} = this.queryResultParams;
				this.custNo = custNo || '';
				this.custName = custName || '';
				this.meterAssetNo = meterAssetNo || '';
			}
			// 从本地存储中恢复上次的活动标签
			const savedTab = uni.getStorageSync('activeUserDataTab');
			if (savedTab && this.tabItems.some(item => item.id === savedTab)) {
				this.activeTab = savedTab;
			}
			
			// 加载用户数据
			this.loadTabData();
		},
		onReady() {
			// 计算滚动区域高度
			this.calcScrollViewHeight();
		},
		onShow() {
			// 页面显示时，根据当前标签更新页面状态
			this.updatePageByActiveTab();
			uni.setStorageSync('activeUserDataTab', this.activeTab);
		},
		onHide() {
			// 页面隐藏时的处理逻辑
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token
				});
			},
			// 根据当前活动标签更新页面状态
			updatePageByActiveTab() {
				// 如果是用户档案标签且数据为空，加载数据
				if (this.activeTab === 'userFile' && this.cards.length === 0 && !this.isLoading) {
					this.loadTabData();
				}
			},
			
			// 获取状态栏高度
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 计算滚动区域高度
			calcScrollViewHeight() {
				// 导航栏高度 + 标签页导航高度
				const topHeight = this.statusBarHeight + this.navbarHeight + this.tabNavHeight;
				this.scrollViewHeight = this.windowHeight - topHeight;
			},
			
			// 跳转到搜索页面
			goToSearch() {
				uni.navigateTo({
					url: '/pages/fileQuery/userDataQuery/userDataQuery',
				});
			},
			screenRotate() {
				console.log('手动触发屏幕旋转');
				const direction = uni.getStorageSync('direction');
				if(direction) {
					this.direction = direction;
					if(this.direction === '1') {
						console.log('竖屏')
						uni.setStorageSync('direction', '0');
						this.direction = '0'
					}else{
						console.log('横屏')
						uni.setStorageSync('direction', '1');
						this.direction = '1'
					}
					wx.invoke("ext_screenControl_direction", {
							data: {
								orientation:  this.direction
							}
						},
						(res) => {
							console.log(JSON.stringify(res));
							if (res.error_msg === 'ext_screenControl_direction:ok') {
								console.log(res.result);
							}
						});
				}else{
					if(this.direction === '1') {
						console.log('竖屏')
						uni.setStorageSync('direction', '0');
						this.direction = '0'
					}else{
						console.log('横屏')
						uni.setStorageSync('direction', '1');
						this.direction = '1'
					}
					wx.invoke("ext_screenControl_direction", {
							data: {
								orientation:  this.direction
							}
						},
						(res) => {
							console.log(JSON.stringify(res));
							if (res.error_msg === 'ext_screenControl_direction:ok') {
								console.log(res.result);
							}
						});
				}
				
			},
			// 根据当前选中的标签加载对应的数据
			loadTabData() {
				// 根据当前标签决定加载哪种数据
				switch(this.activeTab) {
					case 'userFile':
						this.loadUserData();
						break;
					// 其他标签页的数据由各自的组件加载
					default:
						break;
				}
			},
			
			// 加载用户档案数据
			loadUserData() {
				this.isLoading = true;
				
				// 构建请求参数
				const params = {
					custNo: this.custNo,
					custName: this.custName,
					meterAssetNo: this.meterAssetNo
				};
				
				// 添加日期范围参数（仅在非userFile标签页）
				if (this.activeTab !== 'userFile' && this.dateRange && this.dateRange.length > 0) {
					const formatDate = date => {
						if (!date) return '';
						const d = new Date(date);
						const year = d.getFullYear();
						const month = String(d.getMonth() + 1).padStart(2, '0');
						const day = String(d.getDate()).padStart(2, '0');
						return `${year}-${month}-${day}`;
					};
					
					params.startDate = formatDate(this.dateRange[0]);
					params.endDate = formatDate(this.dateRange[this.dateRange.length - 1]);
				}
				if(this.activeTab === 'userFile') {
					if(this.isMockData) {
					   this.processResponseData(this.queryResultParams);
						this.isLoading = false;
					}else{
						uni.request({
							url: `http://127.0.0.1:${port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token':this.token
							},
							data: {
								token: this.token,
								method:"PutHuaYun",
								uri:url,
								data:JSON.stringify({
									"bizCode":bizCode,
									"espFlowId":getUUID(),
									"espRsvField1":"",
									"espRsvField2":"",
									"espRsvField3":"",
									"espSign":"",
									"espTimestamp":getCurrentTimestamp(),
									"espInformation": {
										"service":"DtsUserController",
										"method":"getUserArc",
										"data": {
											"custNo": this.custNo,
											"custName": this.custName,
											"meterAssetNo": this.meterAssetNo,
											"pageNum":1,
										}
									}
								})
							},
							success: (res) => {
								console.log(res)
								if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									const item = rtnData.data.list[0];
									this.processResponseData(item);
									this.isLoading = false;
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
								}else{
									uni.showToast({
										title: '暂无数据',
										icon: 'none',
										duration: 2000
									});
								}
							},
							fail: (error) => {
								console.error("用户档案查询错误:", error);
								this.isLoading = false;
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						});
					}
					
				}
			},
			processResponseData(item) {
				if(!item) return
				// 处理用户资料数据
				this.cards = [];
				const userData = {
					orgName: item.orgName,
					custNo: item.custNo,
					custName: item.custName,
					elecAddr: item.elecAddr,
					tgName: item.tgName,
					elecTypeCode: item.elecTypeCode,
					voltCode: item.voltCode,
					tradeCode: item.tradeCode,
					contractCap: item.contractCap,
					runCap: item.runCap
				}
				const meterData = {
					meterAssetNo: item.meterAssetNo,
					commAddr: item.commAddr,
					wireMode: item.wireMode == '01'?'单相':(item.wireMode == '02'?'三相三线':'三相四线'),
					instDate: item.instDate,
					rate: item.rate,
					commMode: item.commMode == '28'?'HPLC':'',
					ratedCurrent: item.ratedCurrent,
					ratedVoltage: item.ratedVoltage,
				}
					this.userFileFields = this.generateUserFileFields();
					this.cards.push({
						title: '用户资料',
						type: 'userFile',
						fields: this.userFileFields,
						data: userData
					});
					this.meterFields = this.generateMeterFields();
					this.cards.push({
						title: '电能表',
						type: 'meterInfo',
						fields: this.meterFields,
						data: meterData
					});
					console.log(this.cards)
			},
			// 处理接口返回的数据
			// processResponseData(data) {
			// 	if (!data) return;
				
			// 	// 如果接口直接返回了卡片数据数组
			// 	if (Array.isArray(data.cards) && data.cards.length > 0) {
			// 		this.cards = data.cards;
			// 		return;
			// 	}
				
			// 	// 否则，构建卡片数据数组
			// 	this.cards = [];
				
			// 	// 处理用户资料数据
			// 	if (data.userFile) {
			// 		this.userData = data.userFile;
			// 		this.userFileFields = data.userFileFields || this.generateUserFileFields();
					
			// 		this.cards.push({
			// 			title: data.userFileTitle || '用户资料',
			// 			type: 'userFile',
			// 			fields: this.userFileFields,
			// 			data: this.userData
			// 		});
			// 	}
				
			// 	// 处理电能表数据
			// 	if (data.meterInfo) {
			// 		this.meterData = data.meterInfo;
			// 		this.meterFields = data.meterFields || this.generateMeterFields();
					
			// 		this.cards.push({
			// 			title: data.meterInfoTitle || '电能表',
			// 			type: 'meterInfo',
			// 			fields: this.meterFields,
			// 			data: this.meterData
			// 		});
			// 	}
			// },
			
			// 生成默认的用户资料字段配置
			generateUserFileFields() {
				return [
					{ label: '供电单位', key: 'orgName' },
					{ label: '用户编号', key: 'custNo' },
					{ label: '用户名称', key: 'custName' },
					{ label: '用电地址', key: 'elecAddr' },
					{ label: '台区名称', key: 'tgName' },
					{ label: '用电类别', key: 'elecTypeCode' },
					{ label: '供电电压', key: 'voltCode' },
					{ label: '行业类别', key: 'tradeCode' },
					{ label: '合同容量', key: 'contractCap' },
					{ label: '运行容量', key: 'runCap' },
				];
			},
			
			// 生成默认的电能表字段配置
			generateMeterFields() {
				return [
					{ label: '资产编号', key: 'meterAssetNo' },
					{ label: '通讯地址', key: 'commAddr' },
					{ label: '接线方式', key: 'wireMode' },
					{ label: '安装时间', key: 'instDate' },
					{ label: '综合倍率', key: 'rate' },
					{ label: '规约', key: 'commMode' },
					{ label: '额定电压', key: 'ratedVoltage' },
					{ label: '额定电流', key: 'ratedCurrent' }
				];
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				
				// 重新加载数据
				this.loadTabData();
				
				// 设置定时器确保刷新状态至少显示一段时间
				setTimeout(() => {
					// 停止刷新状态
					this.isRefreshing = false;
					
					// 提示用户刷新成功
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					});
				}, 1500);
			},
			
			// 加载更多
			onLoadMore() {
				// 这里可以添加加载更多的逻辑，如果需要的话
				// 例如：this.fetchMoreData();
				
				// 这个示例中不需要加载更多，因为所有数据都已经显示
				// 但保留此方法以便将来扩展
			},
			
			// 切换标签页
			switchTab(tabName) {
				this.activeTab  = tabName
				// if (this.activeTab === tabName) return;
				
				// 添加触感反馈
				uni.vibrateShort();
				
				// 记录要跳转的标签页到本地存储，但不立即切换
				uni.setStorageSync('activeUserDataTab', tabName);
				// 获取之前保存的查询参数
				// const savedQueryParams = uni.getStorageSync('userDataQueryParams') || {};
				// 无论如何都跳转到查询页面，让用户确认或修改查询条件
				// uni.navigateTo({
				// 	url: '/pages/fileQuery/userDataQuery/userDataQuery',
				// 	success: (res) => {
				// 		// 通过eventChannel向被打开页面传送数据
				// 		res.eventChannel.emit('acceptParams', {
				// 			targetTab: tabName,
				// 			tabName: this.tabItems.find(item => item.id === tabName)?.name || '',
				// 			// 传递之前保存的查询参数
				// 			savedQueryParams: {
				// 				custNo: this.custNo,
				// 				custName: this.custName,
				// 				meterAssetNo: this.meterAssetNo,
				// 				dateRange: savedQueryParams.dateRange || [],
				// 				dateRangeText: savedQueryParams.dateRangeText || '',
				// 				tgNo: this.queryResultParams.tgNo || '',
				// 				statDate: this.queryResultParams.statDate
				// 			}
				// 		});
				// 	}
				// });
			},
			
			// 获取标签页占位文本
			getTabPlaceholderText() {
				// 查找当前激活的标签
				const activeTabItem = this.tabItems.find(item => item.id === this.activeTab);
				if (activeTabItem) {
					return `${activeTabItem.name}功能正在开发中`;
				}
				return '功能正在开发中';
			},
			
			// 处理标签页变化后的数据加载
			onTabChange() {
				// 加载对应标签页的数据
				this.loadTabData();
			},
			
			// 获取查询参数
			getQueryParams() {
				return {
					custNo: this.custNo,
					custName: this.custName,
					meterAssetNo: this.meterAssetNo,
					dateRange: this.dateRange,
					dateRangeText: this.dateRangeText,
					tgNo:this.queryResultParams.tgNo,
					statDate:this.queryResultParams.statDate
				};
			},
			
			// 显示异常详情
			showAbnormalDetail(detail) {
				console.log('显示异常详情:', detail);
				
				// 扩充详情数据，确保所有需要的字段都存在
				const fullDetailData = {
					workOrderNo: detail.workOrderNo
				};
				
				// 保存当前查看的详情数据
				this.currentDetailData = fullDetailData;
				
				// 切换到详情视图
				this.isDetail = true;
			},
			
			// 返回异常列表
			backToList() {
				console.log('返回异常列表');
				// 清空当前详情数据
				this.currentDetailData = {};
				// 切换回列表视图
				this.isDetail = false;
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #f5f5f5;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.user-file-query {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
}

/* 顶部导航区域 */
.header-section {
	background-color: #fff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

.search-icon {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 标签页导航 */
.tab-nav {
	position: fixed;
	top: 88rpx; /* 导航栏高度 */
	left: 0;
	right: 0;
	height: 100rpx;
	background-color: #fff;
	z-index: 100;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.tab-scroll {
	height: 100rpx;
	white-space: nowrap;
}

.tab-items {
	display: inline-flex;
	height: 100rpx;
	padding: 0;
}

.tab-item {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 30rpx;
	height: 100rpx;
	color: #262626;
	font-size: 28rpx;
	position: relative;
}

.tab-item:first-child {
	padding-left: 40rpx;
}

.tab-item:last-child {
	border-right: none;
}

.tab-item.active {
	color: #07ac7c;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 12rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 40%;
	height: 4rpx;
	background-color: #07ac7c;
	border-radius: 2rpx;
}


/* 内容区域 */
.content-section {
	margin-top: 188rpx; /* 导航栏高度(88rpx) + 标签页导航高度(100rpx) */
	box-sizing: border-box;
	background-color: #f5f5f5;
	padding: 20rpx 30rpx;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 0;
	margin-bottom: 20rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.loading-text {
	font-size: 28rpx;
	color: #8c8c8c;
	margin-top: 20rpx;
	text-align: center;
}

/* 信息卡片 */
.info-card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.card-animation {
	animation: card-fade-in 0.5s ease forwards;
}

@keyframes card-fade-in {
	from {
		opacity: 0;
		transform: translateY(20rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #262626;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	// border-bottom: 1px solid #f0f0f0;
}

.info-content {
	width: 100%;
}

.info-row {
	display: flex;
	margin-bottom: 24rpx;
	align-items: flex-start;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 28rpx;
	color: #8c8c8c;
	width: 160rpx;
	text-align: left;
	font-weight: normal;
}

.info-value {
	font-size: 28rpx;
	color: #262626;
	flex: 1;
	text-align: right;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 标签内容容器 */
.tab-content-container {
	box-sizing: border-box;
	background-color: #f5f5f5;
	overflow: hidden;
	position: relative;
	width: 100%;
}

/* 其他标签页占位内容 */
.other-tab-content {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	margin-top: 0;
	animation: fade-in 0.3s ease;
}

.tab-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx;
}

.placeholder-text {
	font-size: 28rpx;
	color: #8c8c8c;
	margin-top: 20rpx;
	text-align: center;
}

/* 动画效果 */
@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

/* 标签页切换动画 */
.tab-transition-enter-active, .tab-transition-leave-active {
	transition: opacity 0.3s ease;
}
.tab-transition-enter, .tab-transition-leave-to {
	opacity: 0;
}

/* 底部间距 */
.bottom-space {
	height: 40rpx;
}

/* 修改导航栏样式 */
/deep/ .custom-navbar {
	background-color: #fff !important;
	box-shadow: none !important;
}

/deep/ .navbar-left .svg-icon {
	color: #000000 !important;
}

/deep/ .navbar-title {
	color: #000000 !important;
}
.screen-rotate {
	width: 30rpx;
	height: 30rpx;
	background: url('~@/static/icons/screen.png') no-repeat;
	background-size: 100% 100%;
	margin-right: 60rpx;
}
.container-box {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
</style>
