(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-fileQuery-example-example"],{6492:function(t,a,d){"use strict";d.r(a);var o=d("9c15"),e=d("79a3");for(var r in e)["default"].indexOf(r)<0&&function(t){d.d(a,t,(function(){return e[t]}))}(r);d("a88c");var i=d("828b"),n=Object(i["a"])(e["default"],o["b"],o["c"],!1,null,"d95dd25c",null,!1,o["a"],void 0);a["default"]=n.exports},"79a3":function(t,a,d){"use strict";d.r(a);var o=d("fdd6"),e=d.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){d.d(a,t,(function(){return o[t]}))}(r);a["default"]=e.a},"7e48":function(t,a,d){var o=d("c86c");a=o(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-d95dd25c]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-d95dd25c]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-d95dd25c]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-d95dd25c]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-d95dd25c]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-d95dd25c]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-d95dd25c]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-d95dd25c]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-d95dd25c]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-d95dd25c]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-d95dd25c]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-d95dd25c]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-d95dd25c]::after{border:none}.u-hover-class[data-v-d95dd25c]{opacity:.7}.u-primary-light[data-v-d95dd25c]{color:#ecf5ff}.u-warning-light[data-v-d95dd25c]{color:#fdf6ec}.u-success-light[data-v-d95dd25c]{color:#f5fff0}.u-error-light[data-v-d95dd25c]{color:#fef0f0}.u-info-light[data-v-d95dd25c]{color:#f4f4f5}.u-primary-light-bg[data-v-d95dd25c]{background-color:#ecf5ff}.u-warning-light-bg[data-v-d95dd25c]{background-color:#fdf6ec}.u-success-light-bg[data-v-d95dd25c]{background-color:#f5fff0}.u-error-light-bg[data-v-d95dd25c]{background-color:#fef0f0}.u-info-light-bg[data-v-d95dd25c]{background-color:#f4f4f5}.u-primary-dark[data-v-d95dd25c]{color:#398ade}.u-warning-dark[data-v-d95dd25c]{color:#f1a532}.u-success-dark[data-v-d95dd25c]{color:#53c21d}.u-error-dark[data-v-d95dd25c]{color:#e45656}.u-info-dark[data-v-d95dd25c]{color:#767a82}.u-primary-dark-bg[data-v-d95dd25c]{background-color:#398ade}.u-warning-dark-bg[data-v-d95dd25c]{background-color:#f1a532}.u-success-dark-bg[data-v-d95dd25c]{background-color:#53c21d}.u-error-dark-bg[data-v-d95dd25c]{background-color:#e45656}.u-info-dark-bg[data-v-d95dd25c]{background-color:#767a82}.u-primary-disabled[data-v-d95dd25c]{color:#9acafc}.u-warning-disabled[data-v-d95dd25c]{color:#f9d39b}.u-success-disabled[data-v-d95dd25c]{color:#a9e08f}.u-error-disabled[data-v-d95dd25c]{color:#f7b2b2}.u-info-disabled[data-v-d95dd25c]{color:#c4c6c9}.u-primary[data-v-d95dd25c]{color:#3c9cff}.u-warning[data-v-d95dd25c]{color:#f9ae3d}.u-success[data-v-d95dd25c]{color:#5ac725}.u-error[data-v-d95dd25c]{color:#f56c6c}.u-info[data-v-d95dd25c]{color:#909399}.u-primary-bg[data-v-d95dd25c]{background-color:#3c9cff}.u-warning-bg[data-v-d95dd25c]{background-color:#f9ae3d}.u-success-bg[data-v-d95dd25c]{background-color:#5ac725}.u-error-bg[data-v-d95dd25c]{background-color:#f56c6c}.u-info-bg[data-v-d95dd25c]{background-color:#909399}.u-main-color[data-v-d95dd25c]{color:#303133}.u-content-color[data-v-d95dd25c]{color:#606266}.u-tips-color[data-v-d95dd25c]{color:#909193}.u-light-color[data-v-d95dd25c]{color:#c0c4cc}.u-safe-area-inset-top[data-v-d95dd25c]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-d95dd25c]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-d95dd25c]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-d95dd25c]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-d95dd25c]{z-index:10090}uni-toast .uni-toast[data-v-d95dd25c]{z-index:10090}[data-v-d95dd25c]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-page-body[data-v-d95dd25c]{background-color:#f8f8f8;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}body.?%PAGE?%[data-v-d95dd25c]{background-color:#f8f8f8}.example-container[data-v-d95dd25c]{min-height:100vh;display:flex;flex-direction:column}.content[data-v-d95dd25c]{flex:1;padding:15px}.section[data-v-d95dd25c]{margin-bottom:20px;background-color:#fff;border-radius:8px;padding:15px;box-shadow:0 1px 5px rgba(0,0,0,.05)}.section-title[data-v-d95dd25c]{font-size:16px;font-weight:500;color:#1d2129;margin-bottom:10px}.code-block[data-v-d95dd25c]{background-color:#f5f7fa;padding:10px;border-radius:6px;margin-top:10px}.code-block uni-text[data-v-d95dd25c]{font-family:monospace;font-size:12px;color:#666;word-break:break-all;white-space:pre-wrap}.demo-navbar[data-v-d95dd25c]{margin:10px 0;border:1px solid #eee;border-radius:8px;overflow:hidden}.custom-title[data-v-d95dd25c]{display:flex;flex-direction:column;align-items:center}.custom-title .title-text[data-v-d95dd25c]{font-size:16px;color:#1d2129}.custom-title .subtitle-text[data-v-d95dd25c]{font-size:12px;color:#86909c;margin-top:2px}',""]),t.exports=a},"9c15":function(t,a,d){"use strict";d.d(a,"b",(function(){return o})),d.d(a,"c",(function(){return e})),d.d(a,"a",(function(){}));var o=function(){var t=this,a=t.$createElement,d=t._self._c||a;return d("v-uni-view",{staticClass:"example-container"},[d("custom-navbar",{attrs:{title:"示例页面"}}),d("v-uni-view",{staticClass:"content"},[d("v-uni-view",{staticClass:"section"},[d("v-uni-view",{staticClass:"section-title"},[t._v("基本用法")]),d("v-uni-view",{staticClass:"code-block"},[d("v-uni-text",[t._v('<custom-navbar title="示例页面"></custom-navbar>')])],1)],1),d("v-uni-view",{staticClass:"section"},[d("v-uni-view",{staticClass:"section-title"},[t._v("自定义左侧按钮")]),d("v-uni-view",{staticClass:"demo-navbar"},[d("custom-navbar",{attrs:{title:"自定义左侧"},scopedSlots:t._u([{key:"left",fn:function(){return[d("svg-icon",{attrs:{name:"home",color:"#000000",size:"24"}})]},proxy:!0}])})],1),d("v-uni-view",{staticClass:"code-block"},[d("v-uni-text",[t._v('<custom-navbar title="自定义左侧">\n  <template #left>\n    <svg-icon name="home" color="#000000" size="24"></svg-icon>\n  </template>\n</custom-navbar>')])],1)],1),d("v-uni-view",{staticClass:"section"},[d("v-uni-view",{staticClass:"section-title"},[t._v("自定义右侧按钮")]),d("v-uni-view",{staticClass:"demo-navbar"},[d("custom-navbar",{attrs:{title:"自定义右侧"},scopedSlots:t._u([{key:"right",fn:function(){return[d("svg-icon",{attrs:{name:"plus",color:"#000000",size:"24"}})]},proxy:!0}])})],1),d("v-uni-view",{staticClass:"code-block"},[d("v-uni-text",[t._v('<custom-navbar title="自定义右侧">\n  <template #right>\n    <svg-icon name="plus" color="#000000" size="24"></svg-icon>\n  </template>\n</custom-navbar>')])],1)],1),d("v-uni-view",{staticClass:"section"},[d("v-uni-view",{staticClass:"section-title"},[t._v("自定义标题")]),d("v-uni-view",{staticClass:"demo-navbar"},[d("custom-navbar",{scopedSlots:t._u([{key:"title",fn:function(){return[d("v-uni-view",{staticClass:"custom-title"},[d("v-uni-text",{staticClass:"title-text"},[t._v("自定义标题")]),d("v-uni-text",{staticClass:"subtitle-text"},[t._v("副标题")])],1)]},proxy:!0}])})],1),d("v-uni-view",{staticClass:"code-block"},[d("v-uni-text",[t._v('<custom-navbar>\n  <template #title>\n    <view class="custom-title">\n      <text class="title-text">自定义标题</text>\n      <text class="subtitle-text">副标题</text>\n    </view>\n  </template>\n</custom-navbar>')])],1)],1),d("v-uni-view",{staticClass:"section"},[d("v-uni-view",{staticClass:"section-title"},[t._v("隐藏返回按钮")]),d("v-uni-view",{staticClass:"demo-navbar"},[d("custom-navbar",{attrs:{title:"隐藏返回按钮",showBack:!1}})],1),d("v-uni-view",{staticClass:"code-block"},[d("v-uni-text",[t._v('<custom-navbar title="隐藏返回按钮" :showBack="false"></custom-navbar>')])],1)],1)],1)],1)},e=[]},a88c:function(t,a,d){"use strict";var o=d("f88e"),e=d.n(o);e.a},f88e:function(t,a,d){var o=d("7e48");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var e=d("967d").default;e("3fa82824",o,!0,{sourceMap:!1,shadowMode:!1})},fdd6:function(t,a,d){"use strict";d("6a54");var o=d("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var e=o(d("568d")),r={components:{svgIcon:e.default},data:function(){return{}},methods:{handleClick:function(){uni.showToast({title:"按钮被点击",icon:"none"})}}};a.default=r}}]);