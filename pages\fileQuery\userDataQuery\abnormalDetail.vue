<template>
	<view class="abnormal-detail-page">
		<!-- 内容区域 -->
		<scroll-view 
			class="content-section" 
			scroll-y 
			:style="{ height: contentHeight + 'px' }"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			:show-scrollbar="false"
			enhanced
			:bounces="true"
			:fast-deceleration="true"
		>
			<!-- 加载中提示 -->
			<!-- <view class="loading-container" v-if="isLoading && !isRefreshing">
				<u-loading-icon mode="circle" size="28" color="#07ac7c"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view> -->
			
			<!-- 详情内容 -->
			<view class="detail-content" v-if="!isLoading || isRefreshing">
				<!-- 用户基本信息 -->
				<view class="info-row">
					<text class="info-label">所属单位：</text>
					<text class="info-value">{{ mergedDetailData.orgName }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">用户编号：</text>
					<text class="info-value">{{ mergedDetailData.custNo }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">电能表资产编号：</text>
					<text class="info-value">{{ mergedDetailData.meterAssetNo }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">用户名称：</text>
					<text class="info-value">{{ mergedDetailData.custName }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">用电地址：</text>
					<text class="info-value">{{ mergedDetailData.elecAddr }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">工单编号：</text>
					<text class="info-value">{{ mergedDetailData.workOrderNo }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">工单生成时间：</text>
					<text class="info-value">{{ mergedDetailData.orderTime }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">工单类型：</text>
					<text class="info-value order-type">{{ mergedDetailData.orderChildTypeName }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">检查结果：</text>
					<text class="info-value check-result">{{ mergedDetailData.orderArchiveDes }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">异常问题：</text>
					<text class="info-value">{{ mergedDetailData.abnorDesc }}</text>
				</view>
			</view>
			
			<!-- 无数据提示 -->
			<!-- <view class="empty-container" v-if="!isLoading && !isRefreshing && !mergedDetailData.workOrderNo">
				<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
			</view> -->
			
			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
		
		<!-- 底部返回按钮 -->
		<view class="bottom-button-container">
			<view class="bottom-button" @click="goBack">
				<text>返回列表</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		props: {
			queryParams: {
				type: Object,
				default: () => ({})
			},
			detailData: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				isLoading: true,
				isRefreshing: false,
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				tabNavHeight: 50,
				bottomButtonHeight: 40,
				activeTab: 'abnormal',
				
				// 标签页数据
				tabItems: [
					{ id: 'userFile', name: '用户档案' },
					{ id: 'loadData', name: '负荷数据' },
					{ id: 'powerData', name: '电量数据' },
					{ id: 'historyEvent', name: '历史事件' },
					{ id: 'abnormal', name: '用电异常' },
					{ id: 'lineLoss', name: '线损数据' }
				],
				
				// 本地详情数据（用于备份）
				localDetailData: {},
				isMockData: true,
				token:null
			};
		},
		computed: {
			// 动态页面标题
			pageTitle() {
				const activeTabItem = this.tabItems.find(item => item.id === this.activeTab);
				return activeTabItem ? activeTabItem.name : '用电异常详情';
			},
			
			// 合并后的详情数据
			mergedDetailData() {
				// 优先使用props传入的数据，如果字段不存在则使用本地数据
				return {
					...this.localDetailData,
					...this.detailData
				};
			}
		},
		watch: {
			// 监听detailData变化
			detailData: {
				handler(newVal) {
					console.log('detailData changed:', newVal);
					// 如果有新数据传入，检查是否需要补充
					if (newVal && Object.keys(newVal).length > 0) {
						this.checkAndCompleteData(newVal);
					}
				},
				deep: true,
				immediate: true
			}
		},
		mounted() {
			// 获取系统信息
			this.getSystemInfo();
			
			// 计算内容区域高度
			this.calcContentHeight();
			
			// 检查数据完整性
			this.checkAndCompleteData(this.detailData);
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token
				});
			},
			// 检查并补充数据
			checkAndCompleteData(data) {
				// 如果数据不完整，加载补充数据
				this.loadDetailData(data?.workOrderNo);
			},
			
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 简化计算逻辑，确保可以滚动到底部
				this.contentHeight = this.windowHeight - this.bottomButtonHeight;
			},
			
			// 加载详情数据（仅在没有传入数据或数据不完整时使用）
			loadDetailData(workOrderNo) {
				// 如果是下拉刷新，不设置isLoading，避免闪烁
				if (!this.isRefreshing) {
					this.isLoading = true;
				}
				uni.showLoading({
					title:'加载中...'
				})
				if(this.isMockData) {
					this.localDetailData = {
						  "orgName": "南翔供电服务中心",
						  "workOrderNo": "1868817546379870209",
						  "elecAddr": "火车站路151弄3号101室",
						  "orderTime": "2024-12-17",
						  "orderArchiveDate": "2025-01-03",
						  "custNo": "3100030169550",
						  "custName": "顾伟弟",
						  "meterAssetNo": "110007839864",
						  "orderChildType": "01",
						  "orderChildTypeName": "窃电",
						  "abnorDesc": ""
						}
					this.isLoading = false;
					uni.hideLoading();
				}else{
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"DtsUserController",
									"method":"getUserElecDetail",
									"data": {
										"workOrderNo":workOrderNo
									},
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									this.localDetailData = rtnData.data
									this.isLoading = false;
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
							uni.hideLoading();
						},
						fail: (error) => {
							// 停止加载状态
							this.isLoading = false;
							
							// 如果是刷新操作，处理刷新状态
							if (this.isRefreshing) {
								this.isRefreshing = false;
							}
							uni.hideLoading();
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				
				// 重新加载数据
				this.loadDetailData();
				
				// 设置定时器确保刷新状态至少显示一段时间
				setTimeout(() => {
					// 停止刷新状态
					this.isRefreshing = false;
				}, 1000); // 减少等待时间
			},
			
			// 返回列表页面
			goBack() {
				// 触发返回列表事件
				this.$emit('backToList');
			},
			
			// 跳转到搜索页面
			goToSearch() {
				uni.navigateTo({
					url: '/pages/fileQuery/userDataQuery/userDataQuery',
				});
			},
			
			// 切换标签页
			switchTab(tabName) {
				if (this.activeTab === tabName) return;
				
				// 添加触感反馈
				uni.vibrateShort();
				
				// 如果点击的是"用电异常"标签，不做任何操作
				if (tabName === 'abnormal') return;
				
				// 返回列表并触发切换标签事件
				this.goBack();
				
				// 使用全局事件总线传递消息
				uni.$emit('switchTab', {
					tabName: tabName
				});
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f5f5f5;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.abnormal-detail-page {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
}

/* 顶部导航区域 */
.header-section {
	background-color: #fff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

.search-icon {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 标签页导航 */
.tab-nav {
	position: fixed;
	top: 44px; /* 导航栏高度 */
	left: 0;
	right: 0;
	height: 50px;
	background-color: #fff;
	z-index: 100;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.tab-scroll {
	height: 50px;
	white-space: nowrap;
}

.tab-items {
	display: inline-flex;
	height: 50px;
	padding: 0;
}

.tab-item {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 15px;
	height: 50px;
	color: #262626;
	font-size: 14px;
	position: relative;
}

.tab-item:first-child {
	padding-left: 20px;
}

.tab-item:last-child {
	border-right: none;
}

.tab-item.active {
	color: #07ac7c;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 6px;
	left: 50%;
	transform: translateX(-50%);
	width: 40%;
	height: 2px;
	background-color: #07ac7c;
	border-radius: 1px;
}

/* 内容区域 */
.content-section {
	box-sizing: border-box;
	background-color: #fff;
	padding: 0 15px;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
	/* 确保内容可以滚动，不设置固定高度 */
	height: auto;
	padding-bottom: 0; /* 移除底部内边距 */
	overflow-y: auto;
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 0;
	margin-bottom: 10px;
}

.loading-text {
	font-size: 14px;
	color: #8c8c8c;
	margin-top: 10px;
	text-align: center;
}

/* 详情内容 */
.detail-content {
	animation: fade-in 0.3s ease;
	padding: 0;
	margin-bottom: 0;
	/* 添加白色背景和圆角，提高视觉体验 */
	background-color: #fff;
	border-radius: 8px;
	/* 确保内容底部有足够空间 */
	padding-bottom: 60px;
}

/* 信息行 */
.info-row {
	display: flex;
	padding: 12px 0;
	align-items: flex-start;
	border-bottom: 1px solid #f0f0f0;
	/* 使用统一的淡入动画，无延迟 */
	animation: fade-in 0.3s ease;
}

.info-row:last-child {
	border-bottom: none;
	padding-bottom: 0; /* 移除最后一行的底部内边距 */
}

.info-label {
	font-size: 14px;
	color: #8c8c8c;
	width: 120px;
	text-align: left;
	font-weight: normal;
}

.info-value {
	font-size: 14px;
	color: #262626;
	flex: 1;
	text-align: right;
}

/* 特殊字段样式 */
.order-type, .check-result {
	color: #ff4d4f;
	font-weight: bold;
}

/* 无数据提示 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 0;
	background-color: #fff;
	border-radius: 8px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
	margin-top: 20px;
	animation: fade-in 0.3s ease;
}

.empty-text {
	font-size: 14px;
	color: #999;
	margin-top: 10px;
}

/* 底部按钮 */
.bottom-button-container {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 40px;
	background-color: #07ac7c;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 100;
	/* 添加顶部阴影，增强视觉分离效果 */
	box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.bottom-button {
	width: 100%;
	height: 40px;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
	font-weight: bold;
}

/* 底部间距 */
.bottom-space {
	height: 50px; /* 增加底部间距，确保可以滚动到底部 */
	width: 100%;
}

/* 动画效果 */
@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slide-in {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}
</style>
