<template>
	<view class="mainContainer">
		<threenav title="详情"></threenav>
		<!-- 列表 -->
		<view class="listcontainer">
			<view class="datatext">
				<view class="datacontainer">
					<view class="datatitle">所属单位：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.mgtOrgName}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">台区编号：</view><text class="textstyle" @click="gotoAreaVisit(listData.resrcSuplCode)"
						style="color: aqua;font-size: 30rpx;">{{listData.resrcSuplCode}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">台区名称：</view><text class="textstyle"
						style="font-size: 30rpx;">{{listData.resrcSuplName}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">用户数：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.custCnt}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">采集完成率：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.succRate}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">供电量：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.ppq}}kWh</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">用电量：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.upq}}kWh</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">损失电量：</view><text class="textstyle"
						style="color: red;font-size: 30rpx;">{{listData.lossPq}}kWh</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">损失率：</view><text class="textstyle"
						style="color: red;font-size: 30rpx;">{{listData.llRate}}%</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">窃电线索数：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.totalNum}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">窃电线索查实数：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.realNum}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">窃电线索处理数：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.handleNum}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				listData: {},
			}
		},
		onLoad(options) {
			if (options.data) {
				this.listData = JSON.parse(decodeURIComponent(options.data));
			}
		},
		onReady() {

		},
		methods: {
			gotoAreaVisit(item){
				uni.navigateTo({
					url:'/pages/fileQuery/areaVisitDetail/areaVisitDetail?data='+item
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	.custom-select {
		width: 100%;
		position: relative;
	}

	.select-box {
		border: 1rpx solid #ddd;
		width: 100%;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.arrow {
		float: right;
		border: solid #999;
		border-width: 0 2rpx 2rpx 0;
		padding: 6rpx;
		margin-top: 6rpx;
	}

	.down {
		transform: rotate(45deg);
	}

	.up {
		transform: rotate(-135deg);
	}

	.dropdown {
		position: absolute;
		width: 100%;
		border: 1rpx solid #eee;
		background: #fff;
		z-index: 999;
	}

	.dropdown-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.mainContainer {
		.popupcontainer {
			height: 1500rpx;
			padding: 0 40rpx;

			.poptitle {
				text-align: center;
				font-size: 36rpx;
				font-weight: bold;
				padding: 50rpx 0;
			}

			.formitem {

				// font-weight: bold;
				.calendarContainer {
					width: 100%;

					.calInput {
						width: 100%;
						// margin-right: 30rpx;
					}
				}
			}

			.ubutton {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 400rpx 0 0;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			margin: 20rpx;

			.datatext {
				padding: 10rpx 0 10rpx 0rpx;

				.datacontainer {
					display: flex;
					align-items: center;
					padding: 30rpx 0 30rpx 30rpx;

					.datatitle {
						width: 300rpx;
						font-weight: bold;
						font-size: 30rpx;
						color: gray;
					}
					.textstyle{
						font-weight: bold;
					}
				}
			}

		}
	}
</style>