<template>
	<view class="abnormal-page">
		<!-- 内容区域 - 用电异常 -->
		<scroll-view 
			class="content-section" 
			scroll-y="true"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			:style="{ height: contentHeight + 'px' }"
		>
			<!-- 初始加载中提示 -->
			<!-- <view class="loading-container" v-if="isLoading && !isRefreshing">
				<u-loading-icon mode="circle" size="28" color="#07ac7c"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view> -->
			
			<!-- 异常工单列表 -->
			<view class="abnormal-list" v-if="!isLoading || isRefreshing">
				<!-- 工单卡片 -->
				<view class="abnormal-card" v-for="(item, index) in abnormalData" :key="index" @click="goDetail(item)">
					<!-- 工单编号 -->
					<view class="order-id-section">
						<text class="order-id-label">工单编号：</text>
						<text class="order-id">{{ item.workOrderNo }}</text>
					</view>
					
					<!-- 工单详情 -->
					<view class="order-info">
						<view class="info-row">
							<text class="info-label">用户地址：</text>
							<text class="info-value">{{ item.elecAddr }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">工单类型：</text>
							<text class="info-value">{{ item.orderChildTypeName }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">检查结果：</text>
							<text class="info-value">{{ item.orderArchiveDes }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 无数据提示 -->
			<view class="empty-container" v-if="abnormalData.length === 0">
				<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
			</view>
			
			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		props: {
			queryParams: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				isRefreshing: false,
				isLoading: true,
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				tabNavHeight: 50,
				
				// 用电异常数据
				abnormalData: [],
				isMockData: true,
				token:null
			};
		},
		watch: {
			// 添加queryParams监听，当查询参数变化时重新加载数据
			queryParams: {
				handler(newVal, oldVal) {
					console.log('abnormal - 查询参数变更:', newVal);
					// 检查参数是否真的发生了变化
					const isChanged = JSON.stringify(newVal) !== JSON.stringify(oldVal);
					if (isChanged) {
						// 重新加载数据
						this.loadAbnormalData();
					}
				},
				deep: true // 深度监听对象内部属性变化
			}
		},
		onLoad() {
			
		},
		mounted() {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
			// 获取有效的查询参数
			const effectiveParams = this.getEffectiveQueryParams();
			console.log('mounted周期：', effectiveParams);
			// 加载用电异常数据
			this.loadAbnormalData();
		},
		activated() {
			// 重新计算内容区域高度
			this.getSystemInfo();
			this.calcContentHeight();
			// 获取有效的查询参数
			const effectiveParams = this.getEffectiveQueryParams();
			console.log('activated周期：', effectiveParams);
			
			// 如果没有数据，加载数据
			if (this.abnormalData.length === 0) {
				this.loadAbnormalData();
			}
			
			// 记录当前活动标签到本地存储
			uni.setStorageSync('activeUserDataTab', 'abnormal');
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token
					vm.loadAbnormalData();
				});
			},
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏 + 标签导航）
				const topHeight = this.statusBarHeight + this.navbarHeight + this.tabNavHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight;
			},
			
			// 获取有效的查询参数（优先使用queryParams对象，其次从本地存储获取）
			getEffectiveQueryParams() {
				// 如果有传入的queryParams，优先使用
				if (this.queryParams && Object.keys(this.queryParams).length > 0) {
					return { ...this.queryParams };
				}
				
				// 否则，尝试从本地存储获取
				const savedQueryParams = uni.getStorageSync('userDataQueryParams');
				if (savedQueryParams && Object.keys(savedQueryParams).length > 0) {
					return { ...savedQueryParams };
				}
				
				// 如果都没有，返回空对象
				return {};
			},
			
			// 加载用电异常数据
			loadAbnormalData(isRefresh = false) {
				// 只有在非刷新状态下才显示中央加载提示
				if (!isRefresh) {
					this.isLoading = true;
				}
				uni.showLoading({
					title:'加载中...'
				})
				// 获取有效的查询参数
				const requestParams = this.getEffectiveQueryParams();
				if(this.isMockData) {
					this.abnormalData = [
						 {
						    "workOrderNo": "1868817546379870209",
						    "elecAddr": "火车站路151弄3号101室",
						    "orderArchiveDes": "窃电",
						    "orderChildType": "01",
						    "orderChildTypeName": "窃电"
						  }
					]
					this.isLoading = false;
					this.isRefreshing = false;
					uni.hideLoading();
				}else{
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"DtsUserController",
									"method":"getUserElecOrder",
									"data": {
										"statDateStart": requestParams.dateRange[0],
										"statDateEnd":requestParams.dateRange[requestParams.dateRange.length-1],
										"custNo":requestParams.custNo,
										"assetNo":requestParams.meterAssetNo
									},
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									this.abnormalData = rtnData.data
									this.isLoading = false;
									this.isRefreshing = false;
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
							uni.hideLoading();
						},
						fail: (error) => {
							// 停止加载状态
							this.isLoading = false;
							uni.hideLoading();
							// 如果是刷新操作，处理刷新状态
							if (isRefresh) {
								this.isRefreshing = false;
							}
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
							
							
						}
					});
				}
				
				
			},
			
			//跳转明细
			goDetail(item) {
				// 触发自定义事件，将工单数据传递给父组件
				this.$emit('showAbnormalDetail', item);
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				
				// 重新加载数据，传入isRefresh=true参数
				this.loadAbnormalData(true);
			}
		}
	}
</script>

<style lang="scss" scoped>
.abnormal-page {
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
	width: 100%;
	height: 100%;
}

/* 内容区域 */
.content-section {
	box-sizing: border-box;
	background-color: #f5f5f5;
	padding: 20rpx 30rpx;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
	overflow-y: auto;
	width: 100%;
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
	margin-bottom: 20rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #8c8c8c;
	margin-top: 20rpx;
	text-align: center;
}

/* 异常工单列表 */
.abnormal-list {
	padding: 0;
}

.abnormal-card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 工单编号部分 */
.order-id-section {
	margin-bottom: 24rpx;
	display: flex;
	align-items: center;
}

.order-id-label {
	font-size: 28rpx;
	color: #07ac7c;
	flex-shrink: 0;
	text-align: left;
	font-weight: bold;
}

.order-id {
	color: #07ac7c;
	font-weight: bold;
	font-size: 28rpx;
	margin-left: 10rpx;
	flex: 1;
	margin-top: 4rpx;
}

/* 工单详情 */
.order-info {
	width: 100%;
}

.info-row {
	display: flex;
	margin-bottom: 20rpx;
	align-items: flex-start;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 28rpx;
	color: #8c8c8c;
	width: 160rpx;
	text-align: left;
	font-weight: normal;
}

.info-value {
	font-size: 28rpx;
	color: #262626;
	text-align: right;
	flex: 1;
}

/* 无数据提示 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 0;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	margin-top: 20rpx;
}

/* 底部间距 */
.bottom-space {
	height: 40rpx;
}
</style>
