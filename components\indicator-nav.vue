<template>
	<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
		<!-- 左侧按钮区域 -->
		<view class="navbar-left" @click="handleLeftClick">
			<slot name="left">
				<uni-icons v-if="showBack" type="left" color="black" :size="finalIconSize" />
			</slot>
		</view>

		<!-- 标题区域 -->
		<view class="navbar-title">
			<slot name="title">
				<text style="color: black">{{ title }}</text>
			</slot>
		</view>

		<!-- 右侧按钮区域 -->
		<view @click="handleRightClick">
			<slot name="right">
				<uni-icons type="search" color="black" :size="finalIconSize" />
			</slot>
		</view>
		<view style="padding: 0 10rpx;" @click="handleGearClick">
			<slot name="right">
				<uni-icons type="gear" color="black" :size="finalIconSize" />
			</slot>
		</view>
	</view>
</template>

<script>
	import svgIcon from '@/components/svg-icon.vue';

	export default {
		name: 'CustomNavbar',
		components: {
			svgIcon
		},
		props: {
			// 标题文字
			title: {
				type: String,
				default: ''
			},
			// 是否显示返回按钮
			showBack: {
				type: Boolean,
				default: true
			},
			// 左侧图标大小（可选，如果不提供则自动计算）
			leftIconSize: {
				type: Number,
				default: null
			},
			// 自定义返回事件处理
			customBack: {
				type: Function,
				default: null
			},
			// 右侧按钮点击事件处理
			rightClick: {
				type: Function,
				default: null
			}
		},
		data() {
			return {
				statusBarHeight: 0,
				calculatedIconSize: 24,
			};
		},
		created() {
			// 获取状态栏高度
			this.getStatusBarHeight();
			// 设置图标大小
			this.setIconSize();
		},
		computed: {
			// 计算最终使用的图标大小
			finalIconSize() {
				// 如果提供了leftIconSize属性，则使用它，否则使用自动计算的大小
				return this.leftIconSize !== null ? this.leftIconSize : this.calculatedIconSize;
			}
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				// 设置状态栏高度
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
			},
			// 设置图标大小
			setIconSize() {
				const systemInfo = uni.getSystemInfoSync();
				const screenWidth = systemInfo.screenWidth;
				// 根据屏幕宽度设置图标大小
				if (screenWidth <= 320) {
					this.calculatedIconSize = 22;
				} else if (screenWidth <= 375) {
					this.calculatedIconSize = 24;
				} else {
					this.calculatedIconSize = 26;
				}
			},
			// 处理左侧按钮点击
			handleLeftClick() {
				if (this.customBack) {
					this.customBack();
				} else if (this.showBack) {
					uni.navigateBack({
						delta: 1
					});
				}
				this.$emit('leftClick');
			},
			// 处理右侧按钮点击
			handleRightClick() {
				this.$emit('search-click', {
					keyword: true
				});
			},
			handleGearClick() {
				this.$emit('gear-click', {
					keyword: true
				});
			}
		}
	}
</script>

<style lang="scss">
	/* 自定义导航栏 */
	.custom-navbar {
		width: 100%;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
		height: 44px;
		position: relative;
		z-index: 999;
		box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);

		.navbar-left {
			width: 44px;
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			&:active {
				opacity: 0.7;
			}
		}

		.navbar-title {
			flex: 1;
			text-align: center;
			color: #1d2129;
			font-size: 18px;
			font-weight: 500;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			padding: 0 10px;
			letter-spacing: 0.5px;
		}

		.navbar-right {
			width: 44px;
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: right;
			position: relative;

			&:active {
				opacity: 0.7;
			}
		}
	}

	/* 小屏幕设备，如iPhone 4 (320px宽度) */
	@media screen and (max-width: 320px) {
		.custom-navbar {
			height: 40px;

			.navbar-left,
			.navbar-right {
				width: 40px;
				height: 40px;
			}

			.navbar-title {
				font-size: 16px;
			}
		}
	}

	/* 中等屏幕设备 */
	@media screen and (min-width: 375px) and (max-width: 413px) {
		.custom-navbar {
			height: 44px;
		}
	}

	/* 大屏幕设备 */
	@media screen and (min-width: 414px) {
		.custom-navbar {
			height: 48px;

			.navbar-left,
			.navbar-right {
				width: 48px;
				height: 48px;
			}

			.navbar-title {
				font-size: 19px;
			}
		}
	}
</style>