<template>
	<view class="problem-report-container">
		<!-- 使用公共导航栏组件 -->
		<custom-navbar 
			title="问题提报" 
			:show-back="true" 
			background-color="#00c389"
			title-color="#FFFFFF"
		/>
		
		<!-- 表单内容 -->
		<view class="form-container">
			<!-- 提报类型 -->
			<view class="form-section">
				<view class="section-title">提报类型</view>
				<u-subsection 
					:list="typeList" 
					:current="currentType" 
					@change="typeChange"
					activeColor="#00c389"
					bgColor="#f5f5f5"
					mode="subsection"
				></u-subsection>
			</view>
			
			<!-- 标题输入 -->
			<view class="form-section">
				<view class="section-title">标题</view>
				<u-input
					v-model="reportForm.title"
					placeholder="请输入标题"
					border="surround"
					clearable
					fontSize="24rpx"
					:customStyle="{
						backgroundColor: '#FFFFFF',
						borderRadius: '16rpx',
						borderColor: '#f0f0f0'
					}"
				></u-input>
			</view>
			
			<!-- 内容输入 -->
			<view class="form-section">
				<view class="section-title">内容</view>
				<u-textarea
					v-model="reportForm.content"
					placeholder="请简要描述内容"
					count
					maxlength="500"
					height="240rpx"
					fontSize="24rpx"
					:customStyle="{
						backgroundColor: '#FFFFFF',
						borderRadius: '16rpx',
						borderColor: '#f0f0f0'
					}"
				></u-textarea>
			</view>
			
			<!-- 附件上传 -->
			<view class="form-section">
				<view class="section-title">附件上传</view>
				<view class="image-upload-area">
					<view :class="imagesList ? 'openImgList' : 'closeImgList'" v-for="(item, index) in imgList1[0] && imgList1[0].img" :key="String(index)">
						<u--image class="images" :showLoading="true" :src="item.base64" width="120rpx" height="120rpx" @click="delImg('dangerAnls', index)"></u--image>
						<u-icon name="close-circle-fill" class="right-close" @click="delImg('dangerAnls', index)"></u-icon>
					</view>
					<u--image
						:class="imgList1[0] && imgList1[0].img.length < 3 ? 'upload-box' : 'closeImg'"
						:showLoading="true"
						:src="src"
						width="120rpx"
						height="120rpx"
						@click="imageSrc('dangerAnls')"
					></u--image>
				</view>
				<view>
					<u-modal style="text-align: center;" confirmColor="#07ac7c" :show="showImg" :title="titleImg" :content="content" showCancelButton @confirm="confirmImg" @cancel="cancelImg"></u-modal>
				</view>
				<text class="upload-tip">支持类型:jpg、jpeg、png，单个文件不超过10M</text>
			</view>
		</view>
		
		<!-- 底部提交按钮 -->
		<view class="button-container">
			<u-button 
				type="primary" 
				text="提交" 
				@click="submitProblem"
				:customStyle="{
					backgroundColor: '#00c389',
					borderColor: '#00c389',
					color: '#FFFFFF',
					height: '80rpx',
					borderRadius: '0'
				}"
			></u-button>
		</view>
	</view>
</template>

<script>
	import CustomNavbar from '@/components/custom-navbar.vue';
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	import Compressor from 'compressorjs';
	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				typeList: ['咨询', '系统', '需求'],
				currentType: 0, // 默认选中咨询
				reportForm:{
					title: '',
					content: '',
					type:"01"
				},
				imageStyles: {
					width:70,
					height:70
				},
				fileList: [],
				imageList: [],
				images: true,
				indexImg: '',
				imagesList: true,
				src: '../../static/icons/shangchuan.png',
				chooseImg: [],
				imgList: [
					{
						type: 'dangerAnls',
						img: []
					}
				],
				imageValue:[],
				uploadData:[],
				mgtOrgCode:"",
				username:"",
				showImg: false,
				titleImg: '提示',
				content: '是否删除照片',
				imageValue: [], // 存储上传图片的文件名和base64值，用于传给后台
				token:null
			};
		},
		onLoad() {
			this.init2();
		},
		computed: {
			imgList1() {
				return this.imgList.filter(item => item.type == 'dangerAnls');
			},
		},
		methods: {
			init2() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token;
					vm.nameCode = data?.nameCode
					vm.init();
				});
			},
			init() {
				try {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							"data": JSON.stringify({
								"bizCode": bizCode,
								"espFlowId": getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryUserInfoByBody",
									"data": {
										"operatorId": this.nameCode,
									}
								}
							})
						},
						success: (res) => {
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									this.mgtOrgCode = rtnData.data.mgtOrgCode;
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				}
			},
			//获取上传状态
			select(res) {
				console.log('选择文件',res)
				const fileName = res.tempFiles[0].name;
				const file = res.tempFiles[0].file;
					if (file) {
						const reader = new FileReader();
						reader.onload = (e) => {
							// 获取base64数据
							const base64Data = e.target.result;
							
							// 添加到imageValue用于上传
							this.uploadData.push({
								fileName: fileName,
								base64: base64Data
							});
							
							console.log('已添加图片:', fileName);
						};
						
						reader.onerror = () => {
							console.error('FileReader读取失败');
							uni.showToast({
								title: '图片处理失败',
								icon: 'none'
							});
						};
						
						// 读取文件为DataURL (base64格式)
						reader.readAsDataURL(file);
					} else {
						console.error('无法获取文件对象');
						uni.showToast({
							title: '图片处理失败',
							icon: 'none'
						});
					}
			},
			success(e) {
				console.log('上传成功',e)
			},
			fail(e) {
				console.log('上传失败',e)
			},
			goBack() {
				uni.navigateBack();
			},
			
			typeChange(index) {
				this.currentType = index;
				// 映射索引到类型值
				const typeMap = ['01', '02', '03'];
				this.reportForm.type = typeMap[index];
			},
			delImg(index) {
				console.log(index);
				var img1 = this.imgList.splice(index, 1);
				if (this.imgList.length == 0) {
					this.images = true;
					this.imagesList = false;
				}
			},
			imageSrc(item) {
				var vm = this;
				var acceptFileType = /^jpg?|jpeg|png$/i;
				var item2 = vm.imgList.filter(v => v.type == item);
				if (item2[0].img.length >= 3) {
					uni.showToast({
						title: '最多上传3张照片',
						icon: 'none'
					});
					return;
				}
				
				// 使用uni.chooseImage替代wx.chooseImage
				uni.chooseImage({
					count: 1, // 最多可以选择的图片张数
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
					success: async function(res) {
						console.log(res);
						// 获取选择的图片临时路径
						const tempFilePath = res.tempFilePaths[0];
						console.log(tempFilePath);
						
						// 从res.tempFiles中获取文件类型
						const fileType = res.tempFiles && res.tempFiles[0] && res.tempFiles[0].type 
							? res.tempFiles[0].type.split('/')[1] 
							: 'png'; // 默认为png类型
						
						// 使用文件名
						const fileName = res.tempFiles[0].name;
						
						console.log('文件类型:', fileType);
						console.log('生成的文件名:', fileName);
						const originalSize = res.tempFiles[0].size;
						 console.log('原始图片大小：',`${(originalSize/ 1024).toFixed(2)}kb`)
						console.log(`原始图片字节: ${(originalSize)}`);
						new Compressor(res.tempFiles[0], {
						  quality: 0.6, // 压缩质量0-97.72%，0.2-83.9%，0.4-76.18%，0.6-67.99%，0.8-46.41%
						  convertSize:false,
						  mimeType: res.tempFiles[0].type,
						  success: (result) => {
						    //这里是Bold流转化为新的File
							if(res.tempFiles[0].type ===  result.type) {
								const fileA = new File([result], result.name, { type: result.type })
								console.log(fileA)
								console.log('压缩后的图片大小：',`${(fileA.size/ 1024).toFixed(2)}kb`)
								console.log(`压缩后图片字节: ${(fileA.size)}`);
								vm.getBase64Data(fileA,item,fileName)
							}else{
								const fileA = new File([result], result.name, { type: res.tempFiles[0].type })
								console.log(fileA)
								console.log('压缩后的图片大小：',`${(fileA.size/ 1024).toFixed(2)}kb`)
								console.log(`压缩后图片字节: ${(fileA.size)}`);
								vm.getBase64Data(fileA,item,fileName)
							}
						    
						  },
						  error: (error) => {
						    console.error('图片压缩失败', error);
						  },
						});
						 // 执行压缩
						if (!acceptFileType.test(fileType)) {
							uni.showToast({
								title: '支持格式: .jpg .png .jpeg',
								icon: 'none'
							});
							return;
						}
						
						// 使用FileReader API将文件转换为base64 - 只保留H5实现
						// 获取文件对象
						// const file = res.tempFiles[0];
						// this.getBase64Data(file)
					}
				});
			},
			getBase64Data(file,item,fileName) {
				const vm = this;
				if (file) {
					const reader = new FileReader();
					reader.onload = (e) => {
						// 获取base64数据
						const base64Data = e.target.result;
						// 添加到imgList用于显示
						const targetList = vm.imgList.find(list => list.type === item);
						if(targetList) {
							targetList.img.push({
								base64: base64Data
							});
						}
						
						// 添加到imageValue用于上传
						vm.imageValue.push({
							fileName: fileName,
							base64: base64Data
						});
						// 更新UI显示状态
						vm.imagesList = true;
						vm.updateImageDisplayStatus();
						
						console.log('已添加图片:', fileName);
					};
					
					reader.onerror = () => {
						console.error('FileReader读取失败');
						uni.showToast({
							title: '图片处理失败',
							icon: 'none'
						});
					};
					
					// 读取文件为DataURL (base64格式)
					reader.readAsDataURL(file);
				} else {
					console.error('无法获取文件对象');
					uni.showToast({
						title: '图片处理失败',
						icon: 'none'
					});
				}
			},
			updateImageDisplayStatus() {
				if (this.imgList.some(list => list.img.length > 0)) {
					this.images = false;
					this.imagesList = true;
				} else {
					this.images = true;
					this.imagesList = false;
				}
			},
			handleImgUpload() {
				if(this.imageValue.length > 0) {
					this.imageValue.forEach((item,index) => {
						uni.request({
							url: `http://127.0.0.1:${port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token':this.token
							},
							data: {
								token: this.token,
								method:"PutHuaYun",
								uri:url,
								data:JSON.stringify({
									"bizCode":bizCode,
									"espFlowId":getUUID(),
									"espRsvField1":"",
									"espRsvField2":"",
									"espRsvField3":"",
									"espSign":"",
									"espTimestamp":getCurrentTimestamp(),
									"espInformation": {
										"service": "AseCommonController",
										"method": "uploadFile",
										"data": {
											"base64":item.base64
										}
									}
								})
							},
							success: (res) => {
								console.log(res)
								if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									this.uploadImgs.push(rtnData.data)
									uni.setStorageSync('uploadImgs',JSON.stringify(this.uploadImgs));
									uni.showToast({
										title: '上传成功',
										icon: 'none',
									});
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
								}else{
									uni.showToast({
										title: '暂无数据',
										icon: 'none',
										duration: 2000
									});
								}
							},
							fail: (error) => {
								uni.showToast({
									title: '上传失败',
									icon: 'none',
									duration: 2000
								});
							}
						});
					})
				}
				
			},
			submitProblem() {
				
				var vm = this;
				// 表单验证
				if (!this.reportForm.title.trim()) {
					uni.showToast({
						title: '请输入标题',
						icon: 'none'
					});
					return;
				}
				
				if (!this.reportForm.content.trim()) {
					uni.showToast({
						title: '请输入内容',
						icon: 'none'
					});
					return;
				}
				this.handleImgUpload();
				// 提交表单
				uni.showLoading({
					title: '提交中...'
				});
				uni.request({
					url: `http://127.0.0.1:${port}/xczs/forward/for`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json',
						'token':this.token
					},
					data: {
						method:"PutHuaYun",
						token: this.token,
						uri:url,
						data:JSON.stringify({
							"bizCode":bizCode,
							"espFlowId":getUUID(),
							"espRsvField1":"",
							"espRsvField2":"",
							"espRsvField3":"",
							"espSign":"",
							"espTimestamp":getCurrentTimestamp(),
							"espInformation": {
								"service":"MobileQuestionManageController",
								"method":"questionAdd",
								"data": {
									"questionTitle":vm.reportForm.title,
									"questionCount":vm.reportForm.content,
									"mgtOrgCode":vm.mgtOrgCode,
									"responderId":vm.username,
									"questionType": vm.reportForm.type
								},
							}
						})
					},
					success: (res) => {
						if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
								 uni.showToast({
								 	title: rtnData.data.message,
								 	icon: 'none',
								 	duration: 2000
								 });
								 uni.redirectTo({
								 	url: '/pages/problemManage/problemList'
								 });
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
						}else{
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					},
					fail: (error) => {
						console.lg(error)
						uni.showToast({
							title: '暂无数据',
							icon: 'none',
							duration: 2000
						});
					}
				});
			}
		}
	}
</script>

<style lang="scss">
.problem-report-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.form-container {
	flex: 1;
	padding: 0 30rpx;
	overflow-y: auto;
	background-color: #FFFFFF;
}
::v-deep .uni-textarea-placeholder {
	font-size: 24rpx;
}
.form-section {
	margin-top: 30rpx;
	
	.section-title {
		font-size: 28rpx;
		color: #262626;
		margin-bottom: 20rpx;
		font-weight: 500;
	}
}

.upload-tip-text {
	font-size: 24rpx;
	color: #8c8c8c;
	margin-bottom: 20rpx;
}
::v-deep .myUplad .icon-add{
	width:60rpx;
	height:4rpx;
}
::v-deep .myUplad .file-picker__progress{
	display:none;
}
::v-deep .myUplad .icon-del-box{
	top: 1px;
	right: 1px;
	height: 36rpx;
	width: 36rpx;
	.icon-del {
		width: 20rpx;
	}
}
.button-container {
	width: 100%;
}
.photoWrap {
		padding: 20rpx;
	}

	.openImg {
		margin-left: 5px;
	}

	.closeImg {
		display: none;
	}

	.openImgList {
		display: inline-block;
		position: relative;
		margin: 0 4px 10px;

		.right-close {
			position: absolute;
			top: -10px;
			right: -10px;
			z-index: 1;
		}
	}

	.closeImgList {
		display: none;
	}
	/* 图片上传区域 */
	.image-upload-area {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		margin: 20rpx 0;
	}
	.upload-box {
		display: flex;
		width: 120rpx;
		height: 120rpx;
		border: 1px solid #ddd;
		border-radius: 10rpx;
		background-color: #f9f9f9;
		align-items: center;
		justify-content: center;
		margin: 10rpx;
	}
	
	.openImg {
		margin: 10rpx;
	}
	
	.closeImg {
		display: none;
	}
	
	.openImgList {
		display: inline-block;
		position: relative;
		margin: 10rpx;
		.right-close {
			position: absolute;
			top: -20rpx;
			right: -20rpx;
			z-index: 1;
		}
	}
	
	.closeImgList {
		display: none;
	}
	
	.upload-tip {
		color: #999;
		font-size: 24rpx;
		display: block;
		margin: 10rpx 0 30rpx;
	}
</style>
