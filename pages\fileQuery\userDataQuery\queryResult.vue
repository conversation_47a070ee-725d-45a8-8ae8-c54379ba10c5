<template>
	<view class="query-result">
		<!-- 顶部导航栏 -->
		<view class="header-section">
			<custom-navbar title="查询结果">
				<!-- 自定义右侧搜索按钮 -->
				<template #right>
					<view class="search-icon" @click="goToSearch">
						<uni-icons type="search" color="#000000" size="28"></uni-icons>
					</view>
				</template>
			</custom-navbar>
		</view>
		
		<!-- 结果列表区域 -->
		<scroll-view class="result-section" scroll-y :style="{ height: scrollViewHeight + 'px' }">
			<!-- 用户列表 -->
			<view class="user-list">
				<view 
					class="user-card" 
					v-for="(item, index) in userResults" 
					:key="index"
					@click="viewUserDetail(item)"
				>
					<!-- 用户编号标题行 -->
					<view class="user-id-section">
						<text class="user-id-label">用户编号：</text>
						<text class="user-id">{{ item.custNo }}</text>
					</view>
					
					<!-- 用户详细信息 -->
					<view class="user-info">
						<view class="info-row">
							<text class="info-label">用户名称：</text>
							<text class="info-value">{{ item.custName }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">电能表资产编号：</text>
							<text class="info-value">{{ item.meterAssetNo }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 无数据提示 -->
			<view class="no-data" v-if="userResults.length === 0">
				<u-empty mode="search" icon="/static/icons/noresult.png"></u-empty>
			</view>
			
			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
	</view>
</template>

<script>
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port
	} from '@/static/commonJs/util.js'
	export default {
		data() {
			return {
				statusBarHeight: 0,
				navbarHeight: 44,
				windowHeight: 0,
				scrollViewHeight: 0,
				userResults: [],
				queryParams: {},
				isMockData: true,
				token:null
			};
		},
		onLoad(options) {
			// 获取状态栏高度
			this.getStatusBarHeight();
			
			// 获取上一页传来的数据
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on('acceptQueryResults', (data) => {
				this.loadUserData(data.queryParams || {});
			});
		},
		onReady() {
			// 计算滚动区域高度
			this.calcScrollViewHeight();
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					console.log(data)
					vm.token = data?.token
					vm.loadUserData(data.queryParams || {});
				});
			},
			// 从JSON文件加载用户数据
			loadUserData(params) {
				uni.showLoading({
					title:'加载中...'
				})
				if(this.isMockData) {//模拟数据
				
					this.userResults = [
						{
						  "orgName": "嘉定营销分中心",
						  "custNo": "3100030169550",
						  "custName": "顾伟弟",
						  "meterAssetNo": "110007839864",
						  "elecAddr": "火车站路151弄3号101室",
						  "elecTypeCode": "城镇居民生活用电",
						  "tgName": "车站路四号站_配变",
						  "tgId": "94231_00",
						  "tradeCode": "",
						  "voltCode": "交流220V",
						  "lodeAttrCode": "",
						  "contractCap": "",
						  "runCap": "6",
						  "ratedVoltage": "220",
						  "ratedCurrent": "0.25",
						  "wireMode": "01",
						  "commAddr": "110007839864",
						  "commMode": "28",
						  "rate": "",
						  "instDate": "2023-02-20",
						  "tgNo": "94231_00",
						  "statDate": "2025-04-22"
						}
					]
					uni.hideLoading();
				}else{
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"DtsUserController",
									"method":"getUserArc",
									"data": {
										"custNo": params.custNo,
										"custName": params.custName,
										"meterAssetNo": params.meterAssetNo,
										"pageNum":1,
										"pageSize":10,
									}
								}
							})
						},
						success: (res) => {
							console.log(res)
							uni.hideLoading();
							 if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
								 	this.userResults = rtnData.data.list
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							 }else{
								 uni.showToast({
								 	title: '暂无数据',
								 	icon: 'none',
								 	duration: 2000
								 });
							 }
						},
						fail: (error) => {
							console.error('请求失败:', error);
							uni.hideLoading();
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
			},
			
			// 获取状态栏高度
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 计算滚动区域高度
			calcScrollViewHeight() {
				// 只需考虑导航栏高度
				this.scrollViewHeight = this.windowHeight - (this.statusBarHeight + this.navbarHeight);
			},
			
			// 跳转到搜索页面
			goToSearch() {
				uni.navigateTo({
					url: '/pages/fileQuery/userDataQuery/userDataQuery',
				});
			},
			
			// 查看用户详情
			viewUserDetail(item) {
				// 实际项目中可以跳转到用户详情页
				uni.navigateTo({
				    url: `/pages/fileQuery/userDataQuery/userFileQuery?item=${encodeURIComponent(JSON.stringify(item))}`
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #f5f5f5;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.query-result {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
}

/* 顶部导航区域 */
.header-section {
	background-color: #fff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

.search-icon {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 结果列表区域 */
.result-section {
	margin-top: 44px; /* 导航栏高度 */
	box-sizing: border-box;
	background-color: #f5f5f5;
	padding: 10px 15px;
}

/* 用户列表 */
.user-list {
	padding: 0;
}

.user-card {
	background-color: #fff;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 10px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.user-id-section {
	margin-bottom: 12px;
	display: flex;
	align-items: center;
}

.user-id-label {
	font-size: 14px;
	color: #07ac7c;
	flex-shrink: 0;
	text-align: left;
	font-weight: bold;
}

.user-id {
	color: #07ac7c;
	font-weight: bold;
	font-size: 14px;
	margin-left: 5px;
	flex: 1;
	margin-top: 2px;
}

.user-info {
	width: 100%;
}

.info-row {
	display: flex;
	margin-bottom: 10px;
	align-items: flex-start;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 14px;
	color: #8c8c8c;
	width: 120px;
	text-align: left;
	font-weight: normal;
}

.info-value {
	font-size: 14px;
	color: #262626;
	text-align: right;
	flex: 1;
}

/* 无数据提示 */
.no-data {
	padding: 30px 0;
}

/* 底部间距 */
.bottom-space {
	height: 20px;
}

/* 修改导航栏样式 */
/deep/ .custom-navbar {
	background-color: #fff !important;
	box-shadow: none !important;
}

/deep/ .navbar-left .svg-icon {
	color: #000000 !important;
}

/deep/ .navbar-title {
	color: #000000 !important;
}
</style> 