(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-problemManage-problemList"],{"00c9":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-58eaceb6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-58eaceb6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-58eaceb6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-58eaceb6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-58eaceb6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-58eaceb6]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-58eaceb6]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-58eaceb6]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-58eaceb6]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-58eaceb6]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-58eaceb6]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-58eaceb6]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-58eaceb6]::after{border:none}.u-hover-class[data-v-58eaceb6]{opacity:.7}.u-primary-light[data-v-58eaceb6]{color:#ecf5ff}.u-warning-light[data-v-58eaceb6]{color:#fdf6ec}.u-success-light[data-v-58eaceb6]{color:#f5fff0}.u-error-light[data-v-58eaceb6]{color:#fef0f0}.u-info-light[data-v-58eaceb6]{color:#f4f4f5}.u-primary-light-bg[data-v-58eaceb6]{background-color:#ecf5ff}.u-warning-light-bg[data-v-58eaceb6]{background-color:#fdf6ec}.u-success-light-bg[data-v-58eaceb6]{background-color:#f5fff0}.u-error-light-bg[data-v-58eaceb6]{background-color:#fef0f0}.u-info-light-bg[data-v-58eaceb6]{background-color:#f4f4f5}.u-primary-dark[data-v-58eaceb6]{color:#398ade}.u-warning-dark[data-v-58eaceb6]{color:#f1a532}.u-success-dark[data-v-58eaceb6]{color:#53c21d}.u-error-dark[data-v-58eaceb6]{color:#e45656}.u-info-dark[data-v-58eaceb6]{color:#767a82}.u-primary-dark-bg[data-v-58eaceb6]{background-color:#398ade}.u-warning-dark-bg[data-v-58eaceb6]{background-color:#f1a532}.u-success-dark-bg[data-v-58eaceb6]{background-color:#53c21d}.u-error-dark-bg[data-v-58eaceb6]{background-color:#e45656}.u-info-dark-bg[data-v-58eaceb6]{background-color:#767a82}.u-primary-disabled[data-v-58eaceb6]{color:#9acafc}.u-warning-disabled[data-v-58eaceb6]{color:#f9d39b}.u-success-disabled[data-v-58eaceb6]{color:#a9e08f}.u-error-disabled[data-v-58eaceb6]{color:#f7b2b2}.u-info-disabled[data-v-58eaceb6]{color:#c4c6c9}.u-primary[data-v-58eaceb6]{color:#3c9cff}.u-warning[data-v-58eaceb6]{color:#f9ae3d}.u-success[data-v-58eaceb6]{color:#5ac725}.u-error[data-v-58eaceb6]{color:#f56c6c}.u-info[data-v-58eaceb6]{color:#909399}.u-primary-bg[data-v-58eaceb6]{background-color:#3c9cff}.u-warning-bg[data-v-58eaceb6]{background-color:#f9ae3d}.u-success-bg[data-v-58eaceb6]{background-color:#5ac725}.u-error-bg[data-v-58eaceb6]{background-color:#f56c6c}.u-info-bg[data-v-58eaceb6]{background-color:#909399}.u-main-color[data-v-58eaceb6]{color:#303133}.u-content-color[data-v-58eaceb6]{color:#606266}.u-tips-color[data-v-58eaceb6]{color:#909193}.u-light-color[data-v-58eaceb6]{color:#c0c4cc}.u-safe-area-inset-top[data-v-58eaceb6]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-58eaceb6]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-58eaceb6]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-58eaceb6]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-58eaceb6]{z-index:10090}uni-toast .uni-toast[data-v-58eaceb6]{z-index:10090}[data-v-58eaceb6]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */.problem-list-container[data-v-58eaceb6]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}.tabs[data-v-58eaceb6]{height:%?80?%;padding:%?6?% 0}.subsection-container[data-v-58eaceb6]{background-color:#f5f5f5;padding:0 %?20?% 0}.problem-scroll[data-v-58eaceb6]{flex:1;overflow:hidden}.problem-list[data-v-58eaceb6]{padding:0 %?20?%;z-index:1}.empty-container[data-v-58eaceb6]{display:flex;align-items:center;justify-content:center;height:100%;font-size:%?24?%;margin-top:%?100?%}.problem-item[data-v-58eaceb6]{display:flex;flex-direction:column;background-color:#fff;margin-bottom:%?20?%;border-radius:%?16?%;padding:%?30?%;box-shadow:0 %?2?% %?10?% rgba(0,0,0,.05)}.problem-item[data-v-58eaceb6]:nth-child(2){border:%?1?% solid #f0f0f0}.problem-item .problem-top[data-v-58eaceb6]{display:flex;align-items:flex-start;margin-bottom:%?20?%}.problem-item .problem-top .problem-icon[data-v-58eaceb6]{margin-right:%?20?%;margin-top:%?4?%}.problem-item .problem-top .problem-title[data-v-58eaceb6]{flex:1;font-size:%?28?%;color:#262626;line-height:1.5;font-weight:400;letter-spacing:%?0.5?%}.problem-item .problem-top .problem-arrow[data-v-58eaceb6]{margin-left:%?20?%;margin-top:%?4?%}.problem-item .problem-bottom[data-v-58eaceb6]{display:flex;justify-content:space-between;align-items:center}.problem-item .problem-bottom .problem-info[data-v-58eaceb6]{font-size:%?24?%;color:#8c8c8c}.problem-item .problem-bottom .problem-info .username[data-v-58eaceb6]{margin-right:%?20?%;color:#262626}.problem-item .problem-bottom .problem-time[data-v-58eaceb6]{font-size:%?24?%;color:#8c8c8c;text-align:right}.bottom-button[data-v-58eaceb6]{height:%?80?%;background-color:#07ac7c;color:#fff;font-size:%?32?%;display:flex;align-items:center;justify-content:center;font-weight:500;letter-spacing:%?1?%}.loading-more[data-v-58eaceb6]{width:100%;height:%?60?%;display:flex;justify-content:center;align-items:center;padding:%?20?% 0;color:#999;font-size:%?28?%}',""]),t.exports=e},"013c":function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=o(a("fcf3")),i=o(a("d296")),n={name:"u-subsection",mixins:[uni.$u.mpMixin,uni.$u.mixin,i.default],data:function(){return{itemRect:{width:0,height:0}}},watch:{list:function(t,e){this.init()},current:{immediate:!0,handler:function(t){}}},computed:{wrapperStyle:function(){var t={};return"button"===this.mode&&(t.backgroundColor=this.bgColor),t},barStyle:function(){var t={};return t.width="".concat(this.itemRect.width,"px"),t.height="".concat(this.itemRect.height,"px"),t.transform="translateX(".concat(this.current*this.itemRect.width,"px)"),"subsection"===this.mode&&(t.backgroundColor=this.activeColor),t},itemStyle:function(t){var e=this;return function(t){var a={};return"subsection"===e.mode&&(a.borderColor=e.activeColor,a.borderWidth="1px",a.borderStyle="solid"),a}},textStyle:function(t){var e=this;return function(t){var a={};return a.fontWeight=e.bold&&e.current===t?"bold":"normal",a.fontSize=uni.$u.addUnit(e.fontSize),"subsection"===e.mode?a.color=e.current===t?"#fff":e.inactiveColor:a.color=e.current===t?e.activeColor:e.inactiveColor,a}}},mounted:function(){this.init()},methods:{init:function(){var t=this;uni.$u.sleep().then((function(){return t.getRect()}))},getText:function(t){return"object"===(0,r.default)(t)?t[this.keyName]:t},getRect:function(){var t=this;this.$uGetRect(".u-subsection__item--0").then((function(e){t.itemRect=e}))},clickHandler:function(t){this.$emit("change",t)}}};e.default=n},"1ec4":function(t,e,a){var o=a("00c9");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("967d").default;r("54796f92",o,!0,{sourceMap:!1,shadowMode:!1})},"2a3a":function(t,e,a){"use strict";a.r(e);var o=a("6b79"),r=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=r.a},"2a50":function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=o(a("b7c7"));a("d4b5"),a("c223"),a("8f71"),a("bf0f");var i=o(a("1098")),n=a("b3d7"),d={components:{CustomNavbar:i.default},data:function(){return{currentTab:0,token:null,path:window.location.hash,tabs:[{name:"咨询",type:"01"},{name:"系统",type:"02"},{name:"需求",type:"03"},{name:"我的提问",type:"04"}],tabsList:["咨询","系统","需求","我的提问"],allProblems:[],problemList:[],params:{pageNum:1,pageSize:10},hasMoreData:!0,isRefreshing:!1,isLoading:!1,isMockData:!0}},created:function(){},onLoad:function(){this.showInfo()},methods:{init:function(){var t=this;wx.invoke("ext_DataCache_GetInfo",{data:{key:"1002838.userInfo"}},(function(e){var a=JSON.parse(e.result);t.token=null===a||void 0===a?void 0:a.token}))},showInfo:function(){var t=this;this.isLoading&&!this.isRefreshing||(this.isLoading=!0,this.isMockData?(this.allProblems=[{questionId:"1793583533075009538",questionTitle:"hhjghjghjggh",submitterId:"T00033391",submitterName:"岳恒",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",questionCount:"hhjghjghjggh",questionType:"01",questionTypeName:"咨询",questionTime:"2024-05-23",distLv:"02"},{questionId:"1793583173149200385",questionTitle:"测试",submitterId:"T00033391",submitterName:"岳恒",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",questionCount:"测试测试测试测试测试测试",questionType:"01",questionTypeName:"咨询",questionTime:"2024-05-23",distLv:"02"},{questionId:"1934502796785139714",questionTitle:"存在报错",submitterId:"T00033391",submitterName:"岳恒",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",questionCount:"工单监控页面报错xxx500，页面无数据",questionType:"02",questionTypeName:"系统",questionTime:"2025-06-16",distLv:"02"},{questionId:"1934503236767629313",questionTitle:"成效明细页面无数据",submitterId:"T00033391",submitterName:"岳恒",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",questionCount:"首次打开成效明细页面无数据展示，通过成效统计超链接点击后跳转成效明细有数据展示",questionType:"01",questionTypeName:"咨询",questionTime:"2025-06-16",distLv:"02"},{questionId:"1934502271230459905",questionTitle:"模型线索页面新增展示字段",submitterId:"T00033391",submitterName:"岳恒",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",questionCount:"模型线索页面展示字段增加：是否现场检查、是否归档，初始页面默认展示字段新增：是否派工、是否推送、是否现场检查、是否归档",questionType:"03",questionTypeName:"需求",questionTime:"2025-06-16",distLv:"02"},{questionId:"1934502713498845185",questionTitle:"页面与导出表格内数据不一致",submitterId:"T00033391",submitterName:"岳恒",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",questionCount:"页面导出数据字段要与页面展示字段一致，调整全部页面导出数据字段与页面展示字段一致（数量、内容）",questionType:"02",questionTypeName:"系统",questionTime:"2025-06-16",distLv:"02"},{questionId:"1934502997222539266",questionTitle:"线索页面人员展示问题",submitterId:"T00033391",submitterName:"岳恒",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",questionCount:"线索页面派工时查询可派工人员列表后需展示所属单位（包含下级）下全部掌机人员，现在只到本级有问题",questionType:"03",questionTypeName:"需求",questionTime:"2025-06-16",distLv:"02"},{questionId:"1934502875273150466",questionTitle:"页面改动",submitterId:"T00033391",submitterName:"岳恒",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",questionCount:"更改成效明细、工单明细两个页面导出的表格字段上级单位（为以市公司为基准不要出现上海电力公司）、管理单位（以供服为基准，营销部的工单直接展示为市公司）",questionType:"03",questionTypeName:"需求",questionTime:"2025-06-16",distLv:"02"}],this.filterProblems(),this.isLoading=!1,setTimeout((function(){t.isRefreshing=!1}),1e3)):uni.request({url:"http://127.0.0.1:".concat(n.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:this.token},data:{token:this.token,method:"PutHuaYun",uri:n.url,data:JSON.stringify({bizCode:n.bizCode,espFlowId:(0,n.getUUID)(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:(0,n.getCurrentTimestamp)(),espInformation:{service:"MobileQuestionManageController",method:"getQuestion",data:{questionType:"",responderId:"",pageNum:this.params.pageNum,pageSize:this.params.pageSize}}})},success:function(e){if(console.log(e),e&&1===e.data.Tag){var a=e.data.Data.espInformation;if(a&&200==a.code){var o=a.data.list||[];t.hasMoreData=o.length>=t.params.pageSize,1===t.params.pageNum?t.allProblems=(0,r.default)(o):t.allProblems=[].concat((0,r.default)(t.allProblems),(0,r.default)(o)),t.isLoading=!1,t.filterProblems(),t.isRefreshing&&(t.isRefreshing=!1)}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3})},fail:function(e){t.isRefreshing=!1,t.isLoading=!1,uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}}))},goBack:function(){uni.navigateBack()},switchTab:function(t){this.currentTab=t,this.filterProblems()},filterProblems:function(){var t=this.tabs[this.currentTab].type;this.problemList="04"===t?this.allProblems.filter((function(t){return!t.questionType})):this.allProblems.filter((function(e){return e.questionType===t}))},goToDetail:function(t){console.log("进入跳转"),uni.navigateTo({url:"./problemDetail?item=".concat(encodeURIComponent(JSON.stringify(t)))})},goToReport:function(){uni.navigateTo({url:"/pages/problemManage/problemReport"})},onLoadMore:function(){console.log("滚动到底部了"),!this.isLoading&&this.hasMoreData&&(this.params.pageNum++,this.showInfo())},onRefresh:function(){this.isRefreshing=!0,this.hasMoreData=!0,this.allProblems=[],this.params.pageNum=1,this.showInfo()}}};e.default=d},"3c38":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-4a603381]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-4a603381]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-4a603381]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-4a603381]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-4a603381]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-4a603381]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-4a603381]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-4a603381]::after{border:none}.u-hover-class[data-v-4a603381]{opacity:.7}.u-primary-light[data-v-4a603381]{color:#ecf5ff}.u-warning-light[data-v-4a603381]{color:#fdf6ec}.u-success-light[data-v-4a603381]{color:#f5fff0}.u-error-light[data-v-4a603381]{color:#fef0f0}.u-info-light[data-v-4a603381]{color:#f4f4f5}.u-primary-light-bg[data-v-4a603381]{background-color:#ecf5ff}.u-warning-light-bg[data-v-4a603381]{background-color:#fdf6ec}.u-success-light-bg[data-v-4a603381]{background-color:#f5fff0}.u-error-light-bg[data-v-4a603381]{background-color:#fef0f0}.u-info-light-bg[data-v-4a603381]{background-color:#f4f4f5}.u-primary-dark[data-v-4a603381]{color:#398ade}.u-warning-dark[data-v-4a603381]{color:#f1a532}.u-success-dark[data-v-4a603381]{color:#53c21d}.u-error-dark[data-v-4a603381]{color:#e45656}.u-info-dark[data-v-4a603381]{color:#767a82}.u-primary-dark-bg[data-v-4a603381]{background-color:#398ade}.u-warning-dark-bg[data-v-4a603381]{background-color:#f1a532}.u-success-dark-bg[data-v-4a603381]{background-color:#53c21d}.u-error-dark-bg[data-v-4a603381]{background-color:#e45656}.u-info-dark-bg[data-v-4a603381]{background-color:#767a82}.u-primary-disabled[data-v-4a603381]{color:#9acafc}.u-warning-disabled[data-v-4a603381]{color:#f9d39b}.u-success-disabled[data-v-4a603381]{color:#a9e08f}.u-error-disabled[data-v-4a603381]{color:#f7b2b2}.u-info-disabled[data-v-4a603381]{color:#c4c6c9}.u-primary[data-v-4a603381]{color:#3c9cff}.u-warning[data-v-4a603381]{color:#f9ae3d}.u-success[data-v-4a603381]{color:#5ac725}.u-error[data-v-4a603381]{color:#f56c6c}.u-info[data-v-4a603381]{color:#909399}.u-primary-bg[data-v-4a603381]{background-color:#3c9cff}.u-warning-bg[data-v-4a603381]{background-color:#f9ae3d}.u-success-bg[data-v-4a603381]{background-color:#5ac725}.u-error-bg[data-v-4a603381]{background-color:#f56c6c}.u-info-bg[data-v-4a603381]{background-color:#909399}.u-main-color[data-v-4a603381]{color:#303133}.u-content-color[data-v-4a603381]{color:#606266}.u-tips-color[data-v-4a603381]{color:#909193}.u-light-color[data-v-4a603381]{color:#c0c4cc}.u-safe-area-inset-top[data-v-4a603381]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-4a603381]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-4a603381]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-4a603381]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-4a603381]{z-index:10090}uni-toast .uni-toast[data-v-4a603381]{z-index:10090}[data-v-4a603381]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-view[data-v-4a603381], uni-scroll-view[data-v-4a603381], uni-swiper-item[data-v-4a603381]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-subsection[data-v-4a603381]{display:flex;flex-direction:row;position:relative;overflow:hidden;width:100%;box-sizing:border-box}.u-subsection--button[data-v-4a603381]{height:32px;background-color:#eeeeef;padding:3px;border-radius:3px;align-items:stretch}.u-subsection--button__bar[data-v-4a603381]{background-color:#fff;border-radius:3px!important}.u-subsection--subsection[data-v-4a603381]{height:30px}.u-subsection__bar[data-v-4a603381]{position:absolute;transition-property:color,-webkit-transform;transition-property:transform,color;transition-property:transform,color,-webkit-transform;transition-duration:.3s;transition-timing-function:ease-in-out}.u-subsection__bar--first[data-v-4a603381]{border-top-left-radius:3px;border-bottom-left-radius:3px;border-top-right-radius:0;border-bottom-right-radius:0}.u-subsection__bar--center[data-v-4a603381]{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:0}.u-subsection__bar--last[data-v-4a603381]{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:3px;border-bottom-right-radius:3px}.u-subsection__item[data-v-4a603381]{display:flex;flex-direction:row;flex:1;justify-content:center;align-items:center;position:relative}.u-subsection__item--no-border-right[data-v-4a603381]{border-right-width:0!important}.u-subsection__item--first[data-v-4a603381]{border-top-left-radius:3px;border-bottom-left-radius:3px}.u-subsection__item--last[data-v-4a603381]{border-top-right-radius:3px;border-bottom-right-radius:3px}.u-subsection__item__text[data-v-4a603381]{font-size:12px;line-height:12px;display:flex;flex-direction:row;align-items:center;transition-property:color;transition-duration:.3s}',""]),t.exports=e},"3c98":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return o}));var o={uIcon:a("59b5").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.show?a("v-uni-view",{staticClass:"u-empty",style:[t.emptyStyle]},[t.isSrc?a("v-uni-image",{style:{width:t.$u.addUnit(t.width),height:t.$u.addUnit(t.height)},attrs:{src:t.icon,mode:"widthFix"}}):a("u-icon",{attrs:{name:"message"===t.mode?"chat":"empty-"+t.mode,size:t.iconSize,color:t.iconColor,"margin-top":"14"}}),a("v-uni-text",{staticClass:"u-empty__text",style:[t.textStyle]},[t._v(t._s(t.text?t.text:t.icons[t.mode]))]),t.$slots.default||t.$slots.$default?a("v-uni-view",{staticClass:"u-empty__wrap"},[t._t("default")],2):t._e()],1):t._e()},i=[]},5233:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return o}));var o={uSubsection:a("8fd6").default,uEmpty:a("57a9").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"problem-list-container"},[a("custom-navbar",{attrs:{title:"问题管理",path:t.path,"background-color":"#00c389","title-color":"#FFFFFF"}}),a("v-uni-view",{staticClass:"subsection-container"},[a("u-subsection",{staticClass:"tabs",attrs:{list:t.tabsList,current:t.currentTab,activeColor:"#00c389",inactiveColor:"#262626",mode:"button",buttonColor:"#f5f5f5",bgColor:"#f5f5f5",itemStyle:{height:"80rpx",fontSize:"28rpx"},lineWidth:"40rpx",lineHeight:"4rpx"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.switchTab.apply(void 0,arguments)}}})],1),a("v-uni-scroll-view",{staticClass:"problem-scroll",attrs:{"enable-back-to-top":!0,"scroll-anchoring":!0,enhanced:!0,bounces:!0,"show-scrollbar":!1,"refresher-enabled":!0,"refresher-triggered":t.isRefreshing,"scroll-y":!0},on:{refresherrefresh:function(e){arguments[0]=e=t.$handleEvent(e),t.onRefresh.apply(void 0,arguments)},scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadMore.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"problem-list"},[t.problemList.length>0?a("v-uni-view",t._l(t.problemList,(function(e,o){return a("v-uni-view",{key:o,staticClass:"problem-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goToDetail(e)}}},[a("v-uni-view",{staticClass:"problem-top"},[a("v-uni-view",{staticClass:"problem-icon"},[a("uni-icons",{attrs:{type:"help",color:"#8c8c8c",size:"48rpx"}})],1),a("v-uni-view",{staticClass:"problem-title"},[t._v(t._s(e.questionTitle))]),a("v-uni-view",{staticClass:"problem-arrow"},[a("uni-icons",{attrs:{type:"forward",color:"#8c8c8c",size:"32rpx"}})],1)],1),a("v-uni-view",{staticClass:"problem-bottom"},[a("v-uni-view",{staticClass:"problem-info"},[a("v-uni-text",{staticClass:"username"},[t._v(t._s(e.submitterName))]),a("v-uni-text",{staticClass:"company"},[t._v(t._s(e.mgtOrgName))])],1),a("v-uni-view",{staticClass:"problem-time"},[t._v(t._s(e.questionTime))])],1)],1)})),1):a("v-uni-view",{staticClass:"empty-container"},[a("u-empty",{attrs:{mode:"data",icon:"/static/icons/nodata.jpg"}})],1),t.problemList.length>0?a("v-uni-view",{staticClass:"loading-more"},[t.isLoading&&t.params.pageNum>1?a("v-uni-text",[t._v("加载中...")]):t.hasMoreData?t._e():a("v-uni-text",[t._v("没有更多数据了")])],1):t._e()],1)],1),a("v-uni-view",{staticClass:"bottom-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goToReport.apply(void 0,arguments)}}},[a("v-uni-text",[t._v("问题提报")])],1)],1)},i=[]},"57a9":function(t,e,a){"use strict";a.r(e);var o=a("3c98"),r=a("2a3a");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("60d7");var n=a("828b"),d=Object(n["a"])(r["default"],o["b"],o["c"],!1,null,"6fa087a0",null,!1,o["a"],void 0);e["default"]=d.exports},"60d7":function(t,e,a){"use strict";var o=a("990d"),r=a.n(o);r.a},"6b79":function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2");var r=o(a("c578")),i={name:"u-empty",mixins:[uni.$u.mpMixin,uni.$u.mixin,r.default],data:function(){return{icons:{car:"购物车为空",page:"页面不存在",search:"没有搜索结果",address:"没有收货地址",wifi:"没有WiFi",order:"订单为空",coupon:"没有优惠券",favor:"暂无收藏",permission:"无权限",history:"无历史记录",news:"无新闻列表",message:"消息列表为空",list:"列表为空",data:"数据为空",comment:"暂无评论"}}},computed:{emptyStyle:function(){var t={};return t.marginTop=uni.$u.addUnit(this.marginTop),uni.$u.deepMerge(uni.$u.addStyle(this.customStyle),t)},textStyle:function(){var t={};return t.color=this.textColor,t.fontSize=uni.$u.addUnit(this.textSize),t},isSrc:function(){return this.icon.indexOf("/")>=0}}};e.default=i},"72f8":function(t,e,a){var o=a("3c38");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("967d").default;r("2447bae2",o,!0,{sourceMap:!1,shadowMode:!1})},"7d91":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{ref:"u-subsection",staticClass:"u-subsection",class:["u-subsection--"+t.mode],style:[t.$u.addStyle(t.customStyle),t.wrapperStyle]},[a("v-uni-view",{ref:"u-subsection__bar",staticClass:"u-subsection__bar",class:["button"===t.mode&&"u-subsection--button__bar",0===t.current&&"subsection"===t.mode&&"u-subsection__bar--first",t.current>0&&t.current<t.list.length-1&&"subsection"===t.mode&&"u-subsection__bar--center",t.current===t.list.length-1&&"subsection"===t.mode&&"u-subsection__bar--last"],style:[t.barStyle]}),t._l(t.list,(function(e,o){return a("v-uni-view",{key:o,ref:"u-subsection__item--"+o,refInFor:!0,staticClass:"u-subsection__item",class:["u-subsection__item--"+o,o<t.list.length-1&&"u-subsection__item--no-border-right",0===o&&"u-subsection__item--first",o===t.list.length-1&&"u-subsection__item--last"],style:[t.itemStyle(o)],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler(o)}}},[a("v-uni-text",{staticClass:"u-subsection__item__text",style:[t.textStyle(o)]},[t._v(t._s(t.getText(e)))])],1)}))],2)},r=[]},"8bfc":function(t,e,a){"use strict";var o=a("1ec4"),r=a.n(o);r.a},"8fd6":function(t,e,a){"use strict";a.r(e);var o=a("7d91"),r=a("d7a9");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("f7a4");var n=a("828b"),d=Object(n["a"])(r["default"],o["b"],o["c"],!1,null,"4a603381",null,!1,o["a"],void 0);e["default"]=d.exports},"990d":function(t,e,a){var o=a("9cdc");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("967d").default;r("5cbbef44",o,!0,{sourceMap:!1,shadowMode:!1})},"9cdc":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-6fa087a0]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-6fa087a0]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-6fa087a0]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-6fa087a0]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-6fa087a0]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-6fa087a0]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-6fa087a0]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-6fa087a0]::after{border:none}.u-hover-class[data-v-6fa087a0]{opacity:.7}.u-primary-light[data-v-6fa087a0]{color:#ecf5ff}.u-warning-light[data-v-6fa087a0]{color:#fdf6ec}.u-success-light[data-v-6fa087a0]{color:#f5fff0}.u-error-light[data-v-6fa087a0]{color:#fef0f0}.u-info-light[data-v-6fa087a0]{color:#f4f4f5}.u-primary-light-bg[data-v-6fa087a0]{background-color:#ecf5ff}.u-warning-light-bg[data-v-6fa087a0]{background-color:#fdf6ec}.u-success-light-bg[data-v-6fa087a0]{background-color:#f5fff0}.u-error-light-bg[data-v-6fa087a0]{background-color:#fef0f0}.u-info-light-bg[data-v-6fa087a0]{background-color:#f4f4f5}.u-primary-dark[data-v-6fa087a0]{color:#398ade}.u-warning-dark[data-v-6fa087a0]{color:#f1a532}.u-success-dark[data-v-6fa087a0]{color:#53c21d}.u-error-dark[data-v-6fa087a0]{color:#e45656}.u-info-dark[data-v-6fa087a0]{color:#767a82}.u-primary-dark-bg[data-v-6fa087a0]{background-color:#398ade}.u-warning-dark-bg[data-v-6fa087a0]{background-color:#f1a532}.u-success-dark-bg[data-v-6fa087a0]{background-color:#53c21d}.u-error-dark-bg[data-v-6fa087a0]{background-color:#e45656}.u-info-dark-bg[data-v-6fa087a0]{background-color:#767a82}.u-primary-disabled[data-v-6fa087a0]{color:#9acafc}.u-warning-disabled[data-v-6fa087a0]{color:#f9d39b}.u-success-disabled[data-v-6fa087a0]{color:#a9e08f}.u-error-disabled[data-v-6fa087a0]{color:#f7b2b2}.u-info-disabled[data-v-6fa087a0]{color:#c4c6c9}.u-primary[data-v-6fa087a0]{color:#3c9cff}.u-warning[data-v-6fa087a0]{color:#f9ae3d}.u-success[data-v-6fa087a0]{color:#5ac725}.u-error[data-v-6fa087a0]{color:#f56c6c}.u-info[data-v-6fa087a0]{color:#909399}.u-primary-bg[data-v-6fa087a0]{background-color:#3c9cff}.u-warning-bg[data-v-6fa087a0]{background-color:#f9ae3d}.u-success-bg[data-v-6fa087a0]{background-color:#5ac725}.u-error-bg[data-v-6fa087a0]{background-color:#f56c6c}.u-info-bg[data-v-6fa087a0]{background-color:#909399}.u-main-color[data-v-6fa087a0]{color:#303133}.u-content-color[data-v-6fa087a0]{color:#606266}.u-tips-color[data-v-6fa087a0]{color:#909193}.u-light-color[data-v-6fa087a0]{color:#c0c4cc}.u-safe-area-inset-top[data-v-6fa087a0]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-6fa087a0]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-6fa087a0]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-6fa087a0]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-6fa087a0]{z-index:10090}uni-toast .uni-toast[data-v-6fa087a0]{z-index:10090}[data-v-6fa087a0]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-view[data-v-6fa087a0], uni-scroll-view[data-v-6fa087a0], uni-swiper-item[data-v-6fa087a0]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-empty[data-v-6fa087a0]{display:flex;flex-direction:row;flex-direction:column;justify-content:center;align-items:center}.u-empty__text[data-v-6fa087a0]{display:flex;flex-direction:row;justify-content:center;align-items:center;margin-top:%?20?%}.u-slot-wrap[data-v-6fa087a0]{display:flex;flex-direction:row;justify-content:center;align-items:center;margin-top:%?20?%}',""]),t.exports=e},c578:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var o={props:{icon:{type:String,default:uni.$u.props.empty.icon},text:{type:String,default:uni.$u.props.empty.text},textColor:{type:String,default:uni.$u.props.empty.textColor},textSize:{type:[String,Number],default:uni.$u.props.empty.textSize},iconColor:{type:String,default:uni.$u.props.empty.iconColor},iconSize:{type:[String,Number],default:uni.$u.props.empty.iconSize},mode:{type:String,default:uni.$u.props.empty.mode},width:{type:[String,Number],default:uni.$u.props.empty.width},height:{type:[String,Number],default:uni.$u.props.empty.height},show:{type:Boolean,default:uni.$u.props.empty.show},marginTop:{type:[String,Number],default:uni.$u.props.empty.marginTop}}};e.default=o},c7a2:function(t,e,a){"use strict";a.r(e);var o=a("5233"),r=a("ea94");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("8bfc");var n=a("828b"),d=Object(n["a"])(r["default"],o["b"],o["c"],!1,null,"58eaceb6",null,!1,o["a"],void 0);e["default"]=d.exports},d296:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var o={props:{list:{type:Array,default:uni.$u.props.subsection.list},current:{type:[String,Number],default:uni.$u.props.subsection.current},activeColor:{type:String,default:uni.$u.props.subsection.activeColor},inactiveColor:{type:String,default:uni.$u.props.subsection.inactiveColor},mode:{type:String,default:uni.$u.props.subsection.mode},fontSize:{type:[String,Number],default:uni.$u.props.subsection.fontSize},bold:{type:Boolean,default:uni.$u.props.subsection.bold},bgColor:{type:String,default:uni.$u.props.subsection.bgColor},keyName:{type:String,default:uni.$u.props.subsection.keyName}}};e.default=o},d7a9:function(t,e,a){"use strict";a.r(e);var o=a("013c"),r=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=r.a},ea94:function(t,e,a){"use strict";a.r(e);var o=a("2a50"),r=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=r.a},f7a4:function(t,e,a){"use strict";var o=a("72f8"),r=a.n(o);r.a}}]);