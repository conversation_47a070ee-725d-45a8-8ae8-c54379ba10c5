(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-antiStealingElec-query"],{2393:function(t,e,a){"use strict";a.r(e);var o=a("49e4"),r=a("4111");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("391f");var n=a("828b"),s=Object(n["a"])(r["default"],o["b"],o["c"],!1,null,"5f831e98",null,!1,o["a"],void 0);e["default"]=s.exports},"2d29":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-5f831e98]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-5f831e98]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-5f831e98]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-5f831e98]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-5f831e98]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-5f831e98]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-5f831e98]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-5f831e98]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-5f831e98]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-5f831e98]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-5f831e98]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-5f831e98]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-5f831e98]::after{border:none}.u-hover-class[data-v-5f831e98]{opacity:.7}.u-primary-light[data-v-5f831e98]{color:#ecf5ff}.u-warning-light[data-v-5f831e98]{color:#fdf6ec}.u-success-light[data-v-5f831e98]{color:#f5fff0}.u-error-light[data-v-5f831e98]{color:#fef0f0}.u-info-light[data-v-5f831e98]{color:#f4f4f5}.u-primary-light-bg[data-v-5f831e98]{background-color:#ecf5ff}.u-warning-light-bg[data-v-5f831e98]{background-color:#fdf6ec}.u-success-light-bg[data-v-5f831e98]{background-color:#f5fff0}.u-error-light-bg[data-v-5f831e98]{background-color:#fef0f0}.u-info-light-bg[data-v-5f831e98]{background-color:#f4f4f5}.u-primary-dark[data-v-5f831e98]{color:#398ade}.u-warning-dark[data-v-5f831e98]{color:#f1a532}.u-success-dark[data-v-5f831e98]{color:#53c21d}.u-error-dark[data-v-5f831e98]{color:#e45656}.u-info-dark[data-v-5f831e98]{color:#767a82}.u-primary-dark-bg[data-v-5f831e98]{background-color:#398ade}.u-warning-dark-bg[data-v-5f831e98]{background-color:#f1a532}.u-success-dark-bg[data-v-5f831e98]{background-color:#53c21d}.u-error-dark-bg[data-v-5f831e98]{background-color:#e45656}.u-info-dark-bg[data-v-5f831e98]{background-color:#767a82}.u-primary-disabled[data-v-5f831e98]{color:#9acafc}.u-warning-disabled[data-v-5f831e98]{color:#f9d39b}.u-success-disabled[data-v-5f831e98]{color:#a9e08f}.u-error-disabled[data-v-5f831e98]{color:#f7b2b2}.u-info-disabled[data-v-5f831e98]{color:#c4c6c9}.u-primary[data-v-5f831e98]{color:#3c9cff}.u-warning[data-v-5f831e98]{color:#f9ae3d}.u-success[data-v-5f831e98]{color:#5ac725}.u-error[data-v-5f831e98]{color:#f56c6c}.u-info[data-v-5f831e98]{color:#909399}.u-primary-bg[data-v-5f831e98]{background-color:#3c9cff}.u-warning-bg[data-v-5f831e98]{background-color:#f9ae3d}.u-success-bg[data-v-5f831e98]{background-color:#5ac725}.u-error-bg[data-v-5f831e98]{background-color:#f56c6c}.u-info-bg[data-v-5f831e98]{background-color:#909399}.u-main-color[data-v-5f831e98]{color:#303133}.u-content-color[data-v-5f831e98]{color:#606266}.u-tips-color[data-v-5f831e98]{color:#909193}.u-light-color[data-v-5f831e98]{color:#c0c4cc}.u-safe-area-inset-top[data-v-5f831e98]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-5f831e98]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-5f831e98]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-5f831e98]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-5f831e98]{z-index:10090}uni-toast .uni-toast[data-v-5f831e98]{z-index:10090}[data-v-5f831e98]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-page-body[data-v-5f831e98]{background-color:#fff;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}body.?%PAGE?%[data-v-5f831e98]{background-color:#fff}.user-data-query[data-v-5f831e98]{min-height:100vh;display:flex;flex-direction:column;background-color:#fff;position:relative}\r\n/* 顶部导航区域 */.header-section[data-v-5f831e98]{background-color:#00c389;position:fixed;top:0;left:0;right:0;z-index:101}\r\n/* 中间表单区域 */.form-section[data-v-5f831e98]{position:fixed;top:0;left:0;right:0;bottom:%?180?%;\r\n  /* 底部按钮区域高度 */box-sizing:border-box;background-color:#fff;z-index:99}.form-content[data-v-5f831e98]{padding:%?30?% %?40?%;background-color:#fff}.form-item[data-v-5f831e98]{margin-bottom:%?30?%}.form-label[data-v-5f831e98]{font-size:%?32?%;color:#262626;margin-bottom:%?16?%;font-weight:400}.form-input[data-v-5f831e98]{height:%?80?%}.select-wrapper[data-v-5f831e98]{width:100%;position:relative;cursor:pointer}\r\n/* 单选按钮组 */.radio-group[data-v-5f831e98]{display:flex;flex-direction:row;margin-top:%?20?%}.radio-item[data-v-5f831e98]{display:flex;align-items:center;margin-right:%?60?%}.radio-circle[data-v-5f831e98]{width:%?36?%;height:%?36?%;border-radius:50%;border:%?1?% solid #ddd;display:flex;align-items:center;justify-content:center;margin-right:%?16?%}.radio-circle.active[data-v-5f831e98]{border-color:#00c389}.radio-inner[data-v-5f831e98]{width:%?18?%;height:%?18?%;border-radius:50%;background-color:#00c389}.radio-label[data-v-5f831e98]{font-size:%?28?%;color:#333}\r\n/* 底部按钮区域 */.footer-section[data-v-5f831e98]{position:fixed;bottom:0;left:0;right:0;background-color:#fff;z-index:50;border-top:%?1?% solid #f5f5f5;padding-bottom:env(safe-area-inset-bottom)}\r\n/* 按钮组样式 */.btn-group[data-v-5f831e98]{display:flex;padding:%?20?% %?40?%}.btn[data-v-5f831e98]{flex:1;height:%?80?%;display:flex;align-items:center;justify-content:center;border-radius:%?50?%;font-size:%?32?%;font-weight:400;transition:all .2s}.btn-reset[data-v-5f831e98]{background-color:initial;color:#00c389;border:none;margin-right:%?40?%}.btn-confirm[data-v-5f831e98]{background-color:#00c389;color:#fff}\r\n/* 为小屏幕预留滚动空间 */.form-space[data-v-5f831e98]{height:%?40?%}\r\n/* 修改 u-input 样式 */[data-v-5f831e98] .u-input{height:%?80?%}[data-v-5f831e98] .u-input__input{height:%?92?%;font-size:%?30?%;color:#333;padding:0 %?24?%}[data-v-5f831e98] .u-input--border{border-color:#e0e0e0;border-width:%?1?%;border-radius:%?16?%;height:%?88?%}[data-v-5f831e98] .u-input__placeholder-style{color:#c8c9cc}[data-v-5f831e98] .u-icon{color:#999}[data-v-5f831e98] .u-input__content__field{display:flex;align-items:center}[data-v-5f831e98] .navbar-left .svg-icon{color:#fff!important}[data-v-5f831e98] .uni-datetime-picker--btn{background-color:#00c389}[data-v-5f831e98] .uni-calendar-item--multiple .uni-calendar-item--before-checked,[data-v-5f831e98] .uni-calendar-item--multiple .uni-calendar-item--after-checked,[data-v-5f831e98] .uni-calendar-item__weeks-box .uni-calendar-item--checked{background-color:#00c389}',""]),t.exports=e},"391f":function(t,e,a){"use strict";var o=a("f03a"),r=a.n(o);r.a},4111:function(t,e,a){"use strict";a.r(e);var o=a("fedb"),r=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=r.a},"49e4":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return o}));var o={uInput:a("30b1").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"user-data-query"},[a("v-uni-view",{staticClass:"header-section"},[a("custom-navbar",{attrs:{title:"查询",path:t.path}})],1),a("v-uni-scroll-view",{staticClass:"form-section",style:{height:t.scrollViewHeight+"px",top:t.safeAreaTop+"px"},attrs:{"scroll-y":!0}},[a("v-uni-view",{staticClass:"form-content"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("供电单位")]),a("v-uni-view",{staticClass:"select-wrapper",on:{touchend:function(e){e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.showOrgPicker.apply(void 0,arguments)}}},[a("u-input",{staticClass:"form-input",attrs:{placeholder:"请选择",border:"surround",suffixIcon:"arrow-down",readonly:!0,fontSize:"30rpx",customStyle:t.inputStyle},model:{value:t.formData.mgtOrgName,callback:function(e){t.$set(t.formData,"mgtOrgName",e)},expression:"formData.mgtOrgName"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("日期")]),a("v-uni-view",{staticClass:"date-input-wrapper"},[a("uni-datetime-picker",{attrs:{type:"daterange",start:t.minDate,end:t.maxDate,rangeSeparator:"至",border:!0,"clear-icon":!0,placeholder:"请选择日期"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmDate.apply(void 0,arguments)},maskClick:function(e){arguments[0]=e=t.$handleEvent(e),t.calendarShow=!1}},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("台区编号")]),a("u-input",{staticClass:"form-input",attrs:{placeholder:"请输入台区编号",border:"surround",clearable:!0,fontSize:"30rpx",customStyle:t.inputStyle},model:{value:t.formData.resrcSuplCode,callback:function(e){t.$set(t.formData,"resrcSuplCode",e)},expression:"formData.resrcSuplCode"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("台区名称")]),a("u-input",{staticClass:"form-input",attrs:{placeholder:"请输入台区名称",border:"surround",clearable:!0,fontSize:"30rpx",customStyle:t.inputStyle},model:{value:t.formData.resrcSuplName,callback:function(e){t.$set(t.formData,"resrcSuplName",e)},expression:"formData.resrcSuplName"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("用户编号")]),a("u-input",{staticClass:"form-input",attrs:{placeholder:"请输入用户编号",border:"surround",clearable:!0,fontSize:"30rpx",customStyle:t.inputStyle},model:{value:t.formData.custNo,callback:function(e){t.$set(t.formData,"custNo",e)},expression:"formData.custNo"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("用户名称")]),a("u-input",{staticClass:"form-input",attrs:{placeholder:"请输入用户名称",border:"surround",clearable:!0,fontSize:"30rpx",customStyle:t.inputStyle},model:{value:t.formData.custName,callback:function(e){t.$set(t.formData,"custName",e)},expression:"formData.custName"}})],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("隐患类别")]),a("v-uni-view",{staticClass:"select-wrapper",on:{touchend:function(e){e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.showDangerTypePicker.apply(void 0,arguments)}}},[a("u-input",{staticClass:"form-input",attrs:{placeholder:"请选择",border:"surround",suffixIcon:"arrow-down",readonly:!0,fontSize:"30rpx",customStyle:t.inputStyle},model:{value:t.dangerTypeName,callback:function(e){t.dangerTypeName=e},expression:"dangerTypeName"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[t._v("改造状态")]),a("v-uni-view",{staticClass:"select-wrapper",on:{touchend:function(e){e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.showStatusPicker.apply(void 0,arguments)}}},[a("u-input",{staticClass:"form-input",attrs:{placeholder:"请选择",border:"surround",suffixIcon:"arrow-down",readonly:!0,fontSize:"30rpx",customStyle:t.inputStyle},model:{value:t.retrofitStatusName,callback:function(e){t.retrofitStatusName=e},expression:"retrofitStatusName"}})],1)],1),a("v-uni-view",{staticClass:"form-space"})],1)],1),a("v-uni-view",{staticClass:"footer-section"},[a("v-uni-view",{staticClass:"btn-group"},[a("v-uni-view",{staticClass:"btn btn-reset",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.resetForm.apply(void 0,arguments)}}},[a("v-uni-text",[t._v("重置选择")])],1),a("v-uni-view",{staticClass:"btn btn-confirm",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submitForm.apply(void 0,arguments)}}},[a("v-uni-text",[t._v("确定")])],1)],1)],1)],1)},i=[]},f03a:function(t,e,a){var o=a("2d29");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("967d").default;r("52cc1e22",o,!0,{sourceMap:!1,shadowMode:!1})},fedb:function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d4b5"),a("795c"),a("c223"),a("fd3c");var r=o(a("9b1b")),i=a("b3d7"),n={data:function(){return{statusBarHeight:0,navbarHeight:44,headerHeight:0,footerHeight:140,windowHeight:0,scrollViewHeight:0,path:window.location.hash,safeAreaTop:0,maxDate:"",minDate:"",dateRangeText:"",formData:{mgtOrgName:"上海市电力公司",mgtOrgCode:"31102",resrcSuplCode:"",resrcSuplName:"",custNo:"",custName:"",dangerType:"01",retrofitStatus:"",orderType:"",timeout:"",retrofitStartTime:"",retrofitEndTime:""},dateRange:[],dangerTypeName:"",retrofitStatusName:"",orderTypeName:"",orgOptions:[{text:"浦东供电公司",value:"01"},{text:"市北供电公司",value:"02"},{text:"市南供电公司",value:"03"}],dangerTypeOptions:[{text:"计量装置",value:"01"},{text:"高低压配电线路及设备",value:"02"}],statusOptions:[{text:"待填报",value:"01"},{text:"改造中",value:"02"},{text:"已完成",value:"03"}],workTypeOptions:[{text:"窃电",value:"01"},{text:"违约用电",value:"02"},{text:"无协议用电",value:"03"},{text:"窃电及违约用电",value:"04"},{text:"无违约用电",value:"05"},{text:"其他",value:"99"}],showOrgSelector:!1,showDangerTypeSelector:!1,showStatusSelector:!1,showWorkTypeSelector:!1,inputStyle:{height:"80rpx"},fromPage:"",username:"",isMockData:!0,nameCode:"",token:null}},onLoad:function(t){this.getStatusBarHeight();try{var e=uni.getStorageSync("antiStealingElec_queryParams");e&&(console.log("加载已保存的查询参数:",e),this.formData=(0,r.default)({},e))}catch(a){console.error("获取查询参数失败:",a)}this.fromPage=t.fromPage||"",this.setupCityEvent(),this.isMockData||this.init()},onReady:function(){this.calcScrollViewHeight()},onUnload:function(){uni.$off("citySelected",this.handleCitySelected)},methods:{init2:function(){var t=this;wx.invoke("ext_DataCache_GetInfo",{data:{key:"1002838.userInfo"}},(function(e){var a=JSON.parse(e.result);t.token=null===a||void 0===a?void 0:a.token,t.nameCode=null===a||void 0===a?void 0:a.nameCode}))},init:function(){var t=this;try{uni.request({url:"http://127.0.0.1:".concat(i.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:this.token},data:{token:this.token,method:"PutHuaYun",uri:i.url,data:JSON.stringify({bizCode:i.bizCode,espFlowId:(0,i.getUUID)(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:(0,i.getCurrentTimestamp)(),espInformation:{service:"AseCommonController",method:"queryUserInfoByBody",data:{operatorId:this.nameCode}}})},success:function(e){if(e&&1===e.data.Tag){var a=e.data.Data.espInformation;a&&200==a.code?(t.formData.mgtOrgCode=a.data.mgtOrgCode,t.formData.mgtOrgName=a.data.mgtOrgName):uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3})},fail:function(t){uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}})}catch(e){uni.showToast({title:"网络错误，请稍后再试",icon:"none"})}},getStatusBarHeight:function(){var t=uni.getSystemInfoSync();this.statusBarHeight=t.statusBarHeight||0,this.windowHeight=t.windowHeight||0,this.headerHeight=this.statusBarHeight+this.navbarHeight,this.safeAreaTop=this.headerHeight},calcScrollViewHeight:function(){var t=uni.upx2px(this.footerHeight);this.scrollViewHeight=this.windowHeight-this.headerHeight-t,console.log("计算高度:",{windowHeight:this.windowHeight,headerHeight:this.headerHeight,footerHeightPx:t,scrollViewHeight:this.scrollViewHeight})},goBack:function(){uni.navigateBack({delta:1})},confirmDate:function(t){this.updateDateRangeText(t)},updateDateRangeText:function(t){if(t&&t.length>=2){var e=function(t){if(!t)return"";var e=new Date(t),a=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(r)},a=e(t[0]),o=e(t[1]);this.dateRangeText="".concat(a," 至 ").concat(o)}else this.dateRangeText=""},showOrgPicker:function(){uni.navigateTo({url:"/pages/antiStealingElec/city"})},setupCityEvent:function(){uni.$on("citySelected",this.handleCitySelected)},handleCitySelected:function(t){var e=this;console.log(t),setTimeout((function(){e.formData.mgtOrgName=t.district.shortName,e.formData.mgtOrgCode=t.district.mgtOrgCode}),500)},showDangerTypePicker:function(){var t=this;uni.showActionSheet({itemList:this.dangerTypeOptions.map((function(t){return t.text})),success:function(e){var a=e.tapIndex;t.formData.dangerType=t.dangerTypeOptions[a].value,t.dangerTypeName=t.dangerTypeOptions[a].text}})},showStatusPicker:function(){var t=this;uni.showActionSheet({itemList:this.statusOptions.map((function(t){return t.text})),success:function(e){var a=e.tapIndex;t.formData.retrofitStatus=t.statusOptions[a].value,t.retrofitStatusName=t.statusOptions[a].text}})},showWorkTypePicker:function(){var t=this;uni.showActionSheet({itemList:this.workTypeOptions.map((function(t){return t.text})),success:function(e){var a=e.tapIndex;t.formData.orderType=t.workTypeOptions[a].value,t.orderTypeName=t.workTypeOptions[a].text}})},selectTimeoutOption:function(t){this.formData.timeout=t},resetForm:function(){this.formData={mgtOrgName:"上海市电力公司",mgtOrgCode:"31102",resrcSuplCode:"",resrcSuplName:"",custNo:"",custName:"",dangerType:"",status:"",workType:"",timeout:"",retrofitStartTime:"",retrofitEndTime:""},this.dateRange=[],this.dateRangeText=""},submitForm:function(){var t=this;!this.dateRange||this.dateRange.length<2?uni.showToast({title:"请选择日期",icon:"none"}):this.formData.mgtOrgCode?(this.formData.retrofitStartTime=this.dateRange[0],this.formData.retrofitEndTime=this.dateRange[1],uni.showLoading({title:"查询中..."}),uni.setStorageSync("antiStealingElec_queryParams",this.formData),setTimeout((function(){uni.hideLoading();var e=uni.getStorageSync("antiStealingElec_fromPage")||"",a=e||t.fromPage;"list"===a?uni.navigateBack({delta:1,success:function(){var t=getCurrentPages(),e=t[t.length-1];e&&e.$vm&&e.$vm.onRefresh&&e.$vm.onRefresh()}}):uni.navigateTo({url:"/pages/antiStealingElec/statistics"}),uni.removeStorageSync("antiStealingElec_fromPage")}),1500)):uni.showToast({title:"请选择管理单位",icon:"none"})}}};e.default=n}}]);