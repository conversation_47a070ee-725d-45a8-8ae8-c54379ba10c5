(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-fileQuery-jobMonitor-orderFullDetail"],{"07dc":function(t,a,i){var e=i("4ca9");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var o=i("967d").default;o("db2c6f06",e,!0,{sourceMap:!1,shadowMode:!1})},3751:function(t,a,i){"use strict";i.r(a);var e=i("5909"),o=i("96d9c");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(a,t,(function(){return o[t]}))}(r);i("ca04");var d=i("828b"),n=Object(d["a"])(o["default"],e["b"],e["c"],!1,null,"a730dc24",null,!1,e["a"],void 0);a["default"]=n.exports},"4ca9":function(t,a,i){var e=i("c86c");a=e(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-a730dc24]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-a730dc24]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-a730dc24]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-a730dc24]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-a730dc24]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-a730dc24]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-a730dc24]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-a730dc24]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-a730dc24]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-a730dc24]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-a730dc24]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-a730dc24]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-a730dc24]::after{border:none}.u-hover-class[data-v-a730dc24]{opacity:.7}.u-primary-light[data-v-a730dc24]{color:#ecf5ff}.u-warning-light[data-v-a730dc24]{color:#fdf6ec}.u-success-light[data-v-a730dc24]{color:#f5fff0}.u-error-light[data-v-a730dc24]{color:#fef0f0}.u-info-light[data-v-a730dc24]{color:#f4f4f5}.u-primary-light-bg[data-v-a730dc24]{background-color:#ecf5ff}.u-warning-light-bg[data-v-a730dc24]{background-color:#fdf6ec}.u-success-light-bg[data-v-a730dc24]{background-color:#f5fff0}.u-error-light-bg[data-v-a730dc24]{background-color:#fef0f0}.u-info-light-bg[data-v-a730dc24]{background-color:#f4f4f5}.u-primary-dark[data-v-a730dc24]{color:#398ade}.u-warning-dark[data-v-a730dc24]{color:#f1a532}.u-success-dark[data-v-a730dc24]{color:#53c21d}.u-error-dark[data-v-a730dc24]{color:#e45656}.u-info-dark[data-v-a730dc24]{color:#767a82}.u-primary-dark-bg[data-v-a730dc24]{background-color:#398ade}.u-warning-dark-bg[data-v-a730dc24]{background-color:#f1a532}.u-success-dark-bg[data-v-a730dc24]{background-color:#53c21d}.u-error-dark-bg[data-v-a730dc24]{background-color:#e45656}.u-info-dark-bg[data-v-a730dc24]{background-color:#767a82}.u-primary-disabled[data-v-a730dc24]{color:#9acafc}.u-warning-disabled[data-v-a730dc24]{color:#f9d39b}.u-success-disabled[data-v-a730dc24]{color:#a9e08f}.u-error-disabled[data-v-a730dc24]{color:#f7b2b2}.u-info-disabled[data-v-a730dc24]{color:#c4c6c9}.u-primary[data-v-a730dc24]{color:#3c9cff}.u-warning[data-v-a730dc24]{color:#f9ae3d}.u-success[data-v-a730dc24]{color:#5ac725}.u-error[data-v-a730dc24]{color:#f56c6c}.u-info[data-v-a730dc24]{color:#909399}.u-primary-bg[data-v-a730dc24]{background-color:#3c9cff}.u-warning-bg[data-v-a730dc24]{background-color:#f9ae3d}.u-success-bg[data-v-a730dc24]{background-color:#5ac725}.u-error-bg[data-v-a730dc24]{background-color:#f56c6c}.u-info-bg[data-v-a730dc24]{background-color:#909399}.u-main-color[data-v-a730dc24]{color:#303133}.u-content-color[data-v-a730dc24]{color:#606266}.u-tips-color[data-v-a730dc24]{color:#909193}.u-light-color[data-v-a730dc24]{color:#c0c4cc}.u-safe-area-inset-top[data-v-a730dc24]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-a730dc24]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-a730dc24]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-a730dc24]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-a730dc24]{z-index:10090}uni-toast .uni-toast[data-v-a730dc24]{z-index:10090}[data-v-a730dc24]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */*[data-v-a730dc24]{margin:0;padding:0}.custom-select[data-v-a730dc24]{width:100%;position:relative}.select-box[data-v-a730dc24]{border:%?1?% solid #ddd;width:100%;padding:%?20?%;border-radius:%?8?%}.arrow[data-v-a730dc24]{float:right;border:solid #999;border-width:0 %?2?% %?2?% 0;padding:%?6?%;margin-top:%?6?%}.down[data-v-a730dc24]{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.up[data-v-a730dc24]{-webkit-transform:rotate(-135deg);transform:rotate(-135deg)}.dropdown[data-v-a730dc24]{position:absolute;width:100%;border:%?1?% solid #eee;background:#fff;z-index:999}.dropdown-item[data-v-a730dc24]{padding:%?20?%;border-bottom:%?1?% solid #f5f5f5}.mainContainer .popupcontainer[data-v-a730dc24]{height:%?1500?%;padding:0 %?40?%}.mainContainer .popupcontainer .poptitle[data-v-a730dc24]{text-align:center;font-size:%?36?%;font-weight:700;padding:%?50?% 0}.mainContainer .popupcontainer .formitem .calendarContainer[data-v-a730dc24]{width:100%}.mainContainer .popupcontainer .formitem .calendarContainer .calInput[data-v-a730dc24]{width:100%}.mainContainer .popupcontainer .ubutton[data-v-a730dc24]{display:flex;justify-content:space-between;align-items:center;padding:%?400?% 0 0}.mainContainer .listcontainer[data-v-a730dc24]{background-color:#fff;border-radius:%?20?%;margin:%?20?%}.mainContainer .listcontainer .datatext[data-v-a730dc24]{padding:%?10?% 0 %?10?% %?0?%}.mainContainer .listcontainer .datatext .datacontainer[data-v-a730dc24]{display:flex;align-items:center;padding:%?30?% 0 %?30?% %?30?%}.mainContainer .listcontainer .datatext .datacontainer .datatitle[data-v-a730dc24]{width:%?300?%;font-weight:700;font-size:%?30?%;color:grey}.mainContainer .listcontainer .datatext .datacontainer .textstyle[data-v-a730dc24]{font-weight:700}',""]),t.exports=a},5909:function(t,a,i){"use strict";i.d(a,"b",(function(){return e})),i.d(a,"c",(function(){return o})),i.d(a,"a",(function(){}));var e=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("v-uni-view",{staticClass:"mainContainer"},[i("threenav",{attrs:{title:"详情"}}),i("v-uni-view",{staticClass:"listcontainer"},[i("v-uni-view",{staticClass:"datatext"},[i("v-uni-view",{staticClass:"datacontainer"},[i("v-uni-view",{staticClass:"datatitle"},[t._v("用户编号：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx",color:"red"}},[t._v(t._s(t.listData.custNo))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("工单编号：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.appNo))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("供电公司：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.mgtOrgName))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("派工人：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.orderCreator))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("用户名称：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.custName))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("用户类型：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.consType))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("用电地址：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{color:"red","font-size":"30rpx"}},[t._v(t._s(t.listData.ecAddr))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("工单来源：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.orderSrc))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("工单类型：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.workOrderType))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("工单状态：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.orderStatus))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("检查结果：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.checkRslt))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("环节状态时间：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.stepStateTime))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("是否超时：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.overdue))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("异常问题：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(t.listData.abnorDesc))])],1),i("v-uni-view",{staticClass:"datacontainer",staticStyle:{"border-top":"1px solid lightgray"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoliucheng(t.listData)}}},[i("v-uni-view",{staticClass:"datatitle"},[t._v("流程环节：")]),i("v-uni-text",{staticClass:"textstyle",staticStyle:{"font-size":"30rpx",color:"blue"}},[t._v("查看详情")])],1)],1)],1)],1)},o=[]},"7be4":function(t,a,i){"use strict";i("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,i("d4b5");var e={data:function(){return{listData:{}}},onLoad:function(t){t.data&&(this.listData=JSON.parse(decodeURIComponent(t.data)))},onReady:function(){},methods:{gotoliucheng:function(t){uni.navigateTo({url:"/pages/fileQuery/jobMonitor/liuchenghuanjie?data="+encodeURIComponent(JSON.stringify(t))})}}};a.default=e},"96d9c":function(t,a,i){"use strict";i.r(a);var e=i("7be4"),o=i.n(e);for(var r in e)["default"].indexOf(r)<0&&function(t){i.d(a,t,(function(){return e[t]}))}(r);a["default"]=o.a},ca04:function(t,a,i){"use strict";var e=i("07dc"),o=i.n(e);o.a}}]);