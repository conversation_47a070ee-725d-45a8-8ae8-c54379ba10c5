import http from './http-util'

/**
 * 获取用户信息
 */
const getISCUserInfo = {
    code: 'user',
    type: 'webservice'
}

/**
 * 企信信息
 */
const AppInfo = (process.env.NODE_ENV !== 'production' && {
    agentid: '1001027', // 应用ID
    corpid: 'ww445f8033443a14aa',
    isc_appid: '09e917586b0e4c1e9d0500233220fc58-1001027',
    iscpIP: '***************', // 安全交互平台地址
    iscpPort: '20082',
    user: 'user', // 当前登录用户id
    baseUrlIp: '*************', // app后端服务f5地址
    baseUrlPort: '1080', // app后端服务f5地址对应端口号
    appId: 'shandong', // 需要向安全交互平台申请
    result: '' // 返回的端口号
}) || {
    agentid: '1000645', // 应用ID
    corpid: 'ww4d11a39991ebffdc',
    isc_appid: (new Date().getTime()) + '1000645',
    iscpIP: '************', // 安全交互平台地址
    iscpPort: '18080',
    user: 'user', // 当前登录用户id
    baseUrlIp: '*************', // app后端服务f5地址
    baseUrlPort: '18080', // app后端服务f5地址对应端口号
    appId: 'GSZSYXAPP', // 需要向安全交互平台申请
    result: '' // 返回的端口号
} // 生产环境的信息

const getWxCode = () => {
    let sURL = process.env.VUE_APP_OAUTH_URL
    sURL += `appid=${AppInfo.corpid}`
    sURL += `&redirect_uri=${encodeURIComponent(`${process.env.VUE_APP_REDIRECT_URI}`)}`
    sURL += '&response_type=code'
    sURL += '&scope=SCOPE'
    sURL += '&state=STATE'
    sURL += `&agentid=${AppInfo.agentid}`
    sURL += '#wechat_redirect'
    window.location.replace(sURL)
}

const getParam = (variable) => {
    const query = window.location.search.substring(1)
    const vars = query.split('&')
    for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        if (pair[0] === variable) {
            return pair[1]
        }
    }
    return ''
}

/** 获取安全交互平台返回信息 */
export const iscpConnet = (retryCount = process.env.VUE_APP_RETRY_COUNT) => {
    console.log(`-----进入get_ext_ISCP 11111`, process.env.VUE_APP_RETRY_COUNT)
    return Vegeta.iscpInit({
            iscpIP: AppInfo.isc_appid
        })
        .then((res) => {
            console.log('ext_ISCP_Init 初始化', res)
            return Vegeta.iscpStatus({
                    iscpIP: AppInfo.isc_appid
                }).then((res3) => {
                    console.log('连接状态：', res3)
                    if (res3.result === '2' && localStorage.getItem('dynamicPort')) {
                        console.log(`------已连接的端口：${localStorage.getItem('dynamicPort')}`)
                        return localStorage.getItem('dynamicPort')
                    }
                    return Vegeta
                        .iscpConnectService({
                            ip: AppInfo.iscpIP,
                            port: AppInfo.iscpPort,
                            user: AppInfo.user,
                            appid: AppInfo.appId,
                            iscpIP: AppInfo.isc_appid
                        })
                        .then((res1) => {
                            console.log('ext_ISCP_ConnectService 建立安全交互平台链接', res1)
                            return Vegeta
                                .iscpGetLocalPort({
                                    ip: AppInfo.baseUrlIp,
                                    port: AppInfo.baseUrlPort,
                                    iscpIP: AppInfo.isc_appid
                                })
                                .then((res2) => {
                                    console.log('ext_ISCP_GetLocalPort 转换端口', res2)
                                    console.log(`------端口：${res2.result}`)
                                    if (res2.result == '-1') {
                                        throw '获取动态端口失败，请联系管理员配置安全策略'
                                    }
                                    localStorage.setItem('dynamicPort', res2.result)
                                    return res2.result
                                })
                                .catch((err) => {
                                    console.error(err)
                                    if (retryCount == 0) return err
                                    iscpConnet(retryCount - 1)
                                })
                        })
                        .catch((err) => {
                            console.error(err)
                            if (retryCount == 0) return err
                            iscpConnet(retryCount - 1)
                        })
                })
                .catch((err) => {
                    console.error(err)
                    if (retryCount == 0) return err
                    iscpConnet(retryCount - 1)
                })
        })
        .catch((err) => {
            console.error(err)
            if (retryCount == 0) return err
            iscpConnet(retryCount - 1)
        })
}

export const getUserInfo = () => {
    if (!process.env.VUE_APP_IS_LOCALHOST) {
        if (sessionStorage.getItem('wx_code') && sessionStorage.getItem('getUserInfo')) {
            const userInfo =
                (sessionStorage.getItem('getUserInfo') &&
                    JSON.parse(sessionStorage.getItem('getUserInfo'))) || {}
            return Promise.resolve({
                ...userInfo,
                userNo: userInfo.nameCode
            })
        }
        return http
            .request(process.env.VUE_APP_TICKET_URL, {
                appId: AppInfo.agentid,
                CODE: wx_code
            })
            .then((res) => {
                console.log('获取ticket成功')
                console.log(res)
                return iscpConnet().then(async (res1) => {
                    console.log('获取动态端口成功：', res1)
                    const url = `http://127.0.0.1:${res1}${process.env.VUE_APP_URL}`
                    const token = process.env.NODE_ENV == 'production' ? res.ticket : '11223344'
                    const userInfo = await http.request(url, {
                        ticket: res.ticket,
                        resource: 'getIscUserInfo',
                        token
                        // token: '11223344'
                    })
                    if (userInfo) {
                        // 由于getIscUserInfo没有返回orgNo(存疑，其实上面的getIscUserInfo里可以拿到orgNo)，所以需要网省单独写个业务接口去获取orgNo
                        // 不通用,各个网省需要自行修改为各自代码
                        const user = await http.request(url, {
                            operationType: getISCUserInfo.code,
                            appName: process.env.VUE_APP_APPNAME.replace(/\//g, '$'),
                            authToken: userInfo.authToken,
                            resource: process.env.VUE_APP_RESOURCE,
                            data: {
                                dataXmlStr: [{
                                    userId: userInfo.userId,
                                    nameCode: userInfo.nameCode
                                }]
                            }
                        })

                        if (user) Object.assign(userInfo, ...user)
                        userInfo.orgNo = (user && user.length > 0) ? user[0].orgNo : ''
                        userInfo.userNo = userInfo.userId

                        sessionStorage.setItem('getUserInfo', JSON.stringify(userInfo))
                        sessionStorage.setItem('authToken', userInfo.authToken)
                    }
                    return {
                        ...userInfo,
                        userNo: userInfo.nameCode
                    }
                })
            })
    }
    return Promise.resolve({
        authToken: '',
        deptId: null,
        deptNo: '0016076817',
        deptName: '综合室',
        name: '许川崎',
        orgName: '黄岛区供电公司',
        orgNo: '3740233',
        userId: '8310100030A',
        userName: '许川崎',
        nameCode: 'IGWSGCC273',
        userNo: '8310100030A'
    })
}

const wx_code = getParam('code')

window.onload = function () {
    console.log(`-------wx_code = ${wx_code}`)
    if (!wx_code) {
        if (!process.env.VUE_APP_IS_LOCALHOST) return getWxCode()
    }
    sessionStorage.setItem('wx_code', wx_code)
};
