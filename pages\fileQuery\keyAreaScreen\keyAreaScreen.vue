<template>
	<view class="mainContainer">
		<second-navbar title="重点台区查询结果" @search-click="handleNavbarSearch"></second-navbar>

		<!-- 搜索弹框 -->
		<u-popup :show="showpopup" :round="10" mode="bottom" @close="close" @open="open"
			:closeOnClickOverlay="closeOnClick">
			<view class="popupcontainer">
				<view class="poptitle">
					查询
				</view>
				<u--form :labelStyle="{ fontWeight: 'bold' }" labelPosition="left" :model="uForm" ref="uForm">
					<u-form-item class="formitem" labelWidth="120" label="线损日期" prop="" borderBottom ref="item1">
						<view class="calendarContainer">
							<uni-datetime-picker v-model="uForm.queryDate" type="date" placeholder="请选择日期"
								@change="handleEndDate">
							</uni-datetime-picker>
						</view>
					</u-form-item>
					<!-- 线损率范围 -->
					<u-form-item class="formitem" labelWidth="120" label="线损率范围" prop="" borderBottom ref="item1">
						<u--input style="margin-right: 6rpx;" placeholder="最小值" border="surround"
							v-model="uForm.llRateMin"></u--input>
						<text> ~ </text>
						<u--input style="margin-left: 6rpx;" placeholder="最大值" border="surround"
							v-model="uForm.llRateMax"></u--input>
					</u-form-item>
					<!-- 供电量范围 -->
					<u-form-item class="formitem" labelWidth="120" label="供电量范围" prop="" borderBottom ref="item2">
						<u--input style="margin-right: 6rpx;" placeholder="最小值" border="surround"
							v-model="uForm.ppqMin"></u--input>
						<text> ~ </text>
						<u--input style="margin-left: 6rpx;" placeholder="最大值" border="surround"
							v-model="uForm.ppqMax"></u--input>
					</u-form-item>
					<!-- 线损电量范围 -->
					<u-form-item class="formitem" labelWidth="120" label="线损电量范围" prop="" borderBottom ref="item3">
						<u--input style="margin-right: 6rpx;" placeholder="最小值" border="surround"
							v-model="uForm.lossPQMin"></u--input>
						<text> ~ </text>
						<u--input style="margin-left: 6rpx;" placeholder="最大值" border="surround"
							v-model="uForm.lossPQMax"></u--input>
					</u-form-item>
					<!-- 窃电线索数范围 -->
					<u-form-item class="formitem" labelWidth="120" label="窃电线索数范围" prop="" borderBottom ref="item4">
						<u--input style="margin-right: 6rpx;" placeholder="最小值" border="surround"
							v-model="uForm.clueCountMin"></u--input>
						<text> ~ </text>
						<u--input style="margin-left: 6rpx;" placeholder="最大值" border="surround"
							v-model="uForm.clueCountMax"></u--input>
					</u-form-item>
					<!-- 窃电查实数范围 -->
					<u-form-item class="formitem" labelWidth="120" label="窃电查实数范围" prop="" borderBottom ref="item5">
						<u--input style="margin-right: 6rpx;" placeholder="最小值" border="surround"
							v-model="uForm.realCountMin"></u--input>
						<text> ~ </text>
						<u--input style="margin-left: 6rpx;" placeholder="最大值" border="surround"
							v-model="uForm.realCountMax"></u--input>
					</u-form-item>
					<u-form-item class="formitem" labelWidth="120" label="供电单位" prop="" borderBottom
						style="position: relative;padding-top: 20rpx;" ref="item1">
						<DaTreeVue2 class="DaTreestyle" :style="judge?'background-color:white':''" ref="DaTreeRef"
							:data="treeData" labelField="name" valueField="mgtOrgCode" expandChecked
							:defaultCheckedKeys="defaultCheckedKeysValue" @change="handleTreeChange"
							@expand="handleExpandChange"></DaTreeVue2>
					</u-form-item>
					<view class="ubutton">
						<u-button type="default" style="border: 1px solid lightgray;width: 300rpx;" text="重置"
							@click="reset"></u-button>
						<u-button type="primary" style="color: #fcfefd;width: 300rpx;" color="#07ac7c" text="查询"
							@click="search"></u-button>
					</view>
				</u--form>
			</view>
		</u-popup>


		<!-- 列表 -->
		<view v-if="dataShow"
			style="padding: 20rpx 40rpx 20rpx 40rpx;margin: 20rpx;height: 500rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;">
			<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
			<view style="color: gainsboro;">
				数据为空
			</view>
		</view>
		<view class="listcontainer" v-for="(item,index) in dataArray" :key="index" @click="gotoResults(item)">
			<view class="lefttext">
				<view class="listtitle">
					台区编号：<text>{{item.resrcSuplCode}}</text>
				</view>
				<view class="datatext">
					<view class="datacontainer">
						<view class="datatitle">所属单位：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.mgtOrgName}}</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">台区名称：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.resrcSuplName}}</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">供电量：</view><text style="font-weight: 600;font-size: 26rpx;">{{item.ppq}} kwh</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">用电量：</view><text style="font-weight: 600;font-size: 26rpx;">{{item.upq}} kwh</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">损失电量：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.lossPq}} kwh</text>
					</view>
					<view class="datacontainer">
						<view class="datatitle">损失率：</view><text
							style="font-weight: 600;font-size: 26rpx;">{{item.llRate}}%</text>
					</view>
				</view>
			</view>
			<view class="rightarrow">
				<uni-icons type="right" size="20" color="gray" />
			</view>
		</view>
	</view>
</template>

<script>
	import DaTreeVue2 from '@/components/da-tree-vue2/index.vue'
	import utils from '@/static/commonJs/util.js'
	export default {
		components: {
			DaTreeVue2
		},
		data() {
			return {
				testdev: true,
				dataShow: true,
				judge: false,
				treeData: [],
				// 单选时默认值为字符串或数值，不能为数组
				defaultCheckedKeysValue: '31',
				dataArray: [],
				uForm: {
					mgtOrgCode: "31102",
					queryDate: "2025-04-22",
					realCountMax: "",
					realCountMin: "",
					clueCountMax: "",
					clueCountMin: "",
					lossPQMax: "",
					lossPQMin: "",
					ppqMax: "",
					ppqMin: "",
					llRateMax: "",
					llRateMin: "",
					pageNum: 1,
					pageSize: 5,
					totalCount: 0,
				},
				loading: false,
				showpopup: true,
				closeOnClick: true,
			}
		},
		onLoad() {},
		onReady() {
			// this.init();
			// this.getMgtOrgCode();
			this.dataShow = false;
			this.dataArray = [
				{
				   resrcSuplCode:'56132',
				   mgtOrgName:'浦东供电公司',
				   resrcSuplName:'聚义公司',
				   ppq:'857.6',
				   upq:'828.23',
				   lossPq:'29.37',
				   llRate:'3.42',
				   custCnt:'30',
				   succRate:'100',
				   totalNum:'1',
				   realNum:'1',
				   handleNum:'1'
			    },
				{
				   resrcSuplCode:'33867_00',
				   mgtOrgName:'浦东供电公司',
				   resrcSuplName:'世博高清春_10千伏配变',
				   ppq:'1236.8',
				   upq:'1183.89',
				   lossPq:'52.91',
				   llRate:'4.28',
				   custCnt:'107',
				   succRate:'99.07',
				   totalNum:'1',
				   realNum:'1',
				   handleNum:'1'
				},
				{
				   resrcSuplCode:'63069',
				   mgtOrgName:'浦东供电公司',
				   resrcSuplName:'中久西',
				   ppq:'1569.6',
				   upq:'1483.1',
				   lossPq:'86.5',
				   llRate:'5.51',
				   custCnt:'111',
				   succRate:'92.86',
				   totalNum:'1',
				   realNum:'1',
				   handleNum:'1'
				},
				{
				   resrcSuplCode:'63069',
				   mgtOrgName:'浦东供电公司',
				   resrcSuplName:'牛楼五金',
				   ppq:'940.34',
				   upq:'876.37',
				   lossPq:'63.97',
				   llRate:'6.8',
				   custCnt:'35',
				   succRate:'100',
				   totalNum:'1',
				   realNum:'1',
				   handleNum:'1'
				},
				{
				   resrcSuplCode:'61032',
				   mgtOrgName:'浦东供电公司',
				   resrcSuplName:'新港果园4、6队',
				   ppq:'2627.01',
				   upq:'2357.53',
				   lossPq:'269.48',
				   llRate:'10.26',
				   custCnt:'180',
				   succRate:'100',
				   totalNum:'1',
				   realNum:'1',
				   handleNum:'0'
				},
			]
			this.uForm.totalCount = 5
		},
		onReachBottom() {
			if (this.dataArray.length < this.uForm.totalCount) {
				this.uForm.pageNum++
				this.search()
			} else {
				uni.showToast({
					title: '没有更多了',
					icon: 'none'
				})
			}
		},
		onPullDownRefresh() {
			this.uForm.pageNum = 1
			this.search().finally(() => {
				uni.stopPullDownRefresh()
			})
		},
		methods: {
			reset() {
				this.uForm.mgtOrgCode = "31102"
				this.uForm.queryDate = ""
				this.uForm.realCountMax = ""
				this.uForm.realCountMin = ""
				this.uForm.clueCountMax = ""
				this.uForm.clueCountMin = ""
				this.uForm.lossPQMax = ""
				this.uForm.lossPQMin = ""
				this.uForm.ppqMax = ""
				this.uForm.ppqMin = ""
				this.uForm.llRateMax = ""
				this.uForm.llRateMin = ""
				this.uForm.pageNum = 1
				this.uForm.pageSize = 5
			},
			handleEndDate(value) {
				this.uForm.queryDate = value
			},
			async search() {
				if (this.testdev) {
					this.dataShow = false;
					this.dataArray = [
						{
						   resrcSuplCode:'56132',
						   mgtOrgName:'浦东供电公司',
						   resrcSuplName:'聚义公司',
						   ppq:'857.6',
						   upq:'828.23',
						   lossPq:'29.37',
						   llRate:'3.42',
						   custCnt:'30',
						   succRate:'100',
						   totalNum:'1',
						   realNum:'1',
						   handleNum:'1'
					    },
						{
						   resrcSuplCode:'33867_00',
						   mgtOrgName:'浦东供电公司',
						   resrcSuplName:'世博高清春_10千伏配变',
						   ppq:'1236.8',
						   upq:'1183.89',
						   lossPq:'52.91',
						   llRate:'4.28',
						   custCnt:'107',
						   succRate:'99.07',
						   totalNum:'1',
						   realNum:'1',
						   handleNum:'1'
						},
						{
						   resrcSuplCode:'63069',
						   mgtOrgName:'浦东供电公司',
						   resrcSuplName:'中久西',
						   ppq:'1569.6',
						   upq:'1483.1',
						   lossPq:'86.5',
						   llRate:'5.51',
						   custCnt:'111',
						   succRate:'92.86',
						   totalNum:'1',
						   realNum:'1',
						   handleNum:'1'
						},
						{
						   resrcSuplCode:'63069',
						   mgtOrgName:'浦东供电公司',
						   resrcSuplName:'牛楼五金',
						   ppq:'940.34',
						   upq:'876.37',
						   lossPq:'63.97',
						   llRate:'6.8',
						   custCnt:'35',
						   succRate:'100',
						   totalNum:'1',
						   realNum:'1',
						   handleNum:'1'
						},
						{
						   resrcSuplCode:'61032',
						   mgtOrgName:'浦东供电公司',
						   resrcSuplName:'新港果园4、6队',
						   ppq:'2627.01',
						   upq:'2357.53',
						   lossPq:'269.48',
						   llRate:'10.26',
						   custCnt:'180',
						   succRate:'100',
						   totalNum:'1',
						   realNum:'1',
						   handleNum:'0'
						},
					]
					this.uForm.totalCount = 5
					this.showpopup = false
				} else {
					if (this.loading) return

					this.loading = true;
					// 调用后端接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "AppOrderController",
										"method": "queryAbnorDist",
										"data": {
											"mgtOrgCode": this.uForm.mgtOrgCode,
											"queryDate": this.uForm.queryDate,
											"realCountMax": this.uForm.realCountMax,
											"realCountMin": this.uForm.realCountMin,
											"clueCountMax": this.uForm.clueCountMax,
											"clueCountMin": this.uForm.clueCountMin,
											"lossPQMax": this.uForm.lossPQMax,
											"lossPQMin": this.uForm.lossPQMin,
											"ppqMax": this.uForm.ppqMax,
											"ppqMin": this.uForm.ppqMin,
											"llRateMax": this.uForm.llRateMax,
											"llRateMin": this.uForm.llRateMin,
											"pageNum": this.uForm.pageNum,
											"pageSize": this.uForm.pageSize
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.dataShow = false;
							this.dataArray = this.uForm.pageNum === 1 ? data.list : [...this.dataArray, ...data.list]
							this.uForm.totalCount = data.totalCount
							this.showpopup = false
						} else {
							this.dataShow = true;
							this.loading = false
							this.showpopup = false
						}
					} finally {
						this.loading = false
						this.showpopup = false
					}
				}
			},
			async getMgtOrgCode() {
				try {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryMgtOrgTree",
									"data": {
										"mgtOrgCode": this.uForm.mgtOrgCode,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					if (code === 200) {
						this.treeData = data;
					} else {
						uni.showToast({
							title: '单位树查询失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.showToast({
						title: '单位树查询失败',
						icon: 'none'
					});
					console.log(error)
				}
			},
			async init() {
				try {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryUserInfoByBody",
									"data": {
										"operatorId": utils.userInfo?.nameCode,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					if (code === 200) {
						this.uForm.mgtOrgCode = data.mgtOrgCode;
					}
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				}
			},
			gotoResults(item) {
				uni.navigateTo({
					url: '/pages/fileQuery/keyAreaScreen/keyAreaResults?data=' + encodeURIComponent(JSON.stringify(
						item))
				})
			},
			handleNavbarSearch(payload) {
				// console.log('收到搜索事件，参数:', payload.keyword);
				// 执行父页面的操作（如跳转、弹窗、请求数据等）
				this.showpopup = payload.keyword;
			},
			open() {
				// console.log('open');
			},
			close() {
				this.judge = false;
				this.showpopup = false
			},
			handleTreeChange(values, currentItem) {
				// 支持修改节点数据
				// currentItem.label = `${currentItem.originItem.name}`
				this.uForm.mgtOrgCode = judge;
				// console.log('handleTreeChange ==>', judge, currentItem)
			},
			handleExpandChange(expand, currentItem) {
				// 支持修改节点数据
				// currentItem.label = `${currentItem.originItem.name}`
				if (expand == true) {
					this.judge = true;
				} else {
					this.judge = false;
				}
				// console.log('handleExpandChange ==>', expand);
				// console.log('handleExpandChange ==>', currentItem);
			},

			expandTree(keys, expand) {
				// console.log('expandTree ==>', keys, expand)
				this.$refs.DaTreeRef?.setExpandedKeys(keys, expand)

				const gek = this.$refs.DaTreeRef?.getExpandedKeys()
				// console.log('当前已展开的KEY ==>', gek)
			},
			checkedTree(keys, checked) {
				// console.log('checkedTree ==>', keys, checked)
				this.$refs.DaTreeRef?.setCheckedKeys(keys, checked)

				const gek = this.$refs.DaTreeRef?.getCheckedKeys()
				// console.log('当前已选中的KEY ==>', gek)
			},
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
		// font-family: 'SimHei',sans-serif;
	}

	.select-box {
		border: 1rpx solid #ddd;
		width: 100%;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.arrow {
		float: right;
		border: solid #999;
		border-width: 0 2rpx 2rpx 0;
		padding: 6rpx;
		margin-top: 6rpx;
	}

	.down {
		transform: rotate(45deg);
	}

	.up {
		transform: rotate(-135deg);
	}

	.dropdown {
		position: absolute;
		width: 100%;
		border: 1rpx solid #eee;
		background: #fff;
		z-index: 999;
	}

	.dropdown-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.mainContainer {
		.popupcontainer {
			::v-deep .u-popup__content .u-popup__content__close {
				display: none !important;
			}

			height: 1500rpx;
			padding: 0 40rpx;

			.poptitle {
				text-align: center;
				font-size: 36rpx;
				font-weight: bold;
				padding: 50rpx 0;
			}

			.formitem {
				.DaTreestyle {
					// border: 1px solid black;
					// background-color: white;
					height: 400rpx;
					position: absolute;
					top: 24rpx;
				}

				// font-weight: bold;
				.calendarContainer {
					width: 100%;

					.calInput {
						width: 100%;
						// margin-right: 30rpx;
					}
				}
			}

			.ubutton {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 400rpx 0 0;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 20rpx;

			.lefttext {
				flex: 1;

				.listtitle {
					font-weight: bold;
					font-size: 36rpx;
					padding: 20rpx 20rpx;
				}

				.datatext {
					padding: 20rpx 0 20rpx 20rpx;

					.datacontainer {
						display: flex;
						align-items: center;
						padding: 6rpx 0;

						.datatitle {
							font-size: 26rpx;
							width: 180rpx;
							color: darkgray;
						}
					}

					// display: flex;
					// flex-direction: column;
					// justify-content: center;
					// align-items: center;
				}
			}
		}
	}
</style>