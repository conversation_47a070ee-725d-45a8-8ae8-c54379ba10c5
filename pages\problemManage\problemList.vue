<template>
	<view class="problem-list-container">
		<!-- 使用公共导航栏组件 -->
		<custom-navbar 
			title="问题管理" 
			:path="path"
			background-color="#00c389"
			title-color="#FFFFFF"
		/>
		
		<!-- 分类标签页 - 使用u-subsection -->
		<view class="subsection-container">
			<u-subsection 
				:list="tabsList" 
				:current="currentTab"
				@change="switchTab"
				activeColor="#00c389"
				inactiveColor="#262626"
				mode="button"
				:buttonColor="'#f5f5f5'"
				bgColor="#f5f5f5"
				class="tabs"
				:itemStyle="{
					height: '80rpx',
					fontSize: '28rpx',
				}"
				lineWidth="40rpx"
				lineHeight="4rpx"
			></u-subsection>
		</view>
		
		<!-- 问题列表 -->
		<scroll-view 
		class="problem-scroll"
		enable-back-to-top
		:scroll-anchoring = "true"
		:enhanced="true"
		:bounces="true"
		:show-scrollbar="false"
		refresher-enabled
		:refresher-triggered="isRefreshing"
		@refresherrefresh="onRefresh"
		@scrolltolower="onLoadMore"
		scroll-y>
			<view class="problem-list">
				<view v-if="problemList.length>0">
				<view 
					class="problem-item" 
					v-for="(item, index) in problemList" 
					:key="index"
					@click="goToDetail(item)"
				>
					<view class="problem-top">
						<view class="problem-icon">
							<uni-icons type="help" color="#8c8c8c" size="48rpx"></uni-icons>
						</view>
						<view class="problem-title">{{ item.questionTitle }}</view>
						<view class="problem-arrow">
							<uni-icons type="forward" color="#8c8c8c" size="32rpx"></uni-icons>
						</view>
					</view>
					<view class="problem-bottom">
						<view class="problem-info">
							<text class="username">{{ item.submitterName }}</text>
							<text class="company">{{ item.mgtOrgName }}</text>
						</view>
						<view class="problem-time">{{ item.questionTime }}</view>
					</view>
				</view>
				</view>
				<view v-else class="empty-container">
					<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
				</view>
				<view class="loading-more" v-if="problemList.length>0">
					<text v-if="isLoading && params.pageNum >1">加载中...</text>
					<text v-else-if="!hasMoreData">没有更多数据了</text>
				</view>
			</view>
		</scroll-view>
		
		<!-- 底部固定按钮 -->
		<view class="bottom-button" @click="goToReport">
			<text>问题提报</text>
		</view>
	</view>
</template>

<script>
	import CustomNavbar from '@/components/custom-navbar.vue';
	import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port} from '@/static/commonJs/util.js'
	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				currentTab: 0,
				token:null,
				path:window.location.hash,
				tabs: [
					{ name: '咨询', type: '01' },
					{ name: '系统', type: '02' },
					{ name: '需求', type: '03' },
					{ name: '我的提问', type: '04' }
				],
				tabsList: ['咨询', '系统', '需求', '我的提问'],
				allProblems: [],
				problemList: [],
				params: {
					pageNum: 1,
					pageSize: 10
				},
				hasMoreData:true,
				isRefreshing: false,
				isLoading: false,
				isMockData: true
			};
		},
		created() {
		},
		onLoad() {
			this.showInfo();
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token
				});
			},
			showInfo(){
				if (this.isLoading && !this.isRefreshing) {
					return;
				}
				this.isLoading = true
				if(this.isMockData) {
					this.allProblems = [
						{
							'questionId':'1793583533075009538',
							'questionTitle':'hhjghjghjggh',
							'submitterId':'T00033391',
							'submitterName':'岳恒',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'questionCount':'hhjghjghjggh',
							'questionType':'01',
							'questionTypeName':'咨询',
							'questionTime':'2024-05-23',
							'distLv':'02'
						},
						{
							'questionId':'1793583173149200385',
							'questionTitle':'测试',
							'submitterId':'T00033391',
							'submitterName':'岳恒',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'questionCount':'测试测试测试测试测试测试',
							'questionType':'01',
							'questionTypeName':'咨询',
							'questionTime':'2024-05-23',
							'distLv':'02'
						},
						{
							'questionId':'1934502796785139714',
							'questionTitle':'存在报错',
							'submitterId':'T00033391',
							'submitterName':'岳恒',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'questionCount':'工单监控页面报错xxx500，页面无数据',
							'questionType':'02',
							'questionTypeName':'系统',
							'questionTime':'2025-06-16',
							'distLv':'02'
						},
						{
							'questionId':'1934503236767629313',
							'questionTitle':'成效明细页面无数据',
							'submitterId':'T00033391',
							'submitterName':'岳恒',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'questionCount':'首次打开成效明细页面无数据展示，通过成效统计超链接点击后跳转成效明细有数据展示',
							'questionType':'01',
							'questionTypeName':'咨询',
							'questionTime':'2025-06-16',
							'distLv':'02'
						},
						{
							'questionId':'1934502271230459905',
							'questionTitle':'模型线索页面新增展示字段',
							'submitterId':'T00033391',
							'submitterName':'岳恒',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'questionCount':'模型线索页面展示字段增加：是否现场检查、是否归档，初始页面默认展示字段新增：是否派工、是否推送、是否现场检查、是否归档',
							'questionType':'03',
							'questionTypeName':'需求',
							'questionTime':'2025-06-16',
							'distLv':'02'
						},
						{
							'questionId':'1934502713498845185',
							'questionTitle':'页面与导出表格内数据不一致',
							'submitterId':'T00033391',
							'submitterName':'岳恒',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'questionCount':'页面导出数据字段要与页面展示字段一致，调整全部页面导出数据字段与页面展示字段一致（数量、内容）',
							'questionType':'02',
							'questionTypeName':'系统',
							'questionTime':'2025-06-16',
							'distLv':'02'
						},
						{
							'questionId':'1934502997222539266',
							'questionTitle':'线索页面人员展示问题',
							'submitterId':'T00033391',
							'submitterName':'岳恒',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'questionCount':'线索页面派工时查询可派工人员列表后需展示所属单位（包含下级）下全部掌机人员，现在只到本级有问题',
							'questionType':'03',
							'questionTypeName':'需求',
							'questionTime':'2025-06-16',
							'distLv':'02'
						},
						{
							'questionId':'1934502875273150466',
							'questionTitle':'页面改动',
							'submitterId':'T00033391',
							'submitterName':'岳恒',
							'mgtOrgCode':'31102',
							'mgtOrgName':'上海市电力公司',
							'questionCount':'更改成效明细、工单明细两个页面导出的表格字段上级单位（为以市公司为基准不要出现上海电力公司）、管理单位（以供服为基准，营销部的工单直接展示为市公司）',
							'questionType':'03',
							'questionTypeName':'需求',
							'questionTime':'2025-06-16',
							'distLv':'02'
						}
					]
					this.filterProblems();
					this.isLoading = false;
					setTimeout(() => {
						this.isRefreshing = false;
					},1000)
				}else{
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						// url:"/eas-master-app/interactive/handle",
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token':this.token
						},
						data: {
							token: this.token,
							method:"PutHuaYun",
							uri:url,
							data:JSON.stringify({
								"bizCode":bizCode,
								"espFlowId":getUUID(),
								"espRsvField1":"",
								"espRsvField2":"",
								"espRsvField3":"",
								"espSign":"",
								"espTimestamp":getCurrentTimestamp(),
								"espInformation": {
									"service":"MobileQuestionManageController",
									"method":"getQuestion",
									"data": {
										"questionType":"",
										"responderId":"",
										"pageNum":this.params.pageNum,
										"pageSize":this.params.pageSize
									},
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								 const rtnData = res.data.Data.espInformation;
								 if(rtnData && rtnData.code == 200) {
									 const newList = rtnData.data.list || [];
									 this.hasMoreData = newList.length >= this.params.pageSize;
									 if(this.params.pageNum === 1) {
									 	this.allProblems = [...newList];
									 }else{
									 	this.allProblems = [...this.allProblems,...newList]
									 	
									 }
									 this.isLoading = false;
									 this.filterProblems();
									 // 如果是下拉刷新，需要结束刷新状态
									 if (this.isRefreshing) {
									 	this.isRefreshing = false;
									 }
								 }else{
									 uni.showToast({
									 	title: '暂无数据',
									 	icon: 'none',
									 	duration: 2000
									 });
								 }
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (error) => {
							this.isRefreshing = false;
							this.isLoading = false;
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
						}
					});
				}
				
			},
			goBack() {
				uni.navigateBack();
			},
			switchTab(index) {
				this.currentTab = index;
				this.filterProblems();
			},
			filterProblems() {
				const type = this.tabs[this.currentTab].type;
				if (type === '04') {
					// 我的提问，筛选当前用户的问题
					this.problemList = this.allProblems.filter(item => !item.questionType);
				} else {
					// 按类型筛选
					this.problemList = this.allProblems.filter(item => item.questionType === type);
				}
			},
			goToDetail(obj) {
				var vm = this;
				console.log('进入跳转')
				uni.navigateTo({
					url: `./problemDetail?item=${encodeURIComponent(JSON.stringify(obj))}`
				});
			},
			goToReport() {
				uni.navigateTo({
					url: '/pages/problemManage/problemReport'
				});
			},
			onLoadMore() {
				console.log("滚动到底部了")
				if(this.isLoading || !this.hasMoreData) {
					return;
				}
				this.params.pageNum++;
				this.showInfo();
			},
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				this.hasMoreData = true;
				this.allProblems  = [];
				this.params.pageNum = 1; // 重置为第一页
				// 重新加载数据
				this.showInfo();
			},
		}
	}
</script>

<style lang="scss" scoped>
.problem-list-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}
.tabs {
	height: 80rpx;
	padding: 6rpx 0;
}
.subsection-container {
	background-color: #f5f5f5;
	padding: 0 20rpx 0;
}

.problem-scroll {
	flex: 1;
	overflow: hidden;
}

.problem-list {
	padding: 0 20rpx;
	z-index: 1;
}
.empty-container {
	display:flex;
	align-items:center;
	justify-content: center;
	height:100%;
	font-size: 24rpx;
	margin-top:100rpx;
}
.problem-item {
	display: flex;
	flex-direction: column;
	background-color: #FFFFFF;
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	// 红框标记的样式
	&:nth-child(2) {
		border: 1rpx solid #f0f0f0;
	}
	
	.problem-top {
		display: flex;
		align-items: flex-start;
		margin-bottom: 20rpx;
		
		.problem-icon {
			margin-right: 20rpx;
			margin-top: 4rpx;
		}
		
		.problem-title {
			flex: 1;
			font-size: 28rpx;
			color: #262626;
			line-height: 1.5;
			font-weight: 400;
			letter-spacing: 0.5rpx;
		}
		
		.problem-arrow {
			margin-left: 20rpx;
			margin-top: 4rpx;
		}
	}
	
	.problem-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.problem-info {
			font-size: 24rpx;
			color: #8c8c8c;
			
			.username {
				margin-right: 20rpx;
				color:#262626;
			}
		}
		
		.problem-time {
			font-size: 24rpx;
			color: #8c8c8c;
			text-align: right;
		}
	}
}

.bottom-button {
	height: 80rpx;
	background-color: #07ac7c;
	color: #FFFFFF;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 500;
	letter-spacing: 1rpx;
}
.loading-more {
	width:100%;
	height:60rpx;
	display:flex;
	justify-content: center;
	align-items: center;
	padding:20rpx 0;
	color:#999;
	font-size:28rpx;
}
</style>
