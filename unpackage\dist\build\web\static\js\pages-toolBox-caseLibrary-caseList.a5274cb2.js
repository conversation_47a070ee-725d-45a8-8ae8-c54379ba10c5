(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-toolBox-caseLibrary-caseList"],{"08d67":function(a,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return r})),e.d(t,"a",(function(){return o}));var o={uIcon:e("59b5").default,uEmpty:e("57a9").default},i=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("v-uni-view",{staticClass:"case-list-content"},[e("custom-navbar",{attrs:{title:"案例库",showBack:!0},scopedSlots:a._u([{key:"right",fn:function(){return[e("v-uni-view",{staticClass:"search-icon",on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.goToSearch.apply(void 0,arguments)}}},[e("u-icon",{attrs:{name:"search",color:"#000000",size:"28"}})],1)]},proxy:!0}])}),e("v-uni-scroll-view",{staticClass:"content-section",style:{height:a.contentHeight+"px"},attrs:{"scroll-y":"true","enable-back-to-top":!0,"scroll-anchoring":!0,enhanced:!0,bounces:!0,"show-scrollbar":!1,"refresher-enabled":!0,"refresher-triggered":a.isRefreshing},on:{refresherrefresh:function(t){arguments[0]=t=a.$handleEvent(t),a.onRefresh.apply(void 0,arguments)},scrolltolower:function(t){arguments[0]=t=a.$handleEvent(t),a.onLoadMore.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"case-list"},a._l(a.caseList,(function(t,o){return e("v-uni-view",{key:"case"+String(o),staticClass:"case-item",on:{click:function(e){arguments[0]=e=a.$handleEvent(e),a.goToDetail(t)}}},[e("v-uni-view",{staticClass:"case-header"},[e("v-uni-view",{staticClass:"case-tag",style:a.getTagStyle(t.caseStatus)},[e("v-uni-text",[a._v(a._s(t.caseStatusName))])],1),e("v-uni-view",{staticClass:"case-title"},[e("v-uni-text",[a._v(a._s(t.caseName))])],1)],1),e("v-uni-view",{staticClass:"case-info"},[e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("案件地点：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(t.caseLocal))])],1),"01"==t.caseStatus?e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("案件类型：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(t.caseTypeName))])],1):a._e(),"02"==t.caseStatus?e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("违窃性质：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(t.stealTypeName))])],1):a._e(),"01"==t.caseStatus?e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("审结时间：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(t.caseTime))])],1):a._e(),"02"==t.caseStatus?e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("案例时间：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(t.caseTime))])],1):a._e(),"02"==t.caseStatus?e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"info-label"},[a._v("违窃类别：")]),e("v-uni-text",{staticClass:"info-value"},[a._v(a._s(t.caseClassName))])],1):a._e()],1),e("v-uni-view",{staticClass:"case-arrow"},[e("u-icon",{attrs:{name:"arrow-right",color:"#262626",size:"18"}})],1)],1)})),1),a.isLoading||a.isRefreshing||0!==a.caseList.length?a._e():e("v-uni-view",{staticClass:"empty-container"},[e("u-empty",{attrs:{mode:"data",icon:"/static/icons/nodata.jpg"}})],1),a.caseList.length>0?e("v-uni-view",{staticClass:"loading-more"},[a.isLoading&&a.params.pageNum>1?e("v-uni-text",[a._v("加载中...")]):a.hasMoreData?a._e():e("v-uni-text",[a._v("没有更多数据了")])],1):a._e(),e("v-uni-view",{staticClass:"bottom-space"})],1)],1)},r=[]},"2a3a":function(a,t,e){"use strict";e.r(t);var o=e("6b79"),i=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(a){e.d(t,a,(function(){return o[a]}))}(r);t["default"]=i.a},"3a15":function(a,t,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("d4b5"),e("c223");var i=o(e("b7c7")),r=o(e("9b1b")),n=e("b3d7"),s={data:function(){return{params:{pageNum:1,pageSize:10},token:null,hasMoreData:!0,isRefreshing:!1,isLoading:!1,contentHeight:0,statusBarHeight:0,navbarHeight:44,caseList:[],searchForm:{caseStatus:"",caseName:"",createOrg:"",creator:"",caseStartTime:"",caseEndTime:""},isMockData:!0}},onLoad:function(){this.getSystemInfo(),this.calcContentHeight(),this.params.pageNum=1,this.loadCaseList(this.searchForm),uni.$on("caseSearch",this.caseSearch)},onReady:function(){},onShow:function(){},onUnload:function(){uni.$off("caseSearch",this.caseSearch)},methods:{init:function(){var a=this;wx.invoke("ext_DataCache_GetInfo",{data:{key:"1002838.userInfo"}},(function(t){var e=JSON.parse(t.result);a.token=null===e||void 0===e?void 0:e.token}))},getSystemInfo:function(){var a=uni.getSystemInfoSync();this.statusBarHeight=a.statusBarHeight||0,this.windowHeight=a.windowHeight||0},calcContentHeight:function(){this.windowHeight||this.getSystemInfo();var a=this.statusBarHeight+this.navbarHeight;this.contentHeight=this.windowHeight-a},getTagStyle:function(a){return{background:{"01":"#05d69b","02":"#3a7aff","03":"#ff4d4f",99:"#9254de"}[a]}},onLoadMore:function(){console.log("滚动到底部了"),!this.isLoading&&this.hasMoreData&&(this.params.pageNum++,this.loadCaseList(this.searchParams))},caseSearch:function(a){this.searchParams=(0,r.default)((0,r.default)({},this.searchParams),a),console.log("案例搜索"),this.loadCaseList(this.searchParams)},loadCaseList:function(a){var t=this;this.isLoading&&!this.isRefreshing||(this.isLoading=!0,uni.showLoading({title:"加载中..."}),this.isMockData?(this.caseList=[{caseStatus:"02",caseStatusName:"窃电",theftCaseNo:"966007594567827456",caseName:"浦东公司“职业焊接二次电流”窃电案",caseClass:"12",caseClassName:"故意使供电企业的用电计量装置计量不准或者失效",caseLevel:"03",caseLevelName:"市级",caseState:"03",caseStateName:"已发布",keyWord:"窃电点极其隐蔽，接触二次导线",caseLocal:"上海市浦东新区曹路镇张唐村",caseTime:"2025-06-16",stealType:"01",stealTypeName:"窃电",asQty:"1188",sumExp:"2440.92",creator:"4684257252382615342",createOrg:"上海市电力公司",createTime:"2025-06-16",caseType:"",caseTypeName:"",litigCont:"职业窃电焊接二次电流窃电，窃电点极其隐蔽，检查人员一旦接触二次导线，电表恢复正常，易造成误判",notifAnnexId:"",asExp:"",breachCtrtExp:"1830.69",caseFeature:"职业窃电焊接二次电流窃电，窃电点极其隐蔽，检查人员一旦接触二次导线，电表恢复正常，易造成误判"}],this.isLoading=!1,setTimeout((function(){t.isRefreshing=!1}),1e3),uni.hideLoading()):uni.request({url:"http://127.0.0.1:".concat(n.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:this.token},data:{token:this.token,method:"PutHuaYun",uri:n.url,data:JSON.stringify({bizCode:n.bizCode,espFlowId:(0,n.getUUID)(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:(0,n.getCurrentTimestamp)(),espInformation:{service:"MobileCaseManagerController",method:"stealCaseQuery",data:(0,r.default)((0,r.default)({},a),{},{pageNum:this.params.pageNum,pageSize:this.params.pageSize})}})},success:function(a){if(console.log(a),a&&1===a.data.Tag){var e=a.data.Data.espInformation;if(e&&200==e.code){var o=e.data.list||[];t.hasMoreData=o.length>=t.params.pageSize,1===t.params.pageNum?t.caseList=(0,i.default)(o):t.caseList=[].concat((0,i.default)(t.caseList),(0,i.default)(o)),console.log(t.caseList),t.isLoading=!1,t.isRefreshing&&(t.isRefreshing=!1)}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3});uni.hideLoading()},fail:function(a){t.isRefreshing=!1,t.isLoading=!1,uni.hideLoading(),uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}}))},onRefresh:function(){this.isRefreshing=!0,this.hasMoreData=!0,this.caseList=[];try{var a=uni.getStorageSync("caseSearchParams");a&&(this.searchParams=a)}catch(t){console.log("获取查询参数失败",t)}this.params.pageNum=1,this.loadCaseList(this.searchParams)},goToDetail:function(a){uni.navigateTo({url:"/pages/toolBox/caseLibrary/caseDetail?item=".concat(encodeURIComponent(JSON.stringify(a)))})},goToSearch:function(){uni.navigateTo({url:"/pages/toolBox/caseLibrary/caseSearch"})}}};t.default=s},"3c98":function(a,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return r})),e.d(t,"a",(function(){return o}));var o={uIcon:e("59b5").default},i=function(){var a=this,t=a.$createElement,e=a._self._c||t;return a.show?e("v-uni-view",{staticClass:"u-empty",style:[a.emptyStyle]},[a.isSrc?e("v-uni-image",{style:{width:a.$u.addUnit(a.width),height:a.$u.addUnit(a.height)},attrs:{src:a.icon,mode:"widthFix"}}):e("u-icon",{attrs:{name:"message"===a.mode?"chat":"empty-"+a.mode,size:a.iconSize,color:a.iconColor,"margin-top":"14"}}),e("v-uni-text",{staticClass:"u-empty__text",style:[a.textStyle]},[a._v(a._s(a.text?a.text:a.icons[a.mode]))]),a.$slots.default||a.$slots.$default?e("v-uni-view",{staticClass:"u-empty__wrap"},[a._t("default")],2):a._e()],1):a._e()},r=[]},"57a9":function(a,t,e){"use strict";e.r(t);var o=e("3c98"),i=e("2a3a");for(var r in i)["default"].indexOf(r)<0&&function(a){e.d(t,a,(function(){return i[a]}))}(r);e("60d7");var n=e("828b"),s=Object(n["a"])(i["default"],o["b"],o["c"],!1,null,"6fa087a0",null,!1,o["a"],void 0);t["default"]=s.exports},"60d7":function(a,t,e){"use strict";var o=e("990d"),i=e.n(o);i.a},"6b79":function(a,t,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("5ef2");var i=o(e("c578")),r={name:"u-empty",mixins:[uni.$u.mpMixin,uni.$u.mixin,i.default],data:function(){return{icons:{car:"购物车为空",page:"页面不存在",search:"没有搜索结果",address:"没有收货地址",wifi:"没有WiFi",order:"订单为空",coupon:"没有优惠券",favor:"暂无收藏",permission:"无权限",history:"无历史记录",news:"无新闻列表",message:"消息列表为空",list:"列表为空",data:"数据为空",comment:"暂无评论"}}},computed:{emptyStyle:function(){var a={};return a.marginTop=uni.$u.addUnit(this.marginTop),uni.$u.deepMerge(uni.$u.addStyle(this.customStyle),a)},textStyle:function(){var a={};return a.color=this.textColor,a.fontSize=uni.$u.addUnit(this.textSize),a},isSrc:function(){return this.icon.indexOf("/")>=0}}};t.default=r},"720c":function(a,t,e){"use strict";var o=e("ef81"),i=e.n(o);i.a},"75ec":function(a,t,e){"use strict";e.r(t);var o=e("3a15"),i=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(a){e.d(t,a,(function(){return o[a]}))}(r);t["default"]=i.a},"990d":function(a,t,e){var o=e("9cdc");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[a.i,o,""]]),o.locals&&(a.exports=o.locals);var i=e("967d").default;i("5cbbef44",o,!0,{sourceMap:!1,shadowMode:!1})},"9cdc":function(a,t,e){var o=e("c86c");t=o(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-6fa087a0]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-6fa087a0]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-6fa087a0]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-6fa087a0]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-6fa087a0]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-6fa087a0]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-6fa087a0]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-6fa087a0]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-6fa087a0]::after{border:none}.u-hover-class[data-v-6fa087a0]{opacity:.7}.u-primary-light[data-v-6fa087a0]{color:#ecf5ff}.u-warning-light[data-v-6fa087a0]{color:#fdf6ec}.u-success-light[data-v-6fa087a0]{color:#f5fff0}.u-error-light[data-v-6fa087a0]{color:#fef0f0}.u-info-light[data-v-6fa087a0]{color:#f4f4f5}.u-primary-light-bg[data-v-6fa087a0]{background-color:#ecf5ff}.u-warning-light-bg[data-v-6fa087a0]{background-color:#fdf6ec}.u-success-light-bg[data-v-6fa087a0]{background-color:#f5fff0}.u-error-light-bg[data-v-6fa087a0]{background-color:#fef0f0}.u-info-light-bg[data-v-6fa087a0]{background-color:#f4f4f5}.u-primary-dark[data-v-6fa087a0]{color:#398ade}.u-warning-dark[data-v-6fa087a0]{color:#f1a532}.u-success-dark[data-v-6fa087a0]{color:#53c21d}.u-error-dark[data-v-6fa087a0]{color:#e45656}.u-info-dark[data-v-6fa087a0]{color:#767a82}.u-primary-dark-bg[data-v-6fa087a0]{background-color:#398ade}.u-warning-dark-bg[data-v-6fa087a0]{background-color:#f1a532}.u-success-dark-bg[data-v-6fa087a0]{background-color:#53c21d}.u-error-dark-bg[data-v-6fa087a0]{background-color:#e45656}.u-info-dark-bg[data-v-6fa087a0]{background-color:#767a82}.u-primary-disabled[data-v-6fa087a0]{color:#9acafc}.u-warning-disabled[data-v-6fa087a0]{color:#f9d39b}.u-success-disabled[data-v-6fa087a0]{color:#a9e08f}.u-error-disabled[data-v-6fa087a0]{color:#f7b2b2}.u-info-disabled[data-v-6fa087a0]{color:#c4c6c9}.u-primary[data-v-6fa087a0]{color:#3c9cff}.u-warning[data-v-6fa087a0]{color:#f9ae3d}.u-success[data-v-6fa087a0]{color:#5ac725}.u-error[data-v-6fa087a0]{color:#f56c6c}.u-info[data-v-6fa087a0]{color:#909399}.u-primary-bg[data-v-6fa087a0]{background-color:#3c9cff}.u-warning-bg[data-v-6fa087a0]{background-color:#f9ae3d}.u-success-bg[data-v-6fa087a0]{background-color:#5ac725}.u-error-bg[data-v-6fa087a0]{background-color:#f56c6c}.u-info-bg[data-v-6fa087a0]{background-color:#909399}.u-main-color[data-v-6fa087a0]{color:#303133}.u-content-color[data-v-6fa087a0]{color:#606266}.u-tips-color[data-v-6fa087a0]{color:#909193}.u-light-color[data-v-6fa087a0]{color:#c0c4cc}.u-safe-area-inset-top[data-v-6fa087a0]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-6fa087a0]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-6fa087a0]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-6fa087a0]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-6fa087a0]{z-index:10090}uni-toast .uni-toast[data-v-6fa087a0]{z-index:10090}[data-v-6fa087a0]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-view[data-v-6fa087a0], uni-scroll-view[data-v-6fa087a0], uni-swiper-item[data-v-6fa087a0]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-empty[data-v-6fa087a0]{display:flex;flex-direction:row;flex-direction:column;justify-content:center;align-items:center}.u-empty__text[data-v-6fa087a0]{display:flex;flex-direction:row;justify-content:center;align-items:center;margin-top:%?20?%}.u-slot-wrap[data-v-6fa087a0]{display:flex;flex-direction:row;justify-content:center;align-items:center;margin-top:%?20?%}',""]),a.exports=t},b60b:function(a,t,e){"use strict";e.r(t);var o=e("08d67"),i=e("75ec");for(var r in i)["default"].indexOf(r)<0&&function(a){e.d(t,a,(function(){return i[a]}))}(r);e("720c");var n=e("828b"),s=Object(n["a"])(i["default"],o["b"],o["c"],!1,null,"2a110526",null,!1,o["a"],void 0);t["default"]=s.exports},c578:function(a,t,e){"use strict";e("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("64aa");var o={props:{icon:{type:String,default:uni.$u.props.empty.icon},text:{type:String,default:uni.$u.props.empty.text},textColor:{type:String,default:uni.$u.props.empty.textColor},textSize:{type:[String,Number],default:uni.$u.props.empty.textSize},iconColor:{type:String,default:uni.$u.props.empty.iconColor},iconSize:{type:[String,Number],default:uni.$u.props.empty.iconSize},mode:{type:String,default:uni.$u.props.empty.mode},width:{type:[String,Number],default:uni.$u.props.empty.width},height:{type:[String,Number],default:uni.$u.props.empty.height},show:{type:Boolean,default:uni.$u.props.empty.show},marginTop:{type:[String,Number],default:uni.$u.props.empty.marginTop}}};t.default=o},d1a87:function(a,t,e){var o=e("c86c");t=o(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-2a110526]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-2a110526]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-2a110526]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-2a110526]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-2a110526]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-2a110526]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-2a110526]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-2a110526]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-2a110526]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-2a110526]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-2a110526]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-2a110526]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-2a110526]::after{border:none}.u-hover-class[data-v-2a110526]{opacity:.7}.u-primary-light[data-v-2a110526]{color:#ecf5ff}.u-warning-light[data-v-2a110526]{color:#fdf6ec}.u-success-light[data-v-2a110526]{color:#f5fff0}.u-error-light[data-v-2a110526]{color:#fef0f0}.u-info-light[data-v-2a110526]{color:#f4f4f5}.u-primary-light-bg[data-v-2a110526]{background-color:#ecf5ff}.u-warning-light-bg[data-v-2a110526]{background-color:#fdf6ec}.u-success-light-bg[data-v-2a110526]{background-color:#f5fff0}.u-error-light-bg[data-v-2a110526]{background-color:#fef0f0}.u-info-light-bg[data-v-2a110526]{background-color:#f4f4f5}.u-primary-dark[data-v-2a110526]{color:#398ade}.u-warning-dark[data-v-2a110526]{color:#f1a532}.u-success-dark[data-v-2a110526]{color:#53c21d}.u-error-dark[data-v-2a110526]{color:#e45656}.u-info-dark[data-v-2a110526]{color:#767a82}.u-primary-dark-bg[data-v-2a110526]{background-color:#398ade}.u-warning-dark-bg[data-v-2a110526]{background-color:#f1a532}.u-success-dark-bg[data-v-2a110526]{background-color:#53c21d}.u-error-dark-bg[data-v-2a110526]{background-color:#e45656}.u-info-dark-bg[data-v-2a110526]{background-color:#767a82}.u-primary-disabled[data-v-2a110526]{color:#9acafc}.u-warning-disabled[data-v-2a110526]{color:#f9d39b}.u-success-disabled[data-v-2a110526]{color:#a9e08f}.u-error-disabled[data-v-2a110526]{color:#f7b2b2}.u-info-disabled[data-v-2a110526]{color:#c4c6c9}.u-primary[data-v-2a110526]{color:#3c9cff}.u-warning[data-v-2a110526]{color:#f9ae3d}.u-success[data-v-2a110526]{color:#5ac725}.u-error[data-v-2a110526]{color:#f56c6c}.u-info[data-v-2a110526]{color:#909399}.u-primary-bg[data-v-2a110526]{background-color:#3c9cff}.u-warning-bg[data-v-2a110526]{background-color:#f9ae3d}.u-success-bg[data-v-2a110526]{background-color:#5ac725}.u-error-bg[data-v-2a110526]{background-color:#f56c6c}.u-info-bg[data-v-2a110526]{background-color:#909399}.u-main-color[data-v-2a110526]{color:#303133}.u-content-color[data-v-2a110526]{color:#606266}.u-tips-color[data-v-2a110526]{color:#909193}.u-light-color[data-v-2a110526]{color:#c0c4cc}.u-safe-area-inset-top[data-v-2a110526]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-2a110526]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-2a110526]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-2a110526]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-2a110526]{z-index:10090}uni-toast .uni-toast[data-v-2a110526]{z-index:10090}[data-v-2a110526]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */.case-list-content[data-v-2a110526]{display:flex;flex-direction:column;background-color:#f8f9fc;position:relative;width:100%;height:100%}\r\n/* 导航栏图标 */.search-icon[data-v-2a110526], .icon-box[data-v-2a110526]{display:flex;align-items:center;justify-content:center;padding:0 %?6?%}.icon-green[data-v-2a110526]{color:#3cc792}\r\n/* 内容区域 */.content-section[data-v-2a110526]{box-sizing:border-box;background-color:#f8f9fc;padding:%?24?% %?30?%;-webkit-overflow-scrolling:touch;\r\n  /* 增强iOS滚动体验 */overflow-y:auto;width:100%}\r\n/* 加载中提示 */.loading-container[data-v-2a110526]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?90?% 0;margin-bottom:%?24?%;background-color:#fff;border-radius:%?20?%;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.06)}.loading-text[data-v-2a110526]{font-size:%?28?%;color:#8c8c8c;margin-top:%?24?%;text-align:center}\r\n/* 案例列表 */.case-list[data-v-2a110526]{-webkit-animation:fade-in-data-v-2a110526 .4s ease;animation:fade-in-data-v-2a110526 .4s ease}@-webkit-keyframes fade-in-data-v-2a110526{from{opacity:0;-webkit-transform:translateY(%?16?%);transform:translateY(%?16?%)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fade-in-data-v-2a110526{from{opacity:0;-webkit-transform:translateY(%?16?%);transform:translateY(%?16?%)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}\r\n/* 案例项 */.case-item[data-v-2a110526]{position:relative;background-color:#fff;border-radius:%?20?%;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.06);padding:%?20?% %?30?%;margin-bottom:%?24?%;overflow:hidden;transition:all .2s ease}.case-item[data-v-2a110526]:active{-webkit-transform:scale(.985);transform:scale(.985);box-shadow:0 %?2?% %?10?% rgba(0,0,0,.04)}\r\n/* 案例标签和标题 */.case-header[data-v-2a110526]{display:flex;align-items:flex-start;margin-bottom:%?24?%;padding-right:%?48?%\r\n  /* 为右侧箭头留出空间 */}.case-tag[data-v-2a110526]{min-width:%?80?%;height:%?48?%;line-height:%?48?%;border-radius:%?24?%;display:flex;align-items:center;justify-content:center;margin-right:%?20?%;flex-shrink:0;box-shadow:0 %?4?% %?10?% rgba(0,0,0,.06)}.case-tag uni-text[data-v-2a110526]{font-size:%?24?%;color:#fff;font-weight:600}.case-tag-legal[data-v-2a110526]{background:linear-gradient(135deg,#07ac7c,#05d69b)\r\n  /* 渐变背景 */}.case-tag-violation[data-v-2a110526]{background:linear-gradient(135deg,#3a7aff,#4080ff)\r\n  /* 渐变背景 */}.case-title[data-v-2a110526]{flex:1}.case-title uni-text[data-v-2a110526]{font-size:%?32?%;color:#262626;font-weight:600;line-height:1.5}\r\n/* 案例信息 */.case-info[data-v-2a110526]{padding-right:%?60?%;\r\n  /* 为右侧箭头留出空间 */padding-left:%?6?%}.info-row[data-v-2a110526]{display:flex;margin-bottom:%?14?%;align-items:start}.info-row[data-v-2a110526]:last-child{margin-bottom:0}.info-label[data-v-2a110526]{font-size:%?28?%;color:#8c8c8c;flex-shrink:0;width:%?150?%}.info-value[data-v-2a110526]{font-size:%?28?%;color:#262626;flex:1;font-weight:500}\r\n/* 右侧箭头 */.case-arrow[data-v-2a110526]{position:absolute;right:%?36?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:%?50?%;height:%?50?%;border-radius:50%;display:flex;align-items:center;justify-content:center}\r\n/* 无数据提示 */.empty-container[data-v-2a110526]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?100?% 0;background-color:#fff;border-radius:%?20?%;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.06);-webkit-animation:fade-in-data-v-2a110526 .4s ease;animation:fade-in-data-v-2a110526 .4s ease}.empty-text[data-v-2a110526]{font-size:%?28?%;color:#999;margin-top:%?24?%}\r\n/* 悬浮搜索按钮 */.search-btn[data-v-2a110526]{position:fixed;right:%?40?%;bottom:%?120?%;width:%?110?%;height:%?110?%;border-radius:50%;background:linear-gradient(135deg,#07ac7c,#05d69b);display:flex;align-items:center;justify-content:center;box-shadow:0 %?6?% %?20?% rgba(7,172,124,.3);z-index:99;transition:all .2s ease}.search-btn[data-v-2a110526]:active{-webkit-transform:scale(.95);transform:scale(.95);box-shadow:0 %?4?% %?10?% rgba(7,172,124,.2)}.loading-more[data-v-2a110526]{width:100%;height:%?60?%;display:flex;justify-content:center;align-items:center;padding:%?20?% 0;color:#999;font-size:%?28?%}\r\n/* 底部间距 */.bottom-space[data-v-2a110526]{height:%?120?%}',""]),a.exports=t},ef81:function(a,t,e){var o=e("d1a87");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[a.i,o,""]]),o.locals&&(a.exports=o.locals);var i=e("967d").default;i("4c109d70",o,!0,{sourceMap:!1,shadowMode:!1})}}]);