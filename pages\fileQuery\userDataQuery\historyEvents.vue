<template>
	<view class="history-events-content">
		<!-- 内容区域 - 历史事件 -->
		<scroll-view class="content-section" scroll-y="true" refresher-enabled :refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh" :style="{ height: contentHeight + 'px' }">
			<!-- 加载中提示 -->
			<!-- <view class="loading-container" v-if="isLoading && !isRefreshing">
				<u-loading-icon mode="circle" size="28" color="#07ac7c"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view> -->

			<!-- 历史事件列表 -->
			<view class="events-list" v-if="!isLoading || isRefreshing">
				<!-- 遍历事件类型 -->
				<view class="event-category" v-for="(category, categoryIndex) in eventsData"
					:key="'category-' + categoryIndex">
					<!-- 事件类型标题和计数 -->
					<view class="category-header">
						<view class="category-title">
							<text>{{ getEventName(category.eventType) }}</text>
						</view>
						<view class="category-right">
							<text class="event-count">{{ category.total }}次</text>
							<u-icon name="arrow-right" size="14" color="#262626"></u-icon>
						</view>
					</view>

					<!-- 事件记录列表 - 始终显示所有记录 -->
					<view class="event-records">
						<!-- 遍历事件记录 -->
						<view class="event-record" v-for="(record,recordIndex) in category.eventList"
							:key="'record-'+recordIndex">
							<!-- 事件记录标题 -->
							<view class="record-header">
								<text class="record-index">上 <text class="record-color">{{ record.rowNum }}</text>
									次</text>
							</view>

							<!-- 事件记录详情 - 按照图2布局 -->
							<view class="record-details">
								<view class="record-item">
									<text class="item-label">发生时间:</text>
									<text class="item-value">{{ record.eventSt }}</text>
								</view>
								<view class="record-item">
									<text class="item-label">结束时间:</text>
									<text class="item-value">{{ record.eventEt }}</text>
								</view>
								<!-- <template>
									<view class="record-item">
										<text class="item-label">发生前正向有功总电能:</text>
										<text class="item-value">{{ record.stPapR }}KW.h</text>
									</view>
									<view class="record-item">
										<text class="item-label">发生后正向有功总电能:</text>
										<text class="item-value">{{ record.etPapR }}KW.h</text>
									</view>
								</template> -->
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 无数据提示 -->
			<view class="empty-container" v-if="eventsData.length === 0">
				<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
			</view>

			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		bizCode,
		url,
		getUUID,
		getCurrentTimestamp,
		port
	} from '@/static/commonJs/util.js'
	export default {
		props: {
			queryParams: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				params: {
					pageNum: 1,
					pageSize: 10
				},
				isRefreshing: false,
				isLoading: true,
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44, // 保持导航栏高度不变（px）
				tabNavHeight: 50, // 保持标签导航高度不变（px）
				// 历史事件数据
				eventsData: [],
				isMockData: true,
				token:null
			};
		},
		watch: {
			// 添加queryParams监听，当查询参数变化时重新加载数据
			queryParams: {
				handler(newVal, oldVal) {
					console.log('historyEvents - 查询参数变更:', newVal);
					// 检查参数是否真的发生了变化
					const isChanged = JSON.stringify(newVal) !== JSON.stringify(oldVal);
					if (isChanged) {
						// 重新加载数据
						this.loadEventsData();
					}
					
				},
				deep: true // 深度监听对象内部属性变化
			}
		},
		onLoad() {
			this.loadEventsData();
		},
		mounted() {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
			this.loadEventsData();
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					vm.token = data?.token
				});
			},
			getEventName(type) {
				let eventObj = {
					'01': '电能表开表盖',
					'02': '电能表掉电',
					'03': '电能表失流',
					'04': '电能表失压',
					'05': '电能表开端钮盖'
				}
				return eventObj[type]
			},
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				// 获取屏幕宽度，用于rpx转px的计算
				const screenWidth = systemInfo.screenWidth;

				// 状态栏高度保持px单位，但需要转换为rpx进行内容高度计算
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},

			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}

				// 计算顶部高度（状态栏 + 导航栏 + 标签导航）- 单位为px
				const topHeightPx = this.statusBarHeight + this.navbarHeight + this.tabNavHeight;

				// 将内容高度从px转换为rpx (乘以2是因为1px = 2rpx的一般转换比例)
				this.contentHeight = (this.windowHeight - topHeightPx) * 2;
			},

			// 获取有效的查询参数（优先使用queryParams对象，其次从本地存储获取）
			getEffectiveQueryParams() {
				// 如果有传入的queryParams，优先使用
				if (this.queryParams && Object.keys(this.queryParams).length > 0) {
					return {
						...this.queryParams
					};
				}

				// 否则，尝试从本地存储获取
				const savedQueryParams = uni.getStorageSync('userDataQueryParams');
				if (savedQueryParams && Object.keys(savedQueryParams).length > 0) {
					return {
						...savedQueryParams
					};
				}

				// 如果都没有，返回空对象
				return {};
			},

			// 加载历史事件数据
			loadEventsData() {
				// 如果是下拉刷新，不设置isLoading，避免闪烁
				if (!this.isRefreshing) {
					this.isLoading = true;
				}
				uni.showLoading({
					title: '加载中...'
				})
				const requestParams = this.getEffectiveQueryParams();
				console.log('获取历史事件参数', requestParams)
				let params = {
					"meterAssetNo": requestParams.meterAssetNo,
					"statDateStart": requestParams.dateRange[0],
					"statDateEnd": requestParams.dateRange[1],
					"pageNum": this.params.pageNum,
					"pageSize": this.params.pageSize
				}
				if (this.isMockData) {
					this.eventsData = [{
							"eventType": "01",
							"total":"1",
							"eventList": [
								{
									"eventSt": "2025-05-21 09:09:04",
									"eventEt": "2025-05-21 09:36:52",
									"stPapR": null,
									"etPapR": null,
									"stI": null,
									"meterAssetNo": "110021783135",
									"custNo": "3100124057301",
									"rowNum": 1
								}
							]
						},
						{
							"eventType": "05",
							"total":"1",
							"eventList": [
								{
									"eventSt": "2024-10-25 13:48:52",
									"eventEt": "2024-11-08 11:50:08",
									"stPapR": null,
									"etPapR": null,
									"stI": null,
									"meterAssetNo": "6000466386",
									"custNo": "3101379202688",
									"rowNum": 1
								}
							]
							
						}
					]
					this.isLoading = false;
					setTimeout(() => {
						this.isRefreshing = false;
					}, 1000)
					uni.hideLoading()
				} else {
					uni.request({
						url: `http://127.0.0.1:${port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': this.token
						},
						data: {
							token: this.token,
							method: "PutHuaYun",
							uri: url,
							data: JSON.stringify({
								"bizCode": bizCode,
								"espFlowId": getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": getCurrentTimestamp(),
								"espInformation": {
									"service": "DtsUserController",
									"method": "getMeterAbnor",
									"data": params,
								}
							})
						},
						success: (res) => {
							console.log(res)
							if (res && res.data.Tag === 1) {
								const rtnData = res.data.Data.espInformation;
								if (rtnData && rtnData.code == 200) {
									this.eventsData = rtnData.data;
									this.isLoading = false;
									if (this.isRefreshing) {
										this.isRefreshing = false;
									}
								}else{
									uni.showToast({
										title: '暂无数据',
										icon: 'none',
										duration: 2000
									});
								}
							}else{
								uni.showToast({
									title: '暂无数据',
									icon: 'none',
									duration: 2000
								});
							}
							uni.hideLoading()
						},
						fail: (error) => {
							this.isLoading = false;
							if (this.isRefreshing) {
								this.isRefreshing = false;
							}
							uni.showToast({
								title: '暂无数据',
								icon: 'none',
								duration: 2000
							});
							uni.hideLoading()
						}
					});
				}

			},
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				this.loadEventsData();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.history-events-content {
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
		position: relative;
		width: 100%;
		height: 100%;
	}

	/* 内容区域 */
	.content-section {
		box-sizing: border-box;
		background-color: #f5f5f5;
		padding: 20rpx 30rpx;
		-webkit-overflow-scrolling: touch;
		/* 增强iOS滚动体验 */
		overflow-y: auto;
		width: 100%;
	}

	/* 加载中提示 */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;
		margin-bottom: 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #8c8c8c;
		margin-top: 20rpx;
		text-align: center;
	}

	/* 历史事件列表 */
	.events-list {
		margin-bottom: 30rpx;
	}

	/* 事件类别 */
	.event-category {
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
		overflow: hidden;
		animation: fade-in 0.3s ease;
	}

	@keyframes fade-in {
		from {
			opacity: 0;
			transform: translateY(10rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 类别标题 */
	.category-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 2rpx solid #f0f0f0;
	}

	.category-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #262626;
	}

	.category-right {
		display: flex;
		align-items: center;
	}

	.event-count {
		font-size: 28rpx;
		color: #262626;
		margin-right: 10rpx;
		font-weight: normal;
	}

	/* 事件记录 */
	.event-records {
		padding: 0;
		background-color: #fff;
	}

	.event-record {
		padding: 30rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.event-record:last-child {
		border-bottom: none;
	}

	/* 记录标题 */
	.record-header {
		margin-bottom: 20rpx;
		padding-bottom: 10rpx;
	}

	.record-index {
		font-size: 28rpx;
		font-weight: 600;
	}

	.record-color {
		color: #07ac7c;
		font-size: 32rpx;
		font-weight: 600;
		margin: 0 4rpx;
	}

	/* 记录详情 */
	.record-details {
		padding-left: 0;
	}

	.record-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 16rpx;
		font-size: 28rpx;
		line-height: 1.5;
	}

	.record-item:last-child {
		margin-bottom: 0;
	}

	.item-label {
		color: #8c8c8c;
		text-align: left;
	}

	.item-value {
		color: #262626;
		text-align: right;
		max-width: 60%;
	}

	/* 无数据提示 */
	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		animation: fade-in 0.3s ease;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
		margin-top: 20rpx;
	}

	/* 底部间距 */
	.bottom-space {
		height: 40rpx;
	}
</style>