<template>
	<view class="query-search">
		<!-- 顶部导航区域 - 固定 -->
		<view class="header-section">
			<custom-navbar title="查询" :showBack="true"></custom-navbar>
		</view>
		
		<!-- 中间表单区域 - 可滚动 -->
		<scroll-view class="form-section" scroll-y :style="{ height: scrollViewHeight + 'px', top: safeAreaTop + 'px' }">
			<view class="form-content">
				<!-- 案例类型 -->
				<view class="form-item">
					<view class="form-label">案例类型</view>
					<view class="form-content">
						<u-subsection
							:list="caseTypes"
							:current="caseTypeIndex"
							@change="caseTypeChange"
							activeColor="#07ac7c"
							inactiveColor="#333333"
							mode="button"
							:animation="false"
							fontSize="28rpx"
							height="70rpx"
							buttonColor="#f5f5f5"
						></u-subsection>
					</view>
				</view>
				
				<!-- 案例名称 -->
				<view class="form-item">
					<view class="form-label">案例名称</view>
					<view class="form-content">
						<u-input
							v-model="searchForm.caseName"
							placeholder="请输入案例名称"
							border="surround"
							clearable
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
							@blur="handleInputBlur"
							@focus="handleInputFocus"
						></u-input>
					</view>
				</view>
				
				<!-- 案例时间 -->
				<view class="form-item">
					<view class="form-label">案例时间</view>
					<view class="form-content">
						<view class="date-input-wrapper" @tap="showDatePicker">
							<u-input
								v-model="dateRangeText"
								placeholder="请选择日期范围"
								border="surround"
								fontSize="30rpx"
								suffixIcon="calendar"
								readonly
								:customStyle="inputStyle"
								class="form-input"
							></u-input>
						</view>
					</view>
				</view>
				
				<!-- 编制单位 -->
				<view class="form-item">
					<view class="form-label">编制单位</view>
					<view class="form-content">
						<u-input
							v-model="searchForm.createOrg"
							placeholder="请输入编制单位"
							border="surround"
							clearable
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
							@blur="handleInputBlur"
							@focus="handleInputFocus"
						></u-input>
					</view>
				</view>
				
				<!-- 编制人员 -->
				<view class="form-item">
					<view class="form-label">编制人员</view>
					<view class="form-content">
						<u-input
							v-model="searchForm.creator"
							placeholder="请输入编制人员"
							border="surround"
							clearable
							fontSize="30rpx"
							:customStyle="inputStyle"
							class="form-input"
							@blur="handleInputBlur"
							@focus="handleInputFocus"
						></u-input>
					</view>
				</view>
				
				<!-- 预留空间，确保小屏幕上也能滚动到底部的所有内容 -->
				<view class="form-space"></view>
			</view>
		</scroll-view>
		
		<!-- 底部按钮区域 - 固定 -->
		<view class="footer-section">
			<view class="btn-group">
				<view class="btn btn-reset" @click="resetForm">
					<text>重置选择</text>
				</view>
				<view class="btn btn-confirm" @click="confirmSearch">
					<text>确定</text>
				</view>
			</view>
		</view>
		
		<!-- 日期选择器 -->
		<u-calendar
			:show="calendarShow"
			mode="range"
			:defaultDate="dateRange"
			@confirm="confirmDate"
			@close="calendarShow = false"
			color="#07ac7c"
			startText="开始"
			endText="结束"
		></u-calendar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				navbarHeight: 44, // 导航栏固定高度，单位px
				headerHeight: 0, // 导航栏总高度，单位px
				footerHeight: 120, // 底部按钮区域高度，单位rpx
				windowHeight: 0,
				scrollViewHeight: 0,
				safeAreaTop: 0, // 顶部安全区域高度，单位px
				isKeyboardShow: false, // 键盘是否显示
				
				// 案例类型选项
				caseTypes: ['司法', '违窃'],
				caseTypeIndex: 0,
				
				// 搜索表单
				searchForm: {
					caseStatus: '',
					caseName: '',
					createOrg: '',
					creator: '',
					caseStartTime:"",
					caseEndTime:""
				},
				dateRange:[],
				
				// 日期选择器
				calendarShow: false,
				
				// 输入框样式
				inputStyle: {
					height: '80rpx',
					fontSize: '30rpx',
					padding: '0 24rpx',
					borderColor: '#e0e0e0',
					borderWidth: '1rpx',
					borderRadius: '16rpx',
					borderStyle: 'solid',
					color: '#333',
					backgroundColor: '#fff'
				},
				
				// 日期范围文本
				dateRangeText: ''
			};
		},
		onLoad() {
			// 获取状态栏高度等信息
			this.getStatusBarHeight();
			
			// 监听软键盘高度变化
			uni.onKeyboardHeightChange(res => {
				this.isKeyboardShow = res.height > 0;
				// 键盘高度变化时重新计算滚动区域高度
				this.calcScrollViewHeight();
			});
		},
		onReady() {
			// 计算滚动区域高度
			this.calcScrollViewHeight();
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
				
				// 计算头部总高度（导航栏）
				this.headerHeight = this.statusBarHeight + this.navbarHeight;
				this.safeAreaTop = this.headerHeight;
			},
			
			// 计算滚动区域高度
			calcScrollViewHeight() {
				// 底部区域高度从rpx转为px进行计算
				const footerHeightPx = uni.upx2px(this.footerHeight);
				this.scrollViewHeight = this.windowHeight - this.headerHeight - footerHeightPx;
			},
			
			// 处理输入框获取焦点
			handleInputFocus() {
				// 安卓端键盘弹出时，可能需要额外处理
				// #ifdef APP-ANDROID
				setTimeout(() => {
					this.calcScrollViewHeight();
				}, 300);
				// #endif
			},
			
			// 处理输入框失去焦点
			handleInputBlur() {
				// 安卓端键盘收起时，可能需要额外处理
				// #ifdef APP-ANDROID
				setTimeout(() => {
					this.calcScrollViewHeight();
				}, 300);
				// #endif
			},
			
			// 案例类型切换
			caseTypeChange(index) {
				this.caseTypeIndex = index;
				if (index === 0) {
					this.searchForm.caseStatus = '01';
				} else {
					this.searchForm.caseStatus = '02'
				}
			},
			
			// 显示日期选择器
			showDatePicker() {
				this.calendarShow = true;
			},
			
			// 确认日期
			confirmDate(value) {
				this.dateRange = value;
				
				// 格式化日期为YYYY-MM-DD
				if (value && value.length > 0) {
					const formatDate = date => {
						if (!date) return '';
						const d = new Date(date);
						const year = d.getFullYear();
						const month = String(d.getMonth() + 1).padStart(2, '0');
						const day = String(d.getDate()).padStart(2, '0');
						return `${year}-${month}-${day}`;
					};
					
					const startDate = formatDate(value[0]);
					const endDate = formatDate(value[value.length - 1]);
					
					// 设置日期范围文本
					this.dateRangeText = `${startDate} 至 ${endDate}`;
					
					// 同时设置开始和结束日期，保持兼容性
					this.searchForm.caseStartTime = this.dateRange[0];
					this.searchForm.caseEndTime = this.dateRange[1];
				} else {
					this.dateRangeText = '';
					this.searchForm.caseStartTime = '';
					this.searchForm.caseEndTime = '';
				}
				
				this.calendarShow = false;
			},
			
			// 重置表单
			resetForm() {
				this.caseTypeIndex = 0;
				this.searchForm = {
					caseStatus: '01',
					caseName: '',
					caseStartTime: '',
					caseEndTime: '',
					createOrg: '',
					creator: ''
				};
				this.dateRange = [];
				this.dateRangeText = '';
			},
			
			// 确认搜索
			confirmSearch() {
				// 构建搜索参数
				const searchParams = {
					...this.searchForm
				};
				
				// 存储搜索参数
				uni.setStorageSync('caseSearchParams', searchParams);
				
				// 返回列表页面，并传递参数
				uni.navigateBack({
					delta: 1,
					success: () => {
						// 通过事件总线通知列表页面进行搜索
						uni.$emit('caseSearch', searchParams);
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #f5f5f5;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.query-search {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
}

/* 顶部导航区域 */
.header-section {
	background-color: #fff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

/* 中间表单区域 */
.form-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 120rpx; /* 底部按钮区域高度 */
	box-sizing: border-box;
	background-color: #f5f5f5;
	z-index: 99;
}

.form-content {
	padding: 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
}


.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 30rpx;
	font-weight: 400;
	color: #262626;
}

.form-content {
	width: 100%;
}

.form-input {
	height: 80rpx;
}

.date-input-wrapper {
	width: 100%;
	position: relative;
	cursor: pointer;
}

/* 底部按钮区域 */
.footer-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	z-index: 100;
	border-top: 1rpx solid #f5f5f5;
	padding-bottom: env(safe-area-inset-bottom);
}

/* 按钮组样式 */
.btn-group {
	display: flex;
	padding: 20rpx 40rpx;
}

.btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: 400;
	transition: all 0.2s;
}

.btn-reset {
	background-color: transparent;
	color: #07ac7c;
	border: none;
	margin-right: 40rpx;
}

.btn-reset:active {
	opacity: 0.8;
}

.btn-confirm {
	background-color: #07ac7c;
	color: #FFFFFF;
}

.btn-confirm:active {
	background-color: #33a085;
}

/* 为小屏幕预留滚动空间 */
.form-space {
	height: 40rpx;
}

/* 修改导航栏样式 */
/deep/ .custom-navbar {
	background-color: #fff !important;
	box-shadow: none !important;
}

/deep/ .navbar-left .svg-icon {
	color: #000000 !important;
}

/deep/ .navbar-title {
	color: #000000 !important;
}

/* 自定义u-input样式 */
/deep/ .u-input {
	height: 80rpx;
}

/deep/ .u-input__input {
	height: 92rpx;
	font-size: 30rpx;
	color: #333;
	padding: 0 24rpx;
}

/deep/ .u-input--border {
	border-color: #e0e0e0;
	border-width: 1rpx;
	border-radius: 16rpx;
	height: 88rpx;
}

/deep/ .u-input__placeholder-style {
	color: #cccccc;
	font-size: 30rpx;
}

/* 自定义u-subsection样式 */
/deep/ .u-subsection {
	margin-top: 10rpx;
}

/* 解决安卓输入框问题 */
/deep/ .u-input__content__field {
	display: flex;
	align-items: center;
}
</style>
