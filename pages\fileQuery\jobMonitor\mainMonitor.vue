<template>
	<!-- 列表 -->
	<view class="mainContainer">
		<custom-navbar title="工单整体监控"></custom-navbar>
		<u-tabs :list="list1" @click="clicktabs" :scrollable="false"
			style="border-bottom: 1px solid lightgrey;"></u-tabs>

		<view class="chashiContainer" v-show="chashiContainershow">
			<!-- 查询 -->
			<view class="searchContainer">
				<view class="calendarContainer">
					<uni-datetime-picker v-model="queryDateStart" type="date" placeholder="开始日期" :end="queryDateEnd"
						@change="handleStartDate" style="background-color: white;">
					</uni-datetime-picker>
					<view style="margin: 0 10rpx;">-</view>
					<uni-datetime-picker v-model="queryDateEnd" type="date" placeholder="结束日期" :start="queryDateStart"
						@change="handleEndDate" style="background-color: white;">
					</uni-datetime-picker>
				</view>
				<view class="buttonContainer">
					<u-button type="primary" style="color: #fcfefd; width: 350rpx;" color="#07ac7c" @click="search"
						text="查询"></u-button>
					<u-button type="default" style="border: 1px solid lightgray;background-color: white;width: 350rpx;"
						text="重置" @click="reset"></u-button>
				</view>
			</view>

			<!-- 本单位查实情况 -->
			<view class="tablecontainer" style="margin-top: 20rpx;">
				<view class="tabletitle">
					<view class="titletag"></view>
					<view class="titletext">
						本单位查实情况
					</view>
				</view>
				<view class="" style="padding: 20rpx; font-size: 30rpx;">
					<text>查实率：</text>
					<text style="color: red;">{{stealRate}}%</text>
				</view>
				<view class="rowcontainer" style="padding: 20rpx;">
					<view class="">
						<text>查实总数：</text>
						<text style="color: #2bb98f;" @click="gotoOrderDetail">{{total}}</text>
					</view>
					<view class="" style="width: 300rpx;">
						<text>归档工单数：</text>
						<text style="color: #2bb98f;" @click="gotoOrderDetail">{{filedNum}}</text>
					</view>
				</view>
				<view class="rowcontainer" style="padding: 20rpx;">
					<view class="">
						<text>查实窃电数：</text>
						<text style="color: #2bb98f;" @click="gotoOrderDetail">{{stealNum}}</text>
					</view>
					<view class="" style="width: 300rpx;">
						<text>违约用电数：</text>
						<text style="color: #2bb98f;" @click="gotoOrderDetail">{{defaultNum}}</text>
					</view>
				</view>
				<view class="rowcontainer" style="padding: 20rpx;">
					<view class="">
						<text>工单作废数：</text>
						<text>{{deleteNum}}</text>
					</view>
					<view class="" style="width: 300rpx;">
						<text>工单终止数：</text>
						<text>{{cancelNum}}</text>
					</view>
				</view>
			</view>
			<!-- 柱状图 -->
			<view class="charts-box" v-if="chartMain">
				<view class="tabletitle">
					<view class="titlecontainer">
						<view class="titletag"></view>
						<view class="titletext">
							下级单位查实情况
						</view>
					</view>
					<uni-icons @click="shownextList" type="tune" size="20" color="gray" />
				</view>
				<view v-if="dataShow"
					style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
					<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
					<view style="color: gainsboro;">
						数据为空
					</view>
				</view>
				<qiun-data-charts v-if="chartShow" type="column" :opts="opts" :chartData="chartData" :ontouch="true" />
			</view>
			<view v-if="nextshow" style="height: 600rpx;overflow-y: auto;margin-top: 40rpx;">
				<view class="tablecontainer" v-for="(item,index) in chashilist" :key="index">
					<view class="tabletitle" style="display: flex;justify-content: space-between;align-items: center;">
						<view style="display: flex;align-items: center;justify-content: left;">
							<view class="titletag"></view>
							<view class="titletext">
								{{item.mgtOrgName}}查实情况
							</view>
						</view>
						<uni-icons @click="shownextList" type="tune" size="20" color="gray" />
					</view>
					<view class="" style="padding: 20rpx; font-size: 30rpx;">
						<text>查实率：</text>
						<text style="color: red;">{{item.stealRate}}%</text>
					</view>
					<view class="rowcontainer" style="padding: 20rpx;">
						<view class="">
							<text>查实总数：</text>
							<text style="color: #2bb98f;" @click="gotoOrderDetail">{{item.total}}</text>
						</view>
						<view class="" style="width: 300rpx;">
							<text>归档工单数：</text>
							<text style="color: #2bb98f;" @click="gotoOrderDetail">{{item.filedNum}}</text>
						</view>
					</view>
					<view class="rowcontainer" style="padding: 20rpx;">
						<view class="">
							<text>查实窃电数：</text>
							<text style="color: #2bb98f;" @click="gotoOrderDetail">{{item.stealNum}}</text>
						</view>
						<view class="" style="width: 300rpx;">
							<text>违约用电数：</text>
							<text style="color: #2bb98f;" @click="gotoOrderDetail">{{item.defaultNum}}</text>
						</view>
					</view>
					<view class="rowcontainer" style="padding: 20rpx;">
						<view class="">
							<text>工单作废数：</text>
							<text>{{item.deleteNum}}</text>
						</view>
						<view class="" style="width: 300rpx;">
							<text>工单终止数：</text>
							<text>{{item.cancelNum}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="chuliContainer" v-show="chuliContainershow">
			<view class="" style="padding: 20rpx;">
				<!-- 查询 -->
				<view class="">
					<view class="calendarContainer">
						<uni-datetime-picker v-model="queryDateStart" type="date" placeholder="开始日期" :end="queryDateEnd"
							@change="handleStartDate" style="background-color: white;">
						</uni-datetime-picker>
						<view style="margin: 0 10rpx;">-</view>
						<uni-datetime-picker v-model="queryDateEnd" type="date" placeholder="结束日期"
							:start="queryDateStart" @change="handleEndDate" style="background-color: white;">
						</uni-datetime-picker>
					</view>
					<view class="subsectionContainer" v-if="showsub">
						<u-subsection :list="subsectionlist" :current="curNow" mode="button"
							@change="sectionChange"></u-subsection>
					</view>
				</view>
				<view class="buttonContainer">
					<u-button type="primary" style="color: #fcfefd;width: 350rpx;" color="#07ac7c" text="查询"
						@click="chuliSearch"></u-button>
					<u-button type="default" style="border: 1px solid lightgray;width: 350rpx;" text="重置"
						@click="resetchuli"></u-button>
				</view>
			</view>

			<!-- 本单位查实情况 -->

			<view class="tablecontainer" style="margin-top: 20rpx;">
				<view class="tabletitle">
					<view class="titletag"></view>
					<view class="titletext">
						本单位处理情况
					</view>
				</view>
				<view class="rowcontainer" style="padding: 20rpx;">
					<view class="">
						<text>工单总数：</text>
						<text style="color: #2bb98f;" @click="gotoOrderDetail">{{total}}</text>
					</view>
					<view class="" style="width: 300rpx;">
						<text>归档工单：</text>
						<text style="color: #2bb98f;" @click="gotoOrderDetail">{{filed}}</text>
					</view>
				</view>
				<view class="rowcontainer" style="padding: 20rpx;">
					<view class="">
						<text>待派工数：</text>
						<text style="color: red;" @click="gotoOrderDetail">{{undispatch}}</text>
					</view>
					<view class="" style="width: 300rpx;">
						<text>待检查数：</text>
						<text style="color: red;" @click="gotoOrderDetail">{{unchecked}}</text>
					</view>
				</view>
				<view class="rowcontainer" style="padding: 20rpx;">
					<view class="">
						<text>待处理数：</text>
						<text style="color: red;" @click="gotoOrderDetail">{{unHandle}}</text>
					</view>
					<view class="" style="width: 300rpx;">
						<text>待收费数：</text>
						<text style="color: red;" @click="gotoOrderDetail">{{unCharge}}</text>
					</view>
				</view>
			</view>

			<!-- 柱状图 -->
			<view class="charts-box" v-if="chartMain2">
				<view class="tabletitle">
					<view class="" style="display: flex;justify-content: left;align-items: center;">
						<view class="titletag"></view>
						<view class="titletext">
							下级单位处理情况
						</view>
					</view>
					<uni-icons @click="shownextList2" type="tune" size="20" color="gray" />
				</view>
				<view v-if="dataShow2"
					style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
					<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
					<view style="color: gainsboro;">
						数据为空
					</view>
				</view>
				<qiun-data-charts v-if="chartShow2" type="column" :opts="opts2" :chartData="chartData2"
					:ontouch="true" />
			</view>
			<view class="" style="height: 600rpx;overflow-y: auto;margin-top: 40rpx;" v-if="nextshow2">
				<view class="tablecontainer" v-for="(item,index) in chulilist" :key="index">
					<view class="tabletitle" style="justify-content: space-between;">
						<view style="display: flex;justify-content: left;align-items: center;">
							<view class="titletag"></view>
							<view class="titletext">
								{{item.mgtOrgName}}处理情况
							</view>
						</view>
						<uni-icons @click="shownextList2" type="tune" size="20" color="gray" />
					</view>
					<view class="rowcontainer" style="padding: 20rpx;">
						<view class="">
							<text>工单总数：</text>
							<text style="color: #2bb98f;" @click="gotoOrderDetail">{{item.total}}</text>
						</view>
						<view class="" style="width: 300rpx;">
							<text>归档工单：</text>
							<text style="color: #2bb98f;" @click="gotoOrderDetail">{{item.filed}}</text>
						</view>
					</view>
					<view class="rowcontainer" style="padding: 20rpx;">
						<view class="">
							<text>待派工数：</text>
							<text style="color: red;" @click="gotoOrderDetail">{{item.undispatch}}</text>
						</view>
						<view class="" style="width: 300rpx;">
							<text>待检查数：</text>
							<text style="color: red;" @click="gotoOrderDetail">{{item.unchecked}}</text>
						</view>
					</view>
					<view class="rowcontainer" style="padding: 20rpx;">
						<view class="">
							<text>待处理数：</text>
							<text style="color: red;" @click="gotoOrderDetail">{{item.unHandle}}</text>
						</view>
						<view class="" style="width: 300rpx;">
							<text>待收费数：</text>
							<text style="color: red;" @click="gotoOrderDetail">{{item.unCharge}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import qiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
	import utils from '@/static/commonJs/util.js'
	export default {
		components: {
			qiunDataCharts
		},
		data() {
			return {
				chartMain: true,
				chartMain2: true,
				nextshow2: false,
				nextshow: false,
				testdev: true,
				chartShow: false,
				dataShow: true,
				chartShow2: false,
				dataShow2: true,
				showsub: false,
				unCharge: "",
				unHandle: "",
				unchecked: "",
				undispatch: "",
				filed: "",
				stealType: "00",
				chulilist: [],
				chashilist: [],
				cancelNum: "",
				deleteNum: "",
				defaultNum: "",
				stealNum: "",
				filedNum: "",
				total: "",
				stealRate: "",
				mgtOrgCode: "31102",
				queryDateStart: "2025-01-01",
				queryDateEnd: "2026-06-26",
				subsectionlist: ['全部', '窃电', '违约用电'],
				curNow: 0,
				chuliContainershow: false,
				chashiContainershow: true,
				inputdate: "2025-06-13 - 2025-06-29",
				mode: 'range',
				list1: [{
						name: '查实',
					},
					{
						name: '处理',
					}
				],
				closeOnClick2: true,
				taiqunum: '',
				value: '',
				chartData: {},
				chartData2: {},
				opts: {
					color: ["#3CA272", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					legend: {
						show: false
					},
					padding: [15, 15, 15, 5],
					touchMoveLimit: 24,
					enableScroll: true,
					xAxis: {
						disableGrid: true,
						scrollShow: true,
						itemCount: 4,
					},
					yAxis: {
						data: [{
							min: 0
						}]
					},
					extra: {
						column: {
							type: "stack",
							width: 30,
							activeBgColor: "#000000",
							activeBgOpacity: 0.08,
							labelPosition: "center",
							labelShow: false,
							// linearType:"custom"
						}
					}
				},
				opts2: {
					color: ["#3CA272", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					legend: {
						show: false
					},
					padding: [15, 15, 15, 5],
					touchMoveLimit: 24,
					enableScroll: true,
					xAxis: {
						disableGrid: true,
						scrollShow: true,
						itemCount: 4,
					},
					yAxis: {
						data: [{
							min: 0
						}]
					},
					extra: {
						column: {
							type: "stack",
							width: 30,
							activeBgColor: "#000000",
							activeBgOpacity: 0.08,
							labelPosition: "center"
						}
					}
				}
			}
		},
		onLoad() {},
		onReady() {
			// this.init();
			this.search();
		},
		methods: {
			shownextList2() {
				this.chartMain2 = !this.chartMain2;
				this.nextshow2 = !this.nextshow2;
			},
			shownextList() {
				this.chartMain = !this.chartMain;
				this.nextshow = !this.nextshow;
			},
			async init() {
				try {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryUserInfoByBody",
									"data": {
										"operatorId": utils.userInfo?.nameCode,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					if (code === 200) {
						this.mgtOrgCode = data.mgtOrgCode;
					}
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				}
			},
			reset() {
				this.queryDateStart = ""
				this.queryDateEnd = ""
			},
			resetchuli() {
				this.queryDateStart = ""
				this.queryDateEnd = ""
				this.stealType = "00"
				this.curNow = 0
			},
			handleStartDate(value) {
				this.queryDateStart = value
				if (this.queryDateEnd && new Date(this.queryDateEnd) < new Date(value)) {
					this.queryDateEnd = ''
				}
			},
			handleEndDate(value) {
				this.queryDateEnd = value
			},
			// 查实搜索
			async search() {
				if (this.testdev) {
					this.chartShow = true;
					this.dataShow = false;
					this.stealRate = 50.24;
					this.total = 1037;
					this.filedNum = 793;
					this.stealNum = 521;
					this.defaultNum = 4;
					this.deleteNum = 0;
					this.cancelNum = 0;
					this.chashilist = [{
							mgtOrgName: '市区供电公司',
							stealRate: '56.8',
							total: '125',
							filedNum: '112',
							stealNum: '71',
							deleteNum: '0',
							defaultNum: '2',
							cancelNum: '0'
						},
						{
							mgtOrgName: '市南供电公司',
							stealRate: '37.5',
							total: '224',
							filedNum: '190',
							stealNum: '84',
							deleteNum: '0',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '浦东供电公司',
							stealRate: '46.83',
							total: '205',
							filedNum: '134',
							stealNum: '96',
							deleteNum: '0',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '崇明供电公司',
							stealRate: '23.08',
							total: '13',
							filedNum: '3',
							stealNum: '3',
							deleteNum: '1',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '长兴供电公司',
							stealRate: '100',
							total: '2',
							filedNum: '1',
							stealNum: '2',
							deleteNum: '0',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '市北供电公司',
							stealRate: '63.49',
							total: '315',
							filedNum: '234',
							stealNum: '200',
							deleteNum: '0',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '嘉定供电公司',
							stealRate: '40',
							total: '35',
							filedNum: '27',
							stealNum: '14',
							deleteNum: '0',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '奉贤供电公司',
							stealRate: '56.16',
							total: '43',
							filedNum: '29',
							stealNum: '22',
							deleteNum: '0',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '松江供电公司',
							stealRate: '37.14',
							total: '35',
							filedNum: '27',
							stealNum: '13',
							deleteNum: '0',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '金山供电公司',
							stealRate: '58.82',
							total: '17',
							filedNum: '15',
							stealNum: '10',
							deleteNum: '1',
							defaultNum: '0',
							cancelNum: '0'
						},
						{
							mgtOrgName: '青浦供电公司',
							stealRate: '26.09',
							total: '23',
							filedNum: '21',
							stealNum: '6',
							deleteNum: '0',
							defaultNum: '0',
							cancelNum: '0'
						},
					];
					let mgtOrgNameArray = ["市区", "市南", "浦东", "崇明", "长兴", "市北", "嘉定", "奉贤", "松江", "金山", "青浦"];
					let filedNumArray = [112, 190, 134, 3, 1, 234, 27, 29, 27, 15, 21];
					let stealNumArray = [71, 84, 96, 3, 2, 200, 14, 22, 13, 10, 6];
					let defaultNumArray = [2, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0];
					let resdata = {
						categories: mgtOrgNameArray,
						series: [{
								name: "归档工单数",
								textColor: "#FFFFFF",
								data: filedNumArray,
								labelShow: false
								// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							},
							{
								name: "窃电工单数",
								textColor: "#FFFFFF",
								data: stealNumArray,
								labelShow: false
								// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							},
							{
								name: "违约用电",
								textColor: "#FFFFFF",
								data: defaultNumArray,
								labelShow: false
								// data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							}
						]
					};
					this.chartData = JSON.parse(JSON.stringify(resdata));
				} else {
					// 调用后端登录接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "AppOrderController",
										"method": "queryCheckedOrderStatus",
										"data": {
											"mgtOrgCode": this.mgtOrgCode,
											"queryDateEnd": this.queryDateEnd,
											"queryDateStart": this.queryDateStart
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.chartShow = true;
							this.dataShow = false;
							this.stealRate = data.stealRate;
							this.total = data.total;
							this.filedNum = data.filedNum;
							this.stealNum = data.stealNum;
							this.defaultNum = data.defaultNum;
							this.deleteNum = data.deleteNum;
							this.cancelNum = data.cancelNum;
							this.chashilist = data.list;
							let mgtOrgNameArray = [];
							let filedNumArray = [];
							let stealNumArray = [];
							let defaultNumArray = [];
							data.list.forEach(item => {
								mgtOrgNameArray.push(item.mgtOrgName.substring(0, 2))
								if (item.filedNum == null) {
									filedNumArray.push(0)
								} else {
									filedNumArray.push(Number(item.filedNum))
								}
								if (item.stealNum == null) {
									stealNumArray.push(0)
								} else {
									stealNumArray.push(Number(item.stealNum))
								}
								if (item.defaultNum == null) {
									defaultNumArray.push(0)
								} else {
									defaultNumArray.push(Number(item.defaultNum))
								}
							})
							let resdata = {
								categories: mgtOrgNameArray,
								series: [{
										name: "归档工单数",
										textColor: "#FFFFFF",
										data: filedNumArray,
										labelShow: false
										// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									},
									{
										name: "窃电工单数",
										textColor: "#FFFFFF",
										data: stealNumArray,
										labelShow: false
										// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									},
									{
										name: "违约用电",
										textColor: "#FFFFFF",
										data: defaultNumArray,
										labelShow: false
										// data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									}
								]
							};
							this.chartData = JSON.parse(JSON.stringify(resdata));
						} else {
							this.chartShow = false;
							this.dataShow = true;
						}
					} catch (error) {
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
						console.log(error)
						this.chartShow = false;
						this.dataShow = true;
					}
				}
			},
			// 处理搜索
			async chuliSearch() {
				if (this.testdev) {
					this.chartShow2 = true;
					this.dataShow2 = false;
					this.chulilist = [{
							mgtOrgName: '市区供电公司',
							total: '125',
							filed: '112',
							undispatch: '0',
							unchecked: '3',
							unHandle: '10',
							unCharge: '0'
						},
						{
							mgtOrgName: '市南供电公司',
							total: '224',
							filed: '190',
							undispatch: '4',
							unchecked: '0',
							unHandle: '30',
							unCharge: '0'
						},
						{
							mgtOrgName: '浦东供电公司',
							stealRate: '46.83',
							total: '205',
							filed: '134',
							undispatch: '5',
							unchecked: '56',
							unHandle: '10',
							cancelNum: '0'
						},
						{
							mgtOrgName: '崇明供电公司',
							total: '13',
							filed: '3',
							undispatch: '3',
							unchecked: '5',
							unHandle: '2',
							unCharge: '0'
						},
						{
							mgtOrgName: '长兴供电公司',
							total: '2',
							filed: '1',
							undispatch: '0',
							unchecked: '0',
							unHandle: '1',
							unCharge: '0'
						},
						{
							mgtOrgName: '市北供电公司',
							total: '315',
							filed: '234',
							undispatch: '0',
							unchecked: '37',
							unHandle: '44',
							unCharge: '0'
						},
						{
							mgtOrgName: '嘉定供电公司',
							total: '35',
							filed: '27',
							undispatch: '0',
							unchecked: '2',
							unHandle: '6',
							unCharge: '0'
						},
						{
							mgtOrgName: '奉贤供电公司',
							total: '43',
							filed: '29',
							undispatch: '0',
							unchecked: '5',
							unHandle: '9',
							unCharge: '0'
						},
						{
							mgtOrgName: '松江供电公司',
							total: '35',
							filed: '27',
							undispatch: '1',
							unchecked: '1',
							unHandle: '6',
							unCharge: '0'
						},
						{
							mgtOrgName: '金山供电公司',
							total: '17',
							filed: '15',
							undispatch: '0',
							unchecked: '0',
							unHandle: '2',
							unCharge: '0'
						},
						{
							mgtOrgName: '青浦供电公司',
							total: '23',
							filed: '21',
							undispatch: '0',
							unchecked: '0',
							unHandle: '2',
							unCharge: '0'
						},
					];
					this.total = 1037;
					this.filed = 793;
					this.unchecked = 109;
					this.undispatch = 13;
					this.unHandle = 122;
					this.unCharge = 0;
					let mgtOrgNameArray = ["市区", "市南", "浦东", "崇明", "长兴", "市北", "嘉定", "奉贤", "松江", "金山", "青浦"];
					let unChargeArray = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					let unHandleArray = [10, 30, 10, 2, 1, 44, 6, 9, 6, 2, 2];
					let uncheckedArray = [3, 0, 56, 5, 0, 37, 2, 5, 1, 0, 0];
					let undispatchArray = [0, 4, 5, 3, 0, 0, 0, 0, 1, 0, 0];
					let chulidata = {
						categories: mgtOrgNameArray,
						series: [{
								name: "待收费数",
								textColor: "#FFFFFF",
								data: unChargeArray,
								// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							},
							{
								name: "待处理数",
								textColor: "#FFFFFF",
								data: unHandleArray,
								// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							},
							{
								name: "待检查数",
								textColor: "#FFFFFF",
								data: uncheckedArray,
								// data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							},
							{
								name: "待派工数",
								textColor: "#FFFFFF",
								data: undispatchArray,
							}
						]
					};
					this.chartData2 = JSON.parse(JSON.stringify(chulidata));
				} else {
					// 调用后端登录接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "AppOrderController",
										"method": "queryHandleOrderStatus",
										"data": {
											"stealType": this.stealType,
											"mgtOrgCode": this.mgtOrgCode,
											"queryDateEnd": this.queryDateEnd,
											"queryDateStart": this.queryDateStart
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.chartShow2 = true;
							this.dataShow2 = false;
							this.chulilist = data.list;
							this.total = data.total;
							this.filed = data.filed;
							this.unchecked = data.unchecked;
							this.undispatch = data.undispatch;
							this.unHandle = data.unHandle;
							this.unCharge = data.unCharge;
							let mgtOrgNameArray = [];
							let unChargeArray = [];
							let unHandleArray = [];
							let uncheckedArray = [];
							let undispatchArray = [];
							data.list.forEach(item => {
								mgtOrgNameArray.push(item.mgtOrgName.substring(0, 2))
								if (item.unCharge == null) {
									unChargeArray.push(0)
								} else {
									unChargeArray.push(Number(item.unCharge))
								}
								if (item.unHandle == null) {
									unHandleArray.push(0)
								} else {
									unHandleArray.push(Number(item.unHandle))
								}
								if (item.unchecked == null) {
									uncheckedArray.push(0)
								} else {
									uncheckedArray.push(Number(item.unchecked))
								}
								if (item.undispatch == null) {
									undispatchArray.push(0)
								} else {
									undispatchArray.push(Number(item.undispatch))
								}
							})
							let chulidata = {
								categories: mgtOrgNameArray,
								series: [{
										name: "待收费数",
										textColor: "#FFFFFF",
										data: unChargeArray,
										// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									},
									{
										name: "待处理数",
										textColor: "#FFFFFF",
										data: unHandleArray,
										// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									},
									{
										name: "待检查数",
										textColor: "#FFFFFF",
										data: uncheckedArray,
										// data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									},
									{
										name: "待派工数",
										textColor: "#FFFFFF",
										data: undispatchArray,
									}
								]
							};
							this.chartData2 = JSON.parse(JSON.stringify(chulidata));
						} else {
							this.chartShow2 = false;
							this.dataShow2 = true;
						}
					} catch (error) {
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
						console.log(error)
						this.chartShow2 = false;
						this.dataShow2 = true;
					}
				}
			},
			gotoOrderDetail() {
				uni.navigateTo({
					url: `/pages/fileQuery/jobMonitor/orderDetail`
				});
			},
			sectionChange(index) {
				this.curNow = index;
				if (index == 0) {
					this.stealType = "00"
				} else if (index == 1) {
					this.stealType = "01"
				} else if (index == 2) {
					this.stealType = "02"
				}
			},
			clicktabs(item) {
				if (item.name == "查实") {
					this.chashiContainershow = true;
					this.chuliContainershow = false;
					this.showsub = false;
					this.search();
				} else if (item.name == "处理") {
					this.chashiContainershow = false;
					this.chuliContainershow = true;
					this.showsub = true;
					this.chuliSearch();
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	page {
		background-color: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	.mainContainer {
		::v-deep .u-popup__content .u-popup__content__close {
			display: none !important;
		}

		.chashiContainer {
			.searchContainer {
				padding: 20rpx 20rpx;

				.buttonContainer {
					padding-top: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
				}
			}

			.calendarContainer {
				display: flex;
				align-items: center;
			}

			.charts-box {
				background-color: white;
				border-radius: 20rpx;
				padding: 20rpx;
				margin: 40rpx 20rpx;

				.tabletitle {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 10rpx 0 20rpx 10rpx;

					.titlecontainer {
						display: flex;
						justify-content: left;
						align-items: center;

						.titletag {
							width: 10rpx;
							height: 30rpx;
							background-color: #07ac7c;
							border-radius: 4rpx;
						}

						.titletext {
							font-weight: bold;
							margin-left: 20rpx;
						}
					}
				}

				.chartstitle {
					font-weight: bold;
				}
			}

			.tablecontainer {
				background-color: white;
				border-radius: 20rpx;
				padding: 20rpx;
				margin: 0 20rpx 20rpx 20rpx;
				font-weight: bold;

				.tabletitle {
					.titletag {
						width: 10rpx;
						height: 30rpx;
						background-color: #07ac7c;
						border-radius: 4rpx;
					}

					.titletext {
						font-weight: bold;
						margin-left: 20rpx;
					}

					display: flex;
					justify-content: left;
					align-items: center;
					padding: 10rpx 0 20rpx 10rpx;
				}

				.rowcontainer {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 30rpx;
				}
			}
		}

		.chuliContainer {
			.calendarContainer {
				display: flex;
				align-items: center;
			}

			.subsectionContainer {
				padding: 20rpx 0;
			}

			.buttonContainer {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.charts-box {
				background-color: white;
				border-radius: 20rpx;
				padding: 20rpx;
				margin: 40rpx 20rpx;

				.tabletitle {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 10rpx 0 20rpx 10rpx;

					// margin-bottom: 40rpx;
					.titletag {
						width: 10rpx;
						height: 30rpx;
						background-color: #07ac7c;
						border-radius: 4rpx;
					}

					.titletext {
						font-weight: bold;
						margin-left: 20rpx;
					}
				}

				.chartstitle {
					font-weight: bold;
				}
			}

			.tablecontainer {
				background-color: white;
				border-radius: 20rpx;
				padding: 20rpx;
				margin: 0 20rpx 20rpx 20rpx;
				font-weight: bold;

				.tabletitle {
					.titletag {
						width: 10rpx;
						height: 30rpx;
						background-color: #07ac7c;
						border-radius: 4rpx;
					}

					.titletext {
						margin-left: 20rpx;
					}

					display: flex;
					justify-content: left;
					align-items: center;
					padding: 10rpx 0 20rpx 10rpx;
				}

				.rowcontainer {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 30rpx;
				}
			}
		}
	}
</style>