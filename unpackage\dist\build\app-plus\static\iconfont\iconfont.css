@font-face {
  font-family: "iconfont"; /* Project id 3441647 */
  src: url('@/static/iconfont/iconfont.woff2?t=1655170724452') format('woff2'),
       url('@/static/iconfont/iconfont.woff?t=1655170724452') format('woff'),
       url('@/static/iconfont/iconfont.ttf?t=1655170724452') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-xiangyoujiantou:before {
  content: "\e65f";
}

.icon-shangchuantupian:before {
  content: "\e737";
}

.icon-kefuAPPtubiao:before {
  content: "\e601";
}

.icon-apptubiao-6:before {
  content: "\e61f";
}

.icon-apptubiao-7:before {
  content: "\e625";
}

.icon-apptubiao-8:before {
  content: "\e628";
}

.icon-diedaiapptubiao-:before {
  content: "\e660";
}

.icon-zhongshanghuiapptubiao-01:before {
  content: "\e653";
}

.icon-apptubiao-:before {
  content: "\e650";
}

.icon-apptubiao-1:before {
  content: "\e60e";
}

.icon-apptubiao-2:before {
  content: "\e618";
}

.icon-apptubiao-3:before {
  content: "\e61e";
}

.icon-apptubiao-4:before {
  content: "\e621";
}

.icon-apptubiao-5:before {
  content: "\e627";
}

.icon-iconfontscan:before {
  content: "\e600";
}

.icon-xiangji:before {
  content: "\e8c7";
}

.icon-icon_details:before {
  content: "\e66d";
}
.icon-sousuoxiao:before {
  content: "\e8d6";
}

