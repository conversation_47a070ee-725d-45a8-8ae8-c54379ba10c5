(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-toolBox-dataRead-dataList"],{1511:function(a,t,e){"use strict";e.d(t,"b",(function(){return r})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){}));var r=function(){var a=this,t=a.$createElement,e=a._self._c||t;return a.inited?e("v-uni-view",{ref:"u-transition",staticClass:"u-transition",class:a.classes,style:[a.mergeStyle],on:{touchmove:function(t){arguments[0]=t=a.$handleEvent(t),a.noop.apply(void 0,arguments)},click:function(t){arguments[0]=t=a.$handleEvent(t),a.clickHandler.apply(void 0,arguments)}}},[a._t("default")],2):a._e()},o=[]},1611:function(a,t,e){"use strict";e.r(t);var r=e("1511"),o=e("abfa");for(var n in o)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return o[a]}))}(n);e("f339");var i=e("828b"),d=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"a75f7a08",null,!1,r["a"],void 0);t["default"]=d.exports},"17f7":function(a,t,e){var r=e("c86c");t=r(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-0c0d5caa]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-0c0d5caa]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-0c0d5caa]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-0c0d5caa]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-0c0d5caa]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-0c0d5caa]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-0c0d5caa]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-0c0d5caa]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-0c0d5caa]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-0c0d5caa]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-0c0d5caa]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-0c0d5caa]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-0c0d5caa]::after{border:none}.u-hover-class[data-v-0c0d5caa]{opacity:.7}.u-primary-light[data-v-0c0d5caa]{color:#ecf5ff}.u-warning-light[data-v-0c0d5caa]{color:#fdf6ec}.u-success-light[data-v-0c0d5caa]{color:#f5fff0}.u-error-light[data-v-0c0d5caa]{color:#fef0f0}.u-info-light[data-v-0c0d5caa]{color:#f4f4f5}.u-primary-light-bg[data-v-0c0d5caa]{background-color:#ecf5ff}.u-warning-light-bg[data-v-0c0d5caa]{background-color:#fdf6ec}.u-success-light-bg[data-v-0c0d5caa]{background-color:#f5fff0}.u-error-light-bg[data-v-0c0d5caa]{background-color:#fef0f0}.u-info-light-bg[data-v-0c0d5caa]{background-color:#f4f4f5}.u-primary-dark[data-v-0c0d5caa]{color:#398ade}.u-warning-dark[data-v-0c0d5caa]{color:#f1a532}.u-success-dark[data-v-0c0d5caa]{color:#53c21d}.u-error-dark[data-v-0c0d5caa]{color:#e45656}.u-info-dark[data-v-0c0d5caa]{color:#767a82}.u-primary-dark-bg[data-v-0c0d5caa]{background-color:#398ade}.u-warning-dark-bg[data-v-0c0d5caa]{background-color:#f1a532}.u-success-dark-bg[data-v-0c0d5caa]{background-color:#53c21d}.u-error-dark-bg[data-v-0c0d5caa]{background-color:#e45656}.u-info-dark-bg[data-v-0c0d5caa]{background-color:#767a82}.u-primary-disabled[data-v-0c0d5caa]{color:#9acafc}.u-warning-disabled[data-v-0c0d5caa]{color:#f9d39b}.u-success-disabled[data-v-0c0d5caa]{color:#a9e08f}.u-error-disabled[data-v-0c0d5caa]{color:#f7b2b2}.u-info-disabled[data-v-0c0d5caa]{color:#c4c6c9}.u-primary[data-v-0c0d5caa]{color:#3c9cff}.u-warning[data-v-0c0d5caa]{color:#f9ae3d}.u-success[data-v-0c0d5caa]{color:#5ac725}.u-error[data-v-0c0d5caa]{color:#f56c6c}.u-info[data-v-0c0d5caa]{color:#909399}.u-primary-bg[data-v-0c0d5caa]{background-color:#3c9cff}.u-warning-bg[data-v-0c0d5caa]{background-color:#f9ae3d}.u-success-bg[data-v-0c0d5caa]{background-color:#5ac725}.u-error-bg[data-v-0c0d5caa]{background-color:#f56c6c}.u-info-bg[data-v-0c0d5caa]{background-color:#909399}.u-main-color[data-v-0c0d5caa]{color:#303133}.u-content-color[data-v-0c0d5caa]{color:#606266}.u-tips-color[data-v-0c0d5caa]{color:#909193}.u-light-color[data-v-0c0d5caa]{color:#c0c4cc}.u-safe-area-inset-top[data-v-0c0d5caa]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-0c0d5caa]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-0c0d5caa]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-0c0d5caa]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-0c0d5caa]{z-index:10090}uni-toast .uni-toast[data-v-0c0d5caa]{z-index:10090}[data-v-0c0d5caa]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-view[data-v-0c0d5caa], uni-scroll-view[data-v-0c0d5caa], uni-swiper-item[data-v-0c0d5caa]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-loading-page[data-v-0c0d5caa]{display:flex;flex-direction:column;flex:1;align-items:center;justify-content:center}.u-loading-page__warpper[data-v-0c0d5caa]{margin-top:-150px;justify-content:center;align-items:center;color:#c8c8c8;font-size:19px;display:flex;flex-direction:column}.u-loading-page__warpper__loading-icon[data-v-0c0d5caa]{margin-bottom:10px}.u-loading-page__warpper__loading-icon__img[data-v-0c0d5caa]{width:40px;height:40px}.u-loading-page__warpper__text[data-v-0c0d5caa]{font-size:19px;color:#c8c8c8}',""]),a.exports=t},"1c18":function(a,t,e){"use strict";e.r(t);var r=e("bca7"),o=e.n(r);for(var n in r)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return r[a]}))}(n);t["default"]=o.a},2932:function(a,t,e){"use strict";e("6a54");var r=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(e("c1aa")),n={name:"u-loading-page",mixins:[uni.$u.mpMixin,uni.$u.mixin,o.default],data:function(){return{}},methods:{}};t.default=n},3297:function(a,t,e){var r=e("833d");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[a.i,r,""]]),r.locals&&(a.exports=r.locals);var o=e("967d").default;o("1bd55a46",r,!0,{sourceMap:!1,shadowMode:!1})},3550:function(a,t,e){"use strict";var r=e("c6f04"),o=e.n(r);o.a},"38b7":function(a,t,e){var r=e("17f7");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[a.i,r,""]]),r.locals&&(a.exports=r.locals);var o=e("967d").default;o("191f7115",r,!0,{sourceMap:!1,shadowMode:!1})},"3a7d":function(a,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return n})),e.d(t,"a",(function(){return r}));var r={uInput:e("30b1").default,uIcon:e("59b5").default,uButton:e("6834").default,uLoadingPage:e("5fd8").default},o=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("v-uni-view",{staticClass:"data-read-container"},[e("custom-navbar",{attrs:{title:"抄读助手",showBack:!0},scopedSlots:a._u([{key:"right",fn:function(){},proxy:!0}])}),e("v-uni-view",{staticClass:"content-section",style:{height:a.contentHeight+"px"}},[e("v-uni-view",{staticClass:"meter-card"},[e("v-uni-view",{staticClass:"card-title"},[e("v-uni-view",{staticClass:"title-indicator"}),e("v-uni-text",[a._v("电能表信息")])],1),e("v-uni-view",{staticClass:"scan-item"},[e("v-uni-text",{staticClass:"scan-label"},[a._v("扫描电能表条码")]),a.meterInfo.meterBarCode?e("v-uni-view",{staticClass:"form-value"},[e("u-input",{staticClass:"form-input",attrs:{border:"none",fontSize:"28rpx"},model:{value:a.meterInfo.meterBarCode,callback:function(t){a.$set(a.meterInfo,"meterBarCode",t)},expression:"meterInfo.meterBarCode"}})],1):e("v-uni-view",{staticClass:"scan-button",on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.scanBarcode.apply(void 0,arguments)}}},[e("u-icon",{attrs:{name:"scan",color:"#00BF6B",size:"25"}})],1)],1),e("v-uni-view",{staticClass:"form-item"},[e("v-uni-text",{staticClass:"form-label"},[a._v("资产编号")]),e("v-uni-view",{staticClass:"form-value"},[e("u-input",{staticClass:"form-input",attrs:{border:"none",fontSize:"28rpx"},model:{value:a.meterInfo.meterNo,callback:function(t){a.$set(a.meterInfo,"meterNo",t)},expression:"meterInfo.meterNo"}})],1)],1),e("v-uni-view",{staticClass:"form-item",on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.selectMeterAgreement.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"form-label"},[a._v("规约类型")]),e("v-uni-view",{staticClass:"form-value-with-arrow"},[e("v-uni-text",[a._v(a._s(a.meterInfo.meterAgreementName||"请选择"))]),e("v-uni-view",{staticClass:"arrow-icon"},[e("uni-icons",{attrs:{type:"right",color:"#262626",size:"16"}})],1)],1)],1),e("v-uni-view",{staticClass:"form-item",on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.goDataItem.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"form-label"},[a._v("抄读数据项")]),e("v-uni-view",{staticClass:"form-value-with-arrow"},[e("v-uni-text",[a._v(a._s(a.meterInfo.dataItems||"查看"))]),e("v-uni-view",{staticClass:"arrow-icon"},[e("uni-icons",{attrs:{type:"right",color:"#262626",size:"16"}})],1)],1)],1),e("v-uni-view",{staticClass:"form-item important"},[e("v-uni-view",{staticClass:"font"},[e("v-uni-text",{staticClass:"form-label"},[a._v("*")]),a._v("执行权限")],1),e("v-uni-view",{staticClass:"form-value success",on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.pockertClipAuth.apply(void 0,arguments)}}},[e("v-uni-text",[a._v(a._s(a.meterInfo.permission))])],1)],1),e("v-uni-view",{staticClass:"form-item"},[e("u-button",{attrs:{type:"primary",text:"执行",color:"#07ac7c"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.exectTask.apply(void 0,arguments)}}})],1),e("u-button",{attrs:{type:"primary",text:"抄读结果",color:"#07ac7c"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.goResult.apply(void 0,arguments)}}})],1),e("u-loading-page",{attrs:{loading:a.loading,"loading-text":"执行任务中...","loading-mode":"semicircle","font-size":"16","bg-color":"#fff",color:"#666"}}),e("v-uni-view",{staticClass:"bottom-area"})],1)],1)},n=[]},"42e0":function(a,t,e){"use strict";e("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={fade:{enter:{opacity:0},"enter-to":{opacity:1},leave:{opacity:1},"leave-to":{opacity:0}},"fade-up":{enter:{opacity:0,transform:"translateY(100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateY(100%)"}},"fade-down":{enter:{opacity:0,transform:"translateY(-100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateY(-100%)"}},"fade-left":{enter:{opacity:0,transform:"translateX(-100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateX(-100%)"}},"fade-right":{enter:{opacity:0,transform:"translateX(100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateX(100%)"}},"slide-up":{enter:{transform:"translateY(100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateY(100%)"}},"slide-down":{enter:{transform:"translateY(-100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateY(-100%)"}},"slide-left":{enter:{transform:"translateX(-100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateX(-100%)"}},"slide-right":{enter:{transform:"translateX(100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateX(100%)"}},zoom:{enter:{transform:"scale(0.95)"},"enter-to":{transform:"scale(1)"},leave:{transform:"scale(1)"},"leave-to":{transform:"scale(0.95)"}},"fade-zoom":{enter:{opacity:0,transform:"scale(0.95)"},"enter-to":{opacity:1,transform:"scale(1)"},leave:{opacity:1,transform:"scale(1)"},"leave-to":{opacity:0,transform:"scale(0.95)"}}}},"5fd8":function(a,t,e){"use strict";e.r(t);var r=e("8ebc"),o=e("b36d");for(var n in o)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return o[a]}))}(n);e("eea2");var i=e("828b"),d=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"0c0d5caa",null,!1,r["a"],void 0);t["default"]=d.exports},"724c":function(a,t,e){"use strict";e("6a54");var r=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(e("9b1b")),n=r(e("99a5")),i=r(e("7d8e")),d={name:"u-transition",data:function(){return{inited:!1,viewStyle:{},status:"",transitionEnded:!1,display:!1,classes:""}},computed:{mergeStyle:function(){var a=this.viewStyle,t=this.customStyle;return(0,o.default)((0,o.default)({transitionDuration:"".concat(this.duration,"ms"),transitionTimingFunction:this.timingFunction},uni.$u.addStyle(t)),a)}},mixins:[uni.$u.mpMixin,uni.$u.mixin,i.default,n.default],watch:{show:{handler:function(a){a?this.vueEnter():this.vueLeave()},immediate:!0}}};t.default=d},"7d8e":function(a,t,e){"use strict";e("6a54");var r=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(e("2634")),n=r(e("2fdc"));e("bf0f"),e("c223");r(e("42e0"));var i=function(a){return{enter:"u-".concat(a,"-enter u-").concat(a,"-enter-active"),"enter-to":"u-".concat(a,"-enter-to u-").concat(a,"-enter-active"),leave:"u-".concat(a,"-leave u-").concat(a,"-leave-active"),"leave-to":"u-".concat(a,"-leave-to u-").concat(a,"-leave-active")}},d={methods:{clickHandler:function(){this.$emit("click")},vueEnter:function(){var a=this,t=i(this.mode);this.status="enter",this.$emit("beforeEnter"),this.inited=!0,this.display=!0,this.classes=t.enter,this.$nextTick((0,n.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,uni.$u.sleep(20);case 2:a.$emit("enter"),a.transitionEnded=!1,a.$emit("afterEnter"),a.classes=t["enter-to"];case 6:case"end":return e.stop()}}),e)}))))},vueLeave:function(){var a=this;if(this.display){var t=i(this.mode);this.status="leave",this.$emit("beforeLeave"),this.classes=t.leave,this.$nextTick((function(){a.transitionEnded=!1,a.$emit("leave"),setTimeout(a.onTransitionEnd,a.duration),a.classes=t["leave-to"]}))}},onTransitionEnd:function(){this.transitionEnded||(this.transitionEnded=!0,this.$emit("leave"===this.status?"afterLeave":"afterEnter"),!this.show&&this.display&&(this.display=!1,this.inited=!1))}}};t.default=d},"829a":function(a,t,e){"use strict";e.r(t);var r=e("3a7d"),o=e("1c18");for(var n in o)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return o[a]}))}(n);e("3550");var i=e("828b"),d=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"09d3a235",null,!1,r["a"],void 0);t["default"]=d.exports},"833d":function(a,t,e){var r=e("c86c");t=r(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-a75f7a08]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-a75f7a08]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-a75f7a08]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-a75f7a08]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-a75f7a08]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-a75f7a08]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-a75f7a08]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-a75f7a08]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-a75f7a08]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-a75f7a08]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-a75f7a08]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-a75f7a08]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-a75f7a08]::after{border:none}.u-hover-class[data-v-a75f7a08]{opacity:.7}.u-primary-light[data-v-a75f7a08]{color:#ecf5ff}.u-warning-light[data-v-a75f7a08]{color:#fdf6ec}.u-success-light[data-v-a75f7a08]{color:#f5fff0}.u-error-light[data-v-a75f7a08]{color:#fef0f0}.u-info-light[data-v-a75f7a08]{color:#f4f4f5}.u-primary-light-bg[data-v-a75f7a08]{background-color:#ecf5ff}.u-warning-light-bg[data-v-a75f7a08]{background-color:#fdf6ec}.u-success-light-bg[data-v-a75f7a08]{background-color:#f5fff0}.u-error-light-bg[data-v-a75f7a08]{background-color:#fef0f0}.u-info-light-bg[data-v-a75f7a08]{background-color:#f4f4f5}.u-primary-dark[data-v-a75f7a08]{color:#398ade}.u-warning-dark[data-v-a75f7a08]{color:#f1a532}.u-success-dark[data-v-a75f7a08]{color:#53c21d}.u-error-dark[data-v-a75f7a08]{color:#e45656}.u-info-dark[data-v-a75f7a08]{color:#767a82}.u-primary-dark-bg[data-v-a75f7a08]{background-color:#398ade}.u-warning-dark-bg[data-v-a75f7a08]{background-color:#f1a532}.u-success-dark-bg[data-v-a75f7a08]{background-color:#53c21d}.u-error-dark-bg[data-v-a75f7a08]{background-color:#e45656}.u-info-dark-bg[data-v-a75f7a08]{background-color:#767a82}.u-primary-disabled[data-v-a75f7a08]{color:#9acafc}.u-warning-disabled[data-v-a75f7a08]{color:#f9d39b}.u-success-disabled[data-v-a75f7a08]{color:#a9e08f}.u-error-disabled[data-v-a75f7a08]{color:#f7b2b2}.u-info-disabled[data-v-a75f7a08]{color:#c4c6c9}.u-primary[data-v-a75f7a08]{color:#3c9cff}.u-warning[data-v-a75f7a08]{color:#f9ae3d}.u-success[data-v-a75f7a08]{color:#5ac725}.u-error[data-v-a75f7a08]{color:#f56c6c}.u-info[data-v-a75f7a08]{color:#909399}.u-primary-bg[data-v-a75f7a08]{background-color:#3c9cff}.u-warning-bg[data-v-a75f7a08]{background-color:#f9ae3d}.u-success-bg[data-v-a75f7a08]{background-color:#5ac725}.u-error-bg[data-v-a75f7a08]{background-color:#f56c6c}.u-info-bg[data-v-a75f7a08]{background-color:#909399}.u-main-color[data-v-a75f7a08]{color:#303133}.u-content-color[data-v-a75f7a08]{color:#606266}.u-tips-color[data-v-a75f7a08]{color:#909193}.u-light-color[data-v-a75f7a08]{color:#c0c4cc}.u-safe-area-inset-top[data-v-a75f7a08]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-a75f7a08]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-a75f7a08]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-a75f7a08]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-a75f7a08]{z-index:10090}uni-toast .uni-toast[data-v-a75f7a08]{z-index:10090}[data-v-a75f7a08]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-view[data-v-a75f7a08], uni-scroll-view[data-v-a75f7a08], uni-swiper-item[data-v-a75f7a08]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}\r\n/**\r\n * vue版本动画内置的动画模式有如下：\r\n * fade：淡入\r\n * zoom：缩放\r\n * fade-zoom：缩放淡入\r\n * fade-up：上滑淡入\r\n * fade-down：下滑淡入\r\n * fade-left：左滑淡入\r\n * fade-right：右滑淡入\r\n * slide-up：上滑进入\r\n * slide-down：下滑进入\r\n * slide-left：左滑进入\r\n * slide-right：右滑进入\r\n */.u-fade-enter-active[data-v-a75f7a08],\r\n.u-fade-leave-active[data-v-a75f7a08]{transition-property:opacity}.u-fade-enter[data-v-a75f7a08],\r\n.u-fade-leave-to[data-v-a75f7a08]{opacity:0}.u-fade-zoom-enter[data-v-a75f7a08],\r\n.u-fade-zoom-leave-to[data-v-a75f7a08]{-webkit-transform:scale(.95);transform:scale(.95);opacity:0}.u-fade-zoom-enter-active[data-v-a75f7a08],\r\n.u-fade-zoom-leave-active[data-v-a75f7a08]{transition-property:opacity,-webkit-transform;transition-property:transform,opacity;transition-property:transform,opacity,-webkit-transform}.u-fade-down-enter-active[data-v-a75f7a08],\r\n.u-fade-down-leave-active[data-v-a75f7a08],\r\n.u-fade-left-enter-active[data-v-a75f7a08],\r\n.u-fade-left-leave-active[data-v-a75f7a08],\r\n.u-fade-right-enter-active[data-v-a75f7a08],\r\n.u-fade-right-leave-active[data-v-a75f7a08],\r\n.u-fade-up-enter-active[data-v-a75f7a08],\r\n.u-fade-up-leave-active[data-v-a75f7a08]{transition-property:opacity,-webkit-transform;transition-property:opacity,transform;transition-property:opacity,transform,-webkit-transform}.u-fade-up-enter[data-v-a75f7a08],\r\n.u-fade-up-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0);opacity:0}.u-fade-down-enter[data-v-a75f7a08],\r\n.u-fade-down-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0);opacity:0}.u-fade-left-enter[data-v-a75f7a08],\r\n.u-fade-left-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0);opacity:0}.u-fade-right-enter[data-v-a75f7a08],\r\n.u-fade-right-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0);opacity:0}.u-slide-down-enter-active[data-v-a75f7a08],\r\n.u-slide-down-leave-active[data-v-a75f7a08],\r\n.u-slide-left-enter-active[data-v-a75f7a08],\r\n.u-slide-left-leave-active[data-v-a75f7a08],\r\n.u-slide-right-enter-active[data-v-a75f7a08],\r\n.u-slide-right-leave-active[data-v-a75f7a08],\r\n.u-slide-up-enter-active[data-v-a75f7a08],\r\n.u-slide-up-leave-active[data-v-a75f7a08]{transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform}.u-slide-up-enter[data-v-a75f7a08],\r\n.u-slide-up-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}.u-slide-down-enter[data-v-a75f7a08],\r\n.u-slide-down-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}.u-slide-left-enter[data-v-a75f7a08],\r\n.u-slide-left-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}.u-slide-right-enter[data-v-a75f7a08],\r\n.u-slide-right-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}.u-zoom-enter-active[data-v-a75f7a08],\r\n.u-zoom-leave-active[data-v-a75f7a08]{transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform}.u-zoom-enter[data-v-a75f7a08],\r\n.u-zoom-leave-to[data-v-a75f7a08]{-webkit-transform:scale(.95);transform:scale(.95)}',""]),a.exports=t},"8ebc":function(a,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return n})),e.d(t,"a",(function(){return r}));var r={uTransition:e("1611").default,uLoadingIcon:e("fa5b").default},o=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("u-transition",{attrs:{show:a.loading,"custom-style":{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:a.bgColor,display:"flex"}}},[e("v-uni-view",{staticClass:"u-loading-page"},[e("v-uni-view",{staticClass:"u-loading-page__warpper"},[e("v-uni-view",{staticClass:"u-loading-page__warpper__loading-icon"},[a.image?e("v-uni-image",{staticClass:"u-loading-page__warpper__loading-icon__img",style:{width:a.$u.addUnit(a.iconSize),height:a.$u.addUnit(a.iconSize)},attrs:{src:a.image,mode:"widthFit"}}):e("u-loading-icon",{attrs:{mode:a.loadingMode,size:a.$u.addUnit(a.iconSize),color:a.loadingColor}})],1),a._t("default",[e("v-uni-text",{staticClass:"u-loading-page__warpper__text",style:{fontSize:a.$u.addUnit(a.fontSize),color:a.color}},[a._v(a._s(a.loadingText))])])],2)],1)],1)},n=[]},"99a5":function(a,t,e){"use strict";e("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("64aa");var r={props:{show:{type:Boolean,default:uni.$u.props.transition.show},mode:{type:String,default:uni.$u.props.transition.mode},duration:{type:[String,Number],default:uni.$u.props.transition.duration},timingFunction:{type:String,default:uni.$u.props.transition.timingFunction}}};t.default=r},abfa:function(a,t,e){"use strict";e.r(t);var r=e("724c"),o=e.n(r);for(var n in r)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return r[a]}))}(n);t["default"]=o.a},b36d:function(a,t,e){"use strict";e.r(t);var r=e("2932"),o=e.n(r);for(var n in r)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return r[a]}))}(n);t["default"]=o.a},bca7:function(a,t,e){"use strict";e("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("d4b5"),e("5ef2"),e("fd3c"),e("c9b5"),e("d5c6"),e("5a56"),e("f074");var r=e("b3d7"),o={data:function(){return{contentHeight:0,statusBarHeight:0,navbarHeight:44,size:"40rpx",loading:!1,meterInfo:{meterBarCode:"",meterAddress:"",meterAgreement:"",meterNo:"",meterAgreementName:"",dataItems:"",permission:"获取权限"},readDataItems:null,meterAgreementList:[{text:"645协议09版本",value:"03"},{text:"645协议13版本",value:"01"},{text:"698版本",value:"04"}],token:null}},onLoad:function(){this.getSystemInfo(),this.calcContentHeight();var a=getApp();console.log(a),this.init()},onReady:function(){},mounted:function(){window.getFun=this.exectTask.bind(this),console.log(window)},onShow:function(){var a=uni.getStorageSync("readDataItems");console.log(a),a&&(this.readDataItems=JSON.parse(a),uni.removeStorageSync("readDataItems"))},methods:{init:function(){var a=this;wx.invoke("ext_DataCache_GetInfo",{data:{key:"1002838.userInfo"}},(function(t){var e=JSON.parse(t.result);console.log("获取用户信息",e),a.token=null===e||void 0===e?void 0:e.token}))},getSystemInfo:function(){var a=uni.getSystemInfoSync();this.statusBarHeight=a.statusBarHeight||0,this.windowHeight=a.windowHeight||0},getMeterNo:function(){var a=this;this.meterInfo.meterBarCode&&uni.request({url:"http://127.0.0.1:".concat(r.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:this.token},data:{token:this.token,method:"PutHuaYun",uri:r.url,data:JSON.stringify({bizCode:r.bizCode,espFlowId:(0,r.getUUID)(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:(0,r.getCurrentTimestamp)(),espInformation:{service:"AseCommonController",method:"queryAssetInfo",data:{barCode:this.meterInfo.meterBarCode}}})},success:function(t){if(console.log("获取返回值--",t),t&&1===t.data.Tag){var e=t.data.Data.espInformation;e&&200==e.code&&(a.meterInfo.meterAddress=e.data.commAddr,a.meterInfo.meterNo=e.data.asseNo)}},fail:function(a){console.error("请求失败:",a),uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}})},calcContentHeight:function(){this.windowHeight||this.getSystemInfo();var a=this.statusBarHeight+this.navbarHeight;this.contentHeight=this.windowHeight-a},scanBarcode:function(){var a=this;wx.scanQRCode({desc:"scanQRCode desc",needResult:1,scanType:["qrCode","barCode"],success:function(t){console.log("扫一扫回调",JSON.stringify(t)),a.meterInfo.meterBarCode=t.resultStr,a.getMeterNo()},error:function(a){a.errMsg.indexOf("function_not_exist")>0&&alert("版本过低请升级")}})},selectMeterAgreement:function(){var a=this;uni.showActionSheet({itemList:this.meterAgreementList.map((function(a){return a.text})),success:function(t){var e=t.tapIndex;a.meterInfo.meterAgreement=a.meterAgreementList[e].value,a.meterInfo.meterAgreementName=a.meterAgreementList[e].text}})},pockertClipAuth:function(){var a=this;uni.showLoading({title:"执行中...",mask:!0});wx.invoke("request3rdApp",{scheme:"hycontroler",needToken:0,param:{isOneKeyExecute:"01",taskType:"01",pluginParam:""}},(function(t){if(uni.hideLoading(),console.log("response:",t),"request3rdApp:ok"===t.err_msg){var e=JSON.parse(t.data);0===e.ret?a.meterInfo.permission="获取成功":a.meterInfo.permission=e.msg}else uni.showToast({title:"调用i国网外设安全接入应用失败",icon:"none"})}))},exectTask:function(){var a=this;uni.showLoading({title:"执行中...",mask:!0});var t=a.buildMeterParam({meterAddress:a.meterInfo.meterAddress||"010036086701",meterNo:a.meterInfo.meterNo||"",meterAgreement:a.meterInfo.meterAgreement||"04",optItems:a.readDataItems||[]});console.log(t);var e={isOneKeyExecute:"01",taskType:"02",pluginParam:JSON.stringify(t)};wx.invoke("request3rdApp",{scheme:"hycontroler",needToken:0,param:e},(function(a){if(uni.hideLoading(),console.log("抄读res",a),"request3rdApp:ok"===a.err_msg){var t=JSON.parse(a.data);console.log("抄读返回结果",t),0===t.ret?(console.log(t.data),this.touchao(t.data),uni.showToast({title:t.msg,icon:"none"}),uni.setStorageSync("readResults",JSON.stringify(t.data)),uni.navigateTo({url:"./dataResult?item=".concat(encodeURIComponent(JSON.stringify(t.data)))})):uni.showToast({title:t.msg,icon:"none"})}}))},touchao:function(a){wx.invoke("multiWindows_publish",{channelId:"touchao",message:"透抄信息",data:{param1:JSON.stringify(a),random:Math.random(),currentTime:(new Date).getTime(),message:"抄表信息"}},(function(a){}))},goResult:function(){uni.navigateTo({url:"./dataResult"})},buildMeterParam:function(a){var t=a.meterAddress,e=a.meterAgreement,r=a.meterNo,o=a.optItems,n=a.offline,i="01";"03"===e&&(i="00",e="01");var d={module:"business",method:"shMeterRead",meterAddress:t,meterNo:r,commAgreement:e,is13Meter:i,commModel:"01",optItems:o};return n&&(d.offline=btoa(n),d.operationMode="02"),d},goDataItem:function(){uni.navigateTo({url:"./dataItem"})}}};t.default=o},c1aa:function(a,t,e){"use strict";e("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("64aa");var r={props:{loadingText:{type:[String,Number],default:uni.$u.props.loadingPage.loadingText},image:{type:String,default:uni.$u.props.loadingPage.image},loadingMode:{type:String,default:uni.$u.props.loadingPage.loadingMode},loading:{type:Boolean,default:uni.$u.props.loadingPage.loading},bgColor:{type:String,default:uni.$u.props.loadingPage.bgColor},color:{type:String,default:uni.$u.props.loadingPage.color},fontSize:{type:[String,Number],default:uni.$u.props.loadingPage.fontSize},iconSize:{type:[String,Number],default:uni.$u.props.loadingPage.fontSize},loadingColor:{type:String,default:uni.$u.props.loadingPage.loadingColor}}};t.default=r},c6f04:function(a,t,e){var r=e("ed43");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[a.i,r,""]]),r.locals&&(a.exports=r.locals);var o=e("967d").default;o("e2427510",r,!0,{sourceMap:!1,shadowMode:!1})},ed43:function(a,t,e){var r=e("c86c");t=r(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-09d3a235]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-09d3a235]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-09d3a235]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-09d3a235]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-09d3a235]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-09d3a235]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-09d3a235]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-09d3a235]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-09d3a235]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-09d3a235]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-09d3a235]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-09d3a235]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-09d3a235]::after{border:none}.u-hover-class[data-v-09d3a235]{opacity:.7}.u-primary-light[data-v-09d3a235]{color:#ecf5ff}.u-warning-light[data-v-09d3a235]{color:#fdf6ec}.u-success-light[data-v-09d3a235]{color:#f5fff0}.u-error-light[data-v-09d3a235]{color:#fef0f0}.u-info-light[data-v-09d3a235]{color:#f4f4f5}.u-primary-light-bg[data-v-09d3a235]{background-color:#ecf5ff}.u-warning-light-bg[data-v-09d3a235]{background-color:#fdf6ec}.u-success-light-bg[data-v-09d3a235]{background-color:#f5fff0}.u-error-light-bg[data-v-09d3a235]{background-color:#fef0f0}.u-info-light-bg[data-v-09d3a235]{background-color:#f4f4f5}.u-primary-dark[data-v-09d3a235]{color:#398ade}.u-warning-dark[data-v-09d3a235]{color:#f1a532}.u-success-dark[data-v-09d3a235]{color:#53c21d}.u-error-dark[data-v-09d3a235]{color:#e45656}.u-info-dark[data-v-09d3a235]{color:#767a82}.u-primary-dark-bg[data-v-09d3a235]{background-color:#398ade}.u-warning-dark-bg[data-v-09d3a235]{background-color:#f1a532}.u-success-dark-bg[data-v-09d3a235]{background-color:#53c21d}.u-error-dark-bg[data-v-09d3a235]{background-color:#e45656}.u-info-dark-bg[data-v-09d3a235]{background-color:#767a82}.u-primary-disabled[data-v-09d3a235]{color:#9acafc}.u-warning-disabled[data-v-09d3a235]{color:#f9d39b}.u-success-disabled[data-v-09d3a235]{color:#a9e08f}.u-error-disabled[data-v-09d3a235]{color:#f7b2b2}.u-info-disabled[data-v-09d3a235]{color:#c4c6c9}.u-primary[data-v-09d3a235]{color:#3c9cff}.u-warning[data-v-09d3a235]{color:#f9ae3d}.u-success[data-v-09d3a235]{color:#5ac725}.u-error[data-v-09d3a235]{color:#f56c6c}.u-info[data-v-09d3a235]{color:#909399}.u-primary-bg[data-v-09d3a235]{background-color:#3c9cff}.u-warning-bg[data-v-09d3a235]{background-color:#f9ae3d}.u-success-bg[data-v-09d3a235]{background-color:#5ac725}.u-error-bg[data-v-09d3a235]{background-color:#f56c6c}.u-info-bg[data-v-09d3a235]{background-color:#909399}.u-main-color[data-v-09d3a235]{color:#303133}.u-content-color[data-v-09d3a235]{color:#606266}.u-tips-color[data-v-09d3a235]{color:#909193}.u-light-color[data-v-09d3a235]{color:#c0c4cc}.u-safe-area-inset-top[data-v-09d3a235]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-09d3a235]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-09d3a235]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-09d3a235]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-09d3a235]{z-index:10090}uni-toast .uni-toast[data-v-09d3a235]{z-index:10090}[data-v-09d3a235]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */.data-read-container[data-v-09d3a235]{display:flex;flex-direction:column;background-color:#f8f8f8;height:100vh\r\n  /* 覆盖默认导航栏样式，使其与设计图一致 */}.data-read-container[data-v-09d3a235] .custom-navbar{background-color:#fff!important}.data-read-container[data-v-09d3a235] .custom-navbar .navbar-title uni-text{color:#333}.data-read-container .bluetooth-icon[data-v-09d3a235]{height:%?42?%;margin-left:%?20?%}\r\n/* 内容区域 */.content-section[data-v-09d3a235]{flex:1;box-sizing:border-box;background-color:#f8f8f8;padding:%?30?%;overflow-y:auto;-webkit-overflow-scrolling:touch\r\n  /* 增强iOS滚动体验 */}\r\n/* 电能表信息卡片 */.meter-card[data-v-09d3a235]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.05);margin-bottom:%?30?%}.form-input[data-v-09d3a235] .u-input__content__field-wrapper__field{text-align:right!important}.card-title[data-v-09d3a235]{display:flex;align-items:center}.card-title .title-indicator[data-v-09d3a235]{width:%?6?%;height:%?30?%;background-color:#00bf6b;margin-right:%?14?%;border-radius:%?3?%}.card-title uni-text[data-v-09d3a235]{font-size:%?30?%;color:#333;font-weight:700}.scan-item[data-v-09d3a235]{display:flex;align-items:center;justify-content:space-between;padding:%?20?% 0;border-bottom:%?1?% solid #f2f2f2}.scan-label[data-v-09d3a235]{font-size:%?28?%;color:#333}.scan-button[data-v-09d3a235]{padding:%?10?%;display:flex;align-items:center;justify-content:center}.form-item[data-v-09d3a235]{display:flex;justify-content:space-between;align-items:center;padding:%?24?% 0;border-bottom:%?1?% solid #f2f2f2}.form-item[data-v-09d3a235]:last-child{border-bottom:none}.form-item.important .form-label[data-v-09d3a235]{color:#ff5a5f}.form-item.important .font[data-v-09d3a235]{font-size:%?24?%}.form-label[data-v-09d3a235]{font-size:%?28?%;color:#333}.form-value[data-v-09d3a235]{font-size:%?28?%;color:#666}.form-value.success[data-v-09d3a235]{color:#00bf6b}.form-value-with-arrow[data-v-09d3a235]{font-size:%?28?%;color:#333;display:flex;align-items:center}.form-value-with-arrow .arrow-icon[data-v-09d3a235]{margin-left:%?10?%}\r\n/* 底部区域 */.bottom-area[data-v-09d3a235]{padding:%?20?% 0;flex:1}',""]),a.exports=t},eea2:function(a,t,e){"use strict";var r=e("38b7"),o=e.n(r);o.a},f339:function(a,t,e){"use strict";var r=e("3297"),o=e.n(r);o.a}}]);