<template>
	<view class="mainContainer">
		<threenav title="流程环节"></threenav>
		<view class="tabletitle">
			<view class="titletag"></view>
			<view class="titletext">
				流程环节信息
			</view>
		</view>
		<view class="liuchengtitle" style="padding: 10rpx 40rpx;">
			<view style="margin-right: 40rpx;font-weight: bold;">用户编号</view>
			<view style="color: blue;font-weight: bold;">{{listData.custNo}}</view>
		</view>
		<view class="liuchengtitle" style="padding: 10rpx 40rpx;">
			<view style="margin-right: 40rpx;font-weight: bold;">工单编号</view>
			<view style="font-weight: bold;">{{listData.appNo}}</view>
		</view>
		<!-- 列表 -->
		<view class="listcontainer" v-for="(item,index) in dataArray" :key="index">
			<view class="datatext">
				<view class="datacontainer">
					<view class="datatitle">环节名称：</view><text style="font-weight: bold;">{{item.linkName}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">开始时间：</view><text style="font-weight: bold;">{{item.startTime}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">完成时间：</view><text style="font-weight: bold;">{{item.endTime}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">处理人：</view><text style="font-weight: bold;">{{item.conductor}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">处理单位：</view><text style="font-weight: bold;">{{item.conductorOrg}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import utils from '@/static/commonJs/util.js'
	export default {
		props: {},
		data() {
			return {
				testdev: true,
				closeOnClick: true,
				show: false,
				radio: '',
				switchVal: false,
				closeOnClick: true,
				value: '',
				listData: {},
				dataArray: []
			}
		},
		onLoad(options) {
			if (options.data) {
				this.listData = JSON.parse(decodeURIComponent(options.data));
				this.search()
			}
		},
		onReady() {

		},
		methods: {
			async search() {
				if (this.testdev) {
					this.dataArray = [
						{
							linkName:'工单生成及派工',
							startTime:'2025-07-23 09:01:27',
							endTime:'2025-05-11 09:02:45',
							conductor:'王一鸣',
							conductorOrg:'西区供电服务中心'
						},
						{
							linkName:'现场检查',
							startTime:'2025-07-23 09:02:45',
							endTime:'2025-07-23 09:16:49',
							conductor:'卢旭盛',
							conductorOrg:'西区供电服务中心'
						},
						{
							linkName:'工单归档',
							startTime:'2025-07-23 09:16:50',
							endTime:'2025-07-23 09:16:50',
							conductor:'卢旭盛',
							conductorOrg:'西区供电服务中心'
						},
					];
				} else {
					if (this.loading || this.noMore) return

					this.loading = true
					// 调用后端接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "OrderLinkController",
										"method": "flowLinkInfo",
										"data": {
											"orderNo": this.listData.appNo,
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.dataArray = res.data.data;
						} else {
							uni.showToast({
								title: '查询失败',
								icon: 'none'
							});
						}
					} finally {
					}
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	.custom-select {
		width: 100%;
		position: relative;
	}

	.select-box {
		border: 1rpx solid #ddd;
		width: 100%;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.arrow {
		float: right;
		border: solid #999;
		border-width: 0 2rpx 2rpx 0;
		padding: 6rpx;
		margin-top: 6rpx;
	}

	.down {
		transform: rotate(45deg);
	}

	.up {
		transform: rotate(-135deg);
	}

	.dropdown {
		position: absolute;
		width: 100%;
		border: 1rpx solid #eee;
		background: #fff;
		z-index: 999;
	}

	.dropdown-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.mainContainer {
		.liuchengtitle {
			display: flex;
			align-items: center;
			justify-content: left;
		}

		.tabletitle {
			.titletag {
				width: 10rpx;
				height: 30rpx;
				background-color: #07ac7c;
				border-radius: 4rpx;
			}

			.titletext {
				font-size: 34rpx;
				margin-left: 20rpx;
				font-weight: bold;
			}

			display: flex;
			justify-content: left;
			align-items: center;
			padding: 40rpx 0 20rpx 20rpx;
		}

		.popupcontainer {
			height: 1500rpx;
			padding: 0 40rpx;

			.poptitle {
				text-align: center;
				font-size: 36rpx;
				font-weight: bold;
				padding: 50rpx 0;
			}

			.formitem {

				// font-weight: bold;
				.calendarContainer {
					width: 100%;

					.calInput {
						width: 100%;
						// margin-right: 30rpx;
					}
				}
			}

			.ubutton {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 400rpx 0 0;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			margin: 20rpx;

			.datatext {
				padding: 10rpx 0 10rpx 0rpx;

				.datacontainer {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 30rpx 20rpx 30rpx 20rpx;

					.datatitle {
						width: 300rpx;
						color: darkgray;
						font-weight: bold;
					}
				}
			}

		}
	}
</style>