<template>
	<view class="user-data-query">
		<!-- 顶部导航区域 - 固定 -->
		<view class="header-section">
			<custom-navbar title="查询">
			</custom-navbar>
		</view>
		
		<!-- 中间表单区域 - 可滚动 -->
		<scroll-view class="form-section" scroll-y :style="{ height: scrollViewHeight + 'px', top: safeAreaTop + 'px' }">
			<view class="form-content">
				<!-- 表单项 -->
				<view class="form-item">
					<view class="form-label">专家姓名</view>
					<u-input
						v-model="formData.expertName"
						placeholder="请输入专家姓名"
						border="surround"
						:clearable="true"
						fontSize="30rpx"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<view class="form-item">
					<view class="form-label">专家单位</view>
					<u-input
						v-model="formData.experOrg"
						placeholder="请输入专家单位"
						border="surround"
						:clearable="true"
						fontSize="30rpx"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<view class="form-item">
					<view class="form-label">专家职称</view>
					<u-input
						v-model="formData.title"
						placeholder="请输入专家职称"
						border="surround"
						:clearable="true"
						fontSize="30rpx"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				<!-- 预留空间，确保小屏幕上也能滚动到底部的所有内容 -->
				<view class="form-space"></view>
			</view>
		</scroll-view>
		
		<!-- 底部按钮区域 - 固定 -->
		<view class="footer-section">
			<view class="btn-group">
				<view class="btn btn-reset" @click="resetForm">
					<text>重置选择</text>
				</view>
				<view class="btn btn-confirm" @click="submitForm">
					<text>确定</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				navbarHeight: 44, // 导航栏固定高度，单位px
				headerHeight: 0, // 导航栏总高度，单位px
				footerHeight: 140, // 底部按钮区域高度，单位rpx
				windowHeight: 0,
				scrollViewHeight: 0,
				safeAreaTop: 0, // 顶部安全区域高度，单位px
				
				formData: {
					expertName:"",//专家姓名
					experOrg:"",//专家单位
					title:""//专家职称
					
				},
				// 输入框样式
				inputStyle: {
					height: '80rpx',
				},
			};
		},
		onLoad(options) {
			// 获取状态栏高度
			this.getStatusBarHeight();
		},
		onReady() {
			// 计算滚动区域高度
			this.calcScrollViewHeight();
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
				
				// 计算头部总高度（导航栏）
				this.headerHeight = this.statusBarHeight + this.navbarHeight;
				this.safeAreaTop = this.headerHeight;
			},
			
			// 计算滚动区域高度
			calcScrollViewHeight() {
				// 底部区域高度从rpx转为px进行计算
				const footerHeightPx = uni.upx2px(this.footerHeight);
				this.scrollViewHeight = this.windowHeight - this.headerHeight - footerHeightPx;
				console.log('计算高度:', {
					windowHeight: this.windowHeight,
					headerHeight: this.headerHeight,
					footerHeightPx: footerHeightPx,
					scrollViewHeight: this.scrollViewHeight
				});
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			
			// 重置表单
			resetForm() {
				this.formData = {
					expertName:"",//专家姓名
					experOrg:"",//专家单位
					title:""//专家职称
				};
			},
			
			// 提交表单
			submitForm() {
				// 表单验证 - 这里简化处理，允许所有字段为空
				
				// 模拟查询请求
				uni.showLoading({
					title: '查询中...'
				});
				
					uni.hideLoading();
					
					// 将查询条件保存到本地存储
					uni.setStorageSync('expert_search_params', this.formData);
					uni.navigateTo({
						url: `/pages/toolBox/expertLibrary/expertList`
					});
					// 返回列表页面
					// uni.navigateBack({
					// 	delta: 1,
					// 	success: () => {
					// 		// 通知列表页面刷新数据
					// 		const pages = getCurrentPages();
					// 		const prevPage = pages[pages.length - 1]; // 上一个页面已经变成当前页面
					// 		if (prevPage && prevPage.$vm) {
					// 			// 调用上一个页面的刷新方法
					// 			prevPage.$vm.loadExpertListParams && prevPage.$vm.loadExpertListParams(this.formData);
					// 		}
					// 	}
					// });
					setTimeout(()=>{
						uni.hideLoading()
					},500)
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #fff;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.user-data-query {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	position: relative;
}

/* 顶部导航区域 */
.header-section {
	background-color: #00C389;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

/* 中间表单区域 */
.form-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 180rpx; /* 底部按钮区域高度 */
	box-sizing: border-box;
	background-color: #fff;
	z-index: 99;
}

.form-content {
	padding: 30rpx 40rpx;
	background-color: #fff;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 32rpx;
	color: #262626;
	margin-bottom: 16rpx;
	font-weight: 400;
}

.form-input {
	height: 80rpx;
}

.select-wrapper {
	width: 100%;
	position: relative;
	cursor: pointer;
}

/* 底部按钮区域 */
.footer-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	z-index: 50;
	border-top: 1rpx solid #f5f5f5;
	padding-bottom: env(safe-area-inset-bottom);
}

/* 按钮组样式 */
.btn-group {
	display: flex;
	padding: 20rpx 40rpx;
}

.btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: 400;
	transition: all 0.2s;
}

.btn-reset {
	background-color: transparent;
	color: #00C389;
	border: none;
	margin-right: 40rpx;
}

.btn-confirm {
	background-color: #00C389;
	color: #FFFFFF;
}

/* 为小屏幕预留滚动空间 */
.form-space {
	height: 40rpx;
}

/* 修改 u-input 样式 */
/deep/ .u-input {
	height: 80rpx;
}

/deep/ .u-input__input {
	height: 92rpx;
	font-size: 30rpx;
	color: #333;
	padding: 0 24rpx;
}

/deep/ .u-input--border {
	border-color: #e0e0e0;
	border-width: 1rpx;
	border-radius: 16rpx;
	height: 88rpx;
}

/deep/ .u-input__placeholder-style {
	color: #c8c9cc;
}

/deep/ .u-icon {
	color: #999;
}

/deep/ .u-input__content__field {
	display: flex;
	align-items: center;
}



/deep/ .navbar-left .svg-icon {
	color: #ffffff !important;
}


</style>
