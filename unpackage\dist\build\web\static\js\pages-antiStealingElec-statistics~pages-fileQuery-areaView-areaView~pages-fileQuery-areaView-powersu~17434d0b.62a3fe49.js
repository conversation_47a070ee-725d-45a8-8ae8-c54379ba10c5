(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-antiStealingElec-statistics~pages-fileQuery-areaView-areaView~pages-fileQuery-areaView-powersu~17434d0b"],{"023a":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"chartsview"},[e("v-uni-view",{staticClass:"charts-error"}),e("v-uni-view",{staticClass:"charts-font"},[this._v(this._s(null==this.errorMessage?"请点击重试":this.errorMessage))])],1)},o=[]},"0806":function(t,e,a){"use strict";(function(t){a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("d4b5"),a("4626"),a("5ac7"),a("aa9c"),a("fd3c"),a("5c47");var o=i(a("fcf3")),r=(i(a("66fa")),i(a("f905"))),n=i(a("fe9b"));function l(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length,a=new Array(e>1?e-1:0),i=1;i<e;i++)a[i-1]=arguments[i];for(var r in a)for(var n in a[r])a[r].hasOwnProperty(n)&&(t[n]=a[r][n]&&"object"===(0,o.default)(a[r][n])?l(Array.isArray(a[r][n])?[]:{},t[n],a[r][n]):a[r][n]);return t}function s(t){var e=t.getFullYear(),a=t.getMonth()+1,i=t.getDate();a>=1&&a<=9&&(a="0"+a),i>=0&&i<=9&&(i="0"+i);var o=e+"-"+a+"-"+i;return o}var h={name:"qiun-data-charts",mixins:[t.mixinDatacom],props:{type:{type:String,default:null},canvasId:{type:String,default:"uchartsid"},canvas2d:{type:Boolean,default:!1},background:{type:String,default:"none"},animation:{type:Boolean,default:!0},chartData:{type:Object,default:function(){return{categories:[],series:[]}}},opts:{type:Object,default:function(){return{}}},eopts:{type:Object,default:function(){return{}}},loadingType:{type:Number,default:2},errorShow:{type:Boolean,default:!0},errorReload:{type:Boolean,default:!0},errorMessage:{type:String,default:null},inScrollView:{type:Boolean,default:!1},reshow:{type:Boolean,default:!1},reload:{type:Boolean,default:!1},disableScroll:{type:Boolean,default:!1},optsWatch:{type:Boolean,default:!0},onzoom:{type:Boolean,default:!1},ontap:{type:Boolean,default:!0},ontouch:{type:Boolean,default:!1},onmouse:{type:Boolean,default:!0},onmovetip:{type:Boolean,default:!1},echartsH5:{type:Boolean,default:!1},echartsApp:{type:Boolean,default:!1},tooltipShow:{type:Boolean,default:!0},tooltipFormat:{type:String,default:void 0},tooltipCustom:{type:Object,default:void 0},startDate:{type:String,default:void 0},endDate:{type:String,default:void 0},textEnum:{type:Array,default:function(){return[]}},groupEnum:{type:Array,default:function(){return[]}},pageScrollTop:{type:Number,default:0},directory:{type:String,default:"/"},tapLegend:{type:Boolean,default:!0}},data:function(){return{cid:"uchartsid",inWx:!1,inAli:!1,inTt:!1,inBd:!1,inH5:!1,inApp:!1,inWin:!1,type2d:!0,disScroll:!1,openmouse:!1,pixel:1,cWidth:375,cHeight:250,showchart:!1,echarts:!1,echartsResize:{state:!1},uchartsOpts:{},echartsOpts:{},drawData:{},lastDrawTime:null}},created:function(){if(this.cid=this.canvasId,"uchartsid"==this.canvasId||""==this.canvasId){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",e=t.length,a="",i=0;i<32;i++)a+=t.charAt(Math.floor(Math.random()*e));this.cid=a}var o=uni.getSystemInfoSync();"windows"!==o.platform&&"macos"!==o.platform||(this.inWin=!0),this.type2d=!1,this.disScroll=this.disableScroll},mounted:function(){var t=this;this.inH5=!0,!0===this.inWin&&(this.openmouse=this.onmouse),!0===this.echartsH5&&(this.echarts=!0),this.$nextTick((function(){t.beforeInit()}));var e=this.inH5?500:200,a=this;uni.onWindowResize(function(t,e){var a=!1;return function(){var i=arguments,o=this;clearTimeout(a),a&&clearTimeout(a),a=setTimeout((function(){a=!1,t.apply(o,i)}),e)}}((function(t){if(1!=a.mixinDatacomLoading){var e=a.mixinDatacomErrorMessage;null!==e&&"null"!==e&&""!==e||(a.echarts?a.echartsResize.state=!a.echartsResize.state:a.resizeHandler())}}),e))},destroyed:function(){!0===this.echarts?(delete n.default.option[this.cid],delete n.default.instance[this.cid]):(delete r.default.option[this.cid],delete r.default.instance[this.cid]),uni.offWindowResize((function(){}))},watch:{chartDataProps:{handler:function(t,e){"object"===(0,o.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&(this._clearChart(),t.series&&t.series.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this.showchart=!1,this.mixinDatacomErrorMessage=null)):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：chartData数据类型错误")},immediate:!1,deep:!0},localdata:{handler:function(t,e){JSON.stringify(t)!==JSON.stringify(e)&&(t.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage=null))},immediate:!1,deep:!0},optsProps:{handler:function(t,e){"object"===(0,o.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&!1===this.echarts&&1==this.optsWatch&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：opts数据类型错误")},immediate:!1,deep:!0},eoptsProps:{handler:function(t,e){"object"===(0,o.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&!0===this.echarts&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：eopts数据类型错误")},immediate:!1,deep:!0},reshow:function(t,e){var a=this;!0===t&&!1===this.mixinDatacomLoading&&setTimeout((function(){a.mixinDatacomErrorMessage=null,a.echartsResize.state=!a.echartsResize.state,a.checkData(a.drawData)}),200)},reload:function(t,e){!0===t&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())},mixinDatacomErrorMessage:function(t,e){t&&(this.emitMsg({name:"error",params:{type:"error",errorShow:this.errorShow,msg:t,id:this.cid}}),this.errorShow&&console.log("[秋云图表组件]"+t))},errorMessage:function(t,e){t&&this.errorShow&&null!==t&&"null"!==t&&""!==t?(this.showchart=!1,this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=t):(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())}},computed:{optsProps:function(){return JSON.parse(JSON.stringify(this.opts))},eoptsProps:function(){return JSON.parse(JSON.stringify(this.eopts))},chartDataProps:function(){return JSON.parse(JSON.stringify(this.chartData))}},methods:{beforeInit:function(){this.mixinDatacomErrorMessage=null,"object"===(0,o.default)(this.chartData)&&null!=this.chartData&&void 0!==this.chartData.series&&this.chartData.series.length>0?(this.drawData=l({},this.chartData),this.mixinDatacomLoading=!1,this.showchart=!0,this.checkData(this.chartData)):this.localdata.length>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.localdataInit(this.localdata)):""!==this.collection?(this.mixinDatacomLoading=!1,this.getCloudData()):this.mixinDatacomLoading=!0},localdataInit:function(t){if(this.groupEnum.length>0)for(var e=0;e<t.length;e++)for(var a=0;a<this.groupEnum.length;a++)t[e].group===this.groupEnum[a].value&&(t[e].group=this.groupEnum[a].text);if(this.textEnum.length>0)for(var i=0;i<t.length;i++)for(var o=0;o<this.textEnum.length;o++)t[i].text===this.textEnum[o].value&&(t[i].text=this.textEnum[o].text);var h=!1,c={categories:[],series:[]},d=[],p=[];if(h=!0===this.echarts?n.default.categories.includes(this.type):r.default.categories.includes(this.type),!0===h){if(this.chartData&&this.chartData.categories&&this.chartData.categories.length>0)d=this.chartData.categories;else if(this.startDate&&this.endDate){var u=new Date(this.startDate),x=new Date(this.endDate);while(u<=x)d.push(s(u)),u=u.setDate(u.getDate()+1),u=new Date(u)}else{var f={};t.map((function(t,e){void 0==t.text||f[t.text]||(d.push(t.text),f[t.text]=!0)}))}c.categories=d}var g={};if(t.map((function(t,e){void 0==t.group||g[t.group]||(p.push({name:t.group,data:[]}),g[t.group]=!0)})),0==p.length)if(p=[{name:"默认分组",data:[]}],!0===h)for(var y=0;y<d.length;y++){for(var v=0,m=0;m<t.length;m++)t[m].text==d[y]&&(v=t[m].value);p[0].data.push(v)}else for(var b=0;b<t.length;b++)p[0].data.push({name:t[b].text,value:t[b].value});else for(var A=0;A<p.length;A++)if(d.length>0)for(var S=0;S<d.length;S++){for(var w=0,T=0;T<t.length;T++)p[A].name==t[T].group&&t[T].text==d[S]&&(w=t[T].value);p[A].data.push(w)}else for(var C=0;C<t.length;C++)p[A].name==t[C].group&&p[A].data.push(t[C].value);c.series=p,this.drawData=l({},c),this.checkData(c)},reloading:function(){!1!==this.errorReload&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,""!==this.collection?(this.mixinDatacomLoading=!1,this.onMixinDatacomPropsChange(!0)):this.beforeInit())},checkData:function(t){var e=this,a=this.cid;!0===this.echarts?(n.default.option[a]=l({},this.eopts),n.default.option[a].id=a,n.default.option[a].type=this.type):this.type&&r.default.type.includes(this.type)?(r.default.option[a]=l({},r.default[this.type],this.opts),r.default.option[a].canvasId=a):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：props参数中type类型不正确");var i=l({},t);void 0!==i.series&&i.series.length>0&&(this.mixinDatacomErrorMessage=null,!0===this.echarts?(n.default.option[a].chartData=i,this.$nextTick((function(){e.init()}))):(r.default.option[a].categories=i.categories,r.default.option[a].series=i.series,this.$nextTick((function(){e.init()}))))},resizeHandler:function(){var t=this,e=Date.now(),a=this.lastDrawTime?this.lastDrawTime:e-3e3,i=e-a;if(!(i<1e3))uni.createSelectorQuery().in(this).select("#ChartBoxId"+this.cid).boundingClientRect((function(e){t.showchart=!0,e.width>0&&e.height>0&&(e.width===t.cWidth&&e.height===t.cHeight||t.checkData(t.drawData))})).exec()},getCloudData:function(){var t=this;1!=this.mixinDatacomLoading&&(this.mixinDatacomLoading=!0,this.mixinDatacomGet().then((function(e){t.mixinDatacomResData=e.result.data,t.localdataInit(t.mixinDatacomResData)})).catch((function(e){t.mixinDatacomLoading=!1,t.showchart=!1,t.mixinDatacomErrorMessage="请求错误："+e})))},onMixinDatacomPropsChange:function(t,e){1==t&&""!==this.collection&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this._clearChart(),this.getCloudData())},_clearChart:function(){var t=this.cid;if(!0!==this.echarts&&r.default.option[t]&&r.default.option[t].context){var e=r.default.option[t].context;"object"!==(0,o.default)(e)||r.default.option[t].update||(e.clearRect(0,0,this.cWidth*this.pixel,this.cHeight*this.pixel),e.draw())}},init:function(){var t=this,e=this.cid;uni.createSelectorQuery().in(this).select("#ChartBoxId"+e).boundingClientRect((function(a){a.width>0&&a.height>0?(t.mixinDatacomLoading=!1,t.showchart=!0,t.lastDrawTime=Date.now(),t.cWidth=a.width,t.cHeight=a.height,!0!==t.echarts&&(r.default.option[e].background="none"==t.background?"#FFFFFF":t.background,r.default.option[e].canvas2d=t.type2d,r.default.option[e].pixelRatio=t.pixel,r.default.option[e].animation=t.animation,r.default.option[e].width=a.width*t.pixel,r.default.option[e].height=a.height*t.pixel,r.default.option[e].onzoom=t.onzoom,r.default.option[e].ontap=t.ontap,r.default.option[e].ontouch=t.ontouch,r.default.option[e].onmouse=t.openmouse,r.default.option[e].onmovetip=t.onmovetip,r.default.option[e].tooltipShow=t.tooltipShow,r.default.option[e].tooltipFormat=t.tooltipFormat,r.default.option[e].tooltipCustom=t.tooltipCustom,r.default.option[e].inScrollView=t.inScrollView,r.default.option[e].lastDrawTime=t.lastDrawTime,r.default.option[e].tapLegend=t.tapLegend),t.inH5||t.inApp?1==t.echarts?(n.default.option[e].ontap=t.ontap,n.default.option[e].onmouse=t.openmouse,n.default.option[e].tooltipShow=t.tooltipShow,n.default.option[e].tooltipFormat=t.tooltipFormat,n.default.option[e].tooltipCustom=t.tooltipCustom,n.default.option[e].lastDrawTime=t.lastDrawTime,t.echartsOpts=l({},n.default.option[e])):(r.default.option[e].rotateLock=r.default.option[e].rotate,t.uchartsOpts=l({},r.default.option[e])):(r.default.option[e]=function t(e,a){for(var i in e)e.hasOwnProperty(i)&&null!==e[i]&&"object"===(0,o.default)(e[i])?t(e[i],a):"format"===i&&"string"===typeof e[i]&&(e["formatter"]=a[e[i]]?a[e[i]]:void 0);return e}(r.default.option[e],r.default.formatter),t.mixinDatacomErrorMessage=null,t.mixinDatacomLoading=!1,t.showchart=!0,t.$nextTick((function(){if(!0===t.type2d){var i=uni.createSelectorQuery().in(t);i.select("#"+e).fields({node:!0,size:!0}).exec((function(i){if(i[0]){var o=i[0].node,n=o.getContext("2d");r.default.option[e].context=n,r.default.option[e].rotateLock=r.default.option[e].rotate,r.default.instance[e]&&r.default.option[e]&&!0===r.default.option[e].update?t._updataUChart(e):(o.width=a.width*t.pixel,o.height=a.height*t.pixel,o._width=a.width*t.pixel,o._height=a.height*t.pixel,setTimeout((function(){r.default.option[e].context.restore(),r.default.option[e].context.save(),t._newChart(e)}),100))}else t.showchart=!1,t.mixinDatacomErrorMessage="参数错误：开启2d模式后，未获取到dom节点，canvas-id:"+e}))}else t.inAli&&(r.default.option[e].rotateLock=r.default.option[e].rotate),r.default.option[e].context=uni.createCanvasContext(e,t),r.default.instance[e]&&r.default.option[e]&&!0===r.default.option[e].update?t._updataUChart(e):setTimeout((function(){r.default.option[e].context.restore(),r.default.option[e].context.save(),t._newChart(e)}),100)})))):(t.mixinDatacomLoading=!1,t.showchart=!1,1==t.reshow&&(t.mixinDatacomErrorMessage="布局错误：未获取到父元素宽高尺寸！canvas-id:"+e))})).exec()},saveImage:function(){var t=this;uni.canvasToTempFilePath({canvasId:this.cid,success:function(e){var a=document.createElement("a");a.href=e.tempFilePath,a.download=t.cid,a.target="_blank",a.click()}},this)},getImage:function(){var t=this;if(0==this.type2d)uni.canvasToTempFilePath({canvasId:this.cid,success:function(e){t.emitMsg({name:"getImage",params:{type:"getImage",base64:e.tempFilePath}})}},this);else{var e=uni.createSelectorQuery().in(this);e.select("#"+this.cid).fields({node:!0,size:!0}).exec((function(e){if(e[0]){var a=e[0].node;t.emitMsg({name:"getImage",params:{type:"getImage",base64:a.toDataURL("image/png")}})}}))}},_error:function(t){this.mixinDatacomErrorMessage=t.detail.errMsg},emitMsg:function(t){this.$emit(t.name,t.params)},getRenderType:function(){!0===this.echarts&&!1===this.mixinDatacomLoading&&this.beforeInit()},toJSON:function(){return this}}};e.default=h}).call(this,a("861b")["uniCloud"])},"0bf9":function(t,e,a){"use strict";a.r(e);var i=a("86ff"),o=a("68cc");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("29c1");var n=a("828b"),l=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"701420ba",null,!1,i["a"],void 0);e["default"]=l.exports},1851:function(t,e,a){"use strict";var i=a("8bdb"),o=a("84d6"),r=a("1cb5");i({target:"Array",proto:!0},{fill:o}),r("fill")},"29c1":function(t,e,a){"use strict";var i=a("753c"),o=a.n(i);o.a},"3ec2":function(t,e,a){"use strict";a.r(e);var i=a("023a"),o=a("57de");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("abe0");var n=a("828b"),l=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"b6048934",null,!1,i["a"],void 0);e["default"]=l.exports},"57de":function(t,e,a){"use strict";a.r(e);var i=a("a5f1"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},"66fa":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(a("5de6")),r=i(a("fcf3"));a("7a76"),a("c9b5"),a("bf0f"),a("ab80"),a("2797"),a("aa9c"),a("5c47"),a("a1c1"),a("e966"),a("5ef2"),a("0506"),a("473f"),a("c223"),a("fd3c"),a("8f71"),a("f7a5"),a("dc69"),a("1851"),a("4626"),a("5ac7"),a("3efd");var n={version:"v2.5.0-20230101",yAxisWidth:15,xAxisHeight:22,padding:[10,10,10,10],rotate:!1,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,titleFontSize:20,subtitleFontSize:15,radarLabelTextMargin:13},l=function(t){for(var e=arguments.length,a=new Array(e>1?e-1:0),i=1;i<e;i++)a[i-1]=arguments[i];if(null==t)throw new TypeError("[uCharts] Cannot convert undefined or null to object");if(!a||a.length<=0)return t;function o(t,e){for(var a in e)t[a]=t[a]&&"[object Object]"===t[a].toString()?o(t[a],e[a]):t[a]=e[a];return t}return a.forEach((function(e){t=o(t,e)})),t},s={toFixed:function(t,e){return e=e||2,this.isFloat(t)&&(t=t.toFixed(e)),t},isFloat:function(t){return t%1!==0},approximatelyEqual:function(t,e){return Math.abs(t-e)<1e-10},isSameSign:function(t,e){return Math.abs(t)===t&&Math.abs(e)===e||Math.abs(t)!==t&&Math.abs(e)!==e},isSameXCoordinateArea:function(t,e){return this.isSameSign(t.x,e.x)},isCollision:function(t,e){t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height,e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height;var a=e.start.x>t.end.x||e.end.x<t.start.x||e.end.y>t.start.y||e.start.y<t.end.y;return!a}};function h(t,e){var a=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(t,e,a,i){return e+e+a+a+i+i})),i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a),o=parseInt(i[1],16),r=parseInt(i[2],16),n=parseInt(i[3],16);return"rgba("+o+","+r+","+n+","+e+")"}function c(t,e,a){if(isNaN(t))throw new Error("[uCharts] series数据需为Number格式");a=a||10,e=e||"upper";var i=1;while(a<1)a*=10,i*=10;t="upper"===e?Math.ceil(t*i):Math.floor(t*i);while(t%a!==0)if("upper"===e){if(t==t+1)break;t++}else t--;return t/i}function d(t,e,a,i,o){var r=o.width-o.area[1]-o.area[3],n=a.eachSpacing*(o.chartData.xAxisData.xAxisPoints.length-1);"mount"==o.type&&o.extra&&o.extra.mount&&o.extra.mount.widthRatio&&o.extra.mount.widthRatio>1&&(o.extra.mount.widthRatio>2&&(o.extra.mount.widthRatio=2),n+=(o.extra.mount.widthRatio-1)*a.eachSpacing);var l=e;return e>=0?(l=0,t.uevent.trigger("scrollLeft"),t.scrollOption.position="left",o.xAxis.scrollPosition="left"):Math.abs(e)>=n-r?(l=r-n,t.uevent.trigger("scrollRight"),t.scrollOption.position="right",o.xAxis.scrollPosition="right"):(t.scrollOption.position=e,o.xAxis.scrollPosition=e),l}function p(t,e,a){function i(t){while(t<0)t+=2*Math.PI;while(t>2*Math.PI)t-=2*Math.PI;return t}return t=i(t),e=i(e),a=i(a),e>a&&(a+=2*Math.PI,t<e&&(t+=2*Math.PI)),t>=e&&t<=a}function u(t,e){function a(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].y>=Math.max(t[e-1].y,t[e+1].y)||t[e].y<=Math.min(t[e-1].y,t[e+1].y))}function i(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].x>=Math.max(t[e-1].x,t[e+1].x)||t[e].x<=Math.min(t[e-1].x,t[e+1].x))}var o=.2,r=.2,n=null,l=null,s=null,h=null;if(e<1?(n=t[0].x+(t[1].x-t[0].x)*o,l=t[0].y+(t[1].y-t[0].y)*o):(n=t[e].x+(t[e+1].x-t[e-1].x)*o,l=t[e].y+(t[e+1].y-t[e-1].y)*o),e>t.length-3){var c=t.length-1;s=t[c].x-(t[c].x-t[c-1].x)*r,h=t[c].y-(t[c].y-t[c-1].y)*r}else s=t[e+1].x-(t[e+2].x-t[e].x)*r,h=t[e+1].y-(t[e+2].y-t[e].y)*r;return a(t,e+1)&&(h=t[e+1].y),a(t,e)&&(l=t[e].y),i(t,e+1)&&(s=t[e+1].x),i(t,e)&&(n=t[e].x),(l>=Math.max(t[e].y,t[e+1].y)||l<=Math.min(t[e].y,t[e+1].y))&&(l=t[e].y),(h>=Math.max(t[e].y,t[e+1].y)||h<=Math.min(t[e].y,t[e+1].y))&&(h=t[e+1].y),(n>=Math.max(t[e].x,t[e+1].x)||n<=Math.min(t[e].x,t[e+1].x))&&(n=t[e].x),(s>=Math.max(t[e].x,t[e+1].x)||s<=Math.min(t[e].x,t[e+1].x))&&(s=t[e+1].x),{ctrA:{x:n,y:l},ctrB:{x:s,y:h}}}function x(t,e,a){return{x:a.x+t,y:a.y-e}}function f(t,e){if(e)while(s.isCollision(t,e))t.start.x>0?t.start.y--:t.start.x<0||t.start.y>0?t.start.y++:t.start.y--;return t}function g(t,e,a){for(var i=0,o=0;o<t.length;o++){var r=t[o];if(r.color||(r.color=a.color[i],i=(i+1)%a.color.length),r.linearIndex||(r.linearIndex=o),r.index||(r.index=0),r.type||(r.type=e.type),"undefined"==typeof r.show&&(r.show=!0),r.type||(r.type=e.type),r.pointShape||(r.pointShape="circle"),!r.legendShape)switch(r.type){case"line":r.legendShape="line";break;case"column":case"bar":r.legendShape="rect";break;case"area":case"mount":r.legendShape="triangle";break;default:r.legendShape="circle"}}return t}function y(t,e,a,i){var o=e||[];if("custom"==t&&0==o.length&&(o=i.linearColor),"custom"==t&&o.length<a.length)for(var r=a.length-o.length,n=0;n<r;n++)o.push(i.linearColor[(n+1)%i.linearColor.length]);return o}function v(t,e){var a=0,i=e-t;return a=i>=1e4?1e3:i>=1e3?100:i>=100?10:i>=10?5:i>=1?1:i>=.1?.1:i>=.01?.01:i>=.001?.001:i>=1e-4?1e-4:i>=1e-5?1e-5:1e-6,{minRange:c(t,"lower",a),maxRange:c(e,"upper",a)}}function m(t,e,a){var i=0;if(t=String(t),!1!==a&&void 0!==a&&a.setFontSize&&a.measureText)return a.setFontSize(e),a.measureText(t).width;t=t.split("");for(var o=0;o<t.length;o++){var r=t[o];/[a-zA-Z]/.test(r)?i+=7:/[0-9]/.test(r)?i+=5.5:/\./.test(r)?i+=2.7:/-/.test(r)?i+=3.25:/:/.test(r)?i+=2.5:/[\u4e00-\u9fa5]/.test(r)?i+=10:/\(|\)/.test(r)?i+=3.73:/\s/.test(r)?i+=2.5:/%/.test(r)?i+=8:i+=10}return i*e/10}function b(t){return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data)}),[])}function A(t,e){for(var a=new Array(e),i=0;i<a.length;i++)a[i]=0;for(var o=0;o<t.length;o++)for(i=0;i<a.length;i++)a[i]+=t[o].data[i];return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data).concat(a)}),[])}function S(t,e,a){var i,o;return t.clientX?e.rotate?(o=e.height-t.clientX*e.pix,i=(t.pageY-a.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):(i=t.clientX*e.pix,o=(t.pageY-a.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):e.rotate?(o=e.height-t.x*e.pix,i=t.y*e.pix):(i=t.x*e.pix,o=t.y*e.pix),{x:i,y:o}}function w(t,e,a){var i=[],o=[],r=e.constructor.toString().indexOf("Array")>-1;if(r)for(var n=F(t),l=0;l<a.length;l++)o.push(n[a[l]]);else o=t;for(var s=0;s<o.length;s++){var h=o[s],c=-1;if(c=r?e[s]:e,null!==h.data[c]&&"undefined"!==typeof h.data[c]&&h.show){var d={};d.color=h.color,d.type=h.type,d.style=h.style,d.pointShape=h.pointShape,d.disableLegend=h.disableLegend,d.legendShape=h.legendShape,d.name=h.name,d.show=h.show,d.data=h.formatter?h.formatter(h.data[c]):h.data[c],i.push(d)}}return i}function T(t,e,a){var i=t.map((function(t){return m(t,e,a)}));return Math.max.apply(null,i)}function C(t){for(var e=2*Math.PI/t,a=[],i=0;i<t;i++)a.push(e*i);return a.map((function(t){return-1*t+Math.PI/2}))}function P(t,e,a,i,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},n=e.chartData.calPoints?e.chartData.calPoints:[],l={};if(i.length>0){for(var s=[],h=0;h<i.length;h++)s.push(n[i[h]]);l=s[0][a[0]]}else for(var c=0;c<n.length;c++)if(n[c][a]){l=n[c][a];break}var d=t.map((function(t){var i=null;return e.categories&&e.categories.length>0&&(i=o[a]),{text:r.formatter?r.formatter(t,i,a,e):t.name+": "+t.data,color:t.color,legendShape:"auto"==e.extra.tooltip.legendShape?t.legendShape:e.extra.tooltip.legendShape}})),p={x:Math.round(l.x),y:Math.round(l.y)};return{textList:d,offset:p}}function D(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},r=e.chartData.xAxisPoints[a]+e.chartData.eachSpacing/2,n=t.map((function(t){return{text:o.formatter?o.formatter(t,i[a],a,e):t.name+": "+t.data,color:t.color,disableLegend:!!t.disableLegend,legendShape:"auto"==e.extra.tooltip.legendShape?t.legendShape:e.extra.tooltip.legendShape}}));n=n.filter((function(t){if(!0!==t.disableLegend)return t}));var l={x:Math.round(r),y:0};return{textList:n,offset:l}}function M(t,e,a,i,o,r){var n=a.chartData.calPoints,l=r.color.upFill,s=r.color.downFill,h=[l,l,s,l],c=[];e.map((function(e){0==i?e.data[1]-e.data[0]<0?h[1]=s:h[1]=l:(e.data[0]<t[i-1][1]&&(h[0]=s),e.data[1]<e.data[0]&&(h[1]=s),e.data[2]>t[i-1][1]&&(h[2]=l),e.data[3]<t[i-1][1]&&(h[3]=s));var o={text:"开盘："+e.data[0],color:h[0],legendShape:"auto"==a.extra.tooltip.legendShape?e.legendShape:a.extra.tooltip.legendShape},r={text:"收盘："+e.data[1],color:h[1],legendShape:"auto"==a.extra.tooltip.legendShape?e.legendShape:a.extra.tooltip.legendShape},n={text:"最低："+e.data[2],color:h[2],legendShape:"auto"==a.extra.tooltip.legendShape?e.legendShape:a.extra.tooltip.legendShape},d={text:"最高："+e.data[3],color:h[3],legendShape:"auto"==a.extra.tooltip.legendShape?e.legendShape:a.extra.tooltip.legendShape};c.push(o,r,n,d)}));for(var d=[],p={x:0,y:0},u=0;u<n.length;u++){var x=n[u];"undefined"!==typeof x[i]&&null!==x[i]&&d.push(x[i])}return p.x=Math.round(d[0][0].x),{textList:c,offset:p}}function F(t){for(var e=[],a=0;a<t.length;a++)1==t[a].show&&e.push(t[a]);return e}function L(t,e,a){return t.x<=e.width-e.area[1]+10&&t.x>=e.area[3]-10&&t.y>=e.area[0]&&t.y<=e.height-e.area[2]}function k(t,e,a){return Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)<=Math.pow(a,2)}function _(t,e){var a=[],i=[];return t.forEach((function(t,o){e.connectNulls?null!==t&&i.push(t):null!==t?i.push(t):(i.length&&a.push(i),i=[])})),i.length&&a.push(i),a}function O(t,e,a,i,o){var r={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix},n=e.xAxis.fontSize*e.pix,l=t.map((function(t,a){var i=e.xAxis.formatter?e.xAxis.formatter(t,a,e):t;return m(String(i),n,o)})),s=Math.max.apply(this,l);if(1==e.xAxis.rotateLabel){r.angle=e.xAxis.rotateAngle*Math.PI/180;var h=e.xAxis.marginTop*e.pix*2+Math.abs(s*Math.sin(r.angle));h=h<n+e.xAxis.marginTop*e.pix*2?h+e.xAxis.marginTop*e.pix*2:h,r.xAxisHeight=h}return e.enableScroll&&e.xAxis.scrollShow&&(r.xAxisHeight+=6*e.pix),e.xAxis.disabled&&(r.xAxisHeight=0),r}function I(t,e,a,i){var o=l({},{type:""},e.extra.bar),n={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix};n.ranges=function(t,e,a,i){var o,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;o="stack"==i?A(t,e.categories.length):b(t);var l=[];o=o.filter((function(t){return"object"===(0,r.default)(t)&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t})),o.map((function(t){"object"===(0,r.default)(t)?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){l.push(t)})):l.push(t[0]):l.push(t.value):l.push(t)}));var s=0,h=0;if(l.length>0&&(s=Math.min.apply(this,l),h=Math.max.apply(this,l)),n>-1?("number"===typeof e.xAxis.data[n].min&&(s=Math.min(e.xAxis.data[n].min,s)),"number"===typeof e.xAxis.data[n].max&&(h=Math.max(e.xAxis.data[n].max,h))):("number"===typeof e.xAxis.min&&(s=Math.min(e.xAxis.min,s)),"number"===typeof e.xAxis.max&&(h=Math.max(e.xAxis.max,h))),s===h){var c=h||10;h+=c}for(var d=s,p=h,u=[],x=(p-d)/e.xAxis.splitNumber,f=0;f<=e.xAxis.splitNumber;f++)u.push(d+x*f);return u}(t,e,a,o.type),n.rangesFormat=n.ranges.map((function(t){return t=s.toFixed(t,2),t}));var h=n.ranges.map((function(t){return t=s.toFixed(t,2),t}));n=Object.assign(n,Q(h,e,a));n.eachSpacing,h.map((function(t){return m(t,e.xAxis.fontSize*e.pix,i)}));return!0===e.xAxis.disabled&&(n.xAxisHeight=0),n}function E(t,e,a,i,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=o.extra.radar||{};n.max=n.max||0;for(var l=Math.max(n.max,Math.max.apply(null,b(i))),s=[],h=function(o){var n=i[o],h={};h.color=n.color,h.legendShape=n.legendShape,h.pointShape=n.pointShape,h.data=[],n.data.forEach((function(i,o){var n={};n.angle=t[o],n.proportion=i/l,n.value=i,n.position=x(a*n.proportion*r*Math.cos(n.angle),a*n.proportion*r*Math.sin(n.angle),e),h.data.push(n)})),s.push(h)},c=0;c<i.length;c++)h(c);return s}function z(t,e){for(var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=0,o=0,r=0;r<t.length;r++){var n=t[r];n.data=null===n.data?0:n.data,i+=n.data}for(var l=0;l<t.length;l++){var s=t[l];s.data=null===s.data?0:s.data,s._proportion_=0===i?1/t.length*a:s.data/i*a,s._radius_=e}for(var h=0;h<t.length;h++){var c=t[h];c._start_=o,o+=2*c._proportion_*Math.PI}return t}function B(t,e,a,i){for(var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=0;r<t.length;r++)"funnel"==a.type?t[r].radius=t[r].data/t[0].data*e*o:t[r].radius=i*(t.length-r)/(i*t.length)*e*o,t[r]._proportion_=t[r].data/t[0].data;return t}function W(t,e,a,i){for(var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=0,n=0,l=[],s=0;s<t.length;s++){var h=t[s];h.data=null===h.data?0:h.data,r+=h.data,l.push(h.data)}for(var c=Math.min.apply(null,l),d=Math.max.apply(null,l),p=i-a,u=0;u<t.length;u++){var x=t[u];x.data=null===x.data?0:x.data,0===r?(x._proportion_=1/t.length*o,x._rose_proportion_=1/t.length*o):(x._proportion_=x.data/r*o,x._rose_proportion_="area"==e?1/t.length*o:x.data/r*o),x._radius_=a+p*((x.data-c)/(d-c))||i}for(var f=0;f<t.length;f++){var g=t[f];g._start_=n,n+=2*g._rose_proportion_*Math.PI}return t}function R(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==a&&(a=.999999);for(var i=0;i<t.length;i++){var o=t[i];o.data=null===o.data?0:o.data;var r=void 0;r="circle"==e.type?2:"ccw"==e.direction?e.startAngle<e.endAngle?2+e.startAngle-e.endAngle:e.startAngle-e.endAngle:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,o._proportion_=r*o.data*a+e.startAngle,"ccw"==e.direction&&(o._proportion_=e.startAngle-r*o.data*a),o._proportion_>=2&&(o._proportion_=o._proportion_%2)}return t}function G(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==a&&(a=.999999);for(var i=0;i<t.length;i++){var o=t[i];o.data=null===o.data?0:o.data;var r=void 0;r="circle"==e.type?2:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,o._proportion_=r*o.data*a+e.startAngle,o._proportion_>=2&&(o._proportion_=o._proportion_%2)}return t}function N(t,e,a){var i;i=a<e?2+a-e:e-a;for(var o=e,r=0;r<t.length;r++)t[r].value=null===t[r].value?0:t[r].value,t[r]._startAngle_=o,t[r]._endAngle_=i*t[r].value+e,t[r]._endAngle_>=2&&(t[r]._endAngle_=t[r]._endAngle_%2),o=t[r]._endAngle_;return t}function Y(t,e,a){for(var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,o=0;o<t.length;o++){var r=t[o];if(r.data=null===r.data?0:r.data,"auto"==a.pointer.color){for(var n=0;n<e.length;n++)if(r.data<=e[n].value){r.color=e[n].color;break}}else r.color=a.pointer.color;var l=void 0;l=a.endAngle<a.startAngle?2+a.endAngle-a.startAngle:a.startAngle-a.endAngle,r._endAngle_=l*r.data+a.startAngle,r._oldAngle_=a.oldAngle,a.oldAngle<a.endAngle&&(r._oldAngle_+=2),r.data>=a.oldData?r._proportion_=(r._endAngle_-r._oldAngle_)*i+a.oldAngle:r._proportion_=r._oldAngle_-(r._oldAngle_-r._endAngle_)*i,r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return t}function j(t,e,a,i,o,r){return t.map((function(t){if(null===t)return null;var o=0,n=0;return"mix"==r.type?(o=r.extra.mix.column.seriesGap*r.pix||0,n=r.extra.mix.column.categoryGap*r.pix||0):(o=r.extra.column.seriesGap*r.pix||0,n=r.extra.column.categoryGap*r.pix||0),o=Math.min(o,e/a),n=Math.min(n,e/a),t.width=Math.ceil((e-2*n-o*(a-1))/a),r.extra.mix&&r.extra.mix.column.width&&+r.extra.mix.column.width>0&&(t.width=Math.min(t.width,+r.extra.mix.column.width*r.pix)),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),t.width<=0&&(t.width=1),t.x+=(i+.5-a/2)*(t.width+o),t}))}function H(t,e,a,i,o,r){return t.map((function(t){if(null===t)return null;var o=0,n=0;return o=r.extra.bar.seriesGap*r.pix||0,n=r.extra.bar.categoryGap*r.pix||0,o=Math.min(o,e/a),n=Math.min(n,e/a),t.width=Math.ceil((e-2*n-o*(a-1))/a),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(t.width=Math.min(t.width,+r.extra.bar.width*r.pix)),t.width<=0&&(t.width=1),t.y+=(i+.5-a/2)*(t.width+o),t}))}function X(t,e,a,i,o,r,n){var l=r.extra.column.categoryGap*r.pix||0;return t.map((function(t){return null===t?null:(t.width=e-2*l,r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),i>0&&(t.width-=n),t)}))}function J(t,e,a,i,o,r,n){var l=r.extra.column.categoryGap*r.pix||0;return t.map((function(t,a){return null===t?null:(t.width=Math.ceil(e-2*l),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),t.width<=0&&(t.width=1),t)}))}function q(t,e,a,i,o,r,n){var l=r.extra.bar.categoryGap*r.pix||0;return t.map((function(t,a){return null===t?null:(t.width=Math.ceil(e-2*l),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(t.width=Math.min(t.width,+r.extra.bar.width*r.pix)),t.width<=0&&(t.width=1),t)}))}function Q(t,e,a){var i=e.width-e.area[1]-e.area[3],o=e.enableScroll?Math.min(e.xAxis.itemCount,t.length):t.length;("line"==e.type||"area"==e.type||"scatter"==e.type||"bubble"==e.type||"bar"==e.type)&&o>1&&"justify"==e.xAxis.boundaryGap&&(o-=1);var r=0;"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),r=e.extra.mount.widthRatio-1,o+=r);var n=i/o,l=[],s=e.area[3],h=e.width-e.area[1];return t.forEach((function(t,e){l.push(s+r/2*n+e*n)})),"justify"!==e.xAxis.boundaryGap&&(!0===e.enableScroll?l.push(s+r*n+t.length*n):l.push(h)),{xAxisPoints:l,startX:s,endX:h,eachSpacing:n}}function U(t,e,a,i,o,r,n){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,s=[],h=r.height-r.area[0]-r.area[2];return t.forEach((function(t,n){if(null===t)s.push(null);else{var c=[];t.forEach((function(t,s){var d={};d.x=i[n]+Math.round(o/2);var p=t.value||t,u=h*(p-e)/(a-e);u*=l,d.y=r.height-Math.round(u)-r.area[2],c.push(d)})),s.push(c)}})),s}function Z(t,e,a,i,o,n,l){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,h="center";"line"!=n.type&&"area"!=n.type&&"scatter"!=n.type&&"bubble"!=n.type||(h=n.xAxis.boundaryGap);var c=[],d=n.height-n.area[0]-n.area[2],p=n.width-n.area[1]-n.area[3];return t.forEach((function(t,l){if(null===t)c.push(null);else{var u={};u.color=t.color,u.x=i[l];var x,f,g,y=t;if("object"===(0,r.default)(t)&&null!==t)if(t.constructor.toString().indexOf("Array")>-1)x=[].concat(n.chartData.xAxisData.ranges),f=x.shift(),g=x.pop(),y=t[1],u.x=n.area[3]+p*(t[0]-f)/(g-f),"bubble"==n.type&&(u.r=t[2],u.t=t[3]);else y=t.value;"center"==h&&(u.x+=o/2);var v=d*(y-e)/(a-e);v*=s,u.y=n.height-v-n.area[2],c.push(u)}})),c}function V(t,e,a,i,o,n,l,s,h){h=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var c=n.xAxis.boundaryGap,d=[],p=n.height-n.area[0]-n.area[2],u=n.width-n.area[1]-n.area[3];return t.forEach((function(t,l){if(null===t)d.push(null);else{var x={};if(x.color=t.color,"vertical"==s.animation){x.x=i[l];var f,g,y,v=t;if("object"===(0,r.default)(t)&&null!==t)if(t.constructor.toString().indexOf("Array")>-1)f=[].concat(n.chartData.xAxisData.ranges),g=f.shift(),y=f.pop(),v=t[1],x.x=n.area[3]+u*(t[0]-g)/(y-g);else v=t.value;"center"==c&&(x.x+=o/2);var m=p*(v-e)/(a-e);m*=h,x.y=n.height-m-n.area[2],d.push(x)}else{x.x=i[0]+o*l*h;v=t;"center"==c&&(x.x+=o/2);m=p*(v-e)/(a-e);x.y=n.height-m-n.area[2],d.push(x)}}})),d}function K(t,e,a,i,o,n,l,s,h){h=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var c=[],d=n.height-n.area[0]-n.area[2],p=n.width-n.area[1]-n.area[3];return t.forEach((function(t,l){if(null===t)c.push(null);else{var s={};s.color=t.color,s.x=i[l];var u,x,f,g=t;if("object"===(0,r.default)(t)&&null!==t)if(t.constructor.toString().indexOf("Array")>-1)u=[].concat(n.chartData.xAxisData.ranges),x=u.shift(),f=u.pop(),g=t[1],s.x=n.area[3]+p*(t[0]-x)/(f-x);else g=t.value;s.x+=o/2;var y=d*(g*h-e)/(a-e);s.y=n.height-y-n.area[2],c.push(s)}})),c}function $(t,e,a,i,o,r,n,l){var s=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1,h=[],c=r.height-r.area[0]-r.area[2],d=(r.width,r.area[1],r.area[3],o*n.widthRatio);return t.forEach((function(t,n){if(null===t)h.push(null);else{var l={};l.color=t.color,l.x=i[n],l.x+=o/2;var p=t.data,u=c*(p*s-e)/(a-e);l.y=r.height-u-r.area[2],l.value=p,l.width=d,h.push(l)}})),h}function tt(t,e,a,i,o,n,l){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,h=[],c=(n.height,n.area[0],n.area[2],n.width-n.area[1]-n.area[3]);return t.forEach((function(t,o){if(null===t)h.push(null);else{var l={};l.color=t.color,l.y=i[o];var d=t;"object"===(0,r.default)(t)&&null!==t&&(d=t.value);var p=c*(d-e)/(a-e);p*=s,l.height=p,l.value=d,l.x=p+n.area[3],h.push(l)}})),h}function et(t,e,a,i,o,n,l,s,h){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,d=[],p=n.height-n.area[0]-n.area[2];return t.forEach((function(t,l){if(null===t)d.push(null);else{var u={};if(u.color=t.color,u.x=i[l]+Math.round(o/2),s>0){for(var x=0,f=0;f<=s;f++)x+=h[f].data[l];var g=x-t,y=p*(x-e)/(a-e),v=p*(g-e)/(a-e)}else{x=t;"object"===(0,r.default)(t)&&null!==t&&(x=t.value);y=p*(x-e)/(a-e),v=0}var m=v;y*=c,m*=c,u.y=n.height-Math.round(y)-n.area[2],u.y0=n.height-Math.round(m)-n.area[2],d.push(u)}})),d}function at(t,e,a,i,o,n,l,s,h){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,d=[],p=n.width-n.area[1]-n.area[3];return t.forEach((function(t,o){if(null===t)d.push(null);else{var l={};if(l.color=t.color,l.y=i[o],s>0){for(var u=0,x=0;x<=s;x++)u+=h[x].data[o];var f=u-t,g=p*(u-e)/(a-e),y=p*(f-e)/(a-e)}else{u=t;"object"===(0,r.default)(t)&&null!==t&&(u=t.value);g=p*(u-e)/(a-e),y=0}var v=y;g*=c,v*=c,l.height=g-v,l.x=n.area[3]+g,l.x0=n.area[3]+v,d.push(l)}})),d}function it(t,e,a,i,o){var n;n="stack"==i?A(t,e.categories.length):b(t);var l=[];n=n.filter((function(t){return"object"===(0,r.default)(t)&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t})),n.map((function(t){"object"===(0,r.default)(t)?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){l.push(t)})):l.push(t[1]):l.push(t.value):l.push(t)}));var s=o.min||0,h=o.max||0;l.length>0&&(s=Math.min.apply(this,l),h=Math.max.apply(this,l)),s===h&&(0==h?h=10:s=0);for(var c=v(s,h),d=void 0===o.min||null===o.min?c.minRange:o.min,p=void 0===o.max||null===o.max?c.maxRange:o.max,u=(p-d)/e.yAxis.splitNumber,x=[],f=0;f<=e.yAxis.splitNumber;f++)x.push(d+u*f);return x.reverse()}function ot(t,e,a,i){var o=l({},{type:""},e.extra.column),r=e.yAxis.data.length,n=new Array(r);if(r>0){for(var h=0;h<r;h++){n[h]=[];for(var c=0;c<t.length;c++)t[c].index==h&&n[h].push(t[c])}for(var d=new Array(r),p=new Array(r),u=new Array(r),x=function(t){var r=e.yAxis.data[t];1==e.yAxis.disabled&&(r.disabled=!0),"categories"===r.type?(r.formatter||(r.formatter=function(t,e,a){return t+(r.unit||"")}),r.categories=r.categories||e.categories,d[t]=r.categories):(r.formatter||(r.formatter=function(t,e,a){return s.toFixed(t,r.tofix||0)+(r.unit||"")}),d[t]=it(n[t],e,a,o.type,r,t));var l=r.fontSize*e.pix||a.fontSize;u[t]={position:r.position?r.position:"left",width:0},p[t]=d[t].map((function(a,o){return a=r.formatter(a,o,e),u[t].width=Math.max(u[t].width,m(a,l,i)+5),a}));var h=r.calibration?4*e.pix:0;u[t].width+=h+3*e.pix,!0===r.disabled&&(u[t].width=0)},f=0;f<r;f++)x(f)}else{d=new Array(1),p=new Array(1),u=new Array(1);"bar"===e.type?(d[0]=e.categories,e.yAxis.formatter||(e.yAxis.formatter=function(t,e,a){return t+(a.yAxis.unit||"")})):(e.yAxis.formatter||(e.yAxis.formatter=function(t,e,a){return t.toFixed(a.yAxis.tofix)+(a.yAxis.unit||"")}),d[0]=it(t,e,a,o.type,{})),u[0]={position:"left",width:0};var g=e.yAxis.fontSize*e.pix||a.fontSize;p[0]=d[0].map((function(t,a){return t=e.yAxis.formatter(t,a,e),u[0].width=Math.max(u[0].width,m(t,g,i)+5),t})),u[0].width+=3*e.pix,!0===e.yAxis.disabled?(u[0]={position:"left",width:0},e.yAxis.data[0]={disabled:!0}):(e.yAxis.data[0]={disabled:!1,position:"left",max:e.yAxis.max,min:e.yAxis.min,formatter:e.yAxis.formatter},"bar"===e.type&&(e.yAxis.data[0].categories=e.categories,e.yAxis.data[0].type="categories"))}return{rangesFormat:p,ranges:d,yAxisWidth:u}}function rt(t,e){!0!==e.rotateLock?(t.translate(e.height,0),t.rotate(90*Math.PI/180)):!0!==e._rotate_&&(t.translate(e.height,0),t.rotate(90*Math.PI/180),e._rotate_=!0)}function nt(t,e,a,i,o){if(i.beginPath(),"hollow"==o.dataPointShapeType?(i.setStrokeStyle(e),i.setFillStyle(o.background),i.setLineWidth(2*o.pix)):(i.setStrokeStyle("#ffffff"),i.setFillStyle(e),i.setLineWidth(1*o.pix)),"diamond"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y),i.lineTo(t.x,t.y****),i.lineTo(t.x****,t.y),i.lineTo(t.x,t.y-4.5))}));else if("circle"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x*****o.pix,t.y),i.arc(t.x,t.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x-3.5,t.y-3.5),i.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y****),i.lineTo(t.x****,t.y****),i.lineTo(t.x,t.y-4.5))}));else if("none"===a)return;i.closePath(),i.fill(),i.stroke()}function lt(t,e,a,i,o,r,n){if(o.tooltip&&!(o.tooltip.group.length>0&&0==o.tooltip.group.includes(n))){var l="number"===typeof o.tooltip.index?o.tooltip.index:o.tooltip.index[o.tooltip.group.indexOf(n)];if(i.beginPath(),"hollow"==r.activeType?(i.setStrokeStyle(e),i.setFillStyle(o.background),i.setLineWidth(2*o.pix)):(i.setStrokeStyle("#ffffff"),i.setFillStyle(e),i.setLineWidth(1*o.pix)),"diamond"===a)t.forEach((function(t,e){null!==t&&l==e&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y),i.lineTo(t.x,t.y****),i.lineTo(t.x****,t.y),i.lineTo(t.x,t.y-4.5))}));else if("circle"===a)t.forEach((function(t,e){null!==t&&l==e&&(i.moveTo(t.x*****o.pix,t.y),i.arc(t.x,t.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===a)t.forEach((function(t,e){null!==t&&l==e&&(i.moveTo(t.x-3.5,t.y-3.5),i.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===a)t.forEach((function(t,e){null!==t&&l==e&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y****),i.lineTo(t.x****,t.y****),i.lineTo(t.x,t.y-4.5))}));else if("none"===a)return;i.closePath(),i.fill(),i.stroke()}}function st(t,e,a,i){var o=t.title.fontSize||e.titleFontSize,r=t.subtitle.fontSize||e.subtitleFontSize,n=t.title.name||"",l=t.subtitle.name||"",s=t.title.color||t.fontColor,h=t.subtitle.color||t.fontColor,c=n?o:0,d=l?r:0;if(l){var p=m(l,r*t.pix,a),u=i.x-p/2+(t.subtitle.offsetX||0)*t.pix,x=i.y+r*t.pix/2+(t.subtitle.offsetY||0)*t.pix;n&&(x+=(c*t.pix+5)/2),a.beginPath(),a.setFontSize(r*t.pix),a.setFillStyle(h),a.fillText(l,u,x),a.closePath(),a.stroke()}if(n){var f=m(n,o*t.pix,a),g=i.x-f/2+(t.title.offsetX||0),y=i.y+o*t.pix/2+(t.title.offsetY||0)*t.pix;l&&(y-=(d*t.pix+5)/2),a.beginPath(),a.setFontSize(o*t.pix),a.setFillStyle(s),a.fillText(n,g,y),a.closePath(),a.stroke()}}function ht(t,e,a,i,o){var n=e.data,l=e.textOffset?e.textOffset:0;t.forEach((function(t,s){if(null!==t){i.beginPath();var h=e.textSize?e.textSize*o.pix:a.fontSize;i.setFontSize(h),i.setFillStyle(e.textColor||o.fontColor);var c=n[s];"object"===(0,r.default)(n[s])&&null!==n[s]&&(c=n[s].constructor.toString().indexOf("Array")>-1?n[s][1]:n[s].value);var d=e.formatter?e.formatter(c,s,e,o):c;i.setTextAlign("center"),i.fillText(String(d),t.x,t.y-4+l*o.pix),i.closePath(),i.stroke(),i.setTextAlign("left")}}))}function ct(t,e,a,i,o){var n=e.data,l=e.textOffset?e.textOffset:0,s=o.extra.column.labelPosition;t.forEach((function(t,h){if(null!==t){i.beginPath();var c=e.textSize?e.textSize*o.pix:a.fontSize;i.setFontSize(c),i.setFillStyle(e.textColor||o.fontColor);var d=n[h];"object"===(0,r.default)(n[h])&&null!==n[h]&&(d=n[h].constructor.toString().indexOf("Array")>-1?n[h][1]:n[h].value);var p=e.formatter?e.formatter(d,h,e,o):d;i.setTextAlign("center");var u=t.y-4*o.pix+l*o.pix;t.y>e.zeroPoints&&(u=t.y+l*o.pix+c),"insideTop"==s&&(u=t.y+c+l*o.pix,t.y>e.zeroPoints&&(u=t.y-l*o.pix-4*o.pix)),"center"==s&&(u=t.y+l*o.pix+(o.height-o.area[2]-t.y+c)/2,e.zeroPoints<o.height-o.area[2]&&(u=t.y+l*o.pix+(e.zeroPoints-t.y+c)/2),t.y>e.zeroPoints&&(u=t.y-l*o.pix-(t.y-e.zeroPoints-c)/2),"stack"==o.extra.column.type&&(u=t.y+l*o.pix+(t.y0-t.y+c)/2)),"bottom"==s&&(u=o.height-o.area[2]+l*o.pix-4*o.pix,e.zeroPoints<o.height-o.area[2]&&(u=e.zeroPoints+l*o.pix-4*o.pix),t.y>e.zeroPoints&&(u=e.zeroPoints-l*o.pix+c+2*o.pix),"stack"==o.extra.column.type&&(u=t.y0+l*o.pix-4*o.pix)),i.fillText(String(p),t.x,u),i.closePath(),i.stroke(),i.setTextAlign("left")}}))}function dt(t,e,a,i,o,r){e.data;var n=e.textOffset?e.textOffset:0;o.extra.mount.labelPosition;t.forEach((function(t,l){if(null!==t){i.beginPath();var s=e[l].textSize?e[l].textSize*o.pix:a.fontSize;i.setFontSize(s),i.setFillStyle(e[l].textColor||o.fontColor);var h=t.value,c=e[l].formatter?e[l].formatter(h,l,e,o):h;i.setTextAlign("center");var d=t.y-4*o.pix+n*o.pix;t.y>r&&(d=t.y+n*o.pix+s),i.fillText(String(c),t.x,d),i.closePath(),i.stroke(),i.setTextAlign("left")}}))}function pt(t,e,a,i,o){var n=e.data;e.textOffset&&e.textOffset;t.forEach((function(t,l){if(null!==t){i.beginPath();var s=e.textSize?e.textSize*o.pix:a.fontSize;i.setFontSize(s),i.setFillStyle(e.textColor||o.fontColor);var h=n[l];"object"===(0,r.default)(n[l])&&null!==n[l]&&(h=n[l].value);var c=e.formatter?e.formatter(h,l,e,o):h;i.setTextAlign("left"),i.fillText(String(c),t.x+4*o.pix,t.y+s/2-3),i.closePath(),i.stroke()}}))}function ut(t,e,a,i,o,r){var n;e-=t.width/2+t.labelOffset*i.pix,e=e<10?10:e,n=t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle;for(var l=n/t.splitLine.splitNumber,s=t.endNumber-t.startNumber,h=s/t.splitLine.splitNumber,c=t.startAngle,d=t.startNumber,p=0;p<t.splitLine.splitNumber+1;p++){var u={x:e*Math.cos(c*Math.PI),y:e*Math.sin(c*Math.PI)},x=t.formatter?t.formatter(d,p,i):d;u.x+=a.x-m(x,o.fontSize,r)/2,u.y+=a.y;var f=u.x,g=u.y;r.beginPath(),r.setFontSize(o.fontSize),r.setFillStyle(t.labelColor||i.fontColor),r.fillText(x,f,g+o.fontSize/2),r.closePath(),r.stroke(),c+=l,c>=2&&(c%=2),d+=h}}function xt(t,e,a,i,o,r){var n=i.extra.radar||{};t.forEach((function(t,l){if(!0===n.labelPointShow&&""!==i.categories[l]){var h={x:e*Math.cos(t),y:e*Math.sin(t)},c=x(h.x,h.y,a);r.setFillStyle(n.labelPointColor),r.beginPath(),r.arc(c.x,c.y,n.labelPointRadius*i.pix,0,2*Math.PI,!1),r.closePath(),r.fill()}if(!0===n.labelShow){var d={x:(e+o.radarLabelTextMargin*i.pix)*Math.cos(t),y:(e+o.radarLabelTextMargin*i.pix)*Math.sin(t)},p=x(d.x,d.y,a),u=p.x,f=p.y;s.approximatelyEqual(d.x,0)?u-=m(i.categories[l]||"",o.fontSize,r)/2:d.x<0&&(u-=m(i.categories[l]||"",o.fontSize,r)),r.beginPath(),r.setFontSize(o.fontSize),r.setFillStyle(n.labelColor||i.fontColor),r.fillText(i.categories[l]||"",u,f+o.fontSize/2),r.closePath(),r.stroke()}}))}function ft(t,e,a,i,o,r){for(var n=a.pieChartLinePadding,l=[],h=null,c=t.map((function(a,i){var o=a.formatter?a.formatter(a,i,t,e):s.toFixed(100*a._proportion_.toFixed(4))+"%";o=a.labelText?a.labelText:o;var r=2*Math.PI-(a._start_+2*Math.PI*a._proportion_/2);a._rose_proportion_&&(r=2*Math.PI-(a._start_+2*Math.PI*a._rose_proportion_/2));var n=a.color,l=a._radius_;return{arc:r,text:o,color:n,radius:l,textColor:a.textColor,textSize:a.textSize,labelShow:a.labelShow}})),d=0;d<c.length;d++){var p=c[d],u=Math.cos(p.arc)*(p.radius+n),g=Math.sin(p.arc)*(p.radius+n),y=Math.cos(p.arc)*p.radius,v=Math.sin(p.arc)*p.radius,b=u>=0?u+a.pieChartTextPadding:u-a.pieChartTextPadding,A=g,S=m(p.text,p.textSize*e.pix||a.fontSize,i),w=A;h&&s.isSameXCoordinateArea(h.start,{x:b})&&(w=b>0?Math.min(A,h.start.y):u<0||A>0?Math.max(A,h.start.y):Math.min(A,h.start.y)),b<0&&(b-=S);var T={lineStart:{x:y,y:v},lineEnd:{x:u,y:g},start:{x:b,y:w},width:S,height:a.fontSize,text:p.text,color:p.color,textColor:p.textColor,textSize:p.textSize};h=f(T,h),l.push(h)}for(var C=0;C<l.length;C++)if(!1!==c[C].labelShow){var P=l[C],D=x(P.lineStart.x,P.lineStart.y,r),M=x(P.lineEnd.x,P.lineEnd.y,r),F=x(P.start.x,P.start.y,r);i.setLineWidth(1*e.pix),i.setFontSize(P.textSize*e.pix||a.fontSize),i.beginPath(),i.setStrokeStyle(P.color),i.setFillStyle(P.color),i.moveTo(D.x,D.y);var L=P.start.x<0?F.x+P.width:F.x,k=P.start.x<0?F.x-5:F.x+5;i.quadraticCurveTo(M.x,M.y,L,F.y),i.moveTo(D.x,D.y),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(F.x+P.width,F.y),i.arc(L,F.y,2*e.pix,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(P.textSize*e.pix||a.fontSize),i.setFillStyle(P.textColor||e.fontColor),i.fillText(P.text,k,F.y+3),i.closePath(),i.stroke(),i.closePath()}}function gt(t,e,a){for(var i=l({},{type:"solid",dashLength:4,data:[]},t.extra.markLine),o=t.area[3],r=t.width-t.area[1],n=function(t,e){for(var a,i,o=e.height-e.area[0]-e.area[2],r=0;r<t.length;r++){t[r].yAxisIndex=t[r].yAxisIndex?t[r].yAxisIndex:0;var n=[].concat(e.chartData.yAxisData.ranges[t[r].yAxisIndex]);a=n.pop(),i=n.shift();var l=o*(t[r].value-a)/(i-a);t[r].y=e.height-Math.round(l)-e.area[2]}return t}(i.data,t),s=0;s<n.length;s++){var c=l({},{lineColor:"#DE4A42",showLabel:!1,labelFontSize:13,labelPadding:6,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,labelAlign:"left",labelOffsetX:0,labelOffsetY:0},n[s]);if("dash"==i.type&&a.setLineDash([i.dashLength,i.dashLength]),a.setStrokeStyle(c.lineColor),a.setLineWidth(1*t.pix),a.beginPath(),a.moveTo(o,c.y),a.lineTo(r,c.y),a.stroke(),a.setLineDash([]),c.showLabel){var d=c.labelFontSize*t.pix,p=c.labelText?c.labelText:c.value;a.setFontSize(d);var u=m(p,d,a),x=u+c.labelPadding*t.pix*2,f="left"==c.labelAlign?t.area[3]-x:t.width-t.area[1];f+=c.labelOffsetX;var g=c.y-.5*d-c.labelPadding*t.pix;g+=c.labelOffsetY;var y=f+c.labelPadding*t.pix;c.y;a.setFillStyle(h(c.labelBgColor,c.labelBgOpacity)),a.setStrokeStyle(c.labelBgColor),a.setLineWidth(1*t.pix),a.beginPath(),a.rect(f,g,x,d+2*c.labelPadding*t.pix),a.closePath(),a.stroke(),a.fill(),a.setFontSize(d),a.setTextAlign("left"),a.setFillStyle(c.labelFontColor),a.fillText(String(p),y,g+d+c.labelPadding*t.pix/2),a.stroke(),a.setTextAlign("left")}}}function yt(t,e,a,i,o){var r=l({},{gridType:"solid",dashLength:4},t.extra.tooltip),n=t.area[3],s=t.width-t.area[1];if("dash"==r.gridType&&a.setLineDash([r.dashLength,r.dashLength]),a.setStrokeStyle(r.gridColor||"#cccccc"),a.setLineWidth(1*t.pix),a.beginPath(),a.moveTo(n,t.tooltip.offset.y),a.lineTo(s,t.tooltip.offset.y),a.stroke(),a.setLineDash([]),r.yAxisLabel)for(var c=r.boxPadding*t.pix,d=function(t,e,a,i,o){for(var r=[].concat(a.chartData.yAxisData.ranges),n=a.height-a.area[0]-a.area[2],l=a.area[0],s=[],h=0;h<r.length;h++){var c=Math.max.apply(this,r[h]),d=Math.min.apply(this,r[h]),p=c-(c-d)*(t-l)/n;p=a.yAxis.data&&a.yAxis.data[h].formatter?a.yAxis.data[h].formatter(p,h,a):p.toFixed(0),s.push(String(p))}return s}(t.tooltip.offset.y,t.series,t),p=t.chartData.yAxisData.yAxisWidth,u=t.area[3],x=t.width-t.area[1],f=0;f<d.length;f++){a.setFontSize(r.fontSize*t.pix);var g,y=m(d[f],r.fontSize*t.pix,a),v=void 0,b=void 0;"left"==p[f].position?(v=u-(y+2*c)-2*t.pix,b=Math.max(v,v+y+2*c)):(v=x+2*t.pix,b=Math.max(v+p[f].width,v+y+2*c)),g=b-v;var A=v+(g-y)/2,S=t.tooltip.offset.y;a.beginPath(),a.setFillStyle(h(r.labelBgColor||e.toolTipBackground,r.labelBgOpacity||e.toolTipOpacity)),a.setStrokeStyle(r.labelBgColor||e.toolTipBackground),a.setLineWidth(1*t.pix),a.rect(v,S-.5*e.fontSize-c,g,e.fontSize+2*c),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(e.fontSize),a.setFillStyle(r.labelFontColor||t.fontColor),a.fillText(d[f],A,S+.5*e.fontSize),a.closePath(),a.stroke(),"left"==p[f].position?u-=p[f].width+t.yAxis.padding*t.pix:x+=p[f].width+t.yAxis.padding*t.pix}}function vt(t,e,a,i,o){var r=l({},{activeBgColor:"#000000",activeBgOpacity:.08,activeWidth:o},e.extra.column);r.activeWidth=r.activeWidth>o?o:r.activeWidth;var n=e.area[0],s=e.height-e.area[2];i.beginPath(),i.setFillStyle(h(r.activeBgColor,r.activeBgOpacity)),i.rect(t-r.activeWidth/2,n,r.activeWidth,s-n),i.closePath(),i.fill(),i.setFillStyle("#FFFFFF")}function mt(t,e,a,i,o){var r=l({},{activeBgColor:"#000000",activeBgOpacity:.08},e.extra.bar),n=e.area[3],s=e.width-e.area[1];i.beginPath(),i.setFillStyle(h(r.activeBgColor,r.activeBgOpacity)),i.rect(n,t-o/2,s-n,o),i.closePath(),i.fill(),i.setFillStyle("#FFFFFF")}function bt(t,e,a,i,o,r,n){var s=l({},{showBox:!0,showArrow:!0,showCategory:!1,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,boxPadding:3,fontColor:"#FFFFFF",fontSize:13,lineHeight:20,legendShow:!0,legendShape:"auto",splitLine:!0},a.extra.tooltip);1==s.showCategory&&a.categories&&t.unshift({text:a.categories[a.tooltip.index],color:null});var c=s.fontSize*a.pix,d=s.lineHeight*a.pix,p=s.boxPadding*a.pix,u=c,x=5*a.pix;0==s.legendShow&&(u=0,x=0);var f=s.showArrow?8*a.pix:0,g=!1;"line"!=a.type&&"mount"!=a.type&&"area"!=a.type&&"candle"!=a.type&&"mix"!=a.type||1==s.splitLine&&function(t,e,a,i){var o=e.extra.tooltip||{};o.gridType=void 0==o.gridType?"solid":o.gridType,o.dashLength=void 0==o.dashLength?4:o.dashLength;var r=e.area[0],n=e.height-e.area[2];if("dash"==o.gridType&&i.setLineDash([o.dashLength,o.dashLength]),i.setStrokeStyle(o.gridColor||"#cccccc"),i.setLineWidth(1*e.pix),i.beginPath(),i.moveTo(t,r),i.lineTo(t,n),i.stroke(),i.setLineDash([]),o.xAxisLabel){var l=e.categories[e.tooltip.index];i.setFontSize(a.fontSize);var s=m(l,a.fontSize,i),c=t-.5*s,d=n+2*e.pix;i.beginPath(),i.setFillStyle(h(o.labelBgColor||a.toolTipBackground,o.labelBgOpacity||a.toolTipOpacity)),i.setStrokeStyle(o.labelBgColor||a.toolTipBackground),i.setLineWidth(1*e.pix),i.rect(c-o.boxPadding*e.pix,d,s+2*o.boxPadding*e.pix,a.fontSize+2*o.boxPadding*e.pix),i.closePath(),i.stroke(),i.fill(),i.beginPath(),i.setFontSize(a.fontSize),i.setFillStyle(o.labelFontColor||e.fontColor),i.fillText(String(l),c,d+o.boxPadding*e.pix+a.fontSize),i.closePath(),i.stroke()}}(a.tooltip.offset.x,a,i,o),e=l({x:0,y:0},e),e.y-=8*a.pix;var y=t.map((function(t){return m(t.text,c,o)})),v=u+x+4*p+Math.max.apply(null,y),b=2*p+t.length*d;if(0!=s.showBox){e.x-Math.abs(a._scrollDistance_||0)+f+v>a.width&&(g=!0),b+e.y>a.height&&(e.y=a.height-b),o.beginPath(),o.setFillStyle(h(s.bgColor,s.bgOpacity)),o.setLineWidth(s.borderWidth*a.pix),o.setStrokeStyle(h(s.borderColor,s.borderOpacity));var A=s.borderRadius;g?(v+f>a.width&&(e.x=a.width+Math.abs(a._scrollDistance_||0)+f+(v-a.width)),v>e.x&&(e.x=a.width+Math.abs(a._scrollDistance_||0)+f+(v-a.width)),s.showArrow&&(o.moveTo(e.x,e.y+10*a.pix),o.lineTo(e.x-f,e.y+10*a.pix+5*a.pix)),o.arc(e.x-f-A,e.y+b-A,A,0,Math.PI/2,!1),o.arc(e.x-f-Math.round(v)+A,e.y+b-A,A,Math.PI/2,Math.PI,!1),o.arc(e.x-f-Math.round(v)+A,e.y+A,A,-Math.PI,-Math.PI/2,!1),o.arc(e.x-f-A,e.y+A,A,-Math.PI/2,0,!1),s.showArrow&&(o.lineTo(e.x-f,e.y+10*a.pix-5*a.pix),o.lineTo(e.x,e.y+10*a.pix))):(s.showArrow&&(o.moveTo(e.x,e.y+10*a.pix),o.lineTo(e.x+f,e.y+10*a.pix-5*a.pix)),o.arc(e.x+f+A,e.y+A,A,-Math.PI,-Math.PI/2,!1),o.arc(e.x+f+Math.round(v)-A,e.y+A,A,-Math.PI/2,0,!1),o.arc(e.x+f+Math.round(v)-A,e.y+b-A,A,0,Math.PI/2,!1),o.arc(e.x+f+A,e.y+b-A,A,Math.PI/2,Math.PI,!1),s.showArrow&&(o.lineTo(e.x+f,e.y+10*a.pix+5*a.pix),o.lineTo(e.x,e.y+10*a.pix))),o.closePath(),o.fill(),s.borderWidth>0&&o.stroke(),s.legendShow&&t.forEach((function(t,i){if(null!==t.color){o.beginPath(),o.setFillStyle(t.color);var r=e.x+f+2*p,n=e.y+(d-c)/2+d*i+p+1;switch(g&&(r=e.x-v-f+2*p),t.legendShape){case"line":o.moveTo(r,n+.5*u-2*a.pix),o.fillRect(r,n+.5*u-2*a.pix,u,4*a.pix);break;case"triangle":o.moveTo(r+7.5*a.pix,n+.5*u-5*a.pix),o.lineTo(r*****a.pix,n+.5*u+5*a.pix),o.lineTo(r+12.5*a.pix,n+.5*u+5*a.pix),o.lineTo(r+7.5*a.pix,n+.5*u-5*a.pix);break;case"diamond":o.moveTo(r+7.5*a.pix,n+.5*u-5*a.pix),o.lineTo(r*****a.pix,n+.5*u),o.lineTo(r+7.5*a.pix,n+.5*u+5*a.pix),o.lineTo(r+12.5*a.pix,n+.5*u),o.lineTo(r+7.5*a.pix,n+.5*u-5*a.pix);break;case"circle":o.moveTo(r+7.5*a.pix,n+.5*u),o.arc(r+7.5*a.pix,n+.5*u,5*a.pix,0,2*Math.PI);break;case"rect":o.moveTo(r,n+.5*u-5*a.pix),o.fillRect(r,n+.5*u-5*a.pix,15*a.pix,10*a.pix);break;case"square":o.moveTo(r+2*a.pix,n+.5*u-5*a.pix),o.fillRect(r+2*a.pix,n+.5*u-5*a.pix,10*a.pix,10*a.pix);break;default:o.moveTo(r,n+.5*u-5*a.pix),o.fillRect(r,n+.5*u-5*a.pix,15*a.pix,10*a.pix)}o.closePath(),o.fill()}})),t.forEach((function(t,a){var i=e.x+f+2*p+u+x;g&&(i=e.x-v-f+2*p+u+x);var r=e.y+d*a+(d-c)/2-1+p+c;o.beginPath(),o.setFontSize(c),o.setTextBaseline("normal"),o.setFillStyle(s.fontColor),o.fillText(t.text,i,r),o.closePath(),o.stroke()}))}}function At(t,e,a,i,o,r){var n=t.extra.tooltip||{};n.horizentalLine&&t.tooltip&&1===i&&("line"==t.type||"area"==t.type||"column"==t.type||"mount"==t.type||"candle"==t.type||"mix"==t.type)&&yt(t,e,a),a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&a.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===i&&bt(t.tooltip.textList,t.tooltip.offset,t,e,a),a.restore()}function St(t,e,a,i){var o=e.chartData.xAxisData,r=o.xAxisPoints,n=o.startX,l=o.endX,s=o.eachSpacing,h="center";"bar"!=e.type&&"line"!=e.type&&"area"!=e.type&&"scatter"!=e.type&&"bubble"!=e.type||(h=e.xAxis.boundaryGap);var c=e.height-e.area[2],d=e.area[0];if(e.enableScroll&&e.xAxis.scrollShow){var p=e.height-e.area[2]+a.xAxisHeight,u=l-n,x=s*(r.length-1);"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),x+=(e.extra.mount.widthRatio-1)*s);var f=u*u/x,g=0;e._scrollDistance_&&(g=-e._scrollDistance_*u/x),i.beginPath(),i.setLineCap("round"),i.setLineWidth(6*e.pix),i.setStrokeStyle(e.xAxis.scrollBackgroundColor||"#EFEBEF"),i.moveTo(n,p),i.lineTo(l,p),i.stroke(),i.closePath(),i.beginPath(),i.setLineCap("round"),i.setLineWidth(6*e.pix),i.setStrokeStyle(e.xAxis.scrollColor||"#A6A6A6"),i.moveTo(n+g,p),i.lineTo(n+g+f,p),i.stroke(),i.closePath(),i.setLineCap("butt")}if(i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&i.translate(e._scrollDistance_,0),!0===e.xAxis.calibration&&(i.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),i.setLineCap("butt"),i.setLineWidth(1*e.pix),r.forEach((function(t,a){a>0&&(i.beginPath(),i.moveTo(t-s/2,c),i.lineTo(t-s/2,c+3*e.pix),i.closePath(),i.stroke())}))),!0!==e.xAxis.disableGrid&&(i.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),i.setLineCap("butt"),i.setLineWidth(1*e.pix),"dash"==e.xAxis.gridType&&i.setLineDash([e.xAxis.dashLength*e.pix,e.xAxis.dashLength*e.pix]),e.xAxis.gridEval=e.xAxis.gridEval||1,r.forEach((function(t,a){a%e.xAxis.gridEval==0&&(i.beginPath(),i.moveTo(t,c),i.lineTo(t,d),i.stroke())})),i.setLineDash([])),!0!==e.xAxis.disabled){var y=t.length;e.xAxis.labelCount&&(y=e.xAxis.itemCount?Math.ceil(t.length/e.xAxis.itemCount*e.xAxis.labelCount):e.xAxis.labelCount,y-=1);for(var v=Math.ceil(t.length/y),b=[],A=t.length,S=0;S<A;S++)S%v!==0?b.push(""):b.push(t[S]);b[A-1]=t[A-1];var w=e.xAxis.fontSize*e.pix||a.fontSize;0===a._xAxisTextAngle_?b.forEach((function(t,a){var o=e.xAxis.formatter?e.xAxis.formatter(t,a,e):t,n=-m(String(o),w,i)/2;"center"==h&&(n+=s/2);e.xAxis.scrollShow&&e.pix;var l=e._scrollDistance_||0,d="center"==h?r[a]+s/2:r[a];d-Math.abs(l)>=e.area[3]-1&&d-Math.abs(l)<=e.width-e.area[1]+1&&(i.beginPath(),i.setFontSize(w),i.setFillStyle(e.xAxis.fontColor||e.fontColor),i.fillText(String(o),r[a]+n,c+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.fontSize)*e.pix/2+e.xAxis.fontSize*e.pix),i.closePath(),i.stroke())})):b.forEach((function(t,o){var n=e.xAxis.formatter?e.xAxis.formatter(t):t,l=e._scrollDistance_||0,d="center"==h?r[o]+s/2:r[o];if(d-Math.abs(l)>=e.area[3]-1&&d-Math.abs(l)<=e.width-e.area[1]+1){i.save(),i.beginPath(),i.setFontSize(w),i.setFillStyle(e.xAxis.fontColor||e.fontColor);var p=m(String(n),w,i),u=r[o];"center"==h&&(u=r[o]+s/2);e.xAxis.scrollShow&&6*e.pix;var x=c+e.xAxis.marginTop*e.pix+w-w*Math.abs(Math.sin(a._xAxisTextAngle_));e.xAxis.rotateAngle<0?(u-=w/2,p=0):(u+=w/2,p=-p),i.translate(u,x),i.rotate(-1*a._xAxisTextAngle_),i.fillText(String(n),p,0),i.closePath(),i.stroke(),i.restore()}}))}i.restore(),e.xAxis.title&&(i.beginPath(),i.setFontSize(e.xAxis.titleFontSize*e.pix),i.setFillStyle(e.xAxis.titleFontColor),i.fillText(String(e.xAxis.title),e.width-e.area[1]+e.xAxis.titleOffsetX*e.pix,e.height-e.area[2]+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.titleFontSize)*e.pix/2+(e.xAxis.titleFontSize+e.xAxis.titleOffsetY)*e.pix),i.closePath(),i.stroke()),e.xAxis.axisLine&&(i.beginPath(),i.setStrokeStyle(e.xAxis.axisLineColor),i.setLineWidth(1*e.pix),i.moveTo(n,e.height-e.area[2]),i.lineTo(l,e.height-e.area[2]),i.stroke())}function wt(t,e,a,i){if(!0!==e.yAxis.disableGrid){var o=e.height-e.area[0]-e.area[2],r=o/e.yAxis.splitNumber,n=e.area[3],l=e.chartData.xAxisData.xAxisPoints,s=e.chartData.xAxisData.eachSpacing,h=s*(l.length-1);"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),h+=(e.extra.mount.widthRatio-1)*s);var c=n+h,d=[],p=1;!1===e.xAxis.axisLine&&(p=0);for(var u=p;u<e.yAxis.splitNumber+1;u++)d.push(e.height-e.area[2]-r*u);i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&i.translate(e._scrollDistance_,0),"dash"==e.yAxis.gridType&&i.setLineDash([e.yAxis.dashLength*e.pix,e.yAxis.dashLength*e.pix]),i.setStrokeStyle(e.yAxis.gridColor),i.setLineWidth(1*e.pix),d.forEach((function(t,e){i.beginPath(),i.moveTo(n,t),i.lineTo(c,t),i.stroke()})),i.setLineDash([]),i.restore()}}function Tt(t,e,a,i){if(!0!==e.yAxis.disabled){var o=e.height-e.area[0]-e.area[2],r=o/e.yAxis.splitNumber,n=e.area[3],l=e.width-e.area[1],s=e.height-e.area[2];i.beginPath(),i.setFillStyle(e.background),1==e.enableScroll&&e.xAxis.scrollPosition&&"left"!==e.xAxis.scrollPosition&&i.fillRect(0,0,n,s+2*e.pix),1==e.enableScroll&&e.xAxis.scrollPosition&&"right"!==e.xAxis.scrollPosition&&i.fillRect(l,0,e.width,s+2*e.pix),i.closePath(),i.stroke();var h=e.area[3],c=e.width-e.area[1],d=e.area[3]+(e.width-e.area[1]-e.area[3])/2;if(e.yAxis.data)for(var p=function(t){var n=e.yAxis.data[t];if(x=[],"categories"===n.type)for(var l=0;l<=n.categories.length;l++)x.push(e.area[0]+o/n.categories.length/2+o/n.categories.length*l);else for(var s=0;s<=e.yAxis.splitNumber;s++)x.push(e.area[0]+r*s);if(!0!==n.disabled){var p=e.chartData.yAxisData.rangesFormat[t],u=n.fontSize?n.fontSize*e.pix:a.fontSize,f=e.chartData.yAxisData.yAxisWidth[t],g=n.textAlign||"right";if(p.forEach((function(t,a){var o=x[a];i.beginPath(),i.setFontSize(u),i.setLineWidth(1*e.pix),i.setStrokeStyle(n.axisLineColor||"#cccccc"),i.setFillStyle(n.fontColor||e.fontColor);var r=0,l=4*e.pix;if("left"==f.position){switch(1==n.calibration&&(i.moveTo(h,o),i.lineTo(h-3*e.pix,o),l+=3*e.pix),g){case"left":i.setTextAlign("left"),r=h-f.width;break;case"right":i.setTextAlign("right"),r=h-l;break;default:i.setTextAlign("center"),r=h-f.width/2}i.fillText(String(t),r,o+u/2-3*e.pix)}else if("right"==f.position){switch(1==n.calibration&&(i.moveTo(c,o),i.lineTo(c+3*e.pix,o),l+=3*e.pix),g){case"left":i.setTextAlign("left"),r=c+l;break;case"right":i.setTextAlign("right"),r=c+f.width;break;default:i.setTextAlign("center"),r=c+f.width/2}i.fillText(String(t),r,o+u/2-3*e.pix)}else if("center"==f.position){switch(1==n.calibration&&(i.moveTo(d,o),i.lineTo(d-3*e.pix,o),l+=3*e.pix),g){case"left":i.setTextAlign("left"),r=d-f.width;break;case"right":i.setTextAlign("right"),r=d-l;break;default:i.setTextAlign("center"),r=d-f.width/2}i.fillText(String(t),r,o+u/2-3*e.pix)}i.closePath(),i.stroke(),i.setTextAlign("left")})),!1!==n.axisLine&&(i.beginPath(),i.setStrokeStyle(n.axisLineColor||"#cccccc"),i.setLineWidth(1*e.pix),"left"==f.position?(i.moveTo(h,e.height-e.area[2]),i.lineTo(h,e.area[0])):"right"==f.position?(i.moveTo(c,e.height-e.area[2]),i.lineTo(c,e.area[0])):"center"==f.position&&(i.moveTo(d,e.height-e.area[2]),i.lineTo(d,e.area[0])),i.stroke()),e.yAxis.showTitle){var y=n.titleFontSize*e.pix||a.fontSize,v=n.title;i.beginPath(),i.setFontSize(y),i.setFillStyle(n.titleFontColor||e.fontColor),"left"==f.position?i.fillText(v,h-m(v,y,i)/2+(n.titleOffsetX||0),e.area[0]-(10-(n.titleOffsetY||0))*e.pix):"right"==f.position?i.fillText(v,c-m(v,y,i)/2+(n.titleOffsetX||0),e.area[0]-(10-(n.titleOffsetY||0))*e.pix):"center"==f.position&&i.fillText(v,d-m(v,y,i)/2+(n.titleOffsetX||0),e.area[0]-(10-(n.titleOffsetY||0))*e.pix),i.closePath(),i.stroke()}"left"==f.position?h-=f.width+e.yAxis.padding*e.pix:c+=f.width+e.yAxis.padding*e.pix}},u=0;u<e.yAxis.data.length;u++){var x;p(u)}}}function Ct(t,e,a,i,o){if(!1!==e.legend.show){var r=o.legendData,n=r.points,l=r.area,s=e.legend.padding*e.pix,h=e.legend.fontSize*e.pix,c=15*e.pix,d=5*e.pix,p=e.legend.itemGap*e.pix,u=Math.max(e.legend.lineHeight*e.pix,h);i.beginPath(),i.setLineWidth(e.legend.borderWidth*e.pix),i.setStrokeStyle(e.legend.borderColor),i.setFillStyle(e.legend.backgroundColor),i.moveTo(l.start.x,l.start.y),i.rect(l.start.x,l.start.y,l.width,l.height),i.closePath(),i.fill(),i.stroke(),n.forEach((function(t,o){var n,x=0;x=r.widthArr[o],n=r.heightArr[o];var f=0,g=0;if("top"==e.legend.position||"bottom"==e.legend.position){switch(e.legend.float){case"left":f=l.start.x+s;break;case"right":f=l.start.x+l.width-x;break;default:f=l.start.x+(l.width-x)/2}g=l.start.y+s+o*u}else x=0==o?0:r.widthArr[o-1],f=l.start.x+s+x,g=l.start.y+s+(l.height-n)/2;i.setFontSize(a.fontSize);for(var y=0;y<t.length;y++){var v=t[y];switch(v.area=[0,0,0,0],v.area[0]=f,v.area[1]=g,v.area[3]=g+u,i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(v.show?v.color:e.legend.hiddenColor),i.setFillStyle(v.show?v.color:e.legend.hiddenColor),v.legendShape){case"line":i.moveTo(f,g+.5*u-2*e.pix),i.fillRect(f,g+.5*u-2*e.pix,15*e.pix,4*e.pix);break;case"triangle":i.moveTo(f+7.5*e.pix,g+.5*u-5*e.pix),i.lineTo(f*****e.pix,g+.5*u+5*e.pix),i.lineTo(f+12.5*e.pix,g+.5*u+5*e.pix),i.lineTo(f+7.5*e.pix,g+.5*u-5*e.pix);break;case"diamond":i.moveTo(f+7.5*e.pix,g+.5*u-5*e.pix),i.lineTo(f*****e.pix,g+.5*u),i.lineTo(f+7.5*e.pix,g+.5*u+5*e.pix),i.lineTo(f+12.5*e.pix,g+.5*u),i.lineTo(f+7.5*e.pix,g+.5*u-5*e.pix);break;case"circle":i.moveTo(f+7.5*e.pix,g+.5*u),i.arc(f+7.5*e.pix,g+.5*u,5*e.pix,0,2*Math.PI);break;case"rect":i.moveTo(f,g+.5*u-5*e.pix),i.fillRect(f,g+.5*u-5*e.pix,15*e.pix,10*e.pix);break;case"square":i.moveTo(f+5*e.pix,g+.5*u-5*e.pix),i.fillRect(f+5*e.pix,g+.5*u-5*e.pix,10*e.pix,10*e.pix);break;case"none":break;default:i.moveTo(f,g+.5*u-5*e.pix),i.fillRect(f,g+.5*u-5*e.pix,15*e.pix,10*e.pix)}i.closePath(),i.fill(),i.stroke(),f+=c+d;var b=.5*u+.5*h-2,A=v.legendText?v.legendText:v.name;i.beginPath(),i.setFontSize(h),i.setFillStyle(v.show?e.legend.fontColor:e.legend.hiddenColor),i.fillText(A,f,g+b),i.closePath(),i.stroke(),"top"==e.legend.position||"bottom"==e.legend.position?(f+=m(A,h,i)+p,v.area[2]=f):(v.area[2]=f+m(A,h,i)+p,f-=c+d,g+=u)}}))}}function Pt(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:!1,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},"pie"==e.type?e.extra.pie:e.extra.ring),n={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2};0==a.pieChartLinePadding&&(a.pieChartLinePadding=r.activeRadius*e.pix);var s=Math.min((e.width-e.area[1]-e.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding);s=s<10?10:s,r.customRadius>0&&(s=r.customRadius*e.pix),t=z(t,s,o);var c=r.activeRadius*e.pix;if(r.customColor=y(r.linearType,r.customColor,t,a),t=t.map((function(t){return t._start_+=r.offsetAngle*Math.PI/180,t})),t.forEach((function(t,a){e.tooltip&&e.tooltip.index==a&&(i.beginPath(),i.setFillStyle(h(t.color,r.activeOpacity||.5)),i.moveTo(n.x,n.y),i.arc(n.x,n.y,t._radius_+c,t._start_,t._start_+2*t._proportion_*Math.PI),i.closePath(),i.fill()),i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.lineJoin="round",i.setStrokeStyle(r.borderColor);var o,l=t.color;"custom"==r.linearType&&(o=i.createCircularGradient?i.createCircularGradient(n.x,n.y,t._radius_):i.createRadialGradient(n.x,n.y,0,n.x,n.y,t._radius_),o.addColorStop(0,h(r.customColor[t.linearIndex],1)),o.addColorStop(1,h(t.color,1)),l=o);i.setFillStyle(l),i.moveTo(n.x,n.y),i.arc(n.x,n.y,t._radius_,t._start_,t._start_+2*t._proportion_*Math.PI),i.closePath(),i.fill(),1==r.border&&i.stroke()})),"ring"===e.type){var d=.6*s;"number"===typeof r.ringWidth&&r.ringWidth>0&&(d=Math.max(0,s-r.ringWidth*e.pix)),i.beginPath(),i.setFillStyle(r.centerColor),i.moveTo(n.x,n.y),i.arc(n.x,n.y,d,0,2*Math.PI),i.closePath(),i.fill()}return!1!==e.dataLabel&&1===o&&ft(t,e,a,i,0,n),1===o&&"ring"===e.type&&st(e,a,i,n),{center:n,radius:s,series:t}}function Dt(t,e){var a=Array(2),i=20037508.34*t/180,o=Math.log(Math.tan((90+e)*Math.PI/360))/(Math.PI/180);return o=20037508.34*o/180,a[0]=i,a[1]=o,a}function Mt(t,e,a,i,o,r){return{x:(e-a.xMin)*i+o,y:(a.yMax-t)*i+r}}function Ft(t,e,a){if(e[1]==a[1])return!1;if(e[1]>t[1]&&a[1]>t[1])return!1;if(e[1]<t[1]&&a[1]<t[1])return!1;if(e[1]==t[1]&&a[1]>t[1])return!1;if(a[1]==t[1]&&e[1]>t[1])return!1;if(e[0]<t[0]&&a[1]<t[1])return!1;var i=a[0]-(a[0]-e[0])*(a[1]-t[1])/(a[1]-e[1]);return!(i<t[0])}function Lt(t,e,a){for(var i=0,o=0;o<e.length;o++){var r=e[o][0];1==e.length&&(r=e[o][0]);for(var n=0;n<r.length-1;n++){var l=r[n],s=r[n+1];a&&(l=Dt(r[n][0],r[n][1]),s=Dt(r[n+1][0],r[n+1][1])),Ft(t,l,s)&&(i+=1)}}return i%2==1}function kt(t,e,a){a=0==a?1:a;for(var i=[],o=0;o<a;o++)i[o]=Math.random();return Math.floor(i.reduce((function(t,e){return t+e}))/a*(e-t))+t}function _t(t,e,a,i){for(var o=!1,r=0;r<e.length;r++)if(e[r].area){if(!(t[3]<e[r].area[1]||t[0]>e[r].area[2]||t[1]>e[r].area[3]||t[2]<e[r].area[0])){o=!0;break}if(t[0]<0||t[1]<0||t[2]>a||t[3]>i){o=!0;break}o=!1}return o}function Ot(t,e,a){var i=t.series;switch(e){case"normal":for(var o=0;o<i.length;o++){var r=i[o].name,n=i[o].textSize*t.pix,l=m(r,n,a),s=void 0,h=void 0,c=void 0,d=0;while(1){d++,s=kt(-t.width/2,t.width/2,5)-l/2,h=kt(-t.height/2,t.height/2,5)+n/2,c=[s-5+t.width/2,h-5-n+t.height/2,s+l+5+t.width/2,h+5+t.height/2];var p=_t(c,i,t.width,t.height);if(!p)break;if(1e3==d){c=[-100,-100,-100,-100];break}}i[o].area=c}break;case"vertical":for(var u=function(){return Math.random()>.7},x=0;x<i.length;x++){var f=i[x].name,g=i[x].textSize*t.pix,y=m(f,g,a),v=u(),b=void 0,A=void 0,S=void 0,w=void 0,T=0;while(1){T++;var C=void 0;if(v?(b=kt(-t.width/2,t.width/2,5)-y/2,A=kt(-t.height/2,t.height/2,5)+g/2,S=[A-5-y+t.width/2,-b-5+t.height/2,A+5+t.width/2,-b+g+5+t.height/2],w=[t.width-(t.width/2-t.height/2)-(-b+g+5+t.height/2)-5,t.height/2-t.width/2+(A-5-y+t.width/2)-5,t.width-(t.width/2-t.height/2)-(-b+g+5+t.height/2)+g,t.height/2-t.width/2+(A-5-y+t.width/2)+y+5],C=_t(w,i,t.height,t.width)):(b=kt(-t.width/2,t.width/2,5)-y/2,A=kt(-t.height/2,t.height/2,5)+g/2,S=[b-5+t.width/2,A-5-g+t.height/2,b+y+5+t.width/2,A+5+t.height/2],C=_t(S,i,t.width,t.height)),!C)break;if(1e3==T){S=[-1e3,-1e3,-1e3,-1e3];break}}v?(i[x].area=w,i[x].areav=S):i[x].area=S,i[x].rotate=v}break}return i}function It(t,e,a,i,o,r,n){for(var l=0;l<t.length;l++){var h=t[l];if(!1!==h.labelShow){var c=void 0,d=void 0,p=void 0,u=void 0,x=h.formatter?h.formatter(h,l,t,e):s.toFixed(100*h._proportion_)+"%";x=h.labelText?h.labelText:x,"right"==o&&(c=l==t.length-1?(h.funnelArea[2]+n.x)/2:(h.funnelArea[2]+t[l+1].funnelArea[2])/2,d=c+2*r,p=h.funnelArea[1]+i/2,u=h.textSize*e.pix||e.fontSize*e.pix,a.setLineWidth(1*e.pix),a.setStrokeStyle(h.color),a.setFillStyle(h.color),a.beginPath(),a.moveTo(c,p),a.lineTo(d,p),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(d,p),a.arc(d,p,2*e.pix,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(u),a.setFillStyle(h.textColor||e.fontColor),a.fillText(x,d+5,p+u/2-2),a.closePath(),a.stroke(),a.closePath()),"left"==o&&(c=l==t.length-1?(h.funnelArea[0]+n.x)/2:(h.funnelArea[0]+t[l+1].funnelArea[0])/2,d=c-2*r,p=h.funnelArea[1]+i/2,u=h.textSize*e.pix||e.fontSize*e.pix,a.setLineWidth(1*e.pix),a.setStrokeStyle(h.color),a.setFillStyle(h.color),a.beginPath(),a.moveTo(c,p),a.lineTo(d,p),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(d,p),a.arc(d,p,2,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(u),a.setFillStyle(h.textColor||e.fontColor),a.fillText(x,d-5-m(x,u,a),p+u/2-2),a.closePath(),a.stroke(),a.closePath())}}}function Et(t,e,a,i,o,r,n){for(var l=0;l<t.length;l++){var s=t[l],h=void 0,c=void 0;s.centerText&&(h=s.funnelArea[1]+i/2,c=s.centerTextSize*e.pix||e.fontSize*e.pix,a.beginPath(),a.setFontSize(c),a.setFillStyle(s.centerTextColor||"#FFFFFF"),a.fillText(s.centerText,n.x-m(s.centerText,c,a)/2,h+c/2-2),a.closePath(),a.stroke(),a.closePath())}}function zt(t,e){e.save(),e.translate(0,.5),e.restore(),e.draw()}var Bt={easeIn:function(t){return Math.pow(t,3)},easeOut:function(t){return Math.pow(t-1,3)+1},easeInOut:function(t){return(t/=.5)<1?.5*Math.pow(t,3):.5*(Math.pow(t-2,3)+2)},linear:function(t){return t}};function Wt(t){this.isStop=!1,t.duration="undefined"===typeof t.duration?1e3:t.duration,t.timing=t.timing||"easeInOut";var e=function(){return"undefined"!==typeof setTimeout?function(t,e){setTimeout((function(){var e=+new Date;t(e)}),e)}:"undefined"!==typeof requestAnimationFrame?requestAnimationFrame:function(t){t(null)}}(),a=null,i=function(o){if(null===o||!0===this.isStop)return t.onProcess&&t.onProcess(1),void(t.onAnimationFinish&&t.onAnimationFinish());if(null===a&&(a=o),o-a<t.duration){var r=(o-a)/t.duration,n=Bt[t.timing];r=n(r),t.onProcess&&t.onProcess(r),e(i,17)}else t.onProcess&&t.onProcess(1),t.onAnimationFinish&&t.onAnimationFinish()};i=i.bind(this),e(i,17)}function Rt(t,e,a,i){var r=this,n=this,c=e.series;"pie"!==t&&"ring"!==t&&"mount"!==t&&"rose"!==t&&"funnel"!==t||(c=function(t,e,a){var i=[];if(t.length>0&&t[0].data.constructor.toString().indexOf("Array")>-1){e._pieSeries_=t;for(var o=t[0].data,r=0;r<o.length;r++)o[r].formatter=t[0].formatter,o[r].data=o[r].value,i.push(o[r]);e.series=i}else i=t;return i}(c,e));var d=e.categories;if("mount"===t){d=[];for(var p=0;p<c.length;p++)!1!==c[p].show&&d.push(c[p].name);e.categories=d}c=g(c,e,a);var f=e.animation?e.duration:0;n.animationInstance&&n.animationInstance.stop();var v=null;if("candle"==t){var A=l({},e.extra.candle.average);A.show?(v=function(t,e,a,i){for(var o=[],r=0;r<t.length;r++){for(var n={data:[],name:e[r],color:a[r]},l=0,s=i.length;l<s;l++)if(l<t[r])n.data.push(null);else{for(var h=0,c=0;c<t[r];c++)h+=i[l-c][1];n.data.push(+(h/t[r]).toFixed(3))}o.push(n)}return o}(A.day,A.name,A.color,c[0].data),v=g(v,e,a),e.seriesMA=v):v=e.seriesMA?e.seriesMA=g(e.seriesMA,e,a):c}else v=c;e._series_=c=F(c),e.area=new Array(4);for(var S=0;S<4;S++)e.area[S]=e.padding[S]*e.pix;var w=function(t,e,a,i,o){var r={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(!1===e.legend.show)return i.legendData=r,r;var n=e.legend.padding*e.pix,l=e.legend.margin*e.pix,s=e.legend.fontSize?e.legend.fontSize*e.pix:a.fontSize,h=15*e.pix,c=5*e.pix,d=Math.max(e.legend.lineHeight*e.pix,s);if("top"==e.legend.position||"bottom"==e.legend.position){for(var p=[],u=0,x=[],f=[],g=0;g<t.length;g++){var y=t[g],v=y.legendText?y.legendText:y.name,b=h+c+m(v||"undefined",s,o)+e.legend.itemGap*e.pix;u+b>e.width-e.area[1]-e.area[3]?(p.push(f),x.push(u-e.legend.itemGap*e.pix),u=b,f=[y]):(u+=b,f.push(y))}if(f.length){p.push(f),x.push(u-e.legend.itemGap*e.pix),r.widthArr=x;var A=Math.max.apply(null,x);switch(e.legend.float){case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+A+2*n;break;case"right":r.area.start.x=e.width-e.area[1]-A-2*n,r.area.end.x=e.width-e.area[1];break;default:r.area.start.x=(e.width-A)/2-n,r.area.end.x=(e.width+A)/2+n}r.area.width=A+2*n,r.area.wholeWidth=A+2*n,r.area.height=p.length*d+2*n,r.area.wholeHeight=p.length*d+2*n+2*l,r.points=p}}else{var S=t.length,w=e.height-e.area[0]-e.area[2]-2*l-2*n,T=Math.min(Math.floor(w/d),S);switch(r.area.height=T*d+2*n,r.area.wholeHeight=T*d+2*n,e.legend.float){case"top":r.area.start.y=e.area[0]+l,r.area.end.y=e.area[0]+l+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-l-r.area.height,r.area.end.y=e.height-e.area[2]-l;break;default:r.area.start.y=(e.height-r.area.height)/2,r.area.end.y=(e.height+r.area.height)/2}for(var C=S%T===0?S/T:Math.floor(S/T+1),P=[],D=0;D<C;D++){var M=t.slice(D*T,D*T+T);P.push(M)}if(r.points=P,P.length){for(var F=0;F<P.length;F++){for(var L=P[F],k=0,_=0;_<L.length;_++){var O=h+c+m(L[_].name||"undefined",s,o)+e.legend.itemGap*e.pix;O>k&&(k=O)}r.widthArr.push(k),r.heightArr.push(L.length*d+2*n)}for(var I=0,E=0;E<r.widthArr.length;E++)I+=r.widthArr[E];r.area.width=I-e.legend.itemGap*e.pix+2*n,r.area.wholeWidth=r.area.width+n}}switch(e.legend.position){case"top":r.area.start.y=e.area[0]+l,r.area.end.y=e.area[0]+l+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-r.area.height-l,r.area.end.y=e.height-e.area[2]-l;break;case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+r.area.width;break;case"right":r.area.start.x=e.width-e.area[1]-r.area.width,r.area.end.x=e.width-e.area[1];break}return i.legendData=r,r}(v,e,a,e.chartData,i),P=w.area.wholeHeight,D=w.area.wholeWidth;switch(e.legend.position){case"top":e.area[0]+=P;break;case"bottom":e.area[2]+=P;break;case"left":e.area[3]+=D;break;case"right":e.area[1]+=D;break}var M={},L=0;if("line"===e.type||"column"===e.type||"mount"===e.type||"area"===e.type||"mix"===e.type||"candle"===e.type||"scatter"===e.type||"bubble"===e.type||"bar"===e.type){if(M=ot(c,e,a,i),L=M.yAxisWidth,e.yAxis.showTitle){for(var k=0,it=0;it<e.yAxis.data.length;it++)k=Math.max(k,e.yAxis.data[it].titleFontSize?e.yAxis.data[it].titleFontSize*e.pix:a.fontSize);e.area[0]+=k}for(var yt=0,bt=0,Ft=0;Ft<L.length;Ft++)"left"==L[Ft].position?(e.area[3]+=bt>0?L[Ft].width+e.yAxis.padding*e.pix:L[Ft].width,bt+=1):"right"==L[Ft].position&&(e.area[1]+=yt>0?L[Ft].width+e.yAxis.padding*e.pix:L[Ft].width,yt+=1)}else a.yAxisWidth=L;if(e.chartData.yAxisData=M,e.categories&&e.categories.length&&"radar"!==e.type&&"gauge"!==e.type&&"bar"!==e.type){e.chartData.xAxisData=Q(e.categories,e);var Lt=O(e.categories,e,0,e.chartData.xAxisData.eachSpacing,i),kt=Lt.xAxisHeight,_t=Lt.angle;a.xAxisHeight=kt,a._xAxisTextAngle_=_t,e.area[2]+=kt,e.chartData.categoriesData=Lt}else if("line"===e.type||"area"===e.type||"scatter"===e.type||"bubble"===e.type||"bar"===e.type){e.chartData.xAxisData=I(c,e,a,i),d=e.chartData.xAxisData.rangesFormat;var Bt=O(d,e,0,e.chartData.xAxisData.eachSpacing,i),Rt=Bt.xAxisHeight,Gt=Bt.angle;a.xAxisHeight=Rt,a._xAxisTextAngle_=Gt,e.area[2]+=Rt,e.chartData.categoriesData=Bt}else e.chartData.xAxisData={xAxisPoints:[]};if(e.enableScroll&&"right"==e.xAxis.scrollAlign&&void 0===e._scrollDistance_){var Nt,Yt=e.chartData.xAxisData.xAxisPoints,jt=e.chartData.xAxisData.startX,Ht=e.chartData.xAxisData.endX,Xt=e.chartData.xAxisData.eachSpacing,Jt=Xt*(Yt.length-1),qt=Ht-jt;Nt=qt-Jt,n.scrollOption.currentOffset=Nt,n.scrollOption.startTouchX=Nt,n.scrollOption.distance=0,n.scrollOption.lastMoveTime=0,e._scrollDistance_=Nt}switch("pie"!==t&&"ring"!==t&&"rose"!==t||(a._pieTextMaxLength_=!1===e.dataLabel?0:function(t,e,a,i){t=z(t);for(var o=0,r=0;r<t.length;r++){var n=t[r],l=n.formatter?n.formatter(+n._proportion_.toFixed(2)):s.toFixed(100*n._proportion_)+"%";o=Math.max(o,m(l,n.textSize*i.pix||e.fontSize,a))}return o}(v,a,i,e)),t){case"word":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"normal",autoColors:!0},e.extra.word);e.chartData.wordCloudData||(e.chartData.wordCloudData=Ot(e,r.type,i)),i.beginPath(),i.setFillStyle(e.background),i.rect(0,0,e.width,e.height),i.fill(),i.save();var n=e.chartData.wordCloudData;i.translate(e.width/2,e.height/2);for(var s=0;s<n.length;s++){i.save(),n[s].rotate&&i.rotate(90*Math.PI/180);var h=n[s].name,c=n[s].textSize*e.pix,d=m(h,c,i);i.beginPath(),i.setStrokeStyle(n[s].color),i.setFillStyle(n[s].color),i.setFontSize(c),n[s].rotate?n[s].areav[0]>0&&(e.tooltip&&e.tooltip.index==s?i.strokeText(h,(n[s].areav[0]+5-e.width/2)*o-d*(1-o)/2,(n[s].areav[1]+5+c-e.height/2)*o):i.fillText(h,(n[s].areav[0]+5-e.width/2)*o-d*(1-o)/2,(n[s].areav[1]+5+c-e.height/2)*o)):n[s].area[0]>0&&(e.tooltip&&e.tooltip.index==s?i.strokeText(h,(n[s].area[0]+5-e.width/2)*o-d*(1-o)/2,(n[s].area[1]+5+c-e.height/2)*o):i.fillText(h,(n[s].area[0]+5-e.width/2)*o-d*(1-o)/2,(n[s].area[1]+5+c-e.height/2)*o)),i.stroke(),i.restore()}i.restore()}(c,e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"map":i.clearRect(0,0,e.width,e.height),function(t,e,a,i){var o,r,n=l({},{border:!0,mercator:!1,borderWidth:1,active:!0,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},e.extra.map),s=t,c=function(t){for(var e,a={xMin:180,xMax:0,yMin:90,yMax:0},i=0;i<t.length;i++)for(var o=t[i].geometry.coordinates,r=0;r<o.length;r++){e=o[r],1==e.length&&(e=e[0]);for(var n=0;n<e.length;n++){var l=e[n][0],s=e[n][1],h={x:l,y:s};a.xMin=a.xMin<h.x?a.xMin:h.x,a.xMax=a.xMax>h.x?a.xMax:h.x,a.yMin=a.yMin<h.y?a.yMin:h.y,a.yMax=a.yMax>h.y?a.yMax:h.y}}return a}(s);if(n.mercator){var d=Dt(c.xMax,c.yMax),p=Dt(c.xMin,c.yMin);c.xMax=d[0],c.yMax=d[1],c.xMin=p[0],c.yMin=p[1]}for(var u=e.width/Math.abs(c.xMax-c.xMin),x=e.height/Math.abs(c.yMax-c.yMin),f=u<x?u:x,g=e.width/2-Math.abs(c.xMax-c.xMin)/2*f,y=e.height/2-Math.abs(c.yMax-c.yMin)/2*f,v=0;v<s.length;v++){i.beginPath(),i.setLineWidth(n.borderWidth*e.pix),i.setStrokeStyle(n.borderColor),i.setFillStyle(h(t[v].color,t[v].fillOpacity||n.fillOpacity)),1==n.active&&e.tooltip&&e.tooltip.index==v&&(i.setStrokeStyle(n.activeBorderColor),i.setFillStyle(h(n.activeFillColor,n.activeFillOpacity)));for(var b=s[v].geometry.coordinates,A=0;A<b.length;A++){o=b[A],1==o.length&&(o=o[0]);for(var S=0;S<o.length;S++){var w=Array(2);w=n.mercator?Dt(o[S][0],o[S][1]):o[S],r=Mt(w[1],w[0],c,f,g,y),0===S?(i.beginPath(),i.moveTo(r.x,r.y)):i.lineTo(r.x,r.y)}i.fill(),1==n.border&&i.stroke()}}if(1==e.dataLabel)for(v=0;v<s.length;v++){var T=s[v].properties.centroid;if(T){n.mercator&&(T=Dt(s[v].properties.centroid[0],s[v].properties.centroid[1])),r=Mt(T[1],T[0],c,f,g,y);var C=s[v].textSize*e.pix||a.fontSize,P=s[v].textColor||e.fontColor;n.active&&n.activeTextColor&&e.tooltip&&e.tooltip.index==v&&(P=n.activeTextColor);var D=s[v].properties.name;i.beginPath(),i.setFontSize(C),i.setFillStyle(P),i.fillText(D,r.x-m(D,C,i)/2,r.y+C/2),i.closePath(),i.stroke()}}e.chartData.mapData={bounds:c,scale:f,xoffset:g,yoffset:y,mercator:n.mercator},At(e,a,i,1),i.draw()}(c,e,a,i),setTimeout((function(){r.uevent.trigger("renderComplete")}),50);break;case"funnel":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.funnelData=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"funnel",activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,minSize:0,labelAlign:"right",linearType:"none",customColor:[]},e.extra.funnel),n=(e.height-e.area[0]-e.area[2])/t.length,s={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.height-e.area[2]},c=r.activeWidth*e.pix,d=Math.min((e.width-e.area[1]-e.area[3])/2-c,(e.height-e.area[0]-e.area[2])/2-c),p=B(t,d,r,n,o);if(i.save(),i.translate(s.x,s.y),r.customColor=y(r.linearType,r.customColor,t,a),"pyramid"==r.type)for(var u=0;u<p.length;u++){if(u==p.length-1){e.tooltip&&e.tooltip.index==u&&(i.beginPath(),i.setFillStyle(h(p[u].color,r.activeOpacity)),i.moveTo(-c,-n),i.lineTo(-p[u].radius-c,0),i.lineTo(p[u].radius+c,0),i.lineTo(c,-n),i.lineTo(-c,-n),i.closePath(),i.fill()),p[u].funnelArea=[s.x-p[u].radius,s.y-n*(u+1),s.x+p[u].radius,s.y-n*u],i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(r.borderColor);var x=h(p[u].color,r.fillOpacity);if("custom"==r.linearType){var f=i.createLinearGradient(p[u].radius,-n,-p[u].radius,-n);f.addColorStop(0,h(p[u].color,r.fillOpacity)),f.addColorStop(.5,h(r.customColor[p[u].linearIndex],r.fillOpacity)),f.addColorStop(1,h(p[u].color,r.fillOpacity)),x=f}i.setFillStyle(x),i.moveTo(0,-n),i.lineTo(-p[u].radius,0),i.lineTo(p[u].radius,0),i.lineTo(0,-n),i.closePath(),i.fill(),1==r.border&&i.stroke()}else{e.tooltip&&e.tooltip.index==u&&(i.beginPath(),i.setFillStyle(h(p[u].color,r.activeOpacity)),i.moveTo(0,0),i.lineTo(-p[u].radius-c,0),i.lineTo(-p[u+1].radius-c,-n),i.lineTo(p[u+1].radius+c,-n),i.lineTo(p[u].radius+c,0),i.lineTo(0,0),i.closePath(),i.fill()),p[u].funnelArea=[s.x-p[u].radius,s.y-n*(u+1),s.x+p[u].radius,s.y-n*u],i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(r.borderColor);x=h(p[u].color,r.fillOpacity);if("custom"==r.linearType){f=i.createLinearGradient(p[u].radius,-n,-p[u].radius,-n);f.addColorStop(0,h(p[u].color,r.fillOpacity)),f.addColorStop(.5,h(r.customColor[p[u].linearIndex],r.fillOpacity)),f.addColorStop(1,h(p[u].color,r.fillOpacity)),x=f}i.setFillStyle(x),i.moveTo(0,0),i.lineTo(-p[u].radius,0),i.lineTo(-p[u+1].radius,-n),i.lineTo(p[u+1].radius,-n),i.lineTo(p[u].radius,0),i.lineTo(0,0),i.closePath(),i.fill(),1==r.border&&i.stroke()}i.translate(0,-n)}else{i.translate(0,-(p.length-1)*n);for(var g=0;g<p.length;g++){if(g==p.length-1){e.tooltip&&e.tooltip.index==g&&(i.beginPath(),i.setFillStyle(h(p[g].color,r.activeOpacity)),i.moveTo(-c-r.minSize/2,0),i.lineTo(-p[g].radius-c,-n),i.lineTo(p[g].radius+c,-n),i.lineTo(c+r.minSize/2,0),i.lineTo(-c-r.minSize/2,0),i.closePath(),i.fill()),p[g].funnelArea=[s.x-p[g].radius,s.y-n,s.x+p[g].radius,s.y],i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(r.borderColor);x=h(p[g].color,r.fillOpacity);if("custom"==r.linearType){f=i.createLinearGradient(p[g].radius,-n,-p[g].radius,-n);f.addColorStop(0,h(p[g].color,r.fillOpacity)),f.addColorStop(.5,h(r.customColor[p[g].linearIndex],r.fillOpacity)),f.addColorStop(1,h(p[g].color,r.fillOpacity)),x=f}i.setFillStyle(x),i.moveTo(0,0),i.lineTo(-r.minSize/2,0),i.lineTo(-p[g].radius,-n),i.lineTo(p[g].radius,-n),i.lineTo(r.minSize/2,0),i.lineTo(0,0),i.closePath(),i.fill(),1==r.border&&i.stroke()}else{e.tooltip&&e.tooltip.index==g&&(i.beginPath(),i.setFillStyle(h(p[g].color,r.activeOpacity)),i.moveTo(0,0),i.lineTo(-p[g+1].radius-c,0),i.lineTo(-p[g].radius-c,-n),i.lineTo(p[g].radius+c,-n),i.lineTo(p[g+1].radius+c,0),i.lineTo(0,0),i.closePath(),i.fill()),p[g].funnelArea=[s.x-p[g].radius,s.y-n*(p.length-g),s.x+p[g].radius,s.y-n*(p.length-g-1)],i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(r.borderColor);x=h(p[g].color,r.fillOpacity);if("custom"==r.linearType){f=i.createLinearGradient(p[g].radius,-n,-p[g].radius,-n);f.addColorStop(0,h(p[g].color,r.fillOpacity)),f.addColorStop(.5,h(r.customColor[p[g].linearIndex],r.fillOpacity)),f.addColorStop(1,h(p[g].color,r.fillOpacity)),x=f}i.setFillStyle(x),i.moveTo(0,0),i.lineTo(-p[g+1].radius,0),i.lineTo(-p[g].radius,-n),i.lineTo(p[g].radius,-n),i.lineTo(p[g+1].radius,0),i.lineTo(0,0),i.closePath(),i.fill(),1==r.border&&i.stroke()}i.translate(0,n)}}return i.restore(),!1!==e.dataLabel&&1===o&&It(p,e,i,n,r.labelAlign,c,s),1===o&&Et(p,e,i,n,r.labelAlign,c,s),{center:s,radius:d,series:p}}(c,e,a,i,t),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"line":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),wt(0,e,0,i),St(d,e,a,i);var o=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"straight",width:2,activeType:"none",linearType:"none",onShadow:!1,animation:"vertical"},e.extra.line);r.width*=e.pix;var n=e.chartData.xAxisData,s=n.xAxisPoints,c=n.eachSpacing,d=[];i.save();var p=0,x=e.width+c;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),p=-e._scrollDistance_-2*c+e.area[3],x=p+(e.xAxis.itemCount+4)*c),t.forEach((function(t,n){var l,f,g;i.beginPath(),i.setStrokeStyle(t.color),i.moveTo(-1e4,-1e4),i.lineTo(-10001,-10001),i.stroke(),l=[].concat(e.chartData.yAxisData.ranges[t.index]),f=l.pop(),g=l.shift();var y=t.data,v=V(y,f,g,s,c,e,a,r,o);d.push(v);var m=_(v,t);if("dash"==t.lineType){var b=t.dashLength?t.dashLength:8;b*=e.pix,i.setLineDash([b,b])}i.beginPath();var A=t.color;if("none"!==r.linearType&&t.linearColor&&t.linearColor.length>0){for(var S=i.createLinearGradient(e.chartData.xAxisData.startX,e.height/2,e.chartData.xAxisData.endX,e.height/2),w=0;w<t.linearColor.length;w++)S.addColorStop(t.linearColor[w][0],h(t.linearColor[w][1],1));A=S}i.setStrokeStyle(A),1==r.onShadow&&t.setShadow&&t.setShadow.length>0?i.setShadow(t.setShadow[0],t.setShadow[1],t.setShadow[2],t.setShadow[3]):i.setShadow(0,0,0,"rgba(0,0,0,0)"),i.setLineWidth(r.width),m.forEach((function(t,e){if(1===t.length)i.moveTo(t[0].x,t[0].y);else{i.moveTo(t[0].x,t[0].y);var a=0;if("curve"===r.type)for(var o=0;o<t.length;o++){var n=t[o];if(0==a&&n.x>p&&(i.moveTo(n.x,n.y),a=1),o>0&&n.x>p&&n.x<x){var l=u(t,o-1);i.bezierCurveTo(l.ctrA.x,l.ctrA.y,l.ctrB.x,l.ctrB.y,n.x,n.y)}}if("straight"===r.type)for(var s=0;s<t.length;s++){var h=t[s];0==a&&h.x>p&&(i.moveTo(h.x,h.y),a=1),s>0&&h.x>p&&h.x<x&&i.lineTo(h.x,h.y)}if("step"===r.type)for(var c=0;c<t.length;c++){var d=t[c];0==a&&d.x>p&&(i.moveTo(d.x,d.y),a=1),c>0&&d.x>p&&d.x<x&&(i.lineTo(d.x,t[c-1].y),i.lineTo(d.x,d.y))}i.moveTo(t[0].x,t[0].y)}})),i.stroke(),i.setLineDash([]),!1!==e.dataPointShape&&nt(v,t.color,t.pointShape,i,e),lt(v,t.color,t.pointShape,i,e,r)})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){var n,l,h;n=[].concat(e.chartData.yAxisData.ranges[t.index]),l=n.pop(),h=n.shift();var d=t.data,p=Z(d,l,h,s,c,e,a,o);ht(p,t,a,i,e)})),i.restore(),{xAxisPoints:s,calPoints:d,eachSpacing:c}}(c,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"scatter":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),wt(0,e,0,i),St(d,e,a,i);var o=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=(l({},{type:"circle"},e.extra.scatter),e.chartData.xAxisData),n=r.xAxisPoints,s=r.eachSpacing,h=[];i.save();return e.width,e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),-e._scrollDistance_-2*s+e.area[3],e.xAxis.itemCount),t.forEach((function(t,r){var l,h,c;l=[].concat(e.chartData.yAxisData.ranges[t.index]),h=l.pop(),c=l.shift();var d=t.data,p=Z(d,h,c,n,s,e,a,o);i.beginPath(),i.setStrokeStyle(t.color),i.setFillStyle(t.color),i.setLineWidth(1*e.pix);var u=t.pointShape;if("diamond"===u)p.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y),i.lineTo(t.x,t.y****),i.lineTo(t.x****,t.y),i.lineTo(t.x,t.y-4.5))}));else if("circle"===u)p.forEach((function(t,a){null!==t&&(i.moveTo(t.x*****e.pix,t.y),i.arc(t.x,t.y,3*e.pix,0,2*Math.PI,!1))}));else if("square"===u)p.forEach((function(t,e){null!==t&&(i.moveTo(t.x-3.5,t.y-3.5),i.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===u)p.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y****),i.lineTo(t.x****,t.y****),i.lineTo(t.x,t.y-4.5))}));else if("triangle"===u)return;i.closePath(),i.fill(),i.stroke()})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){var l,h,c;l=[].concat(e.chartData.yAxisData.ranges[t.index]),h=l.pop(),c=l.shift();var d=t.data,p=Z(d,h,c,n,s,e,a,o);ht(p,t,a,i,e)})),i.restore(),{xAxisPoints:n,calPoints:h,eachSpacing:s}}(c,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"bubble":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),wt(0,e,0,i),St(d,e,a,i);var o=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{opacity:1,border:2},e.extra.bubble),n=e.chartData.xAxisData,s=n.xAxisPoints,c=n.eachSpacing,d=[];i.save();return e.width,e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),-e._scrollDistance_-2*c+e.area[3],e.xAxis.itemCount),t.forEach((function(t,n){var l,d,p;l=[].concat(e.chartData.yAxisData.ranges[t.index]),d=l.pop(),p=l.shift();var u=t.data,x=Z(u,d,p,s,c,e,a,o);i.beginPath(),i.setStrokeStyle(t.color),i.setLineWidth(r.border*e.pix),i.setFillStyle(h(t.color,r.opacity)),x.forEach((function(t,a){i.moveTo(t.x+t.r,t.y),i.arc(t.x,t.y,t.r*e.pix,0,2*Math.PI,!1)})),i.closePath(),i.fill(),i.stroke(),!1!==e.dataLabel&&1===o&&x.forEach((function(o,r){i.beginPath();var n=t.textSize*e.pix||a.fontSize;i.setFontSize(n),i.setFillStyle(t.textColor||"#FFFFFF"),i.setTextAlign("center"),i.fillText(String(o.t),o.x,o.y+n/2),i.closePath(),i.stroke(),i.setTextAlign("left")}))})),i.restore(),{xAxisPoints:s,calPoints:d,eachSpacing:c}}(c,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"mix":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),wt(0,e,0,i),St(d,e,a,i);var r=function(t,e,a,i){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,s=n.xAxisPoints,c=n.eachSpacing,d=l({},{width:c/2,barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mix.column),p=l({},{opacity:.2,gradient:!1},e.extra.mix.area),x=l({},{width:2},e.extra.mix.line),f=e.height-e.area[2],g=[],v=0,m=0;t.forEach((function(t,e){"column"==t.type&&(m+=1)})),i.save();var b=-2,A=s.length+2,S=0,w=e.width+c;if(e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),b=Math.floor(-e._scrollDistance_/c)-2,A=b+e.xAxis.itemCount+4,S=-e._scrollDistance_-2*c+e.area[3],w=S+(e.xAxis.itemCount+4)*c),d.customColor=y(d.linearType,d.customColor,t,a),t.forEach((function(t,n){var l,y,T;l=[].concat(e.chartData.yAxisData.ranges[t.index]),y=l.pop(),T=l.shift();var C=t.data,P=Z(C,y,T,s,c,e,a,r);if(g.push(P),"column"==t.type){P=j(P,c,m,v,0,e);for(var D=0;D<P.length;D++){var M=P[D];if(null!==M&&D>b&&D<A){var F=M.x-M.width/2;e.height,M.y,e.area[2];i.beginPath();var L=M.color||t.color,k=M.color||t.color;if("none"!==d.linearType){var O=i.createLinearGradient(F,M.y,F,e.height-e.area[2]);"opacity"==d.linearType?(O.addColorStop(0,h(L,d.linearOpacity)),O.addColorStop(1,h(L,1))):(O.addColorStop(0,h(d.customColor[t.linearIndex],d.linearOpacity)),O.addColorStop(d.colorStop,h(d.customColor[t.linearIndex],d.linearOpacity)),O.addColorStop(1,h(L,1))),L=O}if(d.barBorderRadius&&4===d.barBorderRadius.length||d.barBorderCircle){var I=F,E=M.y,z=M.width,B=e.height-e.area[2]-M.y;d.barBorderCircle&&(d.barBorderRadius=[z/2,z/2,0,0]);var W=(0,o.default)(d.barBorderRadius,4),R=W[0],G=W[1],N=W[2],Y=W[3],H=Math.min(z/2,B/2);R=R>H?H:R,G=G>H?H:G,N=N>H?H:N,Y=Y>H?H:Y,R=R<0?0:R,G=G<0?0:G,N=N<0?0:N,Y=Y<0?0:Y,i.arc(I+R,E+R,R,-Math.PI,-Math.PI/2),i.arc(I+z-G,E+G,G,-Math.PI/2,0),i.arc(I+z-N,E+B-N,N,0,Math.PI/2),i.arc(I+Y,E+B-Y,Y,Math.PI/2,Math.PI)}else i.moveTo(F,M.y),i.lineTo(F+M.width,M.y),i.lineTo(F+M.width,e.height-e.area[2]),i.lineTo(F,e.height-e.area[2]),i.lineTo(F,M.y),i.setLineWidth(1),i.setStrokeStyle(k);i.setFillStyle(L),i.closePath(),i.fill()}}v+=1}if("area"==t.type)for(var X=_(P,t),J=0;J<X.length;J++){var q=X[J];if(i.beginPath(),i.setStrokeStyle(t.color),i.setStrokeStyle(h(t.color,p.opacity)),p.gradient){var Q=i.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);Q.addColorStop("0",h(t.color,p.opacity)),Q.addColorStop("1.0",h("#FFFFFF",.1)),i.setFillStyle(Q)}else i.setFillStyle(h(t.color,p.opacity));if(i.setLineWidth(2*e.pix),q.length>1){var U=q[0],V=q[q.length-1];i.moveTo(U.x,U.y);var K=0;if("curve"===t.style)for(var $=0;$<q.length;$++){var tt=q[$];if(0==K&&tt.x>S&&(i.moveTo(tt.x,tt.y),K=1),$>0&&tt.x>S&&tt.x<w){var et=u(q,$-1);i.bezierCurveTo(et.ctrA.x,et.ctrA.y,et.ctrB.x,et.ctrB.y,tt.x,tt.y)}}else for(var at=0;at<q.length;at++){var it=q[at];0==K&&it.x>S&&(i.moveTo(it.x,it.y),K=1),at>0&&it.x>S&&it.x<w&&i.lineTo(it.x,it.y)}i.lineTo(V.x,f),i.lineTo(U.x,f),i.lineTo(U.x,U.y)}else{var ot=q[0];i.moveTo(ot.x-c/2,ot.y)}i.closePath(),i.fill()}if("line"==t.type){var rt=_(P,t);rt.forEach((function(a,o){if("dash"==t.lineType){var r=t.dashLength?t.dashLength:8;r*=e.pix,i.setLineDash([r,r])}if(i.beginPath(),i.setStrokeStyle(t.color),i.setLineWidth(x.width*e.pix),1===a.length)i.moveTo(a[0].x,a[0].y);else{i.moveTo(a[0].x,a[0].y);var n=0;if("curve"==t.style)for(var l=0;l<a.length;l++){var s=a[l];if(0==n&&s.x>S&&(i.moveTo(s.x,s.y),n=1),l>0&&s.x>S&&s.x<w){var h=u(a,l-1);i.bezierCurveTo(h.ctrA.x,h.ctrA.y,h.ctrB.x,h.ctrB.y,s.x,s.y)}}else for(var c=0;c<a.length;c++){var d=a[c];0==n&&d.x>S&&(i.moveTo(d.x,d.y),n=1),c>0&&d.x>S&&d.x<w&&i.lineTo(d.x,d.y)}i.moveTo(a[0].x,a[0].y)}i.stroke(),i.setLineDash([])}))}"point"==t.type&&(t.addPoint=!0),1==t.addPoint&&"column"!==t.type&&nt(P,t.color,t.pointShape,i,e)})),!1!==e.dataLabel&&1===r){v=0;t.forEach((function(t,o){var n,l,h;n=[].concat(e.chartData.yAxisData.ranges[t.index]),l=n.pop(),h=n.shift();var d=t.data,p=Z(d,l,h,s,c,e,a,r);"column"!==t.type?ht(p,t,a,i,e):(p=j(p,c,m,v,0,e),ht(p,t,a,i,e),v+=1)}))}return i.restore(),{xAxisPoints:s,calPoints:g,eachSpacing:c}}(c,e,a,i,t),n=r.xAxisPoints,s=r.calPoints,p=r.eachSpacing;e.chartData.xAxisPoints=n,e.chartData.calPoints=s,e.chartData.eachSpacing=p,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"column":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),wt(0,e,0,i),St(d,e,a,i);var r=function(t,e,a,i){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,s=n.xAxisPoints,c=n.eachSpacing,d=l({},{type:"group",width:c/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0,labelPosition:"outside"},e.extra.column),p=[];i.save();var u=-2,x=s.length+2;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),u=Math.floor(-e._scrollDistance_/c)-2,x=u+e.xAxis.itemCount+4),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===r&&vt(e.tooltip.offset.x,e,0,i,c),d.customColor=y(d.linearType,d.customColor,t,a),t.forEach((function(n,l){var f,g,y;f=[].concat(e.chartData.yAxisData.ranges[n.index]),g=f.pop(),y=f.shift();var v=e.height-e.area[0]-e.area[2],m=v*(0-g)/(y-g),b=e.height-Math.round(m)-e.area[2];n.zeroPoints=b;var A=n.data;switch(d.type){case"group":var S=K(A,g,y,s,c,e,a,b,r),w=et(A,g,y,s,c,e,a,l,t,r);p.push(w),S=j(S,c,t.length,l,0,e);for(var T=0;T<S.length;T++){var C=S[T];if(null!==C&&T>u&&T<x){var P=C.x-C.width/2,D=e.height-C.y-e.area[2];i.beginPath();var M=C.color||n.color,F=C.color||n.color;if("none"!==d.linearType){var L=i.createLinearGradient(P,C.y,P,b);"opacity"==d.linearType?(L.addColorStop(0,h(M,d.linearOpacity)),L.addColorStop(1,h(M,1))):(L.addColorStop(0,h(d.customColor[n.linearIndex],d.linearOpacity)),L.addColorStop(d.colorStop,h(d.customColor[n.linearIndex],d.linearOpacity)),L.addColorStop(1,h(M,1))),M=L}if(d.barBorderRadius&&4===d.barBorderRadius.length||!0===d.barBorderCircle){var k=P,_=C.y>b?b:C.y,O=C.width,I=Math.abs(b-C.y);d.barBorderCircle&&(d.barBorderRadius=[O/2,O/2,0,0]),C.y>b&&(d.barBorderRadius=[0,0,O/2,O/2]);var E=(0,o.default)(d.barBorderRadius,4),z=E[0],B=E[1],W=E[2],R=E[3],G=Math.min(O/2,I/2);z=z>G?G:z,B=B>G?G:B,W=W>G?G:W,R=R>G?G:R,z=z<0?0:z,B=B<0?0:B,W=W<0?0:W,R=R<0?0:R,i.arc(k+z,_+z,z,-Math.PI,-Math.PI/2),i.arc(k+O-B,_+B,B,-Math.PI/2,0),i.arc(k+O-W,_+I-W,W,0,Math.PI/2),i.arc(k+R,_+I-R,R,Math.PI/2,Math.PI)}else i.moveTo(P,C.y),i.lineTo(P+C.width,C.y),i.lineTo(P+C.width,b),i.lineTo(P,b),i.lineTo(P,C.y),i.setLineWidth(1),i.setStrokeStyle(F);i.setFillStyle(M),i.closePath(),i.fill()}}break;case"stack":S=et(A,g,y,s,c,e,a,l,t,r);p.push(S),S=J(S,c,t.length,0,0,e);for(var N=0;N<S.length;N++){var Y=S[N];if(null!==Y&&N>u&&N<x){i.beginPath();M=Y.color||n.color,P=Y.x-Y.width/2+1,D=e.height-Y.y-e.area[2];var H=e.height-Y.y0-e.area[2];l>0&&(D-=H),i.setFillStyle(M),i.moveTo(P,Y.y),i.fillRect(P,Y.y,Y.width,D),i.closePath(),i.fill()}}break;case"meter":S=Z(A,g,y,s,c,e,a,r);p.push(S),S=X(S,c,t.length,l,0,e,d.meterBorder);for(var q=0;q<S.length;q++){var Q=S[q];if(null!==Q&&q>u&&q<x){i.beginPath(),0==l&&d.meterBorder>0&&(i.setStrokeStyle(n.color),i.setLineWidth(d.meterBorder*e.pix)),0==l?i.setFillStyle(d.meterFillColor):i.setFillStyle(Q.color||n.color);P=Q.x-Q.width/2,D=e.height-Q.y-e.area[2];if(d.barBorderRadius&&4===d.barBorderRadius.length||!0===d.barBorderCircle){var U=P,V=Q.y,$=Q.width,tt=b-Q.y;d.barBorderCircle&&(d.barBorderRadius=[$/2,$/2,0,0]);var at=(0,o.default)(d.barBorderRadius,4),it=at[0],ot=at[1],rt=at[2],nt=at[3],lt=Math.min($/2,tt/2);it=it>lt?lt:it,ot=ot>lt?lt:ot,rt=rt>lt?lt:rt,nt=nt>lt?lt:nt,it=it<0?0:it,ot=ot<0?0:ot,rt=rt<0?0:rt,nt=nt<0?0:nt,i.arc(U+it,V+it,it,-Math.PI,-Math.PI/2),i.arc(U+$-ot,V+ot,ot,-Math.PI/2,0),i.arc(U+$-rt,V+tt-rt,rt,0,Math.PI/2),i.arc(U+nt,V+tt-nt,nt,Math.PI/2,Math.PI),i.fill()}else i.moveTo(P,Q.y),i.lineTo(P+Q.width,Q.y),i.lineTo(P+Q.width,b),i.lineTo(P,b),i.lineTo(P,Q.y),i.fill();0==l&&d.meterBorder>0&&(i.closePath(),i.stroke())}}break}})),!1!==e.dataLabel&&1===r&&t.forEach((function(o,n){var l,h,p;l=[].concat(e.chartData.yAxisData.ranges[o.index]),h=l.pop(),p=l.shift();var u=o.data;switch(d.type){case"group":var x=K(u,h,p,s,c,e,a,r);x=j(x,c,t.length,n,0,e),ct(x,o,a,i,e);break;case"stack":x=et(u,h,p,s,c,e,a,n,t,r);ct(x,o,a,i,e);break;case"meter":x=Z(u,h,p,s,c,e,a,r);ct(x,o,a,i,e);break}})),i.restore(),{xAxisPoints:s,calPoints:p,eachSpacing:c}}(c,e,a,i,t),n=r.xAxisPoints,s=r.calPoints,p=r.eachSpacing;e.chartData.xAxisPoints=n,e.chartData.calPoints=s,e.chartData.eachSpacing=p,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"mount":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),wt(0,e,0,i),St(d,e,a,i);var r=function(t,e,a,i){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,s=n.xAxisPoints,c=n.eachSpacing,d=l({},{type:"mount",widthRatio:1,borderWidth:1,barBorderCircle:!1,barBorderRadius:[],linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mount);d.widthRatio=d.widthRatio<=0?0:d.widthRatio,d.widthRatio=d.widthRatio>=2?2:d.widthRatio,i.save();var p,u,x,f=-2,g=s.length+2;e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),f=Math.floor(-e._scrollDistance_/c)-2,g=f+e.xAxis.itemCount+4),d.customColor=y(d.linearType,d.customColor,t,a),p=[].concat(e.chartData.yAxisData.ranges[0]),u=p.pop(),x=p.shift();var v=e.height-e.area[0]-e.area[2],m=v*(0-u)/(x-u),b=e.height-Math.round(m)-e.area[2],A=$(t,u,x,s,c,e,d,b,r);switch(d.type){case"bar":for(var S=0;S<A.length;S++){var w=A[S];if(null!==w&&S>f&&S<g){var T=w.x-c*d.widthRatio/2,C=e.height-w.y-e.area[2];i.beginPath();var P=w.color||t[S].color,D=w.color||t[S].color;if("none"!==d.linearType){var M=i.createLinearGradient(T,w.y,T,b);"opacity"==d.linearType?(M.addColorStop(0,h(P,d.linearOpacity)),M.addColorStop(1,h(P,1))):(M.addColorStop(0,h(d.customColor[t[S].linearIndex],d.linearOpacity)),M.addColorStop(d.colorStop,h(d.customColor[t[S].linearIndex],d.linearOpacity)),M.addColorStop(1,h(P,1))),P=M}if(d.barBorderRadius&&4===d.barBorderRadius.length||!0===d.barBorderCircle){var F=T,L=w.y>b?b:w.y,k=w.width,_=Math.abs(b-w.y);d.barBorderCircle&&(d.barBorderRadius=[k/2,k/2,0,0]),w.y>b&&(d.barBorderRadius=[0,0,k/2,k/2]);var O=(0,o.default)(d.barBorderRadius,4),I=O[0],E=O[1],z=O[2],B=O[3],W=Math.min(k/2,_/2);I=I>W?W:I,E=E>W?W:E,z=z>W?W:z,B=B>W?W:B,I=I<0?0:I,E=E<0?0:E,z=z<0?0:z,B=B<0?0:B,i.arc(F+I,L+I,I,-Math.PI,-Math.PI/2),i.arc(F+k-E,L+E,E,-Math.PI/2,0),i.arc(F+k-z,L+_-z,z,0,Math.PI/2),i.arc(F+B,L+_-B,B,Math.PI/2,Math.PI)}else i.moveTo(T,w.y),i.lineTo(T+w.width,w.y),i.lineTo(T+w.width,b),i.lineTo(T,b),i.lineTo(T,w.y);i.setStrokeStyle(D),i.setFillStyle(P),d.borderWidth>0&&(i.setLineWidth(d.borderWidth*e.pix),i.closePath(),i.stroke()),i.fill()}}break;case"triangle":for(var R=0;R<A.length;R++){var G=A[R];if(null!==G&&R>f&&R<g){T=G.x-c*d.widthRatio/2,C=e.height-G.y-e.area[2];i.beginPath();P=G.color||t[R].color,D=G.color||t[R].color;if("none"!==d.linearType){M=i.createLinearGradient(T,G.y,T,b);"opacity"==d.linearType?(M.addColorStop(0,h(P,d.linearOpacity)),M.addColorStop(1,h(P,1))):(M.addColorStop(0,h(d.customColor[t[R].linearIndex],d.linearOpacity)),M.addColorStop(d.colorStop,h(d.customColor[t[R].linearIndex],d.linearOpacity)),M.addColorStop(1,h(P,1))),P=M}i.moveTo(T,b),i.lineTo(G.x,G.y),i.lineTo(T+G.width,b),i.setStrokeStyle(D),i.setFillStyle(P),d.borderWidth>0&&(i.setLineWidth(d.borderWidth*e.pix),i.stroke()),i.fill()}}break;case"mount":for(var N=0;N<A.length;N++){var Y=A[N];if(null!==Y&&N>f&&N<g){T=Y.x-c*d.widthRatio/2,C=e.height-Y.y-e.area[2];i.beginPath();P=Y.color||t[N].color,D=Y.color||t[N].color;if("none"!==d.linearType){M=i.createLinearGradient(T,Y.y,T,b);"opacity"==d.linearType?(M.addColorStop(0,h(P,d.linearOpacity)),M.addColorStop(1,h(P,1))):(M.addColorStop(0,h(d.customColor[t[N].linearIndex],d.linearOpacity)),M.addColorStop(d.colorStop,h(d.customColor[t[N].linearIndex],d.linearOpacity)),M.addColorStop(1,h(P,1))),P=M}i.moveTo(T,b),i.bezierCurveTo(Y.x-Y.width/4,b,Y.x-Y.width/4,Y.y,Y.x,Y.y),i.bezierCurveTo(Y.x+Y.width/4,Y.y,Y.x+Y.width/4,b,T+Y.width,b),i.setStrokeStyle(D),i.setFillStyle(P),d.borderWidth>0&&(i.setLineWidth(d.borderWidth*e.pix),i.stroke()),i.fill()}}break;case"sharp":for(var j=0;j<A.length;j++){var H=A[j];if(null!==H&&j>f&&j<g){T=H.x-c*d.widthRatio/2,C=e.height-H.y-e.area[2];i.beginPath();P=H.color||t[j].color,D=H.color||t[j].color;if("none"!==d.linearType){M=i.createLinearGradient(T,H.y,T,b);"opacity"==d.linearType?(M.addColorStop(0,h(P,d.linearOpacity)),M.addColorStop(1,h(P,1))):(M.addColorStop(0,h(d.customColor[t[j].linearIndex],d.linearOpacity)),M.addColorStop(d.colorStop,h(d.customColor[t[j].linearIndex],d.linearOpacity)),M.addColorStop(1,h(P,1))),P=M}i.moveTo(T,b),i.quadraticCurveTo(H.x-0,b-C/4,H.x,H.y),i.quadraticCurveTo(H.x+0,b-C/4,T+H.width,b),i.setStrokeStyle(D),i.setFillStyle(P),d.borderWidth>0&&(i.setLineWidth(d.borderWidth*e.pix),i.stroke()),i.fill()}}break}if(!1!==e.dataLabel&&1===r){var X,J,q;X=[].concat(e.chartData.yAxisData.ranges[0]),J=X.pop(),q=X.shift();A=$(t,J,q,s,c,e,d,b,r);dt(A,t,a,i,e,b)}return i.restore(),{xAxisPoints:s,calPoints:A,eachSpacing:c}}(c,e,a,i,t),n=r.xAxisPoints,s=r.calPoints,p=r.eachSpacing;e.chartData.xAxisPoints=n,e.chartData.calPoints=s,e.chartData.eachSpacing=p,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"bar":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),St(d,e,a,i);var r=function(t,e,a,i){for(var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=[],s=(e.height-e.area[0]-e.area[2])/e.categories.length,c=0;c<e.categories.length;c++)n.push(e.area[0]+s/2+s*c);var d=l({},{type:"group",width:s/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.bar),p=[];i.save();var u=-2,x=n.length+2;return e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===r&&mt(e.tooltip.offset.y,e,0,i,s),d.customColor=y(d.linearType,d.customColor,t,a),t.forEach((function(l,c){var f,g,y;f=[].concat(e.chartData.xAxisData.ranges),y=f.pop(),g=f.shift();var v=l.data;switch(d.type){case"group":var m=tt(v,g,y,n,s,e,a,r),b=at(v,g,y,n,s,e,a,c,t,r);p.push(b),m=H(m,s,t.length,c,0,e);for(var A=0;A<m.length;A++){var S=m[A];if(null!==S&&A>u&&A<x){var w=e.area[3],T=S.y-S.width/2;S.height;i.beginPath();var C=S.color||l.color,P=S.color||l.color;if("none"!==d.linearType){var D=i.createLinearGradient(w,S.y,S.x,S.y);"opacity"==d.linearType?(D.addColorStop(0,h(C,d.linearOpacity)),D.addColorStop(1,h(C,1))):(D.addColorStop(0,h(d.customColor[l.linearIndex],d.linearOpacity)),D.addColorStop(d.colorStop,h(d.customColor[l.linearIndex],d.linearOpacity)),D.addColorStop(1,h(C,1))),C=D}if(d.barBorderRadius&&4===d.barBorderRadius.length||!0===d.barBorderCircle){var M=w,F=S.width,L=S.y-S.width/2,k=S.height;d.barBorderCircle&&(d.barBorderRadius=[F/2,F/2,0,0]);var _=(0,o.default)(d.barBorderRadius,4),O=_[0],I=_[1],E=_[2],z=_[3],B=Math.min(F/2,k/2);O=O>B?B:O,I=I>B?B:I,E=E>B?B:E,z=z>B?B:z,O=O<0?0:O,I=I<0?0:I,E=E<0?0:E,z=z<0?0:z,i.arc(M+z,L+z,z,-Math.PI,-Math.PI/2),i.arc(S.x-O,L+O,O,-Math.PI/2,0),i.arc(S.x-I,L+F-I,I,0,Math.PI/2),i.arc(M+E,L+F-E,E,Math.PI/2,Math.PI)}else i.moveTo(w,T),i.lineTo(S.x,T),i.lineTo(S.x,T+S.width),i.lineTo(w,T+S.width),i.lineTo(w,T),i.setLineWidth(1),i.setStrokeStyle(P);i.setFillStyle(C),i.closePath(),i.fill()}}break;case"stack":m=at(v,g,y,n,s,e,a,c,t,r);p.push(m),m=q(m,s,t.length,0,0,e);for(var W=0;W<m.length;W++){var R=m[W];if(null!==R&&W>u&&W<x){i.beginPath();C=R.color||l.color,w=R.x0;i.setFillStyle(C),i.moveTo(w,R.y-R.width/2),i.fillRect(w,R.y-R.width/2,R.height,R.width),i.closePath(),i.fill()}}break}})),!1!==e.dataLabel&&1===r&&t.forEach((function(o,l){var h,c,p;h=[].concat(e.chartData.xAxisData.ranges),p=h.pop(),c=h.shift();var u=o.data;switch(d.type){case"group":var x=tt(u,c,p,n,s,e,a,r);x=H(x,s,t.length,l,0,e),pt(x,o,a,i,e);break;case"stack":x=at(u,c,p,n,s,e,a,l,t,r);pt(x,o,a,i,e);break}})),{yAxisPoints:n,calPoints:p,eachSpacing:s}}(c,e,a,i,t),n=r.yAxisPoints,s=r.calPoints,p=r.eachSpacing;e.chartData.yAxisPoints=n,e.chartData.xAxisPoints=e.chartData.xAxisData.xAxisPoints,e.chartData.calPoints=s,e.chartData.eachSpacing=p,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"area":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),wt(0,e,0,i),St(d,e,a,i);var o=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"straight",opacity:.2,addLine:!1,width:2,gradient:!1,activeType:"none"},e.extra.area),n=e.chartData.xAxisData,s=n.xAxisPoints,c=n.eachSpacing,d=e.height-e.area[2],p=[];i.save();var x=0,f=e.width+c;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),x=-e._scrollDistance_-2*c+e.area[3],f=x+(e.xAxis.itemCount+4)*c),t.forEach((function(t,n){var l,g,y;l=[].concat(e.chartData.yAxisData.ranges[t.index]),g=l.pop(),y=l.shift();var v=t.data,m=Z(v,g,y,s,c,e,a,o);p.push(m);for(var b=_(m,t),A=0;A<b.length;A++){var S=b[A];if(i.beginPath(),i.setStrokeStyle(h(t.color,r.opacity)),r.gradient){var w=i.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);w.addColorStop("0",h(t.color,r.opacity)),w.addColorStop("1.0",h("#FFFFFF",.1)),i.setFillStyle(w)}else i.setFillStyle(h(t.color,r.opacity));if(i.setLineWidth(r.width*e.pix),S.length>1){var T=S[0],C=S[S.length-1];i.moveTo(T.x,T.y);var P=0;if("curve"===r.type)for(var D=0;D<S.length;D++){var M=S[D];if(0==P&&M.x>x&&(i.moveTo(M.x,M.y),P=1),D>0&&M.x>x&&M.x<f){var F=u(S,D-1);i.bezierCurveTo(F.ctrA.x,F.ctrA.y,F.ctrB.x,F.ctrB.y,M.x,M.y)}}if("straight"===r.type)for(var L=0;L<S.length;L++){var k=S[L];0==P&&k.x>x&&(i.moveTo(k.x,k.y),P=1),L>0&&k.x>x&&k.x<f&&i.lineTo(k.x,k.y)}if("step"===r.type)for(var O=0;O<S.length;O++){var I=S[O];0==P&&I.x>x&&(i.moveTo(I.x,I.y),P=1),O>0&&I.x>x&&I.x<f&&(i.lineTo(I.x,S[O-1].y),i.lineTo(I.x,I.y))}i.lineTo(C.x,d),i.lineTo(T.x,d),i.lineTo(T.x,T.y)}else{var E=S[0];i.moveTo(E.x-c/2,E.y)}if(i.closePath(),i.fill(),r.addLine){if("dash"==t.lineType){var z=t.dashLength?t.dashLength:8;z*=e.pix,i.setLineDash([z,z])}if(i.beginPath(),i.setStrokeStyle(t.color),i.setLineWidth(r.width*e.pix),1===S.length)i.moveTo(S[0].x,S[0].y);else{i.moveTo(S[0].x,S[0].y);var B=0;if("curve"===r.type)for(var W=0;W<S.length;W++){var R=S[W];if(0==B&&R.x>x&&(i.moveTo(R.x,R.y),B=1),W>0&&R.x>x&&R.x<f){var G=u(S,W-1);i.bezierCurveTo(G.ctrA.x,G.ctrA.y,G.ctrB.x,G.ctrB.y,R.x,R.y)}}if("straight"===r.type)for(var N=0;N<S.length;N++){var Y=S[N];0==B&&Y.x>x&&(i.moveTo(Y.x,Y.y),B=1),N>0&&Y.x>x&&Y.x<f&&i.lineTo(Y.x,Y.y)}if("step"===r.type)for(var j=0;j<S.length;j++){var H=S[j];0==B&&H.x>x&&(i.moveTo(H.x,H.y),B=1),j>0&&H.x>x&&H.x<f&&(i.lineTo(H.x,S[j-1].y),i.lineTo(H.x,H.y))}i.moveTo(S[0].x,S[0].y)}i.stroke(),i.setLineDash([])}}!1!==e.dataPointShape&&nt(m,t.color,t.pointShape,i,e),lt(m,t.color,t.pointShape,i,e,r,n)})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){var n,l,h;n=[].concat(e.chartData.yAxisData.ranges[t.index]),l=n.pop(),h=n.shift();var d=t.data,p=Z(d,l,h,s,c,e,a,o);ht(p,t,a,i,e)})),i.restore(),{xAxisPoints:s,calPoints:p,eachSpacing:c}}(c,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"ring":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.pieData=Pt(c,e,a,i,t),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"pie":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.pieData=Pt(c,e,a,i,t),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"rose":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.pieData=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{type:"area",activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF",linearType:"none",customColor:[]},e.extra.rose);0==a.pieChartLinePadding&&(a.pieChartLinePadding=r.activeRadius*e.pix);var n={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},s=Math.min((e.width-e.area[1]-e.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding);s=s<10?10:s;var c=r.minRadius||.5*s;s<c&&(s=c+10),t=W(t,r.type,c,s,o);var d=r.activeRadius*e.pix;return r.customColor=y(r.linearType,r.customColor,t,a),t=t.map((function(t){return t._start_+=(r.offsetAngle||0)*Math.PI/180,t})),t.forEach((function(t,a){e.tooltip&&e.tooltip.index==a&&(i.beginPath(),i.setFillStyle(h(t.color,r.activeOpacity||.5)),i.moveTo(n.x,n.y),i.arc(n.x,n.y,d+t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),i.closePath(),i.fill()),i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.lineJoin="round",i.setStrokeStyle(r.borderColor);var o,l=t.color;"custom"==r.linearType&&(o=i.createCircularGradient?i.createCircularGradient(n.x,n.y,t._radius_):i.createRadialGradient(n.x,n.y,0,n.x,n.y,t._radius_),o.addColorStop(0,h(r.customColor[t.linearIndex],1)),o.addColorStop(1,h(t.color,1)),l=o);i.setFillStyle(l),i.moveTo(n.x,n.y),i.arc(n.x,n.y,t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),i.closePath(),i.fill(),1==r.border&&i.stroke()})),!1!==e.dataLabel&&1===o&&ft(t,e,a,i,0,n),{center:n,radius:s,series:t}}(c,e,a,i,t),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"radar":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.radarData=function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=l({},{gridColor:"#cccccc",gridType:"radar",gridEval:1,axisLabel:!1,axisLabelTofix:0,labelShow:!0,labelColor:"#666666",labelPointShow:!1,labelPointRadius:3,labelPointColor:"#cccccc",opacity:.2,gridCount:3,border:!1,borderWidth:2,linearType:"none",customColor:[]},e.extra.radar),n=C(e.categories.length),s={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},c=(e.width-e.area[1]-e.area[3])/2,d=(e.height-e.area[0]-e.area[2])/2,p=Math.min(c-(T(e.categories,a.fontSize,i)+a.radarLabelTextMargin),d-a.radarLabelTextMargin);p-=a.radarLabelTextMargin*e.pix,p=p<10?10:p,p=r.radius?r.radius:p,i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(r.gridColor),n.forEach((function(t,e){var a=x(p*Math.cos(t),p*Math.sin(t),s);i.moveTo(s.x,s.y),e%r.gridEval==0&&i.lineTo(a.x,a.y)})),i.stroke(),i.closePath();for(var u=function(t){var a={};if(i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(r.gridColor),"radar"==r.gridType)n.forEach((function(e,o){var n=x(p/r.gridCount*t*Math.cos(e),p/r.gridCount*t*Math.sin(e),s);0===o?(a=n,i.moveTo(n.x,n.y)):i.lineTo(n.x,n.y)})),i.lineTo(a.x,a.y);else{var o=x(p/r.gridCount*t*Math.cos(1.5),p/r.gridCount*t*Math.sin(1.5),s);i.arc(s.x,s.y,s.y-o.y,0,2*Math.PI,!1)}i.stroke(),i.closePath()},f=1;f<=r.gridCount;f++)u(f);r.customColor=y(r.linearType,r.customColor,t,a);var g=E(n,s,p,t,e,o);if(g.forEach((function(a,o){i.beginPath(),i.setLineWidth(r.borderWidth*e.pix),i.setStrokeStyle(a.color);var n,l=h(a.color,r.opacity);"custom"==r.linearType&&(n=i.createCircularGradient?i.createCircularGradient(s.x,s.y,p):i.createRadialGradient(s.x,s.y,0,s.x,s.y,p),n.addColorStop(0,h(r.customColor[t[o].linearIndex],r.opacity)),n.addColorStop(1,h(a.color,r.opacity)),l=n);if(i.setFillStyle(l),a.data.forEach((function(t,e){0===e?i.moveTo(t.position.x,t.position.y):i.lineTo(t.position.x,t.position.y)})),i.closePath(),i.fill(),!0===r.border&&i.stroke(),i.closePath(),!1!==e.dataPointShape){var c=a.data.map((function(t){return t.position}));nt(c,a.color,a.pointShape,i,e)}})),!0===r.axisLabel){var v=Math.max(r.max,Math.max.apply(null,b(t))),m=p/r.gridCount,A=e.fontSize*e.pix;i.setFontSize(A),i.setFillStyle(e.fontColor),i.setTextAlign("left");for(f=0;f<r.gridCount+1;f++){var S=f*v/r.gridCount;S=S.toFixed(r.axisLabelTofix),i.fillText(String(S),s.x+3*e.pix,s.y-f*m+A/2)}}return xt(n,p,s,e,a,i),!1!==e.dataLabel&&1===o&&(g.forEach((function(t,o){i.beginPath();var r=t.textSize*e.pix||a.fontSize;i.setFontSize(r),i.setFillStyle(t.textColor||e.fontColor),t.data.forEach((function(t,e){Math.abs(t.position.x-s.x)<2?t.position.y<s.y?(i.setTextAlign("center"),i.fillText(t.value,t.position.x,t.position.y-4)):(i.setTextAlign("center"),i.fillText(t.value,t.position.x,t.position.y+r+2)):t.position.x<s.x?(i.setTextAlign("right"),i.fillText(t.value,t.position.x-4,t.position.y+r/2-2)):(i.setTextAlign("left"),i.fillText(t.value,t.position.x+4,t.position.y+r/2-2))})),i.closePath(),i.stroke()})),i.setTextAlign("left")),{center:s,radius:p,angleList:n}}(c,e,a,i,t),Ct(e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.arcbarData=function(t,e,a,i){var o,r,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=l({},{startAngle:.75,endAngle:.25,type:"default",direction:"cw",lineCap:"round",width:12,gap:2,linearType:"none",customColor:[]},e.extra.arcbar);t=R(t,s,n),o=s.centerX||s.centerY?{x:s.centerX?s.centerX:e.width/2,y:s.centerY?s.centerY:e.height/2}:{x:e.width/2,y:e.height/2},s.radius?r=s.radius:(r=Math.min(o.x,o.y),r-=5*e.pix,r-=s.width/2),r=r<10?10:r,s.customColor=y(s.linearType,s.customColor,t,a);for(var c=0;c<t.length;c++){var d=t[c];i.setLineWidth(s.width*e.pix),i.setStrokeStyle(s.backgroundColor||"#E9E9E9"),i.setLineCap(s.lineCap),i.beginPath(),"default"==s.type?i.arc(o.x,o.y,r-(s.width*e.pix+s.gap*e.pix)*c,s.startAngle*Math.PI,s.endAngle*Math.PI,"ccw"==s.direction):i.arc(o.x,o.y,r-(s.width*e.pix+s.gap*e.pix)*c,0,2*Math.PI,"ccw"==s.direction),i.stroke();var p=d.color;if("custom"==s.linearType){var u=i.createLinearGradient(o.x-r,o.y,o.x+r,o.y);u.addColorStop(1,h(s.customColor[d.linearIndex],1)),u.addColorStop(0,h(d.color,1)),p=u}i.setLineWidth(s.width*e.pix),i.setStrokeStyle(p),i.setLineCap(s.lineCap),i.beginPath(),i.arc(o.x,o.y,r-(s.width*e.pix+s.gap*e.pix)*c,s.startAngle*Math.PI,d._proportion_*Math.PI,"ccw"==s.direction),i.stroke()}return st(e,a,i,o),{center:o,radius:r,series:t}}(c,e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),e.chartData.gaugeData=function(t,e,a,i,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=l({},{type:"default",startAngle:.75,endAngle:.25,width:15,labelOffset:13,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},a.extra.gauge);void 0==n.oldAngle&&(n.oldAngle=n.startAngle),void 0==n.oldData&&(n.oldData=0),t=N(t,n.startAngle,n.endAngle);var s={x:a.width/2,y:a.height/2},c=Math.min(s.x,s.y);c-=5*a.pix,c-=n.width/2,c=c<10?10:c;var d=c-n.width,p=0;if("progress"==n.type){var u=c-3*n.width;o.beginPath();var x=o.createLinearGradient(s.x,s.y-u,s.x,s.y+u);x.addColorStop("0",h(e[0].color,.3)),x.addColorStop("1.0",h("#FFFFFF",.1)),o.setFillStyle(x),o.arc(s.x,s.y,u,0,2*Math.PI,!1),o.fill(),o.setLineWidth(n.width),o.setStrokeStyle(h(e[0].color,.3)),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,d,n.startAngle*Math.PI,n.endAngle*Math.PI,!1),o.stroke(),p=n.endAngle<n.startAngle?2+n.endAngle-n.startAngle:n.startAngle-n.endAngle;n.splitLine.splitNumber;var f=p/n.splitLine.splitNumber/n.splitLine.childNumber,g=-c-.5*n.width-n.splitLine.fixRadius,y=-c-n.width-n.splitLine.fixRadius+n.splitLine.width;o.save(),o.translate(s.x,s.y),o.rotate((n.startAngle-1)*Math.PI);for(var v=n.splitLine.splitNumber*n.splitLine.childNumber+1,m=e[0].data*r,b=0;b<v;b++)o.beginPath(),m>b/v?o.setStrokeStyle(h(e[0].color,1)):o.setStrokeStyle(h(e[0].color,.3)),o.setLineWidth(3*a.pix),o.moveTo(g,0),o.lineTo(y,0),o.stroke(),o.rotate(f*Math.PI);o.restore(),e=G(e,n,r),o.setLineWidth(n.width),o.setStrokeStyle(e[0].color),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,d,n.startAngle*Math.PI,e[0]._proportion_*Math.PI,!1),o.stroke();var A=c-2.5*n.width;o.save(),o.translate(s.x,s.y),o.rotate((e[0]._proportion_-1)*Math.PI),o.beginPath(),o.setLineWidth(n.width/3);var S=o.createLinearGradient(0,.6*-A,0,.6*A);S.addColorStop("0",h("#FFFFFF",0)),S.addColorStop("0.5",h(e[0].color,1)),S.addColorStop("1.0",h("#FFFFFF",0)),o.setStrokeStyle(S),o.arc(0,0,A,.85*Math.PI,1.15*Math.PI,!1),o.stroke(),o.beginPath(),o.setLineWidth(1),o.setStrokeStyle(e[0].color),o.setFillStyle(e[0].color),o.moveTo(-A-n.width/3/2,-4),o.lineTo(-A-n.width/3/2-4,0),o.lineTo(-A-n.width/3/2,4),o.lineTo(-A-n.width/3/2,-4),o.stroke(),o.fill(),o.restore()}else{o.setLineWidth(n.width),o.setLineCap("butt");for(var w=0;w<t.length;w++){var T=t[w];o.beginPath(),o.setStrokeStyle(T.color),o.arc(s.x,s.y,c,T._startAngle_*Math.PI,T._endAngle_*Math.PI,!1),o.stroke()}o.save(),p=n.endAngle<n.startAngle?2+n.endAngle-n.startAngle:n.startAngle-n.endAngle;var C=p/n.splitLine.splitNumber,P=p/n.splitLine.splitNumber/n.splitLine.childNumber,D=-c-.5*n.width-n.splitLine.fixRadius,M=-c-.5*n.width-n.splitLine.fixRadius+n.splitLine.width,F=-c-.5*n.width-n.splitLine.fixRadius+n.splitLine.childWidth;o.translate(s.x,s.y),o.rotate((n.startAngle-1)*Math.PI);for(var L=0;L<n.splitLine.splitNumber+1;L++)o.beginPath(),o.setStrokeStyle(n.splitLine.color),o.setLineWidth(2*a.pix),o.moveTo(D,0),o.lineTo(M,0),o.stroke(),o.rotate(C*Math.PI);o.restore(),o.save(),o.translate(s.x,s.y),o.rotate((n.startAngle-1)*Math.PI);for(var k=0;k<n.splitLine.splitNumber*n.splitLine.childNumber+1;k++)o.beginPath(),o.setStrokeStyle(n.splitLine.color),o.setLineWidth(1*a.pix),o.moveTo(D,0),o.lineTo(F,0),o.stroke(),o.rotate(P*Math.PI);o.restore(),e=Y(e,t,n,r);for(var _=0;_<e.length;_++){var O=e[_];o.save(),o.translate(s.x,s.y),o.rotate((O._proportion_-1)*Math.PI),o.beginPath(),o.setFillStyle(O.color),o.moveTo(n.pointer.width,0),o.lineTo(0,-n.pointer.width/2),o.lineTo(-d,0),o.lineTo(0,n.pointer.width/2),o.lineTo(n.pointer.width,0),o.closePath(),o.fill(),o.beginPath(),o.setFillStyle("#FFFFFF"),o.arc(0,0,n.pointer.width/6,0,2*Math.PI,!1),o.fill(),o.restore()}!1!==a.dataLabel&&ut(n,c,s,a,i,o)}return st(a,i,o,s),1===r&&"gauge"===a.type&&(a.extra.gauge.oldAngle=e[0]._proportion_,a.extra.gauge.oldData=e[0].data),{center:s,radius:c,innerRadius:d,categories:t,totalAngle:p}}(d,c,e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break;case"candle":this.animationInstance=new Wt({timing:e.timing,duration:f,onProcess:function(t){i.clearRect(0,0,e.width,e.height),e.rotate&&rt(i,e),wt(0,e,0,i),St(d,e,a,i);var o=function(t,e,a,i,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=l({},{color:{},average:{}},a.extra.candle);n.color=l({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},n.color),n.average=l({},{show:!1,name:[],day:[],color:i.color},n.average),a.extra.candle=n;var s=a.chartData.xAxisData,h=s.xAxisPoints,c=s.eachSpacing,d=[];o.save();var p=-2,x=h.length+2,f=0,g=a.width+c;return a._scrollDistance_&&0!==a._scrollDistance_&&!0===a.enableScroll&&(o.translate(a._scrollDistance_,0),p=Math.floor(-a._scrollDistance_/c)-2,x=p+a.xAxis.itemCount+4,f=-a._scrollDistance_-2*c+a.area[3],g=f+(a.xAxis.itemCount+4)*c),(n.average.show||e)&&e.forEach((function(t,e){var n,l,s;n=[].concat(a.chartData.yAxisData.ranges[t.index]),l=n.pop(),s=n.shift();for(var d=t.data,p=Z(d,l,s,h,c,a,i,r),x=_(p,t),y=0;y<x.length;y++){var v=x[y];if(o.beginPath(),o.setStrokeStyle(t.color),o.setLineWidth(1),1===v.length)o.moveTo(v[0].x,v[0].y),o.arc(v[0].x,v[0].y,1,0,2*Math.PI);else{o.moveTo(v[0].x,v[0].y);for(var m=0,b=0;b<v.length;b++){var A=v[b];if(0==m&&A.x>f&&(o.moveTo(A.x,A.y),m=1),b>0&&A.x>f&&A.x<g){var S=u(v,b-1);o.bezierCurveTo(S.ctrA.x,S.ctrA.y,S.ctrB.x,S.ctrB.y,A.x,A.y)}}o.moveTo(v[0].x,v[0].y)}o.closePath(),o.stroke()}})),t.forEach((function(t,e){var l,s,u;l=[].concat(a.chartData.yAxisData.ranges[t.index]),s=l.pop(),u=l.shift();var f=t.data,g=U(f,s,u,h,c,a,i,r);d.push(g);for(var y=_(g,t),v=0;v<y[0].length;v++)if(v>p&&v<x){var m=y[0][v];o.beginPath(),f[v][1]-f[v][0]>0?(o.setStrokeStyle(n.color.upLine),o.setFillStyle(n.color.upFill),o.setLineWidth(1*a.pix),o.moveTo(m[3].x,m[3].y),o.lineTo(m[1].x,m[1].y),o.lineTo(m[1].x-c/4,m[1].y),o.lineTo(m[0].x-c/4,m[0].y),o.lineTo(m[0].x,m[0].y),o.lineTo(m[2].x,m[2].y),o.lineTo(m[0].x,m[0].y),o.lineTo(m[0].x+c/4,m[0].y),o.lineTo(m[1].x+c/4,m[1].y),o.lineTo(m[1].x,m[1].y),o.moveTo(m[3].x,m[3].y)):(o.setStrokeStyle(n.color.downLine),o.setFillStyle(n.color.downFill),o.setLineWidth(1*a.pix),o.moveTo(m[3].x,m[3].y),o.lineTo(m[0].x,m[0].y),o.lineTo(m[0].x-c/4,m[0].y),o.lineTo(m[1].x-c/4,m[1].y),o.lineTo(m[1].x,m[1].y),o.lineTo(m[2].x,m[2].y),o.lineTo(m[1].x,m[1].y),o.lineTo(m[1].x+c/4,m[1].y),o.lineTo(m[0].x+c/4,m[0].y),o.lineTo(m[0].x,m[0].y),o.moveTo(m[3].x,m[3].y)),o.closePath(),o.fill(),o.stroke()}})),o.restore(),{xAxisPoints:h,calPoints:d,eachSpacing:c}}(c,v,e,a,i,t),r=o.xAxisPoints,n=o.calPoints,s=o.eachSpacing;e.chartData.xAxisPoints=r,e.chartData.calPoints=n,e.chartData.eachSpacing=s,Tt(0,e,a,i),!1!==e.enableMarkLine&&1===t&&gt(e,0,i),Ct(v?0:e.series,e,a,i,e.chartData),At(e,a,i,t),zt(0,i)},onAnimationFinish:function(){n.uevent.trigger("renderComplete")}});break}}function Gt(){this.events={}}Wt.prototype.stop=function(){this.isStop=!0},Gt.prototype.addEventListener=function(t,e){this.events[t]=this.events[t]||[],this.events[t].push(e)},Gt.prototype.delEventListener=function(t){this.events[t]=[]},Gt.prototype.trigger=function(){for(var t=arguments.length,e=Array(t),a=0;a<t;a++)e[a]=arguments[a];var i=e[0],o=e.slice(1);this.events[i]&&this.events[i].forEach((function(t){try{t.apply(null,o)}catch(e){}}))};var Nt=function(t){t.pix=t.pixelRatio?t.pixelRatio:1,t.fontSize=t.fontSize?t.fontSize:13,t.fontColor=t.fontColor?t.fontColor:n.fontColor,""!=t.background&&"none"!=t.background||(t.background="#FFFFFF"),t.title=l({},t.title),t.subtitle=l({},t.subtitle),t.duration=t.duration?t.duration:1e3,t.yAxis=l({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,gridSet:"number",splitNumber:5,gridType:"solid",dashLength:4*t.pix,gridColor:"#cccccc",padding:10,fontColor:"#666666"},t.yAxis),t.xAxis=l({},{rotateLabel:!1,rotateAngle:45,disabled:!1,disableGrid:!1,splitNumber:5,calibration:!1,fontColor:"#666666",fontSize:13,lineHeight:20,marginTop:0,gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc",titleFontSize:13,titleOffsetY:0,titleOffsetX:0,titleFontColor:"#666666"},t.xAxis),t.xAxis.scrollPosition=t.xAxis.scrollAlign,t.legend=l({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:t.fontSize,lineHeight:t.fontSize,fontColor:t.fontColor,formatter:{},hiddenColor:"#CECECE"},t.legend),t.extra=l({tooltip:{legendShape:"auto"}},t.extra),t.rotate=!!t.rotate,t.animation=!!t.animation,t.rotate=!!t.rotate,t.canvas2d=!!t.canvas2d;var e=l({},n);if(e.color=t.color?t.color:e.color,"pie"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.pie.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"ring"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.ring.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"rose"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.rose.labelWidth*t.pix||e.pieChartLinePadding*t.pix),e.pieChartTextPadding=!1===t.dataLabel?0:e.pieChartTextPadding*t.pix,e.rotate=t.rotate,t.rotate){var a=t.width,i=t.height;t.width=i,t.height=a}if(t.padding=t.padding?t.padding:e.padding,e.yAxisWidth=n.yAxisWidth*t.pix,e.fontSize=t.fontSize*t.pix,e.titleFontSize=n.titleFontSize*t.pix,e.subtitleFontSize=n.subtitleFontSize*t.pix,!t.context)throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！");this.context=t.context,this.context.setTextAlign||(this.context.setStrokeStyle=function(t){return this.strokeStyle=t},this.context.setLineWidth=function(t){return this.lineWidth=t},this.context.setLineCap=function(t){return this.lineCap=t},this.context.setFontSize=function(t){return this.font=t+"px sans-serif"},this.context.setFillStyle=function(t){return this.fillStyle=t},this.context.setTextAlign=function(t){return this.textAlign=t},this.context.setTextBaseline=function(t){return this.textBaseline=t},this.context.setShadow=function(t,e,a,i){this.shadowColor=i,this.shadowOffsetX=t,this.shadowOffsetY=e,this.shadowBlur=a},this.context.draw=function(){}),this.context.setLineDash||(this.context.setLineDash=function(t){}),t.chartData={},this.uevent=new Gt,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=t,this.config=e,Rt.call(this,t.type,t,e,this.context)};Nt.prototype.updateData=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.opts=l({},this.opts,t),this.opts.updateData=!0;var e=t.scrollPosition||"current";switch(e){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":var a=ot(this.opts.series,this.opts,this.config,this.context),i=a.yAxisWidth;this.config.yAxisWidth=i;var o=0,r=Q(this.opts.categories,this.opts,this.config),n=r.xAxisPoints,s=r.startX,h=r.endX,c=r.eachSpacing,d=c*(n.length-1),p=h-s;o=p-d,this.scrollOption={currentOffset:o,startTouchX:o,distance:0,lastMoveTime:0},this.opts._scrollDistance_=o;break}Rt.call(this,this.opts.type,this.opts,this.config,this.context)},Nt.prototype.zoom=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0===this.opts.enableScroll){var e=Math.round(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=t.itemCount;var a=ot(this.opts.series,this.opts,this.config,this.context),i=a.yAxisWidth;this.config.yAxisWidth=i;var o=0,r=Q(this.opts.categories,this.opts,this.config),n=r.xAxisPoints,l=r.startX,s=r.endX,h=r.eachSpacing,c=h*e,p=s-l,u=p-h*(n.length-1);o=p/2-c,o>0&&(o=0),o<u&&(o=u),this.scrollOption={currentOffset:o,startTouchX:0,distance:0,lastMoveTime:0},d(this,o,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=o,Rt.call(this,this.opts.type,this.opts,this.config,this.context)}else console.log("[uCharts] 请启用滚动条后使用")},Nt.prototype.dobuleZoom=function(t){if(!0===this.opts.enableScroll){var e=t.changedTouches;if(!(e.length<2)){for(var a=0;a<e.length;a++)e[a].x=e[a].x?e[a].x:e[a].clientX,e[a].y=e[a].y?e[a].y:e[a].clientY;var i=[S(e[0],this.opts,t),S(e[1],this.opts,t)],o=Math.abs(i[0].x-i[1].x);if(!this.scrollOption.moveCount){var r={changedTouches:[{x:e[0].x,y:this.opts.area[0]/this.opts.pix+2}]},n={changedTouches:[{x:e[1].x,y:this.opts.area[0]/this.opts.pix+2}]};this.opts.rotate&&(r={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[0].y}]},n={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[1].y}]});var l=this.getCurrentDataIndex(r).index,s=this.getCurrentDataIndex(n).index,h=Math.abs(l-s);return this.scrollOption.moveCount=h,this.scrollOption.moveCurrent1=Math.min(l,s),void(this.scrollOption.moveCurrent2=Math.max(l,s))}var c=o/this.scrollOption.moveCount,p=(this.opts.width-this.opts.area[1]-this.opts.area[3])/c;p=p<=2?2:p,p=p>=this.opts.categories.length?this.opts.categories.length:p,this.opts.animation=!1,this.opts.xAxis.itemCount=p;var u=0,x=Q(this.opts.categories,this.opts,this.config),f=x.xAxisPoints,g=x.startX,y=x.endX,v=x.eachSpacing,m=v*this.scrollOption.moveCurrent1,b=y-g,A=b-v*(f.length-1);u=-m+Math.min(i[0].x,i[1].x)-this.opts.area[3]-v,u>0&&(u=0),u<A&&(u=A),this.scrollOption.currentOffset=u,this.scrollOption.startTouchX=0,this.scrollOption.distance=0,d(this,u,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=u,Rt.call(this,this.opts.type,this.opts,this.config,this.context)}}else console.log("[uCharts] 请启用滚动条后使用")},Nt.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},Nt.prototype.addEventListener=function(t,e){this.uevent.addEventListener(t,e)},Nt.prototype.delEventListener=function(t){this.uevent.delEventListener(t)},Nt.prototype.getCurrentDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],e){var a=S(e,this.opts,t);return"pie"===this.opts.type||"ring"===this.opts.type?function(t,e,a){var i=-1,o=z(e.series);if(e&&e.center&&k(t,e.center,e.radius)){var r=Math.atan2(e.center.y-t.y,t.x-e.center.x);r=-r,a.extra.pie&&a.extra.pie.offsetAngle&&(r-=a.extra.pie.offsetAngle*Math.PI/180),a.extra.ring&&a.extra.ring.offsetAngle&&(r-=a.extra.ring.offsetAngle*Math.PI/180);for(var n=0,l=o.length;n<l;n++)if(p(r,o[n]._start_,o[n]._start_+2*o[n]._proportion_*Math.PI)){i=n;break}}return i}({x:a.x,y:a.y},this.opts.chartData.pieData,this.opts):"rose"===this.opts.type?function(t,e,a){var i=-1,o=W(a._series_,a.extra.rose.type,e.radius,e.radius);if(e&&e.center&&k(t,e.center,e.radius)){var r=Math.atan2(e.center.y-t.y,t.x-e.center.x);r=-r,a.extra.rose&&a.extra.rose.offsetAngle&&(r-=a.extra.rose.offsetAngle*Math.PI/180);for(var n=0,l=o.length;n<l;n++)if(p(r,o[n]._start_,o[n]._start_+2*o[n]._rose_proportion_*Math.PI)){i=n;break}}return i}({x:a.x,y:a.y},this.opts.chartData.pieData,this.opts):"radar"===this.opts.type?function(t,e,a){var i=2*Math.PI/a,o=-1;if(k(t,e.center,e.radius)){var r=function(t){return t<0&&(t+=2*Math.PI),t>2*Math.PI&&(t-=2*Math.PI),t},n=Math.atan2(e.center.y-t.y,t.x-e.center.x);n*=-1,n<0&&(n+=2*Math.PI);var l=e.angleList.map((function(t){return t=r(-1*t),t}));l.forEach((function(t,e){var a=r(t-i/2),l=r(t+i/2);l<a&&(l+=2*Math.PI),(n>=a&&n<=l||n+2*Math.PI>=a&&n+2*Math.PI<=l)&&(o=e)}))}return o}({x:a.x,y:a.y},this.opts.chartData.radarData,this.opts.categories.length):"funnel"===this.opts.type?function(t,e){for(var a=-1,i=0,o=e.series.length;i<o;i++){var r=e.series[i];if(t.x>r.funnelArea[0]&&t.x<r.funnelArea[2]&&t.y>r.funnelArea[1]&&t.y<r.funnelArea[3]){a=i;break}}return a}({x:a.x,y:a.y},this.opts.chartData.funnelData):"map"===this.opts.type?function(t,e){for(var a=-1,i=e.chartData.mapData,o=e.series,r=function(t,e,a,i,o,r){return{x:(e-o)/i+a.xMin,y:a.yMax-(t-r)/i}}(t.y,t.x,i.bounds,i.scale,i.xoffset,i.yoffset),n=[r.x,r.y],l=0,s=o.length;l<s;l++){var h=o[l].geometry.coordinates;if(Lt(n,h,e.chartData.mapData.mercator)){a=l;break}}return a}({x:a.x,y:a.y},this.opts):"word"===this.opts.type?function(t,e){for(var a=-1,i=0,o=e.length;i<o;i++){var r=e[i];if(t.x>r.area[0]&&t.x<r.area[2]&&t.y>r.area[1]&&t.y<r.area[3]){a=i;break}}return a}({x:a.x,y:a.y},this.opts.chartData.wordCloudData):"bar"===this.opts.type?function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r={index:-1,group:[]},n=a.chartData.eachSpacing/2,l=a.chartData.yAxisPoints;return e&&e.length>0&&L(t,a,i)&&l.forEach((function(e,a){t.y+o+n>e&&(r.index=a)})),r}({x:a.x,y:a.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset)):function(t,e,a,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r={index:-1,group:[]},n=a.chartData.eachSpacing/2,l=[];if(e&&e.length>0){if(a.categories){for(var s=1;s<a.chartData.xAxisPoints.length;s++)l.push(a.chartData.xAxisPoints[s]-n);"line"!=a.type&&"area"!=a.type||"justify"!=a.xAxis.boundaryGap||(l=a.chartData.xAxisPoints)}else n=0;if(L(t,a,i))if(a.categories)l.forEach((function(e,a){t.x+o+n>e&&(r.index=a)}));else{for(var h=Array(e.length),c=0;c<e.length;c++){h[c]=Array(e[c].length);for(var d=0;d<e[c].length;d++)h[c][d]=Math.abs(e[c][d].x-t.x)}for(var p=Array(h.length),u=Array(h.length),x=0;x<h.length;x++)p[x]=Math.min.apply(null,h[x]),u[x]=h[x].indexOf(p[x]);var f=Math.min.apply(null,p);r.index=[];for(var g=0;g<p.length;g++)p[g]==f&&(r.group.push(g),r.index.push(u[g]))}}return r}({x:a.x,y:a.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},Nt.prototype.getLegendDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],e){var a=S(e,this.opts,t);return function(t,e,a){var i=-1;if(function(t,e){return t.x>e.start.x&&t.x<e.end.x&&t.y>e.start.y&&t.y<e.end.y}(t,e.area)){for(var o=e.points,r=-1,n=0,l=o.length;n<l;n++)for(var s=o[n],h=0;h<s.length;h++){r+=1;var c=s[h]["area"];if(c&&t.x>c[0]-0&&t.x<c[2]+0&&t.y>c[1]-0&&t.y<c[3]+0){i=r;break}}return i}return i}({x:a.x,y:a.y},this.opts.chartData.legendData)}return-1},Nt.prototype.touchLegend=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=null;if(a=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],a){S(a,this.opts,t);var i=this.getLegendDataIndex(t);i>=0&&("candle"==this.opts.type?this.opts.seriesMA[i].show=!this.opts.seriesMA[i].show:this.opts.series[i].show=!this.opts.series[i].show,this.opts.animation=!!e.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,Rt.call(this,this.opts.type,this.opts,this.config,this.context))}},Nt.prototype.showToolTip=function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;i=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],i||console.log("[uCharts] 未获取到event坐标信息");var o=S(i,this.opts,t),r=this.scrollOption.currentOffset,n=l({},this.opts,{_scrollDistance_:r,animation:!1});if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type||"scatter"===this.opts.type||"bubble"===this.opts.type){var s=this.getCurrentDataIndex(t),h=void 0==a.index?s.index:a.index;if(h>-1||h.length>0){var c=w(this.opts.series,h,s.group);if(0!==c.length){var d=P(c,this.opts,h,s.group,this.opts.categories,a),p=d.textList,u=d.offset;u.y=o.y,n.tooltip={textList:void 0!==a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h,group:s.group}}}Rt.call(this,n.type,n,this.config,this.context)}if("mount"===this.opts.type){h=void 0==a.index?this.getCurrentDataIndex(t).index:a.index;if(h>-1){n=l({},this.opts,{animation:!1}),c=l({},n._series_[h]),p=[{text:a.formatter?a.formatter(c,void 0,h,n):c.name+": "+c.data,color:c.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?c.legendShape:this.opts.extra.tooltip.legendShape}],u={x:n.chartData.calPoints[h].x,y:o.y};n.tooltip={textList:a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h}}Rt.call(this,n.type,n,this.config,this.context)}if("bar"===this.opts.type){s=this.getCurrentDataIndex(t),h=void 0==a.index?s.index:a.index;if(h>-1||h.length>0){c=w(this.opts.series,h,s.group);if(0!==c.length){d=P(c,this.opts,h,s.group,this.opts.categories,a),p=d.textList,u=d.offset;u.x=o.x,n.tooltip={textList:void 0!==a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h}}}Rt.call(this,n.type,n,this.config,this.context)}if("mix"===this.opts.type){s=this.getCurrentDataIndex(t),h=void 0==a.index?s.index:a.index;if(h>-1){r=this.scrollOption.currentOffset,n=l({},this.opts,{_scrollDistance_:r,animation:!1}),c=w(this.opts.series,h);if(0!==c.length){var x=D(c,this.opts,h,this.opts.categories,a);p=x.textList,u=x.offset;u.y=o.y,n.tooltip={textList:a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h}}}Rt.call(this,n.type,n,this.config,this.context)}if("candle"===this.opts.type){s=this.getCurrentDataIndex(t),h=void 0==a.index?s.index:a.index;if(h>-1){r=this.scrollOption.currentOffset,n=l({},this.opts,{_scrollDistance_:r,animation:!1}),c=w(this.opts.series,h);if(0!==c.length){d=M(this.opts.series[0].data,c,this.opts,h,this.opts.categories,this.opts.extra.candle,a),p=d.textList,u=d.offset;u.y=o.y,n.tooltip={textList:a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h}}}Rt.call(this,n.type,n,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type||"funnel"===this.opts.type){h=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(h>-1){n=l({},this.opts,{animation:!1}),c=l({},n._series_[h]),p=[{text:a.formatter?a.formatter(c,void 0,h,n):c.name+": "+c.data,color:c.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?c.legendShape:this.opts.extra.tooltip.legendShape}],u={x:o.x,y:o.y};n.tooltip={textList:a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h}}Rt.call(this,n.type,n,this.config,this.context)}if("map"===this.opts.type){h=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(h>-1){n=l({},this.opts,{animation:!1}),c=l({},this.opts.series[h]);c.name=c.properties.name;p=[{text:a.formatter?a.formatter(c,void 0,h,this.opts):c.name,color:c.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?c.legendShape:this.opts.extra.tooltip.legendShape}],u={x:o.x,y:o.y};n.tooltip={textList:a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h}}n.updateData=!1,Rt.call(this,n.type,n,this.config,this.context)}if("word"===this.opts.type){h=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(h>-1){n=l({},this.opts,{animation:!1}),c=l({},this.opts.series[h]),p=[{text:a.formatter?a.formatter(c,void 0,h,this.opts):c.name,color:c.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?c.legendShape:this.opts.extra.tooltip.legendShape}],u={x:o.x,y:o.y};n.tooltip={textList:a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h}}n.updateData=!1,Rt.call(this,n.type,n,this.config,this.context)}if("radar"===this.opts.type){h=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(h>-1){n=l({},this.opts,{animation:!1}),c=w(this.opts.series,h);if(0!==c.length){p=c.map((function(t){return{text:a.formatter?a.formatter(t,e.opts.categories[h],h,e.opts):t.name+": "+t.data,color:t.color,legendShape:"auto"==e.opts.extra.tooltip.legendShape?t.legendShape:e.opts.extra.tooltip.legendShape}})),u={x:o.x,y:o.y};n.tooltip={textList:a.textList?a.textList:p,offset:void 0!==a.offset?a.offset:u,option:a,index:h}}}Rt.call(this,n.type,n,this.config,this.context)}},Nt.prototype.translate=function(t){this.scrollOption={currentOffset:t,startTouchX:t,distance:0,lastMoveTime:0};var e=l({},this.opts,{_scrollDistance_:t,animation:!1});Rt.call(this,this.opts.type,e,this.config,this.context)},Nt.prototype.scrollStart=function(t){var e=null;e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0];var a=S(e,this.opts,t);e&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=a.x)},Nt.prototype.scroll=function(t){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());var e=this.opts.touchMoveLimit||60,a=Date.now(),i=a-this.scrollOption.lastMoveTime;if(!(i<Math.floor(1e3/e))&&0!=this.scrollOption.startTouchX){this.scrollOption.lastMoveTime=a;var o=null;if(o=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],o&&!0===this.opts.enableScroll){var r,n=S(o,this.opts,t);r=n.x-this.scrollOption.startTouchX;var s=this.scrollOption.currentOffset,h=d(this,s+r,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=r=h-s;var c=l({},this.opts,{_scrollDistance_:s+r,animation:!1});return this.opts=c,Rt.call(this,c.type,c,this.config,this.context),s+r}}},Nt.prototype.scrollEnd=function(t){if(!0===this.opts.enableScroll){var e=this.scrollOption,a=e.currentOffset,i=e.distance;this.scrollOption.currentOffset=a+i,this.scrollOption.distance=0,this.scrollOption.moveCount=0}};var Yt=Nt;e.default=Yt},"68b1a":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var o=i(a("0bf9")),r={components:{Loading2:o.default},name:"qiun-loading",props:{loadingType:{type:Number,default:2}},data:function(){return{}}};e.default=r},"68cc":function(t,e,a){"use strict";a.r(e);var i=a("c13f"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},"753c":function(t,e,a){var i=a("7dc0");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("7ae363ca",i,!0,{sourceMap:!1,shadowMode:!1})},"7dc0":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".container[data-v-701420ba]{width:30px;height:30px;position:relative}.container.loading2[data-v-701420ba]{-webkit-transform:rotate(10deg);transform:rotate(10deg)}.container.loading2 .shape[data-v-701420ba]{border-radius:5px}.container.loading2[data-v-701420ba]{-webkit-animation:rotation 1s infinite;animation:rotation 1s infinite}.container .shape[data-v-701420ba]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-701420ba]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-701420ba]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-701420ba]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-701420ba]{bottom:0;right:0;background-color:#e66}.loading2 .shape1[data-v-701420ba]{-webkit-animation:animation2shape1-data-v-701420ba .5s ease 0s infinite alternate;animation:animation2shape1-data-v-701420ba .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape1-data-v-701420ba{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,20px);transform:translate(20px,20px)}}@keyframes animation2shape1-data-v-701420ba{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,20px);transform:translate(20px,20px)}}.loading2 .shape2[data-v-701420ba]{-webkit-animation:animation2shape2-data-v-701420ba .5s ease 0s infinite alternate;animation:animation2shape2-data-v-701420ba .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape2-data-v-701420ba{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,20px);transform:translate(-20px,20px)}}@keyframes animation2shape2-data-v-701420ba{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,20px);transform:translate(-20px,20px)}}.loading2 .shape3[data-v-701420ba]{-webkit-animation:animation2shape3-data-v-701420ba .5s ease 0s infinite alternate;animation:animation2shape3-data-v-701420ba .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape3-data-v-701420ba{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,-20px);transform:translate(20px,-20px)}}@keyframes animation2shape3-data-v-701420ba{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,-20px);transform:translate(20px,-20px)}}.loading2 .shape4[data-v-701420ba]{-webkit-animation:animation2shape4-data-v-701420ba .5s ease 0s infinite alternate;animation:animation2shape4-data-v-701420ba .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape4-data-v-701420ba{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,-20px);transform:translate(-20px,-20px)}}@keyframes animation2shape4-data-v-701420ba{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,-20px);transform:translate(-20px,-20px)}}",""]),t.exports=e},"828a":function(t,e,a){"use strict";var i=a("b2d1"),o=a.n(i);o.a},"86ff":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"container loading2"},[e("v-uni-view",{staticClass:"shape shape1"}),e("v-uni-view",{staticClass:"shape shape2"}),e("v-uni-view",{staticClass:"shape shape3"}),e("v-uni-view",{staticClass:"shape shape4"})],1)},o=[]},"8a21":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={qiunLoading:a("aaa0").default,qiunError:a("3ec2").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"chartsview",attrs:{id:"ChartBoxId"+t.cid}},[t.mixinDatacomLoading?a("v-uni-view",[a("qiun-loading",{attrs:{loadingType:t.loadingType}})],1):t._e(),t.mixinDatacomErrorMessage&&t.errorShow?a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reloading.apply(void 0,arguments)}}},[a("qiun-error",{attrs:{errorMessage:t.errorMessage}})],1):t._e(),t.echarts?[a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showchart,expression:"showchart"}],wxsProps:{"change:resize":"echartsResize","change:prop":"echartsOpts"},staticStyle:{width:"100%",height:"100%"},style:{background:t.background},attrs:{"data-directory":t.directory,id:"EC"+t.cid,prop:t.echartsOpts,"change:prop":t.rdcharts.ecinit,resize:t.echartsResize,"change:resize":t.rdcharts.ecresize}})]:[a("v-uni-view",{wxsProps:{"change:prop":"uchartsOpts"},attrs:{id:"UC"+t.cid,prop:t.uchartsOpts,"change:prop":t.rdcharts.ucinit},on:{mousemove:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseMove(e,t.$getComponentDescriptor())},mousedown:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseDown(e,t.$getComponentDescriptor())},mouseup:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseUp(e,t.$getComponentDescriptor())},touchstart:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchStart(e,t.$getComponentDescriptor())},touchmove:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchMove(e,t.$getComponentDescriptor())},touchend:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchEnd(e,t.$getComponentDescriptor())},click:function(e){e=t.$handleWxsEvent(e),t.rdcharts.tap(e,t.$getComponentDescriptor())}}},[a("v-uni-canvas",{directives:[{name:"show",rawName:"v-show",value:t.showchart,expression:"showchart"}],style:{width:t.cWidth+"px",height:t.cHeight+"px",background:t.background},attrs:{id:t.cid,canvasId:t.cid,"disable-scroll":t.disableScroll},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t._error.apply(void 0,arguments)}}})],1)]],2)},r=[]},"984f":function(t,e,a){"use strict";a.r(e);var i=a("e487"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},a3e6:function(t,e,a){"use strict";a.r(e);var i=a("8a21"),o=a("984f");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);var n=a("f70c");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("828a");var l=a("828b");o["default"].__module="rdcharts";var s=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"dcd13c1a",null,!1,i["a"],o["default"]);e["default"]=s.exports},a5f1:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"qiun-error",props:{errorMessage:{type:String,default:null}},data:function(){return{}}};e.default=i},aaa0:function(t,e,a){"use strict";a.r(e);var i=a("ec57"),o=a("efe6");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);var n=a("828b"),l=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"514523bb",null,!1,i["a"],void 0);e["default"]=l.exports},abe0:function(t,e,a){"use strict";var i=a("e955"),o=a.n(i);o.a},b2d1:function(t,e,a){var i=a("d3f7");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("01e8eb67",i,!0,{sourceMap:!1,shadowMode:!1})},c13f:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"loading2",data:function(){return{}}}},d3f7:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".chartsview[data-v-dcd13c1a]{width:100%;height:100%;display:flex;flex:1;justify-content:center;align-items:center}",""]),t.exports=e},e2e8:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.chartsview[data-v-b6048934]{width:100%;height:100%;display:flex;flex-direction:column;flex:1;justify-content:center;align-items:center}.charts-font[data-v-b6048934]{font-size:14px;color:#ccc;margin-top:10px}.charts-error[data-v-b6048934]{width:128px;height:128px;background:url("data:image/png;base64,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");background-position:50%}',""]),t.exports=e},e487:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d4b5"),a("4626"),a("5ac7"),a("aa9c"),a("3efd");var o=i(a("fcf3")),r=i(a("66fa")),n=i(a("f905")),l=i(a("fe9b")),s={},h=null;function c(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length,a=new Array(e>1?e-1:0),i=1;i<e;i++)a[i-1]=arguments[i];for(var r in a)for(var n in a[r])a[r].hasOwnProperty(n)&&(t[n]=a[r][n]&&"object"===(0,o.default)(a[r][n])?c(Array.isArray(a[r][n])?[]:{},t[n],a[r][n]):a[r][n]);return t}function d(t,e){for(var a in t)t.hasOwnProperty(a)&&null!==t[a]&&"object"===(0,o.default)(t[a])?d(t[a],e):"format"===a&&"string"===typeof t[a]&&(t["formatter"]=e[t[a]]?e[t[a]]:void 0);return t}var p={data:function(){return{rid:null,startTime:0,endTime:0}},mounted:function(){var t=this;h={top:0,left:0};var e=document.querySelectorAll("uni-main")[0];void 0===e&&(e=document.querySelectorAll("uni-page-wrapper")[0]),h={top:e.offsetTop,left:e.offsetLeft},setTimeout((function(){null===t.rid&&t.$ownerInstance.callMethod("getRenderType")}),200)},destroyed:function(){delete n.default.option[this.rid],delete n.default.instance[this.rid],delete l.default.option[this.rid],delete l.default.instance[this.rid]},methods:{ecinit:function(t,e,a,i){var r=JSON.stringify(t.id);this.rid=r,s[r]=this.$ownerInstance;var n=JSON.parse(JSON.stringify(t)),h=n.type;h&&l.default.type.includes(h)?l.default.option[r]=c({},l.default[h],n):l.default.option[r]=c({},n);var d=n.chartData;if(d){l.default.option[r].xAxis&&l.default.option[r].xAxis.type&&"category"===l.default.option[r].xAxis.type&&(l.default.option[r].xAxis.data=d.categories),l.default.option[r].yAxis&&l.default.option[r].yAxis.type&&"category"===l.default.option[r].yAxis.type&&(l.default.option[r].yAxis.data=d.categories),l.default.option[r].series=[];for(var p=0;p<d.series.length;p++){l.default.option[r].seriesTemplate=l.default.option[r].seriesTemplate?l.default.option[r].seriesTemplate:{};var u=c({},l.default.option[r].seriesTemplate,d.series[p]);l.default.option[r].series.push(u)}}if("object"===(0,o.default)(window.echarts))this.newEChart();else{var x=document.createElement("script");window.location.origin;"./";x.src="./static/h5/echarts.min.js",x.onload=this.newEChart,document.head.appendChild(x)}},ecresize:function(t,e,a,i){l.default.instance[this.rid]&&l.default.instance[this.rid].resize()},newEChart:function(){var t=this.rid;void 0===l.default.instance[t]?(l.default.instance[t]=echarts.init(s[t].$el.children[0]),!0===l.default.option[t].ontap&&(l.default.instance[t].getZr().on("click",(function(e){e.target||(s[t].callMethod("emitMsg",{name:"clickEvent",params:{type:"getClickEvent"}}),console.log("点击了空白处"))})),l.default.instance[t].on("click",(function(e){var a=JSON.parse(JSON.stringify({x:e.event.offsetX,y:e.event.offsetY}));s[t].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:a,currentIndex:e.dataIndex,value:e.data,seriesName:e.seriesName,id:t}})})),l.default.instance[t].on("highlight",(function(e){s[t].callMethod("emitMsg",{name:"getHighlight",params:{type:"highlight",res:e,id:t}})}))),this.updataEChart(t,l.default.option[t])):this.updataEChart(t,l.default.option[t])},updataEChart:function(t,e){if(e=d(e,l.default.formatter),e.tooltip&&(e.tooltip.show=!!e.tooltipShow,e.tooltip.position=this.tooltipPosition(),"string"===typeof e.tooltipFormat&&l.default.formatter[e.tooltipFormat]&&(e.tooltip.formatter=e.tooltip.formatter?e.tooltip.formatter:l.default.formatter[e.tooltipFormat])),e.series)for(var a in e.series){var i=e.series[a].linearGradient;i&&(e.series[a].color=new echarts.graphic.LinearGradient(i[0],i[1],i[2],i[3],i[4]))}l.default.instance[t].setOption(e,e.notMerge),l.default.instance[t].dispatchAction({type:"showTip",seriesIndex:0,dataIndex:8}),l.default.instance[t].on("finished",(function(){s[t].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:t}}),l.default.instance[t]&&l.default.instance[t].off("finished")})),"undefined"!=typeof s[t].$el.children[0].clientWidth&&(Math.abs(s[t].$el.children[0].clientWidth-l.default.instance[t].getWidth())>3||Math.abs(s[t].$el.children[0].clientHeight-l.default.instance[t].getHeight())>3)&&this.ecresize()},tooltipPosition:function(){return function(t,e,a,i,o){var r=t[0],n=t[1],l=o.viewSize[0],s=o.viewSize[1],h=o.contentSize[0],c=o.contentSize[1],d=r+30,p=n+30;return d+h>l&&(d=r-h-30),p+c>s&&(p=n-c-30),[d,p]}},ucinit:function(t,e,a,i){var o=this;if(JSON.stringify(t)!=JSON.stringify(e)&&t.canvasId){var r=JSON.parse(JSON.stringify(t.canvasId));this.rid=r,s[r]=this.$ownerInstance,n.default.option[r]=JSON.parse(JSON.stringify(t)),n.default.option[r]=d(n.default.option[r],n.default.formatter);var l=document.getElementById(r);l&&l.children[0]&&(n.default.option[r].context=l.children[0].getContext("2d"),n.default.instance[r]&&n.default.option[r]&&!0===n.default.option[r].update?this.updataUChart():setTimeout((function(){n.default.option[r].context.restore(),n.default.option[r].context.save(),o.newUChart()}),100))}},newUChart:function(){var t=this.rid;n.default.instance[t]=new r.default(n.default.option[t]),n.default.instance[t].addEventListener("renderComplete",(function(){s[t].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:t,opts:n.default.instance[t].opts}}),n.default.instance[t].delEventListener("renderComplete")})),n.default.instance[t].addEventListener("scrollLeft",(function(){s[t].callMethod("emitMsg",{name:"scrollLeft",params:{type:"scrollLeft",scrollLeft:!0,id:t,opts:n.default.instance[t].opts}})})),n.default.instance[t].addEventListener("scrollRight",(function(){s[t].callMethod("emitMsg",{name:"scrollRight",params:{type:"scrollRight",scrollRight:!0,id:t,opts:n.default.instance[t].opts}})}))},updataUChart:function(){var t=this.rid;n.default.instance[t].updateData(n.default.option[t])},tooltipDefault:function(t,e,a,i){if(e){var r=t.data;return"object"===(0,o.default)(t.data)&&(r=t.data.value),e+" "+t.name+":"+r}return t.properties&&t.properties.name?t.properties.name:t.name+":"+t.data},showTooltip:function(t,e){var a=this,i=n.default.option[e].tooltipCustom;if(i&&void 0!==i&&null!==i){var o=void 0;i.x>=0&&i.y>=0&&(o={x:i.x,y:i.y+10}),n.default.instance[e].showToolTip(t,{index:i.index,offset:o,textList:i.textList,formatter:function(t,i,o,r){return"string"===typeof n.default.option[e].tooltipFormat&&n.default.formatter[n.default.option[e].tooltipFormat]?n.default.formatter[n.default.option[e].tooltipFormat](t,i,o,r):a.tooltipDefault(t,i,o,r)}})}else n.default.instance[e].showToolTip(t,{formatter:function(t,i,o,r){return"string"===typeof n.default.option[e].tooltipFormat&&n.default.formatter[n.default.option[e].tooltipFormat]?n.default.formatter[n.default.option[e].tooltipFormat](t,i,o,r):a.tooltipDefault(t,i,o,r)}})},tap:function(t){var e=this.rid,a=n.default.option[e].ontap,i=n.default.option[e].tooltipShow,o=n.default.option[e].tapLegend;if(0!=a){var r,l,c=document.getElementById("UC"+e).getBoundingClientRect(),d={};d=t.detail.x?{x:t.detail.x-c.left,y:t.detail.y-c.top+h.top}:{x:t.clientX-c.left,y:t.clientY-c.top+h.top},t.changedTouches.unshift(d),r=n.default.instance[e].getCurrentDataIndex(t),l=n.default.instance[e].getLegendDataIndex(t),!0===o&&n.default.instance[e].touchLegend(t),1==i&&this.showTooltip(t,e),s[e].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:d,currentIndex:r,legendIndex:l,id:e,opts:n.default.instance[e].opts}})}},touchStart:function(t){var e=this.rid,a=n.default.option[e].ontouch;0!=a&&(!0===n.default.option[e].enableScroll&&1==t.touches.length&&n.default.instance[e].scrollStart(t),s[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"touchStart",event:t.changedTouches[0],id:e,opts:n.default.instance[e].opts}}))},touchMove:function(t){var e=this.rid,a=n.default.option[e].ontouch;if(0!=a){if(!0===n.default.option[e].enableScroll&&1==t.changedTouches.length&&n.default.instance[e].scroll(t),!0===n.default.option[e].ontap&&!1===n.default.option[e].enableScroll&&!0===n.default.option[e].onmovetip){var i=document.getElementById("UC"+e).getBoundingClientRect(),o={x:t.changedTouches[0].clientX-i.left,y:t.changedTouches[0].clientY-i.top+h.top};t.changedTouches.unshift(o),!0===n.default.option[e].tooltipShow&&this.showTooltip(t,e)}!0===a&&!0===n.default.option[e].enableScroll&&!0===n.default.option[e].onzoom&&2==t.changedTouches.length&&n.default.instance[e].dobuleZoom(t),s[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"touchMove",event:t.changedTouches[0],id:e,opts:n.default.instance[e].opts}})}},touchEnd:function(t){var e=this.rid,a=n.default.option[e].ontouch;0!=a&&(!0===n.default.option[e].enableScroll&&0==t.touches.length&&n.default.instance[e].scrollEnd(t),s[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"touchEnd",event:t.changedTouches[0],id:e,opts:n.default.instance[e].opts}}))},mouseDown:function(t){var e=this.rid,a=n.default.option[e].onmouse;if(0!=a){var i,o=document.getElementById("UC"+e).getBoundingClientRect();i={x:t.clientX-o.left,y:t.clientY-o.top+h.top},t.changedTouches.unshift(i),n.default.instance[e].scrollStart(t),n.default.option[e].mousedown=!0,s[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"mouseDown",event:i,id:e,opts:n.default.instance[e].opts}})}},mouseMove:function(t){var e=this.rid,a=n.default.option[e].onmouse,i=n.default.option[e].tooltipShow;if(0!=a){var o,r=document.getElementById("UC"+e).getBoundingClientRect();o={x:t.clientX-r.left,y:t.clientY-r.top+h.top},t.changedTouches.unshift(o),n.default.option[e].mousedown?(n.default.instance[e].scroll(t),s[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"mouseMove",event:o,id:e,opts:n.default.instance[e].opts}})):n.default.instance[e]&&1==i&&this.showTooltip(t,e)}},mouseUp:function(t){var e=this.rid,a=n.default.option[e].onmouse;if(0!=a){var i,o=document.getElementById("UC"+e).getBoundingClientRect();i={x:t.clientX-o.left,y:t.clientY-o.top+h.top},t.changedTouches.unshift(i),n.default.instance[e].scrollEnd(t),n.default.option[e].mousedown=!1,s[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"mouseUp",event:i,id:e,opts:n.default.instance[e].opts}})}}}};e.default=p},e955:function(t,e,a){var i=a("e2e8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("0c3d79ee",i,!0,{sourceMap:!1,shadowMode:!1})},ec57:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[2==this.loadingType?e("Loading2"):this._e()],1)},o=[]},efe6:function(t,e,a){"use strict";a.r(e);var i=a("68b1a"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},f70c:function(t,e,a){"use strict";a.r(e);var i=a("0806"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},f905:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],o={type:["pie","ring","rose","word","funnel","map","arcbar","line","column","mount","bar","area","radar","gauge","candle","mix","tline","tarea","scatter","bubble","demotype"],range:["饼状图","圆环图","玫瑰图","词云图","漏斗图","地图","圆弧进度条","折线图","柱状图","山峰图","条状图","区域图","雷达图","仪表盘","K线图","混合图","时间轴折线","时间轴区域","散点图","气泡图","自定义类型"],categories:["line","column","mount","bar","area","radar","gauge","candle","mix","demotype"],instance:{},option:{},formatter:{yAxisDemo1:function(t,e,a){return t+"元"},yAxisDemo2:function(t,e,a){return t.toFixed(2)},xAxisDemo1:function(t,e,a){return t+"年"},xAxisDemo2:function(t,e,a){return function(t,e){var a=new Date;a.setTime(1e3*t);var i=a.getFullYear(),o=a.getMonth()+1;o=o<10?"0"+o:o;var r=a.getDate();r=r<10?"0"+r:r;var n=a.getHours();n=n<10?"0"+n:n;var l=a.getMinutes(),s=a.getSeconds();return l=l<10?"0"+l:l,s=s<10?"0"+s:s,"full"==e?i+"-"+o+"-"+r+" "+n+":"+l+":"+s:"y-m-d"==e?i+"-"+o+"-"+r:"h:m"==e?n+":"+l:"h:m:s"==e?n+":"+l+":"+s:[i,o,r,n,l,s]}(t,"h:m")},seriesDemo1:function(t,e,a,i){return t+"元"},tooltipDemo1:function(t,e,a,i){return 0==a?"随便用"+t.data+"年":"其他我没改"+t.data+"天"},pieDemo:function(t,e,a,i){if(void 0!==e)return a[e].name+"："+a[e].data+"元"}},demotype:{type:"line",color:i,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"curve",width:2}}},pie:{type:"pie",color:i,padding:[5,5,5,5],extra:{pie:{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},ring:{type:"ring",color:i,padding:[5,5,5,5],rotate:!1,dataLabel:!0,legend:{show:!0,position:"right",lineHeight:25},title:{name:"收益率",fontSize:15,color:"#666666"},subtitle:{name:"70%",fontSize:25,color:"#7cb5ec"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},rose:{type:"rose",color:i,padding:[5,5,5,5],legend:{show:!0,position:"left",lineHeight:25},extra:{rose:{type:"area",minRadius:50,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF"}}},word:{type:"word",color:i,extra:{word:{type:"normal",autoColors:!1}}},funnel:{type:"funnel",color:i,padding:[15,15,0,15],extra:{funnel:{activeOpacity:.3,activeWidth:10,border:!0,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"}}},map:{type:"map",color:i,padding:[0,0,0,0],dataLabel:!0,extra:{map:{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#F04864",activeFillColor:"#FACC14",activeFillOpacity:1}}},arcbar:{type:"arcbar",color:i,title:{name:"百分比",fontSize:25,color:"#00FF00"},subtitle:{name:"默认标题",fontSize:15,color:"#666666"},extra:{arcbar:{type:"default",width:12,backgroundColor:"#E9E9E9",startAngle:.75,endAngle:.25,gap:2}}},line:{type:"line",color:i,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"straight",width:2,activeType:"hollow"}}},tline:{type:"line",color:i,padding:[15,10,0,15],xAxis:{disableGrid:!1,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{line:{type:"curve",width:2,activeType:"hollow"}}},tarea:{type:"area",color:i,padding:[15,10,0,15],xAxis:{disableGrid:!0,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{area:{type:"curve",opacity:.2,addLine:!0,width:2,gradient:!0,activeType:"hollow"}}},column:{type:"column",color:i,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}},mount:{type:"mount",color:i,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{mount:{type:"mount",widthRatio:1.5}}},bar:{type:"bar",color:i,padding:[15,30,0,5],xAxis:{boundaryGap:"justify",disableGrid:!1,min:0,axisLine:!1},yAxis:{},legend:{},extra:{bar:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08}}},area:{type:"area",color:i,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{area:{type:"straight",opacity:.2,addLine:!0,width:2,gradient:!1,activeType:"hollow"}}},radar:{type:"radar",color:i,padding:[5,5,5,5],dataLabel:!1,legend:{show:!0,position:"right",lineHeight:25},extra:{radar:{gridType:"radar",gridColor:"#CCCCCC",gridCount:3,opacity:.2,max:200,labelShow:!0}}},gauge:{type:"gauge",color:i,title:{name:"66Km/H",fontSize:25,color:"#2fc25b",offsetY:50},subtitle:{name:"实时速度",fontSize:15,color:"#1890ff",offsetY:-50},extra:{gauge:{type:"default",width:30,labelColor:"#666666",startAngle:.75,endAngle:.25,startNumber:0,endNumber:100,labelFormat:"",splitLine:{fixRadius:0,splitNumber:10,width:30,color:"#FFFFFF",childNumber:5,childWidth:12},pointer:{width:24,color:"auto"}}}},candle:{type:"candle",color:i,padding:[15,15,0,15],enableScroll:!0,enableMarkLine:!0,dataLabel:!1,xAxis:{labelCount:4,itemCount:40,disableGrid:!0,gridColor:"#CCCCCC",gridType:"solid",dashLength:4,scrollShow:!0,scrollAlign:"left",scrollColor:"#A6A6A6",scrollBackgroundColor:"#EFEBEF"},yAxis:{},legend:{},extra:{candle:{color:{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},average:{show:!0,name:["MA5","MA10","MA30"],day:[5,10,20],color:["#1890ff","#2fc25b","#facc14"]}},markLine:{type:"dash",dashLength:5,data:[{value:2150,lineColor:"#f04864",showLabel:!0},{value:2350,lineColor:"#f04864",showLabel:!0}]}}},mix:{type:"mix",color:i,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{disabled:!1,disableGrid:!1,splitNumber:5,gridType:"dash",dashLength:4,gridColor:"#CCCCCC",padding:10,showTitle:!0,data:[]},legend:{},extra:{mix:{column:{width:20}}}},scatter:{type:"scatter",color:i,padding:[15,15,0,15],dataLabel:!1,xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0},yAxis:{disableGrid:!1,gridType:"dash"},legend:{},extra:{scatter:{}}},bubble:{type:"bubble",color:i,padding:[15,15,0,15],xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0,max:250},yAxis:{disableGrid:!1,gridType:"dash",data:[{min:0,max:150}]},legend:{},extra:{bubble:{border:2,opacity:.5}}}},r=o;e.default=r},fe9b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],o={type:["pie","ring","rose","funnel","line","column","area","radar","gauge","candle","demotype"],categories:["line","column","area","radar","gauge","candle","demotype"],instance:{},option:{},formatter:{tooltipDemo1:function(t){var e="";for(var a in t){0==a&&(e+=t[a].axisValueLabel+"年销售额");var i="--";null!==t[a].data&&(i=t[a].data),e+="\n"+t[a].seriesName+"："+i+" 万元"}return e},legendFormat:function(t){return"自定义图例+"+t},yAxisFormatDemo:function(t,e){return t+"元"},seriesFormatDemo:function(t){return t.name+"年"+t.value+"元"}},demotype:{color:i},column:{color:i,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"bar",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},line:{color:i,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},area:{color:i,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],areaStyle:{},label:{show:!0,color:"#666666",position:"top"}}},pie:{color:i,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:"50%",label:{show:!0,color:"#666666",position:"top"}}},ring:{color:i,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#666666",position:"top"},labelLine:{show:!0}}},rose:{color:i,title:{text:""},tooltip:{trigger:"item"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"pie",data:[],radius:"55%",center:["50%","50%"],roseType:"area"}},funnel:{color:i,title:{text:""},tooltip:{trigger:"item",formatter:"{b} : {c}%"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{bordercolor:"#fff",borderwidth:1},emphasis:{label:{fontSize:20}},data:[]}},gauge:{color:i,tooltip:{formatter:"{a} <br/>{b} : {c}%"},seriesTemplate:{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}]}},candle:{xAxis:{data:[]},yAxis:{},color:i,title:{text:""},dataZoom:[{type:"inside",xAxisIndex:[0,1],start:10,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:10,start:10,end:100}],seriesTemplate:{name:"",type:"k",data:[]}}},r=o;e.default=r}}]);