<template>
	<!-- 列表 -->
	<view class="mainContainer">
		<custom-navbar title="台区违窃工作详情"></custom-navbar>
		<!-- 查询 -->
		<view class="searchContainer">
			<u--input class="calInput" placeholder="请输入台区编号" border="surround" v-model="taiqunum"></u--input>
			<u-button type="primary" style="color: #fcfefd;width: 160rpx;" color="#07ac7c" text="查询"
				@click="search"></u-button>
		</view>
		<!-- 小表格 -->
		<view class="tablecontainer">
			<view class="" style="padding: 10rpx;">
				<text style="color: darkgray;font-weight: bold;font-size: 30rpx;">供电公司：</text>
				<text style="color: black;font-weight: bold;font-size: 30rpx;">{{mgtOrgName}}</text>
			</view>
			<view class="" style="padding: 10rpx;">
				<text style="color: darkgray;font-weight: bold;font-size: 30rpx;">台区名称：</text>
				<text style="color: black;font-weight: bold;font-size: 30rpx;">{{resrcSuplName}}</text>
			</view>
			<view class="rowcontainer" style="padding: 10rpx;">
				<view class="">
					<text style="color: darkgray;font-weight: bold;font-size: 30rpx;">用户数：</text>
					<text style="color: black;font-weight: bold;font-size: 30rpx;">{{custCnt}}</text>
				</view>
				<view class="" style="width: 300rpx;">
					<text style="color: darkgray;font-weight: bold;font-size: 30rpx;">线索数：</text>
					<text style="color: black;font-weight: bold;font-size: 30rpx;">{{totalNum}}</text>
				</view>
			</view>
			<view class="rowcontainer" style="padding: 10rpx;">
				<view class="">
					<text style="color: darkgray;font-weight: bold;font-size: 30rpx;">历史违窃数：</text>
					<text style="color: black;font-weight: bold;font-size: 30rpx;">{{hisNum}}</text>
				</view>
				<view class="" style="width: 300rpx;">
					<text style="color: darkgray;font-weight: bold;font-size: 30rpx;">未查线索数：</text>
					<text style="color: black;font-weight: bold;font-size: 30rpx;">{{uncheckNum}}</text>
				</view>
			</view>
		</view>
		<!-- 柱状图 -->
		<view class="charts-box">
			<text class="chartstitle">台区线损曲线</text>
			<view v-if="dataShow"
				style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
				<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
				<view style="color: gainsboro;">
					数据为空
				</view>
			</view>
			<qiun-data-charts v-if="chartShow" type="line" :opts="opts" :chartData="chartData" :ontouch="true" />
		</view>
		<!-- 列表 -->
		<view class="listcontainer" v-if="showlist">
			<view class="listtitle">
				<view class="huhao">户号</view>
				<view style="width: 200rpx;text-align: center;">用户名</view>
				<view style="width: 200rpx;text-align: center;">线索类型</view>
			</view>
			<view v-if="dataShow"
				style="padding: 20rpx 40rpx 20rpx 40rpx;margin: 20rpx;height: 500rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;">
				<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
				<view style="color: gainsboro;">
					数据为空
				</view>
			</view>
			<view class="listdata" v-for="(item,index) in custList" :key="index" @click="selectedIndex = index"
				:style="{backgroundColor: selectedIndex === index ? 'aliceblue' : ''}">
				<view class="huhao" @click="showdetailfun(item)">{{item.custNo}}</view>
				<view style="width: 200rpx;text-align: center;color: gray;">{{item.custName}}</view>
				<view style="width: 200rpx;text-align: center;color: gray;">{{item.type}}</view>
			</view>
		</view>
		<view class="detailcontainer" v-if="showdetail">
			<view class="datatext">
				<view class="datacontainer">
					<view class="datatitle">户名：</view><text>{{custNamed}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">户号：</view><text>{{custNod}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">线索类型：</view><text>{{typed}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">线索描述：</view><text>{{abnorDescd}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">是否生成工单：</view><text>{{isOrderd}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">工单环节：</view><text>{{orderStatusd}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">是否查实：</view><text>{{isReald}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">追补电量(kWh)：</view><text style="color: red;">{{pqd}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">追补电费及违约金(元)：</view><text style="color: red;">{{retrTotald}}</text>
				</view>
			</view>
			<view class="" style="padding: 10rpx;">
				<u-button type="primary" style="color: #fcfefd;" color="#07ac7c" text="返回列表"
					@click="showlistfun"></u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import qiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
	import utils from '@/static/commonJs/util.js'
	export default {
		components: {
			qiunDataCharts
		},
		data() {
			return {
				custNo: '',
				selectedIndex: 0,
				todayDate: '',
				agoDate: '',
				testdev: true,
				chartShow: false,
				dataShow: true,
				retrTotald: "",
				pqd: "",
				isReald: "",
				orderStatusd: "",
				isOrderd: "",
				abnorDescd: "",
				typed: "",
				custNod: "",
				custNamed: "",
				custList: [],
				uncheckNum: '0',
				hisNum: '0',
				totalNum: '0',
				custCnt: '0',
				resrcSuplName: '',
				mgtOrgName: '',
				showlist: true,
				showdetail: false,
				taiqunum: '32477',
				chartData: {},
				series: [],
				opts: {
					color: ["#1890FF", "#91CB74", 'orange'],
					padding: [15, 15, 15, 15],
					enableScroll: true,
					legend: {},
					xAxis: {
						disableGrid: true,
						scrollShow: true,
						itemCount: 4
					},
					yAxis: {
						showTitle: true,
						data: [{
								position: "left",
								title: '线损率(%)    ',
								format: val => val + '%',
								textAlign: "center"
							},
							{
								position: "right",
								title: "(kWh)    ",
								textAlign: "center",
								format: val => val + 'kWh'
							},
						]
					},
					extra: {
						yAxis: {
							startFromZero: true
						},
						line: {
							type: "curve",
							width: 2,
							activeType: "hollow"
						}
					}
				}
			}
		},
		onLoad(options) {
			if (options.data) {
				this.taiqunum = options.data;
			}
			this.getDates();
			this.search();
			this.custSearch();
		},
		onReady() {

		},
		methods: {
			getDates() {
				const today = new Date();

				const threeMonthsAgo = new Date();
				threeMonthsAgo.setMonth(today.getMonth() - 3);

				function formatDate(date) {
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					return `${year}-${month}-${day}`;
				}

				this.todayDate = formatDate(today);
				this.agoDate = formatDate(threeMonthsAgo);
			},
			async custSearch() {
				if (this.testdev) {
					let dataDateArray = ['7月9日', '7月10日', '7月11日', '7月12日'];
					let llRateArray = [0.9, 0.84, 0.96, 0.82];
					let upqArray = [8.5, 6.578, 11, 9.1];
					let shiArray = [11.29, 10.66, 12.14, 10.31];
					let res = {
						categories: dataDateArray,
						series: [{
								name: "线损率",
								data: llRateArray,
								index: 0,
								type: "line",
							},
							{
								name: "用户电量",
								data: upqArray,
								index: 1,
								type: "line",
							},
							{
								name: "实际线损量",
								data: shiArray,
								index: 1,
								type: "line",
							},
						]
					};
					this.chartData = JSON.parse(JSON.stringify(res));
				} else {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "ExceptionPointAnalyzeController",
									"method": "custElectric",
									"data": {
										"custNo": this.custNo,
										"startTime": this.todayDate,
										"endTime": this.agoDate,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					let dataDateArray = [];
					let shiArray = [];
					data.forEach(item => {
						dataDateArray.push(item.time)
						shiArray.push(item.custElectric)
					})
					this.series[2].data = shiArray;
					let resshi = {
						categories: dataDateArray,
						series: this.series
					};
					this.chartData = JSON.parse(JSON.stringify(resshi));
				}
			},
			async search() {
				if (this.testdev) {
					this.chartShow = true;
					this.dataShow = false;
					this.custList = [{
							custNo: '31231313445214',
							custName: '北宅北机口',
							type: '模型预警',
							isReal: '是',
							pq: '156789',
							retrTotal: '100515'
						}, {
							custNo: '31231313445215',
							custName: '英雄14组西',
							type: '95598举报',
							isReal: '是',
							pq: '1565259',
							retrTotal: '41546'
						},
						{
							custNo: '31231313445216',
							custName: '上海奥普化工',
							type: '外部转派',
							isReal: '是',
							pq: '1574659',
							retrTotal: '145455'
						},
						{
							custNo: '31231313445217',
							custName: '连波都市_1号配变',
							type: '其他专业移交',
							isReal: '是',
							pq: '154528',
							retrTotal: '15464'
						},
						{
							custNo: '31231313445218',
							custName: '新一东',
							type: '专项任务',
							isReal: '是',
							pq: '1547686',
							retrTotal: '143564'
						},
					];
					this.mgtOrgName = '市区供电公司';
					this.resrcSuplName = '新村1388弄3号甲_站变';
					this.custCnt = 84;
					this.totalNum = 1;
					this.hisNum = 1;
					this.uncheckNum = 0;
					let dataDateArray = ['7月9日', '7月10日', '7月11日', '7月12日'];
					let llRateArray = [0.9, 0.84, 0.96, 0.82];
					let upqArray = [10.5, 9.578, 12.11, 10.1];
					let shiArray = [11.29, 10.66, 12.14, 10.31];
					let res = {
						categories: dataDateArray,
						series: [{
								name: "线损率",
								data: llRateArray,
								index: 0,
								type: "line",
							},
							{
								name: "用户电量",
								data: upqArray,
								index: 1,
								type: "line",
							},
							{
								name: "实际线损量",
								data: shiArray,
								index: 1,
								type: "line",
							},
						]
					};
					this.chartData = JSON.parse(JSON.stringify(res));
				} else {
					// 调用后端登录接口
					try {
						const res1 = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "AppOrderController",
										"method": "queryDistDetail",
										"data": {
											"resrcSuplCode": this.taiqunum,
										}
									}
								})
							}
						});
						const res2 = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "ExceptionPointAnalyzeController",
										"method": "lineLossElectric",
										"data": {
											"resrcSuplCode": this.taiqunum,
											"endTime": this.todayDate,
											"startTime": this.agoDate
										}
									}
								})
							}
						});
						const res3 = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "ExceptionPointAnalyzeController",
										"method": "lineLossRate",
										"data": {
											"resrcSuplCode": this.taiqunum,
											"endTime": this.todayDate,
											"startTime": this.agoDate
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res1.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.chartShow = true;
							this.dataShow = false;
							this.custList = data.custList;
							this.mgtOrgName = data.mgtOrgName;
							this.resrcSuplName = data.resrcSuplName;
							this.custCnt = data.custCnt ? data.custCnt : 0;
							this.totalNum = data.totalNum ? data.totalNum : 0;
							this.hisNum = data.hisNum ? data.hisNum : 0;
							this.uncheckNum = data.uncheckNum ? data.uncheckNum : 0;
						} else {
							this.chartShow = false;
							this.dataShow = true;
						}


						const {
							code2,
							message2,
							data2
						} = res2.data;
						const {
							code3,
							message3,
							data3
						} = res3.data;
						let dataDateArray = [];
						let llRateArray = [];
						let upqArray = [];
						let shiArray = [];
						data2.forEach(item => {
							dataDateArray.push(item.dataDate)
							upqArray.push(item.lineLossElectric)
						})
						data3.forEach(item => {
							llRateArray.push(item.lineLossRate)
						})
						this.series = [{
								name: "线损率",
								data: llRateArray,
								index: 0,
								type: "line",
							},
							{
								name: "用户电量",
								data: upqArray,
								index: 1,
								type: "line",
							},
							{
								name: "实际线损量",
								data: shiArray,
								index: 1,
								type: "line",
							},
						]
						let res = {
							categories: dataDateArray,
							series: this.series
						};
						this.chartData = JSON.parse(JSON.stringify(res));
					} catch (error) {
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
						this.chartShow = false;
						this.dataShow = true;
					}
				}
			},
			showdetailfun(item) {
				this.showdetail = true;
				this.showlist = false;
				this.custNamed = item.custName;
				this.custNod = item.custNo;
				this.typed = item.type;
				this.abnorDescd = item.abnorDesc;
				this.isOrderd = item.isOrder;
				this.orderStatusd = item.orderStatus;
				this.isReald = item.isReal;
				this.pqd = item.pq;
				this.retrTotald = item.retrTotal;
				this.custNo = item.custNo;
				// this.custSearch();
				let dataDateArray = ['7月9日', '7月10日', '7月11日', '7月12日'];
				let llRateArray = [0.9, 0.84, 0.96, 0.82];
				let upqArray = [8.5, 6.578, 11, 9.1];
				let shiArray = [14.29, 11.66, 15.14, 12.31];
				let res = {
					categories: dataDateArray,
					series: [{
							name: "线损率",
							data: llRateArray,
							index: 0,
							type: "line",
						},
						{
							name: "用户电量",
							data: upqArray,
							index: 1,
							type: "line",
						},
						{
							name: "实际线损量",
							data: shiArray,
							index: 1,
							type: "line",
						},
					]
				};
				this.chartData = JSON.parse(JSON.stringify(res));
			},
			showlistfun() {
				this.showdetail = false;
				this.showlist = true;
			}
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	page {
		background-color: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	.mainContainer {
		.searchContainer {
			background-color: white;
			padding: 30rpx 20rpx 30rpx;
			border-radius: 20rpx;
			margin: 20rpx 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.calInput {
				// width: 300rpx;
				margin-right: 30rpx;
			}

		}

		.charts-box {
			height: 600rpx;
			background-color: white;
			border-radius: 20rpx;
			padding: 20rpx 40rpx 20rpx 40rpx;
			margin: 20rpx 20rpx;

			.chartstitle {
				font-weight: bold;
			}
		}

		.tablecontainer {
			background-color: white;
			border-radius: 20rpx;
			padding: 20rpx;
			margin: 0 20rpx;

			.rowcontainer {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			padding: 20rpx;
			margin: 0 20rpx;

			.listtitle {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;

				.huhao {
					width: 280rpx;
					text-align: center;
				}
			}

			.listdata {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 10rpx 0;
				font-size: 30rpx;

				.huhao {
					width: 280rpx;
					color: lightseagreen;
					text-align: center;
				}
			}
		}

		.detailcontainer {
			background-color: white;
			border-radius: 20rpx;
			margin: 20rpx 20rpx;

			.datatext {
				padding: 10rpx 0 10rpx 0rpx;

				.datacontainer {
					display: flex;
					align-items: center;
					padding: 30rpx 0 40rpx 20rpx;

					.datatitle {
						width: 400rpx;
						color: gray;
					}
				}
			}

		}
	}
</style>