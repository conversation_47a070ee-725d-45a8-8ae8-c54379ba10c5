import http from './http-util'
import * as igwConfig from './igw-config'
/**
 * 获取用户信息
 */
const getISCUserInfo = {
    code: 'user',
    type: 'webservice'
}

const getWxCode = () => {
	 console.log(`--to get WxCode-----`)
    let sURL = igwConfig.VUE_APP_OAUTH_URL
    sURL += `?appid=${igwConfig.APP_INFO.corpid}`
    sURL += `&redirect_uri=${encodeURIComponent(`${igwConfig.VUE_APP_REDIRECT_URI}`)}`
    sURL += '&response_type=code'
    sURL += '&scope=SCOPE'
    sURL += '&state=STATE'
    sURL += `&agentid=${igwConfig.APP_INFO.agentid}`
    sURL += '#wechat_redirect'
	 console.log(`--getWxCodeURL-----`, sURL);
    location.href= sURL;
}

const getParam = (variable) => {
    const query = window.location.search.substring(1)
    const vars = query.split('&')
    for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        if (pair[0] === variable) {
			console.log(pair[1])
            return pair[1]
        }
    }
    return ''
}

/** 获取安全交互平台返回信息 */
export const iscpConnet = (retryCount = igwConfig.VUE_APP_RETRY_COUNT) => {
    console.log(`-----进入get_ext_ISCP 11111`, igwConfig.VUE_APP_RETRY_COUNT)
	console.log(Vegeta)
	console.log(igwConfig.APP_INFO.agentid)
    return Vegeta.iscpInit({
            iscpIP: igwConfig.APP_INFO.agentid
        })
        .then((res) => {
            console.log('ext_ISCP_Init 初始化', res)
            return Vegeta.iscpStatus({
                    iscpIP: igwConfig.APP_INFO.agentid
                }).then((res3) => {
                    console.log('连接状态：', res3)
                    if (res3.result === '2' && window.HBStorage.getData('dynamicPort').data) {
                        console.log(`------已连接的端口：`,window.HBStorage.getData('dynamicPort').data);
                        return window.HBStorage.getData('dynamicPort').data;
                    }
                    return Vegeta
                        .iscpConnectService({
                            ip: igwConfig.APP_INFO.iscpIP,
                            port: igwConfig.APP_INFO.iscpPort,
                            user: igwConfig.APP_INFO.user,
                            appid: igwConfig.APP_INFO.iscAppid,
                            iscpIP: igwConfig.APP_INFO.agentid
                        })
                        .then((res1) => {
                            console.log('ext_ISCP_ConnectService 建立安全交互平台链接', res1)
                            return Vegeta
                                .iscpGetLocalPort({
                                    ip: igwConfig.APP_INFO.baseUrlIp,
                                    port: igwConfig.APP_INFO.baseUrlPort,
                                    iscpIP: igwConfig.APP_INFO.agentid
                                })
                                .then((res2) => {
                                    console.log('ext_ISCP_GetLocalPort 转换端口', res2)
                                    console.log(`------端口：${res2.result}`)
                                    if (res2.result == '-1') {
                                        throw '获取动态端口失败，请联系管理员配置安全策略'
                                    }
                                    window.HBStorage.setData('dynamicPort', res2.result)
                                    return res2.result
                                })
                                .catch((err) => {
								    console.log(`------errorerrorerror`,err);
                                    console.error(err)
                                    if (retryCount == 0) return err
                                    iscpConnet(retryCount - 1)
                                })
                        })
                        .catch((err) => {
                            console.error(err)
                            if (retryCount == 0) return err
                            iscpConnet(retryCount - 1)
                        })
                })
                .catch((err) => {
                    console.error(err)
                    if (retryCount == 0) return err
                    iscpConnet(retryCount - 1)
                })
        })
        .catch((err) => {
            console.error(err)
            if (retryCount == 0) return err
            iscpConnet(retryCount - 1)
        })
}

export const getUserInfo = () => {
    if (!igwConfig.VUE_APP_IS_LOCALHOST) {
		console.log("是否是本地调试1",igwConfig.VUE_APP_IS_LOCALHOST);
		console.log("是否是本地调试11",sessionStorage.getItem('wx_code'));
		console.log("是否是本地调试12",sessionStorage.getItem('getUserInfo'));
        if (sessionStorage.getItem('wx_code') && sessionStorage.getItem('getUserInfo')) {
            const userInfo =
                (sessionStorage.getItem('getUserInfo') &&
                    JSON.parse(sessionStorage.getItem('getUserInfo'))) || {}
            return Promise.resolve({
                ...userInfo,
                userNo: userInfo.nameCode
            })
        }
		console.log("是否是本地调试13",wx_code);
        return http.requestYh(igwConfig.VUE_APP_TICKET_URL, {
                appId: igwConfig.APP_INFO.agentid,
                CODE: wx_code
            },'post',"")
            .then((res) => {
				console.log("获取ticket返回:",res);
				const recticket = res.data.ticket;
				console.log('获取ticket成功:',recticket);
                return iscpConnet().then(async (res1) => {
                    console.log('获取动态端口成功：', res1)
                    const url = `http://127.0.0.1:${res1}${igwConfig.VUE_APP_URL}`;
					const hyheaders = {
						headers:{
							'Content-Type': 'application/json; charset=utf-8',
							token: recticket,
							getUserInfoToISC: '01',
							}
					}		
                    let resultinfo = await http.requestYh(url, {}, 'post',hyheaders); //到这er
                    if (resultinfo.data.code=='100001') {
						const userInfo = resultinfo.data.data;
						let orgno = resultinfo.data.orgNO;
						if (orgno){
						    console.log('获取orgno：', orgno)
							userInfo.orgNo= orgno;
						}
                        window.HBStorage.setData('curUserInfo_meas', userInfo);
                        //window.HBStorage.setData('accessToken', userInfo.accessToken);
                    } 
                    return userInfo;
                })
            })
    }else{
		console.log("是否是本地调试2",igwConfig.VUE_APP_IS_LOCALHOST);
		const url = igwConfig.VUE_APP_URL;
		console.log(url);
		const hyheaders = {
			headers:{
				'Content-Type': 'application/json; charset=utf-8',
				token: '112233',
				getUserInfoToISC: '01'
				}
		}
		const userInfo = http.requestYh(url, {}, 'post',hyheaders);
		return userInfo;
	}
}

const wx_code = getParam('code')

window.onload = function () {
    console.log(`-------wx_code = ${wx_code}`)
    if (!wx_code) {
        if (!igwConfig.VUE_APP_IS_LOCALHOST) return getWxCode()
    } 
    sessionStorage.setItem('wx_code', wx_code)
};
