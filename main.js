import App from './App'
import axios from 'axios'
import uView from '@/uni_modules/uview-ui'
import uniIcons from '@/node_modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue'
import uniDatetimePicker from '@/node_modules/@dcloudio/uni-ui/lib/uni-datetime-picker/uni-datetime-picker.vue'
import uniFilePicker from '@/node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue'
import uniCalendar from '@/node_modules/@dcloudio/uni-ui/lib/uni-calendar/uni-calendar.vue'
// #ifndef VUE3
import Vue from 'vue'
import * as config from './static/api/igw-config.js';
import './uni.promisify.adaptor'
import './static/iconfont/iconfont.css';

// 导入自定义导航栏组件
import customNavbar from '@/components/custom-navbar.vue'
import secondNavbar from '@/components/second-navbar.vue'
import indicator from '@/components/indicator-nav.vue'
import threenav from '@/components/threenav.vue'
import svgIcon from '@/components/svg-icon.vue'
import uvsubsection from '@/components/uv-ui/components/uv-subsection/uv-subsection.vue'
Vue.config.productionTip = false
Vue.prototype.$axios = axios;
Vue.prototype.$eventBus = new Vue()
Vue.use(uView)
// 注册全局组件
Vue.component('custom-navbar', customNavbar)
Vue.component('svgIcon', svgIcon)
Vue.component('second-navbar', secondNavbar)
Vue.component('indicator-nav', indicator)
Vue.component('threenav', threenav)
Vue.component('uni-icons', uniIcons)
Vue.component('uni-datetime-picker', uniDatetimePicker)
Vue.component('uni-file-picker', uniFilePicker)
Vue.component('uv-subsection', uvsubsection)
Vue.component('uni-calendar', uniCalendar)
App.mpType = 'app'
const app = new Vue({
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif