(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-fileQuery-jobMonitor-jobMonitor"],{"0abd":function(a,t,d){var r=d("550d");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[a.i,r,""]]),r.locals&&(a.exports=r.locals);var o=d("967d").default;o("57cd89a6",r,!0,{sourceMap:!1,shadowMode:!1})},"1b62":function(a,t,d){"use strict";d.d(t,"b",(function(){return r})),d.d(t,"c",(function(){return o})),d.d(t,"a",(function(){}));var r=function(){var a=this,t=a.$createElement,d=a._self._c||t;return d("v-uni-view",{staticClass:"file-query-container"},[d("custom-navbar",{attrs:{title:"工单监控"}}),d("v-uni-view",{staticClass:"content-area"},[d("v-uni-view",{staticClass:"card-grid"},[d("v-uni-view",{staticClass:"card-row"},[d("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.navigateTo("mainMonitor")}}},[d("v-uni-view",{staticClass:"card-inner"},[d("v-uni-view",{staticClass:"icon-box icon-green"},[d("svg-icon",{attrs:{name:"jiankong",color:"#3CC792",size:a.iconSize}})],1),d("v-uni-text",{staticClass:"card-text"},[a._v("工单整体监控")])],1)],1),d("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.navigateTo("timeMonitor")}}},[d("v-uni-view",{staticClass:"card-inner"},[d("v-uni-view",{staticClass:"icon-box icon-orange"},[d("svg-icon",{attrs:{name:"jiankong",color:"#F49B2C",size:a.iconSize}})],1),d("v-uni-text",{staticClass:"card-text"},[a._v("工单时效监控")])],1)],1)],1)],1)],1)],1)},o=[]},"275a":function(a,t,d){"use strict";d.r(t);var r=d("3adc"),o=d.n(r);for(var e in r)["default"].indexOf(e)<0&&function(a){d.d(t,a,(function(){return r[a]}))}(e);t["default"]=o.a},"3adc":function(a,t,d){"use strict";d("6a54");var r=d("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(d("568d")),e={components:{svgIcon:o.default},data:function(){return{iconSize:40}},methods:{navigateTo:function(a){uni.navigateTo({url:"/pages/fileQuery/jobMonitor/".concat(a)})}}};t.default=e},"550d":function(a,t,d){var r=d("c86c");t=r(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-9a17dba8]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-9a17dba8]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-9a17dba8]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-9a17dba8]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-9a17dba8]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-9a17dba8]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-9a17dba8]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-9a17dba8]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-9a17dba8]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-9a17dba8]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-9a17dba8]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-9a17dba8]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-9a17dba8]::after{border:none}.u-hover-class[data-v-9a17dba8]{opacity:.7}.u-primary-light[data-v-9a17dba8]{color:#ecf5ff}.u-warning-light[data-v-9a17dba8]{color:#fdf6ec}.u-success-light[data-v-9a17dba8]{color:#f5fff0}.u-error-light[data-v-9a17dba8]{color:#fef0f0}.u-info-light[data-v-9a17dba8]{color:#f4f4f5}.u-primary-light-bg[data-v-9a17dba8]{background-color:#ecf5ff}.u-warning-light-bg[data-v-9a17dba8]{background-color:#fdf6ec}.u-success-light-bg[data-v-9a17dba8]{background-color:#f5fff0}.u-error-light-bg[data-v-9a17dba8]{background-color:#fef0f0}.u-info-light-bg[data-v-9a17dba8]{background-color:#f4f4f5}.u-primary-dark[data-v-9a17dba8]{color:#398ade}.u-warning-dark[data-v-9a17dba8]{color:#f1a532}.u-success-dark[data-v-9a17dba8]{color:#53c21d}.u-error-dark[data-v-9a17dba8]{color:#e45656}.u-info-dark[data-v-9a17dba8]{color:#767a82}.u-primary-dark-bg[data-v-9a17dba8]{background-color:#398ade}.u-warning-dark-bg[data-v-9a17dba8]{background-color:#f1a532}.u-success-dark-bg[data-v-9a17dba8]{background-color:#53c21d}.u-error-dark-bg[data-v-9a17dba8]{background-color:#e45656}.u-info-dark-bg[data-v-9a17dba8]{background-color:#767a82}.u-primary-disabled[data-v-9a17dba8]{color:#9acafc}.u-warning-disabled[data-v-9a17dba8]{color:#f9d39b}.u-success-disabled[data-v-9a17dba8]{color:#a9e08f}.u-error-disabled[data-v-9a17dba8]{color:#f7b2b2}.u-info-disabled[data-v-9a17dba8]{color:#c4c6c9}.u-primary[data-v-9a17dba8]{color:#3c9cff}.u-warning[data-v-9a17dba8]{color:#f9ae3d}.u-success[data-v-9a17dba8]{color:#5ac725}.u-error[data-v-9a17dba8]{color:#f56c6c}.u-info[data-v-9a17dba8]{color:#909399}.u-primary-bg[data-v-9a17dba8]{background-color:#3c9cff}.u-warning-bg[data-v-9a17dba8]{background-color:#f9ae3d}.u-success-bg[data-v-9a17dba8]{background-color:#5ac725}.u-error-bg[data-v-9a17dba8]{background-color:#f56c6c}.u-info-bg[data-v-9a17dba8]{background-color:#909399}.u-main-color[data-v-9a17dba8]{color:#303133}.u-content-color[data-v-9a17dba8]{color:#606266}.u-tips-color[data-v-9a17dba8]{color:#909193}.u-light-color[data-v-9a17dba8]{color:#c0c4cc}.u-safe-area-inset-top[data-v-9a17dba8]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-9a17dba8]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-9a17dba8]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-9a17dba8]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-9a17dba8]{z-index:10090}uni-toast .uni-toast[data-v-9a17dba8]{z-index:10090}[data-v-9a17dba8]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-page-body[data-v-9a17dba8]{background-color:#f8f8f8;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}body.?%PAGE?%[data-v-9a17dba8]{background-color:#f8f8f8}.file-query-container[data-v-9a17dba8]{min-height:100vh;display:flex;flex-direction:column;background:#f8f8f8}\r\n/* 内容区域 */.content-area[data-v-9a17dba8]{flex:1;overflow-y:auto;background:#f8f8f8}\r\n/* 卡片网格 */.card-grid[data-v-9a17dba8]{padding:15px 16px}.card-row[data-v-9a17dba8]{display:flex;justify-content:space-between;margin-bottom:15px}.card-item[data-v-9a17dba8]{width:48%;border-radius:12px;overflow:hidden;box-shadow:0 2px 10px rgba(0,0,0,.03);background-color:#fff}.card-hover[data-v-9a17dba8]{-webkit-transform:scale(.98);transform:scale(.98);transition:all .2s;opacity:.9}.card-inner[data-v-9a17dba8]{height:140px;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:15px 10px;background-color:#fff}\r\n/* 渐变背景 - 完全匹配原型图 */.orange-green-gradient[data-v-9a17dba8]{background:linear-gradient(180deg,#f68d04,#6dbd4e)}.icon-box[data-v-9a17dba8]{display:flex;align-items:center;justify-content:center;width:80px;height:80px;border-radius:50%;background-color:#f5f5f5;overflow:hidden;box-sizing:border-box}.icon-green[data-v-9a17dba8]{background-color:#3cc792}.icon-orange[data-v-9a17dba8]{background-color:#f49b2c}.icon-orange-dark[data-v-9a17dba8]{background-color:#feead1}.icon-blue[data-v-9a17dba8]{background-color:#e1eefb}.icon-purple[data-v-9a17dba8]{background-color:#e9e1ff}.icon-cyan[data-v-9a17dba8]{background-color:#d7f6f5}.icon-red[data-v-9a17dba8]{background-color:#ffe5e5}.card-text[data-v-9a17dba8]{font-size:16px;color:#1d2129;font-weight:400;text-align:center;line-height:1.4;margin-top:12px;width:100%;padding:0 5px;box-sizing:border-box;word-break:break-word}.small-text[data-v-9a17dba8]{font-size:15px;letter-spacing:-.3px}\r\n/* 媒体查询 - 适配不同设备 */\r\n/* 小屏幕设备，如iPhone 4 (320px宽度) */@media screen and (max-width:320px){.card-grid[data-v-9a17dba8]{padding:10px 8px}.card-row[data-v-9a17dba8]{margin-bottom:10px}.card-inner[data-v-9a17dba8]{height:120px;padding:10px 5px}.icon-box[data-v-9a17dba8]{width:60px;height:60px}.card-text[data-v-9a17dba8]{font-size:13px;margin-top:8px;\r\n    /* 确保文字不会被截断 */width:100%;white-space:normal;word-break:break-word}.small-text[data-v-9a17dba8]{font-size:12px;letter-spacing:-.5px}\r\n  /* 调整图标大小 */.svg-icon[data-v-9a17dba8]{-webkit-transform:scale(.8);transform:scale(.8)}}@media screen and (min-width:375px){.card-inner[data-v-9a17dba8]{height:140px}.icon-box[data-v-9a17dba8]{width:80px;height:80px}.card-text[data-v-9a17dba8]{font-size:16px;margin-top:12px}}@media screen and (min-width:414px){.card-grid[data-v-9a17dba8]{padding:15px 20px}.card-row[data-v-9a17dba8]{margin-bottom:15px}.card-inner[data-v-9a17dba8]{height:150px}.icon-box[data-v-9a17dba8]{width:85px;height:85px}.card-text[data-v-9a17dba8]{font-size:16px}}\r\n/* 平板和大屏幕设备 */@media screen and (min-width:768px){.card-grid[data-v-9a17dba8]{padding:25px;max-width:1200px;margin:0 auto}.card-row[data-v-9a17dba8]{margin-bottom:25px}.card-inner[data-v-9a17dba8]{height:170px;padding:20px}.icon-box[data-v-9a17dba8]{width:90px;height:90px}.card-text[data-v-9a17dba8]{font-size:18px;margin-top:10px}}\r\n/* 功能服务卡片样式 */.function-card[data-v-9a17dba8]{background-color:#fff;border-radius:12px;margin:15px;padding:15px;box-shadow:0 2px 12px rgba(0,0,0,.06)}',""]),a.exports=t},"8c7a":function(a,t,d){"use strict";var r=d("0abd"),o=d.n(r);o.a},"920d":function(a,t,d){"use strict";d.r(t);var r=d("1b62"),o=d("275a");for(var e in o)["default"].indexOf(e)<0&&function(a){d.d(t,a,(function(){return o[a]}))}(e);d("8c7a");var i=d("828b"),n=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"9a17dba8",null,!1,r["a"],void 0);t["default"]=n.exports}}]);