<template>
	<view class="mainContainer">
		<threenav title="详情"></threenav>
		<!-- <second-navbar style="background-color: #2bb98f;height: 150rpx;" title="工单明细"
			@search-click="handleNavbarSearch"></second-navbar> -->
		<!-- 列表 -->
		<view class="listcontainer">
			<view class="datatext">
				<view class="datacontainer">
					<view class="datatitle">用户编号：</view><text class="textstyle" style="font-size: 30rpx;color: red;">{{listData.custNo}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">工单编号：</view><text class="textstyle"
						style="font-size: 30rpx;">{{listData.appNo}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">供电公司：</view><text class="textstyle"
						style="font-size: 30rpx;">{{listData.mgtOrgName}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">派工人：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.orderCreator}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">用户名称：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.custName}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">用户类型：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.consType}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">用电地址：</view><text class="textstyle"
						style="color: red;font-size: 30rpx;">{{listData.ecAddr}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">工单来源：</view><text class="textstyle"
						style="font-size: 30rpx;">{{listData.orderSrc}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">工单类型：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.workOrderType}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">工单状态：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.orderStatus}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">检查结果：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.checkRslt}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">环节状态时间：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.stepStateTime}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">是否超时：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.overdue}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;">
					<view class="datatitle">异常问题：</view><text class="textstyle" style="font-size: 30rpx;">{{listData.abnorDesc}}</text>
				</view>
				<view class="datacontainer" style="border-top: 1px solid lightgray;" @click="gotoliucheng(listData)">
					<view class="datatitle">流程环节：</view><text class="textstyle" style="font-size: 30rpx;color: blue;">查看详情</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				listData: {},
			}
		},
		onLoad(options) {
			if (options.data) {
				this.listData = JSON.parse(decodeURIComponent(options.data));
			}
		},
		onReady() {

		},
		methods: {
			gotoliucheng(listData){
				uni.navigateTo({
					url: '/pages/fileQuery/jobMonitor/liuchenghuanjie?data=' + encodeURIComponent(JSON.stringify(
						listData))
				})
			},
			// gotoproblem(listData){
			// 	uni.navigateTo({
			// 		url: '/pages/fileQuery/jobMonitor/orderProblem?data=' + encodeURIComponent(JSON.stringify(
			// 			listData))
			// 	})
			// }
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	.custom-select {
		width: 100%;
		position: relative;
	}

	.select-box {
		border: 1rpx solid #ddd;
		width: 100%;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.arrow {
		float: right;
		border: solid #999;
		border-width: 0 2rpx 2rpx 0;
		padding: 6rpx;
		margin-top: 6rpx;
	}

	.down {
		transform: rotate(45deg);
	}

	.up {
		transform: rotate(-135deg);
	}

	.dropdown {
		position: absolute;
		width: 100%;
		border: 1rpx solid #eee;
		background: #fff;
		z-index: 999;
	}

	.dropdown-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.mainContainer {
		.popupcontainer {
			height: 1500rpx;
			padding: 0 40rpx;

			.poptitle {
				text-align: center;
				font-size: 36rpx;
				font-weight: bold;
				padding: 50rpx 0;
			}

			.formitem {

				// font-weight: bold;
				.calendarContainer {
					width: 100%;

					.calInput {
						width: 100%;
						// margin-right: 30rpx;
					}
				}
			}

			.ubutton {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 400rpx 0 0;
			}
		}

		.listcontainer {
			background-color: white;
			border-radius: 20rpx;
			margin: 20rpx;

			.datatext {
				padding: 10rpx 0 10rpx 0rpx;

				.datacontainer {
					display: flex;
					align-items: center;
					padding: 30rpx 0 30rpx 30rpx;

					.datatitle {
						width: 300rpx;
						font-weight: bold;
						font-size: 30rpx;
						color: gray;
					}
					.textstyle{
						font-weight: bold;
					}
				}
			}

		}
	}
</style>