import SM from "./SM.js";

let pubKey =
  "041A4157A495021D757C15A45CA4959EC7D5ACBC926F151AD1AC517E01D12FA51F3777320E357BCE4DD2DBD0A85C40C3A4B9611F8038253BFF0F5C7FAC319AB01A";
let prikey = "195DEFE342D4D387E9751B8B28D9123DFD4CAA27D4DE58E6D123BF73F6CE5B34";

// 加密
const encryption = (data) => {
  let jsonStrData = null;
  // 此处判断说不定可以替换为obj.constructor === Object
  if (
    typeof data == "object" &&
    Object.prototype.toString.call(data).toLowerCase() == "[object object]" &&
    !data.length
  ) {
    jsonStrData = JSON.stringify(data);
  } else {
    jsonStrData = data;
  }
  let sm3Str = SM.SG_sm3encrypt(jsonStrData);
  let sm2Str = SM.SG_sm2Encrypt(`${sm3Str}@${jsonStrData}`, pubKey);

  return sm2Str;
};

// 解密
const decrypt = (data) => {
  let decryptData = null;
  // 待解密参数
  let decryptedData = SM.SG_sm2Decrypt(data, prikey);
  let before = decryptedData.slice(0, 64);
  let after = decryptedData.slice(65, decryptedData.length);
  // 重新解密操作
  if (before === SM.SG_sm3encrypt(after)) {
    decryptData = JSON.parse(after);
  }

  return decryptData;
};

export default { encryption, decrypt };
