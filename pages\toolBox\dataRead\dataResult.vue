<template>
	<view class="data-result-container">
		<!-- 自定义导航栏 -->
		<custom-navbar title="抄读助手" :showBack="true">
			<template #right>
				<!-- <view class="setting-icon">
					<u-icon name="setting" color="#333333" :size="size"></u-icon>
				</view> -->
				<!-- <view class="bluetooth-icon">
					<svg-icon name="bluetooth" size="40"></svg-icon>
				</view> -->
			</template>
		</custom-navbar>
		
		<!-- 内容区域 -->
		<view class="content-section" :style="{ height: contentHeight + 'px' }">
			<!-- 抄读结果卡片 -->
			<view class="result-card">
				<view class="card-title">
					<view class="title-indicator"></view>
					<text>抄读结果</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">抄表时间段</text>
					<view class="form-value">
						<text>{{formatDate(selectedDate)}}</text>
					</view>
				</view>
				
				<!-- 日历组件 -->
				<view class="calendar-container">
					<view class="calendar-header">
						<view class="weekday" v-for="(day, index) in weekdays" :key="index">
							<text>{{day}}</text>
						</view>
					</view>
					
					<view class="calendar-days">
						<view class="day" v-for="(day, index) in calendarDays" :key="index" 
							:class="{'other-month': day.otherMonth, 'current-day': day.isCurrentDay, 'selected-day': day.isSelectedDay}"
							@click="selectDay(day)">
							<text>{{day.day}}</text>
						</view>
					</view>
				</view>
				
				<!-- 按列表形式展示抄读结果 -->
				<view class="read-result-section" v-if="groupedResultData && groupedResultData.length > 0">
					<view class="result-data-list">
						<view v-for="(group, groupIndex) in groupedResultData" :key="groupIndex" class="data-group">
							<view v-for="(item, itemIndex) in group.items" :key="itemIndex" class="data-item">
								<text class="item-name">{{ item.name }}</text>
								<text class="item-value" :class="{'success': item.isSuc === '1', 'fail': item.isSuc === '0'}">
									{{ item.value }}
								</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 无数据提示 -->
				<view class="no-data" v-if="!groupedResultData || groupedResultData.length === 0">
					<u-empty mode="data" icon="/static/icons/nodata.jpg"></u-empty>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				size:"40rpx",
				windowHeight: 0,
				selectedDate: new Date(),
				currentDate: new Date(),
				weekdays: ['日', '一', '二', '三', '四', '五', '六'],
				calendarDays: [],
				readResultData: null,
				groupedResultData: null
			};
		},
		onLoad(options) {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
			
			// 处理URL参数传递的数据
			if(options.item) {
				try {
					const item = JSON.parse(decodeURIComponent(options.item));
					console.log('获取URL参数传递的数据', item);
					this.readResultData = item;
					// 处理分组数据
					this.groupedResultData = this.processReadResults(item);
					
					// 更新selectedDate为数据中的dataTime
					this.updateSelectedDateFromData();
				} catch(e) {
					console.error('解析URL参数错误', e);
				}
			} 
			
			// 生成日历数据
			this.generateCalendarDays();
		},
		onReady() {
			// onReady生命周期
		},
		onShow() {
			// 页面显示时可以执行的逻辑
			// 从Storage中获取数据
			const readResultData = uni.getStorageSync('readResults');
			if(readResultData){
				try {
					const data = JSON.parse(readResultData);
					console.log('获取抄读结果数据', data);
					this.readResultData = data;
					// 处理分组数据
					this.groupedResultData = this.processReadResults(data);
					uni.removeStorageSync('readResults');
					
					// 更新selectedDate为数据中的dataTime
					this.updateSelectedDateFromData();
				} catch(e) {
					console.error('解析Storage数据错误', e);
				}
			} 
		},
		methods: {
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 将原始数据按照itemCode分组并只保留最新数据
			processReadResults(data) {
				if (!data || !Array.isArray(data)) return null;
				
				// 创建结果数组，用于存储按itemCode分组后的数据
				const groupedData = [];
				
				// 处理每个数据项
				data.forEach(item => {
					// 如果dataName是数组
					if (item.dataName && Array.isArray(item.dataName)) {
						// 创建当前组的数据项列表
						const groupItems = [];
						
						// 处理每个dataName元素
						item.dataName.forEach((name, index) => {
							// 获取对应的dataValue，如果不存在则使用默认值
							const value = item.dataValue && Array.isArray(item.dataValue) && index < item.dataValue.length 
								? item.dataValue[index] 
								: '-';
							
							// 添加到当前组的数据项列表
							groupItems.push({
								name: name,
								value: value,
								isSuc: item.isSuc
							});
						});
						
						// 将当前组添加到结果数组
						if (groupItems.length > 0) {
							groupedData.push({
								itemCode: item.itemCode,
								dataTime: item.dataTime,
								items: groupItems
							});
						}
					}
				});
				
				return groupedData;
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏）
				const topHeight = this.statusBarHeight + this.navbarHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight;
			},
			
			// 格式化日期
			formatDate(date) {
				if (!date) return '';
				const d = new Date(date);
				const year = d.getFullYear();
				const month = String(d.getMonth() + 1).padStart(2, '0');
				const day = String(d.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 生成日历数据
			generateCalendarDays() {
				const year = this.selectedDate.getFullYear();
				const month = this.selectedDate.getMonth();
				const selectedDay = this.selectedDate.getDate();
				const today = new Date();
				
				// 当月第一天
				const firstDayOfMonth = new Date(year, month, 1);
				// 当月最后一天
				const lastDayOfMonth = new Date(year, month + 1, 0);
				// 当月天数
				const daysInMonth = lastDayOfMonth.getDate();
				// 当月第一天是周几（0-6）
				const firstDayWeekday = firstDayOfMonth.getDay();
				
				// 上月最后几天
				const daysFromPrevMonth = firstDayWeekday;
				const prevMonth = month === 0 ? 11 : month - 1;
				const prevMonthYear = month === 0 ? year - 1 : year;
				const daysInPrevMonth = new Date(prevMonthYear, prevMonth + 1, 0).getDate();
				
				let days = [];
				
				// 添加上月的日期
				for (let i = daysInPrevMonth - daysFromPrevMonth + 1; i <= daysInPrevMonth; i++) {
					days.push({
						day: i,
						month: prevMonth,
						year: prevMonthYear,
						otherMonth: true,
						isCurrentDay: false,
						isSelectedDay: false
					});
				}
				
				// 添加当月的日期
				for (let i = 1; i <= daysInMonth; i++) {
					days.push({
						day: i,
						month: month,
						year: year,
						otherMonth: false,
						isCurrentDay: i === today.getDate() && month === today.getMonth() && year === today.getFullYear(),
						isSelectedDay: i === selectedDay && month === this.selectedDate.getMonth() && year === this.selectedDate.getFullYear()
					});
				}
				
				// 添加下月的日期以填满日历（确保总数为42，显示6周）
				const totalDaysNeeded = 42;
				const nextMonth = month === 11 ? 0 : month + 1;
				const nextMonthYear = month === 11 ? year + 1 : year;
				
				let nextMonthDays = totalDaysNeeded - days.length;
				for (let i = 1; i <= nextMonthDays; i++) {
					days.push({
						day: i,
						month: nextMonth,
						year: nextMonthYear,
						otherMonth: true,
						isCurrentDay: false,
						isSelectedDay: false
					});
				}
				
				this.calendarDays = days;
			},
			
			// 选择日期
			selectDay(day) {
				if (day.otherMonth) return; // 不允许选择非当月日期
				
				// 更新选中的日期
				this.selectedDate = new Date(day.year, day.month, day.day);
				
				// 重新生成日历数据以更新选中状态
				this.generateCalendarDays();
			},

			// 更新selectedDate为数据中的dataTime
			updateSelectedDateFromData() {
				if (this.readResultData && this.readResultData.length > 0) {
					const latestData = this.readResultData[this.readResultData.length - 1];
					if (latestData && latestData.dataTime) {
						this.selectedDate = new Date(latestData.dataTime);
						this.generateCalendarDays(); // 重新生成日历以更新选中状态
					}
				}
			}
		}
	}
</script>

<style lang="scss">
.data-result-container {
	display: flex;
	flex-direction: column;
	background-color: #f8f8f8;
	height: 100vh;
	
	/* 覆盖默认导航栏样式，使其与设计图一致 */
	/deep/ .custom-navbar {
		background-color: #FFFFFF !important;
		
		.navbar-title text {
			color: #333333;
		}
	}
	
	.bluetooth-icon {
		height: 42rpx;
		margin-left: 20rpx;
	}
	
	.setting-icon {
		height: 42rpx;
	}
}

/* 内容区域 */
.content-section {
	flex: 1;
	box-sizing: border-box;
	background-color: #f8f8f8;
	padding: 30rpx;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 抄读结果卡片 */
.result-card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	margin-bottom: 30rpx;
}

.card-title {
	display: flex;
	align-items: center;
	.title-indicator {
		width: 6rpx;
		height: 30rpx;
		background-color: #00BF6B;
		margin-right: 14rpx;
		border-radius: 3rpx;
	}
	
	text {
		font-size: 30rpx;
		color: #262626;
		font-weight: bold;
	}
}

.form-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	&:last-child {
		border-bottom: none;
	}
}

.form-label {
	font-size: 28rpx;
	color: #8c8c8c;
}

.form-value {
	font-size: 28rpx;
	color: #262626;
}

/* 日历组件样式 */
.calendar-container {
	border-top: 1rpx solid #f2f2f2;
	padding-top: 20rpx;
}

.calendar-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	
	.weekday {
		flex: 1;
		text-align: center;
		padding: 10rpx 0;
		
		text {
			font-size: 26rpx;
			color: #999999;
		}
	}
}

.calendar-days {
	display: flex;
	flex-wrap: wrap;
	
	.day {
		width: 14.28%;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 5rpx;
		box-sizing: border-box;
		
		text {
			width: 56rpx;
			height: 56rpx;
			border-radius: 28rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			color: #262626;
		}
		
		&.other-month {
			text {
				color: #cccccc;
			}
		}
		
		&.current-day {
			text {
				border: 1rpx solid #07ac7c;
				color: #07ac7c;
			}
		}
		
		&.selected-day {
			text {
				background-color: #07ac7c;
				color: #FFFFFF;
			}
		}
	}
}

/* 抄读结果数据样式 */
.read-result-section {
	padding: 0;
	border-top: 1rpx solid #f2f2f2;
	margin-top: 10rpx;
}

.result-data-list {
	margin-top:20rpx;
	.data-group {
		border-bottom: 1rpx solid #f2f2f2;
		&:last-child {
			border-bottom: none;
			margin-bottom: 0;
		}
	}
	.data-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: none;
		
		&:last-child {
			border-bottom: none;
		}
		
		.item-name {
			font-size: 28rpx;
			color: #8c8c8c;
			flex: 3;
			padding-right: 20rpx;
		}
		
		.item-value {
			font-size: 28rpx;
			color: #333333;
			font-weight: 500;
			text-align: right;
			flex: 1;
		}
		
		.success {
			color: #333333;
		}
		
		.fail {
			color: #ff0000;
		}
	}
}

.no-data {
	text-align: center;
	padding: 50rpx 0;
	color: #999999;
	font-size: 28rpx;
}
</style>
