<template>
	<view class="case-detail-content">
		<!-- 自定义导航栏 -->
		<custom-navbar title="案例详情" :showBack="true"></custom-navbar>
		
		<!-- 内容区域 -->
		<scroll-view 
			class="content-section" 
			scroll-y="true"
			:style="{ height: contentHeight + 'px' }"
		>
			<!-- 加载中提示 -->
		<!-- 	<view class="loading-container" v-if="isLoading">
				<u-loading-icon mode="circle" size="28" color="#07ac7c"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view>
			 -->
			<!-- 案例详情内容 -->
			<view class="case-detail">
				<!-- 案例标题区域 -->
				<view class="case-header">
					<view class="header-left">
						<view class="case-tag" :class="{'case-tag-legal': caseData.caseStatus === '01', 'case-tag-violation': caseData.caseStatus === '02'}">
							<text>{{ caseData.caseStatusName }}</text>
						</view>
					</view>
					<view class="header-right">
						<text class="case-title">{{ caseData.caseName }}</text>
					</view>
				</view>
				
				<!-- 基本信息区域 -->
				<view class="info-section">
					<view class="info-row" v-if="caseData.caseStatus === '01'">
						<view class="info-left">
							<text class="info-label">案件地点：</text>
						</view>
						<view class="info-right">
							<text class="info-value">{{ caseData.caseLocal }}</text>
						</view>
					</view>
					<view class="info-row" v-if="caseData.caseStatus === '01'">
						<view class="info-left">
							<text class="info-label">案件类型：</text>
						</view>
						<view class="info-right">
							<text class="info-value">{{ caseData.caseTypeName }}</text>
						</view>
					</view>
					<view class="info-row" v-if="caseData.caseStatus === '02'">
						<view class="info-left">
							<text class="info-label">窃电性质：</text>
						</view>
						<view class="info-right">
							<text class="info-value">{{ caseData.stealTypeName }}</text>
						</view>
					</view>
					<view class="info-row" v-if="caseData.caseStatus === '02'">
						<view class="info-left">
							<text class="info-label">违窃类别：</text>
						</view>
						<view class="info-right">
							<text class="info-value">{{ caseData.caseClassName }}</text>
						</view>
					</view>
					<view class="info-row" v-if="caseData.caseStatus === '02'">
						<view class="info-left">
							<text class="info-label">案例时间：</text>
						</view>
						<view class="info-right">
							<text class="info-value">{{ caseData.caseTime }}</text>
						</view>
					</view>
					<view class="info-row" v-if="caseData.caseStatus === '02'">
						<view class="info-left">
							<text class="info-label">案例地点：</text>
						</view>
						<view class="info-right">
							<text class="info-value">{{ caseData.caseLocal }}</text>
						</view>
					</view>
					<view class="info-row" v-if="caseData.caseStatus === '02'">
						<view class="info-left">
							<text class="info-label">案例特点：</text>
						</view>
						<view class="info-right">
							<text class="info-value">{{ caseData.caseFeature }}</text>
						</view>
					</view>
					
					<!-- 案例过程描述 -->
					<view class="info-row description-row">
						<view class="info-left">
							<text class="info-label">案例过程描述：</text>
						</view>
						<view class="info-right">
							<view class="full-width-content">
								<text class="description-text">{{ caseData.litigCont }}</text>
							</view>
						</view>
					</view>
					
					<!-- 判决结论 - 司法类案例 -->
					<view class="info-row description-row" v-if="caseData.caseStatus === '01'">
						<view class="info-left">
							<text class="info-label">判决结论：</text>
						</view>
						<view class="info-right">
							<view class="full-width-content">
								<text class="conclusion-text">{{ caseData.conclusion }}</text>
							</view>
						</view>
					</view>
					
					<!-- 判决文书 - 司法类案例 -->
					<view class="info-row description-row" v-if="caseData.caseStatus === '01'">
						<view class="info-left">
							<text class="info-label">判决文书：</text>
						</view>
						<view class="info-right">
							<view class="full-width-content">
								<view class="document-item" @click="openDocument(caseData.document)">
									<text class="document-name">{{ caseData.notifAnnexId }}</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 追补电量信息 - 违窃类案例 -->
					<view v-if="caseData.caseStatus === '02'">
						<view class="info-row description-row">
							<view class="info-right">
								<view class="full-width-content">
									<view class="data-row">
										<text class="data-label">追补电量 (千瓦时)：</text>
										<text class="data-value">{{ caseData.asQty }}</text>
									</view>
									<view class="data-row">
										<text class="data-label">追补电费 (元)：</text>
										<text class="data-value">{{ caseData.asExp }}</text>
									</view>
									<view class="data-row">
										<text class="data-label">违约使用电费 (元)：</text>
										<text class="data-value">{{ caseData.breachCtrtExp }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部间距 -->
			<view class="bottom-space"></view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isLoading: true,
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				theftCaseNo: '',
				caseData: {}
			};
		},
		onLoad(options) {
			// 获取传递的案例ID
			const item = JSON.parse(decodeURIComponent(options.item));
			console.log('获取上页面参数',item)
			if(item) {
				this.caseData = item
			}
			
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
			// 加载案例详情数据
			
		},
		methods: {
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏）
				const topHeight = this.statusBarHeight + this.navbarHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight;
			},
			// 打开文档
			openDocument(documentName) {
				// 实际项目中应该根据文档路径打开文档
				uni.showToast({
					title: '暂无数据',
					icon: 'none',
					duration: 2000
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
.case-detail-content {
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
	width: 100%;
	height: 100%;
}

/* 内容区域 */
.content-section {
	box-sizing: border-box;
	background-color: #f5f5f5;
	padding: 20rpx;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
	overflow-y: auto;
	width: 100%;
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;
	margin-bottom: 20rpx;
	background-color: #fff;
	border-radius: 8rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
	margin-top: 20rpx;
	text-align: center;
}

/* 案例详情 */
.case-detail {
	animation: fade-in 0.3s ease;
}

@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

/* 案例标题区域 */
.case-header {
	background-color: #fff;
	border-radius: 8rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
}

.header-left {
	width: 160rpx;
	flex-shrink: 0;
	display: flex;
	justify-content: flex-start;
}

.header-right {
	flex: 1;
}

.case-tag {
	text-align: center;
	min-width: 90rpx;
	height: 50rpx;
	line-height: 50rpx;
	border-radius: 34rpx;
	padding: 0 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	
	text {
		font-size: 24rpx;
		color: #fff;
		font-weight: 500;
	}
}

.case-tag-legal {
	background-color: #07ac7c;
}

.case-tag-violation {
	background-color: #4080ff;
}

.case-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	line-height: 1.4;
	display: block;
}

/* 基本信息区域 */
.info-section {
	background-color: #fff;
	border-radius: 8rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.info-row {
	display: flex;
	margin-bottom: 16rpx;
	align-items: flex-start;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-left {
	width: 200rpx;
	flex-shrink: 0;
}

.info-right {
	flex: 1;
}

.info-label {
	font-size: 28rpx;
	color: #999;
}

.info-value {
	font-size: 28rpx;
	color: #333;
}

.description-row {
	margin-top: 20rpx;
	flex-direction: column;
}

.description-text, .conclusion-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	text-align: justify;
}

.full-width-content {
	width: 100%;
	margin-top: 10rpx;
}

/* 文档项 */
.document-item {
	display: flex;
	align-items: center;
	border-radius: 8rpx;
}

.document-name {
	flex: 1;
	font-size: 24rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 数据行 */
.data-row {
	display: flex;
	margin-bottom: 16rpx;
	align-items: center;
}

.data-row:last-child {
	margin-bottom: 0;
}

.data-label {
	font-size: 28rpx;
	color: #999;
	flex-shrink: 0;
	width: 280rpx;
}

.data-value {
	font-size: 28rpx;
	color: #333;
}



/* 底部间距 */
.bottom-space {
	height: 100rpx;
}
</style>
