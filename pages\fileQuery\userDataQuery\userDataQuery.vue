<template>
	<view class="user-data-query">
		<!-- 顶部导航区域 - 固定 -->
		<view class="header-section">
			<custom-navbar title="查询"></custom-navbar>
		</view>
		<!-- 中间表单区域 - 可滚动 -->
		<scroll-view class="form-section" scroll-y :style="{ height: scrollViewHeight + 'px',top: safeAreaTop + 'px' }">
			<view class="form-content">
				<!-- 表单项 -->
				<view class="form-item">
					<view class="form-label">用户编号</view>
					<u-input
						v-model="formData.custNo"
						placeholder="请输入用户编号"
						border="surround"
						:clearable="true"
						fontSize="30rpx"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<view class="form-item">
					<view class="form-label">用户名称</view>
					<u-input
						v-model="formData.custName"
						placeholder="请输入用户名称"
						border="surround"
						fontSize="30rpx"
						:clearable="true"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<view class="form-item">
					<view class="form-label">电能表资产编号</view>
					<u-input
						v-model="formData.meterAssetNo"
						placeholder="请输入电能表资产编号"
						border="surround"
						fontSize="30rpx"
						:clearable="true"
						:customStyle="inputStyle"
						class="form-input"
					></u-input>
				</view>
				
				<!-- 日期选项，默认显示，用户档案tab时隐藏 -->
				<view class="form-item" v-if="targetTab && targetTab !== 'userFile'">
					<view class="form-label">日期</view>
					<view class="date-input-wrapper">
						<!-- 日期选择器弹窗 -->
						<uni-datetime-picker v-model="formData.dateRange" 
						type="daterange" 
						:start="minDate"
						:end="maxDate"
						rangeSeparator="至"
						@change="confirmDate"
						:border="true"
						:clear-icon="true"
						:placeholder="'请选择日期'"
						@maskClick="calendarShow =false"></uni-datetime-picker>
					</view>
				</view>
				
				
				<!-- <u-calendar
					:show="calendarShow"
					mode="range"
					:defaultDate="formData.dateRange"
					@confirm="confirmDate"
					@close="calendarShow = false"
					color="#07ac7c"
					startText="开始"
					endText="结束"
					:maxDate="maxDate"
					:minDate="minDate"
				></u-calendar>
				 -->
				<!-- 预留空间，确保小屏幕上也能滚动到底部的所有内容 -->
				<view class="form-space"></view>
			</view>
		</scroll-view>
		
		<!-- 底部按钮区域 - 固定 -->
		<view class="footer-section">
			<view class="btn-group">
				<view class="btn btn-reset" @click="resetForm">
					<text>重置选择</text>
				</view>
				<view class="btn btn-confirm" @click="submitForm">
					<text>确定</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				navbarHeight: 44, // 导航栏固定高度，单位px
				headerHeight: 0, // 导航栏总高度，单位px
				footerHeight: 120, // 底部按钮区域高度，单位rpx
				windowHeight: 0,
				scrollViewHeight: 0,
				safeAreaTop: 0, // 顶部安全区域高度，单位px
				formData: {
					custNo: '',
					custName: '',
					meterAssetNo: '',
					dateRange: [],
					workType: '',
					excessType: '',
					tgNo: "",
					statDate: null
				},
				dateRangeText: '',
				calendarShow: false,
				// 保存目标标签页信息
				targetTab: '',
				targetTabName: '',
				// 日期范围限制
				maxDate: '', // 最大日期（当前日期）
				minDate: '', // 最小日期（当前日期前1个月）
				inputStyle: {
					height: '80rpx',
					fontSize: '30rpx',
					padding: '0 24rpx',
					borderColor: '#e0e0e0',
					borderWidth: '1rpx',
					borderRadius: '16rpx',
					borderStyle: 'solid',
					color: '#333',
					backgroundColor: '#fff'
				}
			};
		},
		onLoad(options) {
			// 获取状态栏高度
			console.log('组件激活')
			const storageParams = uni.getStorageSync('userDataQueryParams') || {};
			console.log(storageParams)
			if(storageParams) {
				this.formData.custNo = storageParams.custNo || '';
				this.formData.custName = storageParams.custName || '';
				this.formData.meterAssetNo = storageParams.meterAssetNo || '';
				this.formData.dateRange = storageParams.dateRange||[];
			}
			this.getStatusBarHeight();
			// 计算日期范围限制
			this.calculateDateLimits();
			
			const savedTab = uni.getStorageSync('activeUserDataTab');
			if(savedTab) {
				this.targetTab = savedTab;
			}
			// 获取eventChannel
			// const eventChannel = this.getOpenerEventChannel();
			// 监听acceptParams事件，获取上一页面通过eventChannel传送到当前页面的数据
			// if(eventChannel) {
			// 	eventChannel && eventChannel.on('acceptParams', (data) => {
			// 		console.log('接收到的参数:', data);
			// 		if (data) {
			// 			this.targetTab = data.targetTab || '';
			// 			this.targetTabName = data.tabName || '';
			// 			// 如果有标签页名称，更新页面标题
			// 			if (this.targetTabName) {
			// 				uni.setNavigationBarTitle({
			// 					title: this.targetTabName + '查询'
			// 				});
			// 			}
						
			// 			// 加载并显示之前保存的查询参数
			// 			if (data.savedQueryParams) {
			// 				const params = data.savedQueryParams;
							
			// 				// 填充表单数据
			// 				this.formData.custNo = params.custNo || '';
			// 				this.formData.custName = params.custName || '';
			// 				this.formData.meterAssetNo = params.meterAssetNo || '';
			// 				if(this.targetTab == "lineLoss") {
			// 					this.formData.tgNo = params.tgNo || '';
			// 					this.formData.statDate = params.statDate;
			// 				}else {
			// 					this.formData.tgNo = '';
			// 					this.formData.statDate = null;
			// 				}
			// 				// 填充日期范围
			// 				if (params.dateRange && params.dateRange.length > 0) {
			// 					// 验证日期范围是否在限制范围内
			// 					const startDate = new Date(params.dateRange[0]);
			// 					const endDate = new Date(params.dateRange[params.dateRange.length - 1]);
			// 					const minDate = new Date(this.minDate);
			// 					const maxDate = new Date(this.maxDate);
								
			// 					// 如果日期范围在限制范围内，则使用保存的日期范围
			// 					if (startDate >= minDate && endDate <= maxDate) {
			// 						this.formData.dateRange = [...params.dateRange];
			// 						// this.dateRangeText = params.dateRangeText || '';
			// 						this.updateDateRangeText(this.formData.dateRange)
			// 					} else {
			// 						// 否则，清空日期范围
			// 						this.formData.dateRange = [];
			// 						this.dateRangeText = '';
			// 						// 可以选择显示提示
			// 						uni.showToast({
			// 							title: '日期范围已重置（超出限制范围）',
			// 							icon: 'none',
			// 							duration: 2000
			// 						});
			// 					}
			// 				}
			// 			}
			// 		}
			// 	});
			// }
			
		},
		onReady() {
			// 计算滚动区域高度
			this.calcScrollViewHeight();
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
				
				// 计算头部总高度（导航栏）
				this.headerHeight = this.statusBarHeight + this.navbarHeight;
				this.safeAreaTop = this.headerHeight;
			},
			
			// 计算滚动区域高度
			calcScrollViewHeight() {
				// 底部区域高度从rpx转为px进行计算
				const footerHeightPx = uni.upx2px(this.footerHeight);
				this.scrollViewHeight = this.windowHeight - this.headerHeight - footerHeightPx;
				console.log('计算高度:', {
					windowHeight: this.windowHeight,
					headerHeight: this.headerHeight,
					footerHeightPx,
					scrollViewHeight: this.scrollViewHeight
				});
			},
			
			// 计算日期范围限制
			calculateDateLimits() {
				// 计算最大日期（当前日期）
				const today = new Date();
				this.maxDate = this.formatDateToString(today);
				
				// 计算最小日期（当前日期前1个月）
				const oneMonthAgo = new Date();
				oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
				this.minDate = this.formatDateToString(oneMonthAgo);
				
				console.log('日期范围限制：', {
					minDate: this.minDate,
					maxDate: this.maxDate
				});
			},
			
			// 格式化日期为字符串 YYYY-MM-DD
			formatDateToString(date) {
				if (!date) return '';
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			maskClick() {
				
			},
			// 重置表单
			resetForm() {
				// 计算日期范围限制
				this.calculateDateLimits();
				
				this.formData = {
					custNo: '',
					custName: '',
					meterAssetNo: '',
					dateRange: [],
					workType: '',
					excessType: '',
					tgNo:"",
					statDate:null
				};
				this.dateRangeText = '';
			},
			
			// 显示日期选择器
			showDatePicker() {
				// 更新日期范围限制
				this.calculateDateLimits();
				this.calendarShow = true;
			},
			
			// 显示工单类型选择器
			showWorkTypePicker() {
				// 实现工单类型选择功能
				uni.showToast({
					title: '选择工单类型',
					icon: 'none'
				});
			},
			
			// 显示超标类型选择器
			showExcessTypePicker() {
				// 实现超标类型选择功能
				uni.showToast({
					title: '选择超标类型',
					icon: 'none'
				});
			},
			
			// 提交表单
			submitForm() {
				if(!this.formData.custNo && !this.formData.custName &&!this.formData.meterAssetNo) {
					uni.showToast({
						title: '请至少输入一项',
						icon: 'none'
					});
					return
				}
				// 保存用户输入到本地存储，以便后续使用
				uni.setStorageSync('userDataQueryParams', {
					custNo: this.formData.custNo,
					custName: this.formData.custName,
					meterAssetNo: this.formData.meterAssetNo,
					dateRange: this.targetTab !== 'userFile' ? this.formData.dateRange : [],
					dateRangeText: this.targetTab !== 'userFile' ? this.dateRangeText : '',
					tgNo: this.formData.tgNo,
					statDate: this.formData.statDate
				});
				
				// 显示加载中提示
				uni.showLoading({
					title: '查询中...'
				});
			
				console.log('激活tab',this.targetTab)
				// 如果有目标标签页，返回上一页并切换到目标标签页
				if (this.targetTab) {
					if(this.targetTab !== 'userFile') {
						if(!this.formData.dateRange.length) {
							uni.showToast({
								title: '请选择日期',
								icon: 'none'
							});
							return;
						}
					}
					
					// 获取上一页实例
					const pages = getCurrentPages();
					const prevPage = pages[pages.length - 2];
					console.log(`准备返回上一页并切换到标签页: ${this.targetTab}`);
					// 设置上一页的数据
					if (prevPage && prevPage.$vm) {
						// 设置查询参数
						prevPage.$vm.custNo = this.formData.custNo;
						prevPage.$vm.custName = this.formData.custName;
						prevPage.$vm.meterAssetNo = this.formData.meterAssetNo;
						// queryResultParams
						// 添加日期范围数据
						if (this.formData.dateRange && this.formData.dateRange.length >=2) {
							prevPage.$vm.dateRange = [...this.formData.dateRange];
							prevPage.$vm.dateRangeText = this.dateRangeText;
						}
						
						if(this.targetTab == "lineLoss") {
							prevPage.$vm.tgNo = this.formData.tgNo || '';
							prevPage.$vm.statDate = this.formData.statDate;
						}else{
							prevPage.$vm.tgNo = '';
							prevPage.$vm.statDate = null;
						}
						
						
						// 记录当前活动标签到本地存储
						uni.setStorageSync('activeUserDataTab', this.targetTab);
						
						// 使用setTimeout确保在返回上一页后再切换标签，以避免界面闪烁
						setTimeout(() => {
							// 切换到目标标签页
							prevPage.$vm.activeTab = this.targetTab;
							console.log(`已切换到标签页: ${this.targetTab}`);
						}, 50);
					}
					// 返回上一页
					uni.navigateBack();
					
				} else {
					// 查询成功，跳转到结果页
					uni.navigateTo({
						url: '/pages/fileQuery/userDataQuery/queryResult',
						success: (res) => {
							// 通过eventChannel向被打开页面传送数据
							res.eventChannel.emit('acceptQueryResults', {
								queryParams: this.formData
							});
						}
					});
				}
			},
			updateDateRangeText(dateRange) {
				if(dateRange && dateRange.length>=2){
					const formatDate = date => {
						if (!date) return '';
						const d = new Date(date);
						const year = d.getFullYear();
						const month = String(d.getMonth() + 1).padStart(2, '0');
						const day = String(d.getDate()).padStart(2, '0');
						return `${year}-${month}-${day}`;
					};
					const startDate = formatDate(dateRange[0]);
					const endDate = formatDate(dateRange[1]);
					this.dateRangeText = `${startDate} 至 ${endDate}`;
				}else{
					this.dateRangeText = '';
				}
			},
			confirmDate(e) {
				this.updateDateRangeText(e)
			}
		}
	}
</script>

<style lang="scss" scoped>
page {
	background-color: #fff;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.user-data-query {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f8f8f8;
	position: relative;
}

/* 顶部导航区域 */
.header-section {
	background-color: #fff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 101;
}

/* 内容区域标题和听筒 */
.content-header {
	height: 160rpx;
	display: flex;
	margin-top: 20rpx;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: #fff;
	position: fixed;
	top: 88rpx; /* 导航栏高度 */
	left: 0;
	right: 0;
	z-index: 100;
	padding-bottom: 20rpx;
	border-top-left-radius: 50rpx;
	border-top-right-radius: 50rpx;
}

.indicator {
	width: 160rpx;
	height: 10rpx;
	background-color: #e0e0e0;
	border-radius: 5rpx;
	margin-bottom: 20rpx;
	opacity: 0.8;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

/* 中间表单区域 */
.form-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 180rpx; /* 底部按钮区域高度 */
	box-sizing: border-box;
	background-color: #fff;
	z-index: 99;
	padding: 10rpx 0;
}

.form-content {
	padding: 40rpx;
	background-color: #fff;
}

.form-item {
	margin-bottom: 48rpx;
}

.form-label {
	font-size: 30rpx;
	color: #262626;
	margin-bottom: 24rpx;
	font-weight: 400;
}

.form-input {
	height: 80rpx;
}

.date-input-wrapper {
	width: 100%;
	position: relative;
	// cursor: pointer;
}

/* 底部按钮区域 */
.footer-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	z-index: 50;
	border-top: 1rpx solid #f5f5f5;
	padding-bottom: env(safe-area-inset-bottom);
}

/* 按钮组样式 */
.btn-group {
	display: flex;
	padding: 20rpx 40rpx;
}

.btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: 400;
	transition: all 0.2s;
}

.btn-reset {
	background-color: transparent;
	color: #07ac7c;
	border: none;
	margin-right: 40rpx;
}

.btn-reset:active {
	opacity: 0.8;
}

.btn-confirm {
	background-color: #07ac7c;
	color: #FFFFFF;
}

.btn-confirm:active {
	background-color: #33a085;
}

/* 为小屏幕预留滚动空间 */
.form-space {
	height: 40rpx;
}

/* 修改 u-input 样式 */
/deep/ .u-input {
	height: 80rpx;
}

/deep/ .u-input__input {
	height: 92rpx;
	font-size: 30rpx;
	color: #333;
	padding: 0 24rpx;
}

/deep/ .u-input--border {
	border-color: #e0e0e0;
	border-width: 1rpx;
	border-radius: 16rpx;
	height: 88rpx;
}

/deep/ .u-input__placeholder-style {
	color: #cccccc;
	font-size: 30rpx;
}

/* 修改导航栏样式 */
/deep/ .custom-navbar {
	background-color: transparent !important;
	box-shadow: none !important;
}

/deep/ .navbar-left .svg-icon {
	color: #000000 !important;
}

/deep/ .navbar-title {
	color: #000000 !important;
}

/deep/ .u-icon {
	color: #999;
}

/deep/ .u-input__content__field {
	display: flex;
	align-items: center;
}
/deep/ .uni-datetime-picker--btn {
	background-color: #00C389;
}
/deep/ .uni-calendar-item--multiple .uni-calendar-item--before-checked,
/deep/ .uni-calendar-item--multiple .uni-calendar-item--after-checked,
/deep/ .uni-calendar-item__weeks-box .uni-calendar-item--checked
{
	background-color: #00C389;
}
</style>
