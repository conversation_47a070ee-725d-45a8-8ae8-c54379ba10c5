(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-problemManage-problemDetail"],{"387a":function(e,t,o){"use strict";o("6a54");var a=o("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("d4b5"),o("0c26"),o("5c47"),o("795c"),o("c223");var r=a(o("9b1b")),i=a(o("1098")),n=o("b3d7"),l={components:{CustomNavbar:i.default},data:function(){return{scrollTop:0,oldScrollTop:0,replyContent:"",token:null,nameCode:"",currentUser:{username:"",company:"",questionId:null,userId:null},problemDetail:{questionTypeName:"",questionTitle:"",questionCount:"",submitterName:"",mgtOrgName:"",questionTime:""},replyList:[],isMockData:!0}},onLoad:function(e){var t=JSON.parse(decodeURIComponent(e.item));console.log("获取上页面参数",t),t&&(this.currentUser.questionId=t.questionId,this.currentUser.username=t.submitterName,this.currentUser.company=t.mgtOrgName,this.currentUser.userId=this.nameCode,this.problemDetail=(0,r.default)({},t),this.getReplyData(t))},methods:{init:function(){var e=this;wx.invoke("ext_DataCache_GetInfo",{data:{key:"1002838.userInfo"}},(function(t){var o=JSON.parse(t.result);e.token=null===o||void 0===o?void 0:o.token,e.nameCode=null===o||void 0===o?void 0:o.nameCode}))},goBack:function(){uni.navigateTo({url:"/pages/problemManage/problemList"})},getReplyData:function(e){var t=this;this.isMockData?this.replyList=[{questionId:"1793583533075009538",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",replyId:"1793836252660355074",responderId:"T00033391",responderName:"岳恒",replyCont:"okokkokookokkokookokkokookokkokookokkokookokkokookokkokookokkoko",replyTime:"2024-05-24"},{questionId:"1793583533075009538",mgtOrgCode:"31102",mgtOrgName:"上海市电力公司",replyId:"1793836178979016705",responderId:"T00033391",responderName:"岳恒",replyCont:"好的  好的 好的 好的 好的 好的 好的 好的 好的 好的 ",replyTime:"2024-05-24"}]:uni.request({url:"http://127.0.0.1:".concat(n.port,"/xczs/forward/for"),method:"POST",header:{"Content-Type":"application/json",token:this.token},data:{token:this.token,method:"PutHuaYun",uri:n.url,data:JSON.stringify({bizCode:n.bizCode,espFlowId:(0,n.getUUID)(),espRsvField1:"",espRsvField2:"",espRsvField3:"",espSign:"",espTimestamp:(0,n.getCurrentTimestamp)(),espInformation:{service:"MobileQuestionManageController",method:"questionDetail",data:{questionId:e.questionId,replyCont:this.replyContent,mgtOrgCode:e.mgtOrgCode,distLv:e.distLv,responderId:this.currentUser.userId}}})},success:function(e){if(e&&1===e.data.Tag){var o=e.data.Data.espInformation;o&&200==o.code?t.replyList=o.data:uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}else uni.showToast({title:"暂无数据",icon:"none",duration:2e3})},fail:function(e){uni.showToast({title:"暂无数据",icon:"none",duration:2e3})}})},sendReply:function(){var e=this;this.replyContent.trim()?(this.getReplyData(this.problemDetail),this.replyContent="",this.$nextTick((function(){var t=uni.createSelectorQuery().in(e).select("#detailScroll"),o=uni.createSelectorQuery().in(e).select("#scrollBottom");o.boundingClientRect((function(o){t.boundingClientRect((function(t){e.scrollTop=e.oldScrollTop,e.$nextTick((function(){e.oldScrollTop=1e5,e.scrollTop=1e5}))})).exec()})).exec()}))):uni.showToast({title:"请输入回复内容",icon:"none"})},formatDate:function(e){var t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0"),r=String(e.getHours()).padStart(2,"0"),i=String(e.getMinutes()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a," ").concat(r,":").concat(i)}}};t.default=l},"38df":function(e,t,o){"use strict";o.r(t);var a=o("707d"),r=o("e738");for(var i in r)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return r[e]}))}(i);o("9fac");var n=o("828b"),l=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,"3f4e1516",null,!1,a["a"],void 0);t["default"]=l.exports},"707d":function(e,t,o){"use strict";o.d(t,"b",(function(){return a})),o.d(t,"c",(function(){return r})),o.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("v-uni-view",{staticClass:"problem-detail-container"},[o("custom-navbar",{attrs:{title:"问题详情","show-back":!0,"background-color":"#00c389","title-color":"#FFFFFF"},on:{leftClick:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}}),o("v-uni-scroll-view",{staticClass:"detail-scroll",attrs:{"scroll-y":!0,id:"detailScroll","scroll-top":e.scrollTop}},[o("v-uni-view",{staticClass:"detail-section"},[o("v-uni-view",{staticClass:"problem-type-row"},[o("v-uni-view",{staticClass:"label"},[e._v("问题类型")]),o("v-uni-view",{staticClass:"value type-value"},[e._v(e._s(e.problemDetail.questionTypeName))])],1),o("v-uni-view",{staticClass:"problem-row"},[o("v-uni-view",{staticClass:"label"},[e._v("标题")]),o("v-uni-view",{staticClass:"value"},[e._v(e._s(e.problemDetail.questionTitle))])],1),o("v-uni-view",{staticClass:"problem-row"},[o("v-uni-view",{staticClass:"label"},[e._v("内容")]),o("v-uni-view",{staticClass:"value"},[e._v(e._s(e.problemDetail.questionCount))])],1),o("v-uni-view",{staticClass:"problem-row"},[o("v-uni-view",{staticClass:"label"},[e._v("提问人")]),o("v-uni-view",{staticClass:"value"},[o("v-uni-text",[e._v(e._s(e.problemDetail.submitterName))]),o("v-uni-text",{staticClass:"company"},[e._v(e._s(e.problemDetail.mgtOrgName))])],1)],1),o("v-uni-view",{staticClass:"problem-row"},[o("v-uni-view",{staticClass:"label"},[e._v("提问时间")]),o("v-uni-view",{staticClass:"value time-value"},[e._v(e._s(e.problemDetail.questionTime))])],1)],1),o("v-uni-view",{staticClass:"divider"}),o("v-uni-view",{staticClass:"reply-section"},[o("v-uni-view",{staticClass:"reply-header"},[e._v("全部回复 ("+e._s(e.replyList.length)+")")]),o("v-uni-view",{staticClass:"reply-list"},e._l(e.replyList,(function(t,a){return o("v-uni-view",{key:a,staticClass:"reply-item"},[o("v-uni-view",{staticClass:"reply-top"},[o("v-uni-view",{staticClass:"reply-user-info"},[o("v-uni-text",{staticClass:"username"},[e._v(e._s(t.responderName))]),o("v-uni-text",{staticClass:"company"},[e._v(e._s(t.mgtOrgName))])],1),o("v-uni-text",{staticClass:"reply-time"},[e._v(e._s(t.replyTime))])],1),o("v-uni-view",{staticClass:"reply-content"},[e._v(e._s(t.replyCont))])],1)})),1),o("v-uni-view",{attrs:{id:"scrollBottom"}})],1)],1),o("v-uni-view",{staticClass:"input-footer"},[o("v-uni-view",{staticClass:"input-area"},[o("v-uni-view",{staticClass:"input-box"},[o("v-uni-input",{attrs:{type:"text",placeholder:"说点什么吧...","placeholder-style":"color: #C8C9CC;"},model:{value:e.replyContent,callback:function(t){e.replyContent=t},expression:"replyContent"}})],1),o("v-uni-view",{staticClass:"send-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendReply.apply(void 0,arguments)}}},[o("uni-icons",{attrs:{type:"paperplane-filled",color:"#FFFFFF",size:"36rpx"}})],1)],1)],1)],1)},r=[]},"9fac":function(e,t,o){"use strict";var a=o("d8e0"),r=o.n(a);r.a},d8e0:function(e,t,o){var a=o("f9db");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=o("967d").default;r("86f901c0",a,!0,{sourceMap:!1,shadowMode:!1})},e738:function(e,t,o){"use strict";o.r(t);var a=o("387a"),r=o.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},f9db:function(e,t,o){var a=o("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-3f4e1516]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-3f4e1516]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-3f4e1516]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-3f4e1516]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-3f4e1516]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-3f4e1516]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-3f4e1516]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-3f4e1516]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-3f4e1516]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-3f4e1516]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-3f4e1516]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-3f4e1516]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-3f4e1516]::after{border:none}.u-hover-class[data-v-3f4e1516]{opacity:.7}.u-primary-light[data-v-3f4e1516]{color:#ecf5ff}.u-warning-light[data-v-3f4e1516]{color:#fdf6ec}.u-success-light[data-v-3f4e1516]{color:#f5fff0}.u-error-light[data-v-3f4e1516]{color:#fef0f0}.u-info-light[data-v-3f4e1516]{color:#f4f4f5}.u-primary-light-bg[data-v-3f4e1516]{background-color:#ecf5ff}.u-warning-light-bg[data-v-3f4e1516]{background-color:#fdf6ec}.u-success-light-bg[data-v-3f4e1516]{background-color:#f5fff0}.u-error-light-bg[data-v-3f4e1516]{background-color:#fef0f0}.u-info-light-bg[data-v-3f4e1516]{background-color:#f4f4f5}.u-primary-dark[data-v-3f4e1516]{color:#398ade}.u-warning-dark[data-v-3f4e1516]{color:#f1a532}.u-success-dark[data-v-3f4e1516]{color:#53c21d}.u-error-dark[data-v-3f4e1516]{color:#e45656}.u-info-dark[data-v-3f4e1516]{color:#767a82}.u-primary-dark-bg[data-v-3f4e1516]{background-color:#398ade}.u-warning-dark-bg[data-v-3f4e1516]{background-color:#f1a532}.u-success-dark-bg[data-v-3f4e1516]{background-color:#53c21d}.u-error-dark-bg[data-v-3f4e1516]{background-color:#e45656}.u-info-dark-bg[data-v-3f4e1516]{background-color:#767a82}.u-primary-disabled[data-v-3f4e1516]{color:#9acafc}.u-warning-disabled[data-v-3f4e1516]{color:#f9d39b}.u-success-disabled[data-v-3f4e1516]{color:#a9e08f}.u-error-disabled[data-v-3f4e1516]{color:#f7b2b2}.u-info-disabled[data-v-3f4e1516]{color:#c4c6c9}.u-primary[data-v-3f4e1516]{color:#3c9cff}.u-warning[data-v-3f4e1516]{color:#f9ae3d}.u-success[data-v-3f4e1516]{color:#5ac725}.u-error[data-v-3f4e1516]{color:#f56c6c}.u-info[data-v-3f4e1516]{color:#909399}.u-primary-bg[data-v-3f4e1516]{background-color:#3c9cff}.u-warning-bg[data-v-3f4e1516]{background-color:#f9ae3d}.u-success-bg[data-v-3f4e1516]{background-color:#5ac725}.u-error-bg[data-v-3f4e1516]{background-color:#f56c6c}.u-info-bg[data-v-3f4e1516]{background-color:#909399}.u-main-color[data-v-3f4e1516]{color:#303133}.u-content-color[data-v-3f4e1516]{color:#606266}.u-tips-color[data-v-3f4e1516]{color:#909193}.u-light-color[data-v-3f4e1516]{color:#c0c4cc}.u-safe-area-inset-top[data-v-3f4e1516]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-3f4e1516]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-3f4e1516]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-3f4e1516]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-3f4e1516]{z-index:10090}uni-toast .uni-toast[data-v-3f4e1516]{z-index:10090}[data-v-3f4e1516]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */.problem-detail-container[data-v-3f4e1516]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5}.detail-scroll[data-v-3f4e1516]{flex:1;overflow:hidden}.detail-section[data-v-3f4e1516]{background-color:#fff;padding:0}.problem-type-row[data-v-3f4e1516]{display:flex;justify-content:space-between;padding:%?30?%;border-bottom:%?2?% solid #f5f5f5}.problem-type-row .label[data-v-3f4e1516]{color:#262626;font-size:%?28?%}.problem-type-row .type-value[data-v-3f4e1516]{color:#00c389;font-size:%?28?%;font-weight:500}.problem-row[data-v-3f4e1516]{display:flex;padding:%?30?%;border-bottom:%?2?% solid #f5f5f5}.problem-row[data-v-3f4e1516]:last-child{border-bottom:none}.problem-row .label[data-v-3f4e1516]{width:%?160?%;color:#262626;font-size:%?28?%;flex-shrink:0}.problem-row .value[data-v-3f4e1516]{flex:1;color:#8c8c8c;font-size:%?28?%;line-height:1.5;text-align:right}.problem-row .value .company[data-v-3f4e1516]{color:#8c8c8c;font-size:%?26?%;margin-left:%?20?%}.problem-row .time-value[data-v-3f4e1516]{color:#8c8c8c}.divider[data-v-3f4e1516]{height:%?20?%;background-color:#f5f5f5}.reply-section[data-v-3f4e1516]{background-color:#fff;padding:%?30?%}.reply-section .reply-header[data-v-3f4e1516]{font-size:%?32?%;font-weight:500;color:#262626;margin-bottom:%?30?%}.reply-section .reply-list .reply-item[data-v-3f4e1516]{padding-bottom:%?30?%;margin-bottom:%?30?%;border-bottom:%?2?% solid #f0f0f0}.reply-section .reply-list .reply-item[data-v-3f4e1516]:last-child{border-bottom:none;margin-bottom:0}.reply-section .reply-list .reply-item .reply-top[data-v-3f4e1516]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?20?%}.reply-section .reply-list .reply-item .reply-top .reply-user-info .username[data-v-3f4e1516]{font-size:%?28?%;color:#262626;margin-right:%?20?%}.reply-section .reply-list .reply-item .reply-top .reply-user-info .company[data-v-3f4e1516]{font-size:%?24?%;color:#8c8c8c}.reply-section .reply-list .reply-item .reply-top .reply-time[data-v-3f4e1516]{font-size:%?24?%;color:#8c8c8c}.reply-section .reply-list .reply-item .reply-content[data-v-3f4e1516]{font-size:%?28?%;color:#262626;line-height:1.6;word-wrap:break-word}.input-footer[data-v-3f4e1516]{height:%?100?%;background-color:#fff;border-top:%?2?% solid #f0f0f0;display:flex;align-items:center;padding:0 %?30?%}.input-footer .input-area[data-v-3f4e1516]{display:flex;align-items:center;width:100%}.input-footer .input-area .input-box[data-v-3f4e1516]{flex:1;height:%?72?%;background-color:#f5f5f5;border-radius:%?36?%;padding:0 %?30?%;display:flex;align-items:center}.input-footer .input-area .input-box uni-input[data-v-3f4e1516]{width:100%;height:100%;font-size:%?28?%}.input-footer .input-area .send-btn[data-v-3f4e1516]{width:%?72?%;height:%?72?%;border-radius:50%;background-color:#00c389;display:flex;align-items:center;justify-content:center;margin-left:%?20?%}',""]),e.exports=t}}]);