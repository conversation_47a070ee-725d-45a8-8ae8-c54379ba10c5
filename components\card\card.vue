<template>
	<view class="card-wrap card-bg">
		<view class="card-title">
			<view style="display:flex">
				<u--text :text="title" bold></u--text>
				<slot name="left"></slot>
			</view>
			<u-switch v-model="value" @change="change" inactiveColor="#ccc" activeColor="#009d85" v-if="isShowBtn"></u-switch>
		</view>
		<view class="card-content">
			<slot></slot>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			isShowBtn: {
			  type: Boolean,
			  default: false,
			},
			title: {
				type: String,
				default: '',
			}
		},
		data() {
			return {
				value: false
			};
		},
		methods: {
			change(e) {
				this.$emit('change',e)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.card-bg {
		height: auto;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		margin-bottom: 16rpx;
		// background: url('~@/static/img/bg.png') no-repeat;
		// background-size: contain;
		background-image: linear-gradient(to bottom, rgba(0, 157, 133,.3) 0%,transparent 70%);
		padding: 40rpx 20rpx 0;
		.card-title {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
			&:before{
				content: "";
				position: absolute;
				width: 4px;
				height: 16px;
				border-radius: 10px;
				background-color: $custom-theme-color;
			}
			.u-text {
				padding-left: 8px;
			}
			// border-left: 4px solid $custom-theme-color;
		}
	}
	.card-content {
		background-color: #fff;
		border-radius: 10px;
		margin-top: 8px;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 8px 20px 0px;
		position: relative;
		// overflow: hidden;
	}

</style>