let appInfo = {
  agentid: '1000242', // 应用ID
  corpid: 'ww445f8033443a14aa',
  iscAppid: `${new Date().getTime()}1000242`,
  ip: '*************', // ip
  iscpIP: '***************', // 安全交互平台地址
  iscpPort: '20082',
  user: 'user', // 当前登录用户id
  baseUrlIp: '*************', // app后端服务f5地址
  baseUrlPort: '1080', // app后端服务f5地址对应端口号
  appId: 'shandong', // 需要向安全交互平台申请
  result: '' // 返回的端口号
}
let authUrl = '' // 获取wx_code地址
let getTicketUrl = '' // 获取ISC票据地址
const redirectUrl = 'zipapp://local.host/index.html' // 获取wx_code重定向地址
const iscpRetryCount = 5 // 安全链接建立失败重试次数
let appName = '20210917210351707000001104601025$20200806165835119064103174270427$ANDROID' // 移动支撑平台I国网前置配置
let appUrl = 'http://*************:1080/istateinvoke-server/iStateGridInvoke' // 接口请求地址
const resourceUrl = 'masp' // 移动支撑平台I国网前置配置
let isLocalhost = '1' // 是否是本地调试 1 在线， 离线为空
const appFtpKey = '' // 文件中转服务上传方案

if (process.env.VUE_APP_TYPE === 'prod') {
  // 正式环境
  appInfo = {
    agentid: '1000645', // 应用ID
    corpid: 'ww4d11a39991ebffdc',
    iscAppid: `${new Date().getTime()}1000645`,
    iscpIP: '************', // 安全交互平台地址
    iscpPort: '18080',
    user: 'user', // 当前登录用户id
    baseUrlIp: '*************', // app后端服务f5地址
    baseUrlPort: '18080', // app后端服务f5地址对应端口号
    appId: 'GSZSYXAPP', // 需要向安全交互平台申请
    result: '' // 返回的端口号
  }
  authUrl = 'https://igw.sgcc.com.cn/connect/oauth2/authorize?'
  getTicketUrl = 'http://id.sgcc.com.cn:18088/eeee/identity/getAuthTicketByWechatCode'
  // appName = '20211110145403629057028240441883$20211109183424643057029223981198$ANDROID'
  appName = '20211211190635204001094000031851$20180523164526158068060000170585$ANDROID'
  appUrl = '/istateinvoke-server/iStateGridInvoke'
  isLocalhost = ''
} else if (process.env.VUE_APP_TYPE === 'test') {
  // test 环境
  appInfo = {
    agentid: '1000645', // 应用ID
    corpid: 'ww4d11a39991ebffdc',
    iscAppid: `${new Date().getTime()}1000645`,
    iscpIP: '************', // 安全交互平台地址
    iscpPort: '18080',
    user: 'user', // 当前登录用户id
    baseUrlIp: '*************', // app后端服务f5地址
    baseUrlPort: '18080', // app后端服务f5地址对应端口号
    appId: 'GSZSYXAPP', // 需要向安全交互平台申请
    result: '' // 返回的端口号
  }
  authUrl = 'https://igw.sgcc.com.cn/connect/oauth2/authorize?'
  getTicketUrl = 'http://id.sgcc.com.cn:18088/eeee/identity/getAuthTicketByWechatCode' 
  appName = '20220121103039356001117063390952$20180523164526158068060000170585$ANDROID'
  appUrl = '/istateinvoke-server/iStateGridInvoke'
  isLocalhost = ''
} else if (process.env.VUE_APP_TYPE === 'uat') {
  // uat 环境
  appInfo = {
    agentid: '1000242', // 应用ID
    corpid: 'ww445f8033443a14aa',
    iscAppid: `${new Date().getTime()}1000242`,
    ip: '*************', // ip
    iscpIP: '***************', // 安全交互平台地址
    iscpPort: '20082',
    user: 'user', // 当前登录用户id
    baseUrlIp: '*************', // app后端服务f5地址
    baseUrlPort: '1080', // app后端服务f5地址对应端口号
    appId: 'shandong', // 需要向安全交互平台申请
    result: '' // 返回的端口号
  }
  authUrl = 'https://igw.isgcc.net:18081/connect/oauth2/authorize?'
  getTicketUrl = 'https://igw.isgcc.net:18443/identity/getAuthTicketByWechatCode'
  appName = '20210917210351707000001104601025$20200806165835119064103174270427$ANDROID'
  appUrl = '/istateinvoke-server/iStateGridInvoke'
  // appUrl = 'http://*************:1080/istateinvoke-server/iStateGridInvoke'
  isLocalhost = ''
}

export const APP_INFO = appInfo
export const VUE_APP_OAUTH_URL = authUrl
export const VUE_APP_TICKET_URL = getTicketUrl
export const VUE_APP_REDIRECT_URI = redirectUrl
export const VUE_APP_RETRY_COUNT = iscpRetryCount
export const VUE_APP_APPNAME = appName
export const VUE_APP_URL = appUrl
export const VUE_APP_RESOURCE = resourceUrl
export const VUE_APP_IS_LOCALHOST = isLocalhost
export const VUE_APP_FTPKEY = appFtpKey  
export const VUE_APP_FTPURL = `/istateinvoke-server/iStateGridMaspBase64FileInvoke`
export const VUE_APP_DOWNFLET = `/istateinvoke-server/iStateGridMaspFileDownInvoke`
export const VUE_URL = `/istateinvoke-server/iStateGridInvoke`
export const VUE_SURL = 'http://localhost:9066/hyAcqMServer/workController/cjycywOrder'
//export const VUE_SURL = 'http://27931fe4.cpolar.top/lms/receiveReq'
//export const VUE_SURL = 'http://3dc65218.r3.cpolar.top/lms/receiveReq'