<template>
  <view class="svg-icon" :style="{width: size + 'rpx', height: size + 'rpx'}" @click="clickHandler">
    <image v-if="isCustomSvg" :src="svgSrc" class="svg-image" :style="{
      width: size + 'rpx', 
      height: size + 'rpx',
    }" />
    <uni-icons v-else :type="name" :size="size" :color="color"></uni-icons>
  </view>
</template>

<script>
export default {
  name: 'svg-icon',
  props: {
    // 图标名称
    name: {
      type: String,
      required: true
    },
    // 图标颜色
    color: {
      type: String,
      default: '#FFFFFF'
    },
    // 图标大小
    size: {
      type: [Number, String],
      default: 64
    },
    // 图标右侧文字
    label: {
      type: [String, Number],
      default: ''
    },
    // label位置
    labelPos: {
      type: String,
      default: 'right'
    },
    // label大小
    labelSize: {
      type: [String, Number],
      default: '15'
    },
    // label颜色
    labelColor: {
      type: String,
      default: '#333'
    },
    // 点击时的样式类
    hoverClass: {
      type: String,
      default: ''
    },
    // 点击图标的索引
    index: {
      type: [String, Number],
      default: ''
    }
  },
  computed: {
    isCustomSvg() {
      // 判断是否使用自定义SVG图标
      const customIcons = [
        'yonghushuju', 'taiqushitu', 'zhongdiantaiqu', 
        'gongzuoxiangqing', 'jiankong', 'zhibiao', 'line','lineGreen','case','sort','bluetooth'
      ];
      return customIcons.includes(this.name);
    },
    svgSrc() {
      // 返回SVG图标路径
      return `/static/icons/${this.name}.svg`;
    },
    getSvgFilter() {
      // 根据color属性动态生成filter
      if (this.color === '#FFFFFF' || this.color === '#ffffff' || this.color === 'white') {
        return 'brightness(0) invert(1)'; // 白色
      } else if (this.color === '#07ac7c') {
        // 绿色 - 使用特定的filter值来显示主题绿色
        return 'invert(47%) sepia(80%) saturate(1462%) hue-rotate(127deg) brightness(94%) contrast(89%)';
      } else {
        // 其他颜色 - 尝试通过CSS变量或内联样式处理
        // 这里可能需要更复杂的颜色转换逻辑
        return ''; // 默认不应用filter
      }
    }
  },
  methods: {
    clickHandler(e) {
      this.$emit('click', this.index);
    }
  }
}
</script>

<style lang="scss" scoped>
.svg-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  .svg-image {
    width: 100%;
    height: 100%;
    /* 移除固定的filter，改为通过计算属性动态设置 */
  }
}
</style> 