<template>
	<view class="step-info">
		<view class="step-number">
			<block v-for="(item,index) in stepList" :key="index">
				<view @click.stop="openItem(item)" class="item-state" :style="{opacity:getOpacity(index)}">
					<view class="line" :style="{width:'80%',left:size.width/2+'px',background:getLine(index) || (item.unCheckedLine || unCheckedLine)}"
						v-if="index < stepList.length-1"></view>
					<!-- <image class="state-img" :src="getImg(index) || (item.unCheckedImg || unCheckedImg)"></image> -->       
					<view class="step-wrap">
						<view class="step-bg" :style="{background:getColor(index) || (item.unCheckedColor || unCheckedColor)}">
							<u-icon
							   color="#fff"
							    :name="getImg(index) || (item.unCheckedImg || unCheckedImg)"
								:size="item.size||'20'"
								>
							</u-icon>
						</view>
					</view>
					<view class="step-name">
						<view class="name" :style="{color:getColor(index) || (item.unCheckedColor || unCheckedColor)}">
							{{item.name}}
						</view>
					</view>
				</view>
				
			</block>
		</view>
	</view>
</template>

<script>
	// import checkedImg from '../../static/checkedImg.png'
	// import unCheckedImg from '../../static/unCheckedImg.png'
	export default {
		props: {
			//当前步骤
			step: {
				type: Number,
				default: 1
			},
			//步骤列表
			stepList: {
				type: Array,
				default: null
			},
			//已完成的图片路径
			checkedImg: {
				type: String,
				default: null
			},
			//未完成的图片路径
			unCheckedImg: {
				type: String,
				default: null
			},
			//已完成的字体颜色
			checkedColor: {
				type: String,
				default: '#009d85'
			},
			//未完成的字体颜色
			unCheckedColor: {
				type: String,
				default: '#333333'
			},
			//已完成的线条颜色
			checkedLine: {
				type: String,
				default: '#287BF8'
			},
			//未完成的线条颜色
			unCheckedLine: {
				type: String,
				default: '#bebebe'
			}
		},
		data() {
			return {
				size: {
					height: 0,
					width: 0
				},
			}
		},
		mounted() {
			uni.$u.sleep().then(() => {
				this.getStepsItemRect()
			})
		},
		methods: {
			//获取图片
			getImg(e) {
				let index = Number(e)
				if (this.step > index) return this?.stepList?.[index].checkedImg || this.checkedImg
			},
			//获取线
			getLine(e) {
				let index = Number(e)
				if (this.step > index) return this?.stepList?.[index].checkedLine || this.checkedLine
			},
			getOpacity(e) {
				let index = Number(e)
				if (this.step > index+1) return '.5'
			},
			//获取颜色
			getColor(e) {
				let index = Number(e)
				if (this.step > index) return this?.stepList?.[index].checkedColor || this.checkedColor
			},
			openItem(item) {
				this.$emit('clickStep', item)
			},
			// 获取组件的尺寸，用于设置横线的位置
			getStepsItemRect() {
				// #ifndef APP-NVUE
				this.$uGetRect('.item-state').then(size => {
					this.size = size
				})
				// #endif
			
				// #ifdef APP-NVUE
				dom.getComponentRect(this.$refs['item-state'], res => {
					const {
						size
					} = res
					this.size = size
				})
				// #endif
			},
			$uGetRect(selector, all) {
			    return new Promise((resolve) => {
			        uni.createSelectorQuery()
			            .in(this)[all ? 'selectAll' : 'select'](selector)
			            .boundingClientRect((rect) => {
			                if (all && Array.isArray(rect) && rect.length) {
			                    resolve(rect)
			                }
			                if (!all && rect) {
			                    resolve(rect)
			                }
			            })
			            .exec()
			    })
			},
		}
	}
</script>

<style>
	.step-info {
		width: 100%;
		height: auto;
	}

	.step-number {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.item-state {
		/* height: 64rpx; */
		flex-direction: column;
		align-items: center;
		position: relative;
		display: flex;
		flex: 1;
	}

	.state-img {
		width: 64rpx;
		height: 64rpx;
		display: flex;
		margin: auto;
		padding:  0 20rpx;
		background-color: #fff;
	}

	.line {
		width: 100%;
		height: 1rpx;
		margin: 0 8rpx;
		top: 15px;
		position: absolute;
	}


	.step-name {
		margin-top: 16rpx;
		width: 120rpx;
		align-items: center;
		justify-content: space-between;
		font-weight: bold;
		text-align: center
	}

	.name {
		font-size: 28rpx;
		line-height: 28rpx;
		color: #333333;
	}
	.step-wrap {
		width: 100rpx;
		height: 60rpx;
		background: #fff;
		border-radius: 50%;
		padding: 0 10px;
		position: relative;
	}
	.step-bg {
		background: #009d85;
		border-radius: 50%;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}
</style>