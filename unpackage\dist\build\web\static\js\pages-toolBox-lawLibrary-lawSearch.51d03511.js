(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-toolBox-lawLibrary-lawSearch"],{"002b":function(t,a,e){var r=e("fcfa");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=e("967d").default;o("ea1beb40",r,!0,{sourceMap:!1,shadowMode:!1})},"013c":function(t,a,e){"use strict";e("6a54");var r=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var o=r(e("fcf3")),i=r(e("d296")),n={name:"u-subsection",mixins:[uni.$u.mpMixin,uni.$u.mixin,i.default],data:function(){return{itemRect:{width:0,height:0}}},watch:{list:function(t,a){this.init()},current:{immediate:!0,handler:function(t){}}},computed:{wrapperStyle:function(){var t={};return"button"===this.mode&&(t.backgroundColor=this.bgColor),t},barStyle:function(){var t={};return t.width="".concat(this.itemRect.width,"px"),t.height="".concat(this.itemRect.height,"px"),t.transform="translateX(".concat(this.current*this.itemRect.width,"px)"),"subsection"===this.mode&&(t.backgroundColor=this.activeColor),t},itemStyle:function(t){var a=this;return function(t){var e={};return"subsection"===a.mode&&(e.borderColor=a.activeColor,e.borderWidth="1px",e.borderStyle="solid"),e}},textStyle:function(t){var a=this;return function(t){var e={};return e.fontWeight=a.bold&&a.current===t?"bold":"normal",e.fontSize=uni.$u.addUnit(a.fontSize),"subsection"===a.mode?e.color=a.current===t?"#fff":a.inactiveColor:e.color=a.current===t?a.activeColor:a.inactiveColor,e}}},mounted:function(){this.init()},methods:{init:function(){var t=this;uni.$u.sleep().then((function(){return t.getRect()}))},getText:function(t){return"object"===(0,o.default)(t)?t[this.keyName]:t},getRect:function(){var t=this;this.$uGetRect(".u-subsection__item--0").then((function(a){t.itemRect=a}))},clickHandler:function(t){this.$emit("change",t)}}};a.default=n},"0b9e":function(t,a,e){var r=e("c86c");a=r(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-1ba40ab6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-1ba40ab6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-1ba40ab6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-1ba40ab6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-1ba40ab6]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-1ba40ab6]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-1ba40ab6]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-1ba40ab6]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-1ba40ab6]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-1ba40ab6]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-1ba40ab6]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-1ba40ab6]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-1ba40ab6]::after{border:none}.u-hover-class[data-v-1ba40ab6]{opacity:.7}.u-primary-light[data-v-1ba40ab6]{color:#ecf5ff}.u-warning-light[data-v-1ba40ab6]{color:#fdf6ec}.u-success-light[data-v-1ba40ab6]{color:#f5fff0}.u-error-light[data-v-1ba40ab6]{color:#fef0f0}.u-info-light[data-v-1ba40ab6]{color:#f4f4f5}.u-primary-light-bg[data-v-1ba40ab6]{background-color:#ecf5ff}.u-warning-light-bg[data-v-1ba40ab6]{background-color:#fdf6ec}.u-success-light-bg[data-v-1ba40ab6]{background-color:#f5fff0}.u-error-light-bg[data-v-1ba40ab6]{background-color:#fef0f0}.u-info-light-bg[data-v-1ba40ab6]{background-color:#f4f4f5}.u-primary-dark[data-v-1ba40ab6]{color:#398ade}.u-warning-dark[data-v-1ba40ab6]{color:#f1a532}.u-success-dark[data-v-1ba40ab6]{color:#53c21d}.u-error-dark[data-v-1ba40ab6]{color:#e45656}.u-info-dark[data-v-1ba40ab6]{color:#767a82}.u-primary-dark-bg[data-v-1ba40ab6]{background-color:#398ade}.u-warning-dark-bg[data-v-1ba40ab6]{background-color:#f1a532}.u-success-dark-bg[data-v-1ba40ab6]{background-color:#53c21d}.u-error-dark-bg[data-v-1ba40ab6]{background-color:#e45656}.u-info-dark-bg[data-v-1ba40ab6]{background-color:#767a82}.u-primary-disabled[data-v-1ba40ab6]{color:#9acafc}.u-warning-disabled[data-v-1ba40ab6]{color:#f9d39b}.u-success-disabled[data-v-1ba40ab6]{color:#a9e08f}.u-error-disabled[data-v-1ba40ab6]{color:#f7b2b2}.u-info-disabled[data-v-1ba40ab6]{color:#c4c6c9}.u-primary[data-v-1ba40ab6]{color:#3c9cff}.u-warning[data-v-1ba40ab6]{color:#f9ae3d}.u-success[data-v-1ba40ab6]{color:#5ac725}.u-error[data-v-1ba40ab6]{color:#f56c6c}.u-info[data-v-1ba40ab6]{color:#909399}.u-primary-bg[data-v-1ba40ab6]{background-color:#3c9cff}.u-warning-bg[data-v-1ba40ab6]{background-color:#f9ae3d}.u-success-bg[data-v-1ba40ab6]{background-color:#5ac725}.u-error-bg[data-v-1ba40ab6]{background-color:#f56c6c}.u-info-bg[data-v-1ba40ab6]{background-color:#909399}.u-main-color[data-v-1ba40ab6]{color:#303133}.u-content-color[data-v-1ba40ab6]{color:#606266}.u-tips-color[data-v-1ba40ab6]{color:#909193}.u-light-color[data-v-1ba40ab6]{color:#c0c4cc}.u-safe-area-inset-top[data-v-1ba40ab6]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-1ba40ab6]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-1ba40ab6]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-1ba40ab6]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-1ba40ab6]{z-index:10090}uni-toast .uni-toast[data-v-1ba40ab6]{z-index:10090}[data-v-1ba40ab6]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-view[data-v-1ba40ab6], uni-scroll-view[data-v-1ba40ab6], uni-swiper-item[data-v-1ba40ab6]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-textarea[data-v-1ba40ab6]{border-radius:4px;background-color:#fff;position:relative;display:flex;flex-direction:row;flex:1;padding:9px}.u-textarea--radius[data-v-1ba40ab6]{border-radius:4px}.u-textarea--no-radius[data-v-1ba40ab6]{border-radius:0}.u-textarea--disabled[data-v-1ba40ab6]{background-color:#f5f7fa}.u-textarea__field[data-v-1ba40ab6]{flex:1;font-size:15px;color:#606266;width:100%}.u-textarea__count[data-v-1ba40ab6]{position:absolute;right:5px;bottom:2px;font-size:12px;color:#909193;background-color:#fff;padding:1px 4px}',""]),t.exports=a},"105a":function(t,a,e){"use strict";e.r(a);var r=e("3ea5"),o=e.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return r[t]}))}(i);a["default"]=o.a},"3c38":function(t,a,e){var r=e("c86c");a=r(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-4a603381]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-4a603381]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-4a603381]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-4a603381]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-4a603381]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-4a603381]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-4a603381]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-4a603381]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-4a603381]::after{border:none}.u-hover-class[data-v-4a603381]{opacity:.7}.u-primary-light[data-v-4a603381]{color:#ecf5ff}.u-warning-light[data-v-4a603381]{color:#fdf6ec}.u-success-light[data-v-4a603381]{color:#f5fff0}.u-error-light[data-v-4a603381]{color:#fef0f0}.u-info-light[data-v-4a603381]{color:#f4f4f5}.u-primary-light-bg[data-v-4a603381]{background-color:#ecf5ff}.u-warning-light-bg[data-v-4a603381]{background-color:#fdf6ec}.u-success-light-bg[data-v-4a603381]{background-color:#f5fff0}.u-error-light-bg[data-v-4a603381]{background-color:#fef0f0}.u-info-light-bg[data-v-4a603381]{background-color:#f4f4f5}.u-primary-dark[data-v-4a603381]{color:#398ade}.u-warning-dark[data-v-4a603381]{color:#f1a532}.u-success-dark[data-v-4a603381]{color:#53c21d}.u-error-dark[data-v-4a603381]{color:#e45656}.u-info-dark[data-v-4a603381]{color:#767a82}.u-primary-dark-bg[data-v-4a603381]{background-color:#398ade}.u-warning-dark-bg[data-v-4a603381]{background-color:#f1a532}.u-success-dark-bg[data-v-4a603381]{background-color:#53c21d}.u-error-dark-bg[data-v-4a603381]{background-color:#e45656}.u-info-dark-bg[data-v-4a603381]{background-color:#767a82}.u-primary-disabled[data-v-4a603381]{color:#9acafc}.u-warning-disabled[data-v-4a603381]{color:#f9d39b}.u-success-disabled[data-v-4a603381]{color:#a9e08f}.u-error-disabled[data-v-4a603381]{color:#f7b2b2}.u-info-disabled[data-v-4a603381]{color:#c4c6c9}.u-primary[data-v-4a603381]{color:#3c9cff}.u-warning[data-v-4a603381]{color:#f9ae3d}.u-success[data-v-4a603381]{color:#5ac725}.u-error[data-v-4a603381]{color:#f56c6c}.u-info[data-v-4a603381]{color:#909399}.u-primary-bg[data-v-4a603381]{background-color:#3c9cff}.u-warning-bg[data-v-4a603381]{background-color:#f9ae3d}.u-success-bg[data-v-4a603381]{background-color:#5ac725}.u-error-bg[data-v-4a603381]{background-color:#f56c6c}.u-info-bg[data-v-4a603381]{background-color:#909399}.u-main-color[data-v-4a603381]{color:#303133}.u-content-color[data-v-4a603381]{color:#606266}.u-tips-color[data-v-4a603381]{color:#909193}.u-light-color[data-v-4a603381]{color:#c0c4cc}.u-safe-area-inset-top[data-v-4a603381]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-4a603381]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-4a603381]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-4a603381]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-4a603381]{z-index:10090}uni-toast .uni-toast[data-v-4a603381]{z-index:10090}[data-v-4a603381]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-view[data-v-4a603381], uni-scroll-view[data-v-4a603381], uni-swiper-item[data-v-4a603381]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-subsection[data-v-4a603381]{display:flex;flex-direction:row;position:relative;overflow:hidden;width:100%;box-sizing:border-box}.u-subsection--button[data-v-4a603381]{height:32px;background-color:#eeeeef;padding:3px;border-radius:3px;align-items:stretch}.u-subsection--button__bar[data-v-4a603381]{background-color:#fff;border-radius:3px!important}.u-subsection--subsection[data-v-4a603381]{height:30px}.u-subsection__bar[data-v-4a603381]{position:absolute;transition-property:color,-webkit-transform;transition-property:transform,color;transition-property:transform,color,-webkit-transform;transition-duration:.3s;transition-timing-function:ease-in-out}.u-subsection__bar--first[data-v-4a603381]{border-top-left-radius:3px;border-bottom-left-radius:3px;border-top-right-radius:0;border-bottom-right-radius:0}.u-subsection__bar--center[data-v-4a603381]{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:0}.u-subsection__bar--last[data-v-4a603381]{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:3px;border-bottom-right-radius:3px}.u-subsection__item[data-v-4a603381]{display:flex;flex-direction:row;flex:1;justify-content:center;align-items:center;position:relative}.u-subsection__item--no-border-right[data-v-4a603381]{border-right-width:0!important}.u-subsection__item--first[data-v-4a603381]{border-top-left-radius:3px;border-bottom-left-radius:3px}.u-subsection__item--last[data-v-4a603381]{border-top-right-radius:3px;border-bottom-right-radius:3px}.u-subsection__item__text[data-v-4a603381]{font-size:12px;line-height:12px;display:flex;flex-direction:row;align-items:center;transition-property:color;transition-duration:.3s}',""]),t.exports=a},"3ea5":function(t,a,e){"use strict";e("6a54");var r=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("c223"),e("aa9c");var o=r(e("72d7")),i={name:"u-textarea",mixins:[uni.$u.mpMixin,uni.$u.mixin,o.default],data:function(){return{innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:function(t){return t}}},watch:{value:{immediate:!0,handler:function(t,a){this.innerValue=t,!1===this.firstChange&&!1===this.changeFromInner&&this.valueChange(),this.firstChange=!1,this.changeFromInner=!1}}},computed:{textareaClass:function(){var t=[],a=this.border,e=this.disabled;this.shape;return"surround"===a&&(t=t.concat(["u-border","u-textarea--radius"])),"bottom"===a&&(t=t.concat(["u-border-bottom","u-textarea--no-radius"])),e&&t.push("u-textarea--disabled"),t.join(" ")},textareaStyle:function(){return uni.$u.deepMerge({},uni.$u.addStyle(this.customStyle))}},methods:{setFormatter:function(t){this.innerFormatter=t},onFocus:function(t){this.$emit("focus",t)},onBlur:function(t){this.$emit("blur",t),uni.$u.formValidate(this,"blur")},onLinechange:function(t){this.$emit("linechange",t)},onInput:function(t){var a=this,e=t.detail||{},r=e.value,o=void 0===r?"":r,i=this.formatter||this.innerFormatter,n=i(o);this.innerValue=o,this.$nextTick((function(){a.innerValue=n,a.valueChange()}))},valueChange:function(){var t=this,a=this.innerValue;this.$nextTick((function(){t.$emit("input",a),t.changeFromInner=!0,t.$emit("change",a),uni.$u.formValidate(t,"change")}))},onConfirm:function(t){this.$emit("confirm",t)},onKeyboardheightchange:function(t){this.$emit("keyboardheightchange",t)}}};a.default=i},"700e":function(t,a,e){"use strict";e.d(a,"b",(function(){return r})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){}));var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"u-textarea",class:t.textareaClass,style:[t.textareaStyle]},[e("v-uni-textarea",{staticClass:"u-textarea__field",style:{height:t.$u.addUnit(t.height)},attrs:{value:t.innerValue,placeholder:t.placeholder,"placeholder-style":t.$u.addStyle(t.placeholderStyle,"string"),"placeholder-class":t.placeholderClass,disabled:t.disabled,focus:t.focus,autoHeight:t.autoHeight,fixed:t.fixed,cursorSpacing:t.cursorSpacing,cursor:t.cursor,showConfirmBar:t.showConfirmBar,selectionStart:t.selectionStart,selectionEnd:t.selectionEnd,adjustPosition:t.adjustPosition,disableDefaultPadding:t.disableDefaultPadding,holdKeyboard:t.holdKeyboard,maxlength:t.maxlength,confirmType:t.confirmType,ignoreCompositionEvent:t.ignoreCompositionEvent},on:{focus:function(a){arguments[0]=a=t.$handleEvent(a),t.onFocus.apply(void 0,arguments)},blur:function(a){arguments[0]=a=t.$handleEvent(a),t.onBlur.apply(void 0,arguments)},linechange:function(a){arguments[0]=a=t.$handleEvent(a),t.onLinechange.apply(void 0,arguments)},input:function(a){arguments[0]=a=t.$handleEvent(a),t.onInput.apply(void 0,arguments)},confirm:function(a){arguments[0]=a=t.$handleEvent(a),t.onConfirm.apply(void 0,arguments)},keyboardheightchange:function(a){arguments[0]=a=t.$handleEvent(a),t.onKeyboardheightchange.apply(void 0,arguments)}}}),t.count?e("v-uni-text",{staticClass:"u-textarea__count",style:{"background-color":t.disabled?"transparent":"#fff"}},[t._v(t._s(t.innerValue.length)+"/"+t._s(t.maxlength))]):t._e()],1)},o=[]},"72d7":function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("64aa");var r={props:{value:{type:[String,Number],default:uni.$u.props.textarea.value},placeholder:{type:[String,Number],default:uni.$u.props.textarea.placeholder},placeholderClass:{type:String,default:uni.$u.props.input.placeholderClass},placeholderStyle:{type:[String,Object],default:uni.$u.props.input.placeholderStyle},height:{type:[String,Number],default:uni.$u.props.textarea.height},confirmType:{type:String,default:uni.$u.props.textarea.confirmType},disabled:{type:Boolean,default:uni.$u.props.textarea.disabled},count:{type:Boolean,default:uni.$u.props.textarea.count},focus:{type:Boolean,default:uni.$u.props.textarea.focus},autoHeight:{type:Boolean,default:uni.$u.props.textarea.autoHeight},fixed:{type:Boolean,default:uni.$u.props.textarea.fixed},cursorSpacing:{type:Number,default:uni.$u.props.textarea.cursorSpacing},cursor:{type:[String,Number],default:uni.$u.props.textarea.cursor},showConfirmBar:{type:Boolean,default:uni.$u.props.textarea.showConfirmBar},selectionStart:{type:Number,default:uni.$u.props.textarea.selectionStart},selectionEnd:{type:Number,default:uni.$u.props.textarea.selectionEnd},adjustPosition:{type:Boolean,default:uni.$u.props.textarea.adjustPosition},disableDefaultPadding:{type:Boolean,default:uni.$u.props.textarea.disableDefaultPadding},holdKeyboard:{type:Boolean,default:uni.$u.props.textarea.holdKeyboard},maxlength:{type:[String,Number],default:uni.$u.props.textarea.maxlength},border:{type:String,default:uni.$u.props.textarea.border},formatter:{type:[Function,null],default:uni.$u.props.textarea.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}};a.default=r},"72f8":function(t,a,e){var r=e("3c38");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=e("967d").default;o("2447bae2",r,!0,{sourceMap:!1,shadowMode:!1})},"7d91":function(t,a,e){"use strict";e.d(a,"b",(function(){return r})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){}));var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{ref:"u-subsection",staticClass:"u-subsection",class:["u-subsection--"+t.mode],style:[t.$u.addStyle(t.customStyle),t.wrapperStyle]},[e("v-uni-view",{ref:"u-subsection__bar",staticClass:"u-subsection__bar",class:["button"===t.mode&&"u-subsection--button__bar",0===t.current&&"subsection"===t.mode&&"u-subsection__bar--first",t.current>0&&t.current<t.list.length-1&&"subsection"===t.mode&&"u-subsection__bar--center",t.current===t.list.length-1&&"subsection"===t.mode&&"u-subsection__bar--last"],style:[t.barStyle]}),t._l(t.list,(function(a,r){return e("v-uni-view",{key:r,ref:"u-subsection__item--"+r,refInFor:!0,staticClass:"u-subsection__item",class:["u-subsection__item--"+r,r<t.list.length-1&&"u-subsection__item--no-border-right",0===r&&"u-subsection__item--first",r===t.list.length-1&&"u-subsection__item--last"],style:[t.itemStyle(r)],on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.clickHandler(r)}}},[e("v-uni-text",{staticClass:"u-subsection__item__text",style:[t.textStyle(r)]},[t._v(t._s(t.getText(a)))])],1)}))],2)},o=[]},8032:function(t,a,e){"use strict";var r=e("cbab"),o=e.n(r);o.a},"8fd6":function(t,a,e){"use strict";e.r(a);var r=e("7d91"),o=e("d7a9");for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(i);e("f7a4");var n=e("828b"),d=Object(n["a"])(o["default"],r["b"],r["c"],!1,null,"4a603381",null,!1,r["a"],void 0);a["default"]=d.exports},a12d:function(t,a,e){"use strict";e("6a54");var r=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var o=r(e("9b1b")),i={data:function(){return{statusBarHeight:0,navbarHeight:44,headerHeight:0,footerHeight:120,windowHeight:0,scrollViewHeight:0,safeAreaTop:0,isKeyboardShow:!1,keyboardHeight:0,platform:"",caseTypes:["国家法律","行政法规","部门规章"],caseTypeIndex:0,searchForm:{lawType:"01",fileName:"",fileDescribe:"",lssuingUnit:""},inputStyle:{height:"80rpx",fontSize:"30rpx",padding:"0 24rpx",borderColor:"#e0e0e0",borderWidth:"1rpx",borderRadius:"16rpx",borderStyle:"solid",color:"#333",backgroundColor:"#fff"}}},onLoad:function(){var t=this;this.getStatusBarHeight();var a=uni.getSystemInfoSync();this.platform=a.platform,uni.onKeyboardHeightChange((function(a){t.isKeyboardShow=a.height>0,t.keyboardHeight=a.height,t.calcScrollViewHeight()}))},onReady:function(){this.calcScrollViewHeight()},methods:{getStatusBarHeight:function(){var t=uni.getSystemInfoSync();this.statusBarHeight=t.statusBarHeight||0,this.windowHeight=t.windowHeight||0,this.headerHeight=this.statusBarHeight+this.navbarHeight,this.safeAreaTop=this.headerHeight},calcScrollViewHeight:function(){var t=uni.upx2px(this.footerHeight),a=this.windowHeight-this.headerHeight-t;this.scrollViewHeight=a},handleInputFocus:function(){},handleInputBlur:function(){},caseTypeChange:function(t){this.caseTypeIndex=t;this.searchForm.lawType=["01","02","03"][t]},resetForm:function(){this.caseTypeIndex=0,this.searchForm={lawType:"01",fileName:"",fileDescribe:"",lssuingUnit:""}},confirmSearch:function(){uni.hideKeyboard();var t=(0,o.default)({},this.searchForm);uni.navigateBack({delta:1,success:function(){uni.$emit("lawSearch",t)}})}}};a.default=i},a61d:function(t,a,e){"use strict";e.r(a);var r=e("a12d"),o=e.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return r[t]}))}(i);a["default"]=o.a},ae6b:function(t,a,e){"use strict";e.r(a);var r=e("700e"),o=e("105a");for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(i);e("8032");var n=e("828b"),d=Object(n["a"])(o["default"],r["b"],r["c"],!1,null,"1ba40ab6",null,!1,r["a"],void 0);a["default"]=d.exports},b471:function(t,a,e){"use strict";e.r(a);var r=e("c3cf"),o=e("a61d");for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(i);e("dcb0");var n=e("828b"),d=Object(n["a"])(o["default"],r["b"],r["c"],!1,null,"722602bc",null,!1,r["a"],void 0);a["default"]=d.exports},c3cf:function(t,a,e){"use strict";e.d(a,"b",(function(){return o})),e.d(a,"c",(function(){return i})),e.d(a,"a",(function(){return r}));var r={uSubsection:e("8fd6").default,uInput:e("30b1").default,uTextarea:e("ae6b").default},o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"query-search"},[e("v-uni-view",{staticClass:"header-section"},[e("custom-navbar",{attrs:{title:"查询",showBack:!0}})],1),e("v-uni-scroll-view",{staticClass:"form-section",style:{height:t.scrollViewHeight+"px",top:t.safeAreaTop+"px"},attrs:{"scroll-y":!0}},[e("v-uni-view",{staticClass:"form-content"},[e("v-uni-view",{staticClass:"form-item"},[e("v-uni-view",{staticClass:"form-label"},[t._v("法律法规类型")]),e("v-uni-view",{staticClass:"form-content"},[e("u-subsection",{attrs:{list:t.caseTypes,current:t.caseTypeIndex,activeColor:"#07ac7c",inactiveColor:"#333333",mode:"button",animation:!1,fontSize:"28rpx",height:"70rpx",buttonColor:"#f5f5f5"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.caseTypeChange.apply(void 0,arguments)}}})],1)],1),e("v-uni-view",{staticClass:"form-item"},[e("v-uni-view",{staticClass:"form-label"},[t._v("法律法规名称")]),e("v-uni-view",{staticClass:"form-content"},[e("u-input",{staticClass:"form-input",attrs:{placeholder:"请输入法律法规名称",border:"surround",clearable:!0,fontSize:"30rpx",customStyle:t.inputStyle},on:{blur:function(a){arguments[0]=a=t.$handleEvent(a),t.handleInputBlur.apply(void 0,arguments)},focus:function(a){arguments[0]=a=t.$handleEvent(a),t.handleInputFocus.apply(void 0,arguments)}},model:{value:t.searchForm.fileName,callback:function(a){t.$set(t.searchForm,"fileName",a)},expression:"searchForm.fileName"}})],1)],1),e("v-uni-view",{staticClass:"form-item"},[e("v-uni-view",{staticClass:"form-label"},[t._v("法条内容")]),e("v-uni-view",{staticClass:"form-content"},[e("u-textarea",{attrs:{placeholder:"请输入法条内容",count:!0,maxlength:"800",height:"240rpx",fontSize:"24rpx",customStyle:{backgroundColor:"#FFFFFF",borderRadius:"16rpx",borderColor:"#f0f0f0"}},model:{value:t.searchForm.fileDescribe,callback:function(a){t.$set(t.searchForm,"fileDescribe",a)},expression:"searchForm.fileDescribe"}})],1)],1),e("v-uni-view",{staticClass:"form-item"},[e("v-uni-view",{staticClass:"form-label"},[t._v("发布机关")]),e("v-uni-view",{staticClass:"form-content"},[e("u-input",{staticClass:"form-input",attrs:{placeholder:"请输入发布机关",border:"surround",clearable:!0,fontSize:"30rpx",customStyle:t.inputStyle},on:{blur:function(a){arguments[0]=a=t.$handleEvent(a),t.handleInputBlur.apply(void 0,arguments)},focus:function(a){arguments[0]=a=t.$handleEvent(a),t.handleInputFocus.apply(void 0,arguments)}},model:{value:t.searchForm.lssuingUnit,callback:function(a){t.$set(t.searchForm,"lssuingUnit",a)},expression:"searchForm.lssuingUnit"}})],1)],1),e("v-uni-view",{staticClass:"form-space"})],1)],1),e("v-uni-view",{staticClass:"footer-section"},[e("v-uni-view",{staticClass:"btn-group"},[e("v-uni-view",{staticClass:"btn btn-reset",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.resetForm.apply(void 0,arguments)}}},[e("v-uni-text",[t._v("重置选择")])],1),e("v-uni-view",{staticClass:"btn btn-confirm",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.confirmSearch.apply(void 0,arguments)}}},[e("v-uni-text",[t._v("确定")])],1)],1)],1)],1)},i=[]},cbab:function(t,a,e){var r=e("0b9e");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=e("967d").default;o("0d642aca",r,!0,{sourceMap:!1,shadowMode:!1})},d296:function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("64aa");var r={props:{list:{type:Array,default:uni.$u.props.subsection.list},current:{type:[String,Number],default:uni.$u.props.subsection.current},activeColor:{type:String,default:uni.$u.props.subsection.activeColor},inactiveColor:{type:String,default:uni.$u.props.subsection.inactiveColor},mode:{type:String,default:uni.$u.props.subsection.mode},fontSize:{type:[String,Number],default:uni.$u.props.subsection.fontSize},bold:{type:Boolean,default:uni.$u.props.subsection.bold},bgColor:{type:String,default:uni.$u.props.subsection.bgColor},keyName:{type:String,default:uni.$u.props.subsection.keyName}}};a.default=r},d7a9:function(t,a,e){"use strict";e.r(a);var r=e("013c"),o=e.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return r[t]}))}(i);a["default"]=o.a},dcb0:function(t,a,e){"use strict";var r=e("002b"),o=e.n(r);o.a},f7a4:function(t,a,e){"use strict";var r=e("72f8"),o=e.n(r);o.a},fcfa:function(t,a,e){var r=e("c86c");a=r(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-722602bc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-722602bc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-722602bc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-722602bc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-722602bc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-722602bc]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-722602bc]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-722602bc]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-722602bc]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-722602bc]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-722602bc]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-722602bc]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-722602bc]::after{border:none}.u-hover-class[data-v-722602bc]{opacity:.7}.u-primary-light[data-v-722602bc]{color:#ecf5ff}.u-warning-light[data-v-722602bc]{color:#fdf6ec}.u-success-light[data-v-722602bc]{color:#f5fff0}.u-error-light[data-v-722602bc]{color:#fef0f0}.u-info-light[data-v-722602bc]{color:#f4f4f5}.u-primary-light-bg[data-v-722602bc]{background-color:#ecf5ff}.u-warning-light-bg[data-v-722602bc]{background-color:#fdf6ec}.u-success-light-bg[data-v-722602bc]{background-color:#f5fff0}.u-error-light-bg[data-v-722602bc]{background-color:#fef0f0}.u-info-light-bg[data-v-722602bc]{background-color:#f4f4f5}.u-primary-dark[data-v-722602bc]{color:#398ade}.u-warning-dark[data-v-722602bc]{color:#f1a532}.u-success-dark[data-v-722602bc]{color:#53c21d}.u-error-dark[data-v-722602bc]{color:#e45656}.u-info-dark[data-v-722602bc]{color:#767a82}.u-primary-dark-bg[data-v-722602bc]{background-color:#398ade}.u-warning-dark-bg[data-v-722602bc]{background-color:#f1a532}.u-success-dark-bg[data-v-722602bc]{background-color:#53c21d}.u-error-dark-bg[data-v-722602bc]{background-color:#e45656}.u-info-dark-bg[data-v-722602bc]{background-color:#767a82}.u-primary-disabled[data-v-722602bc]{color:#9acafc}.u-warning-disabled[data-v-722602bc]{color:#f9d39b}.u-success-disabled[data-v-722602bc]{color:#a9e08f}.u-error-disabled[data-v-722602bc]{color:#f7b2b2}.u-info-disabled[data-v-722602bc]{color:#c4c6c9}.u-primary[data-v-722602bc]{color:#3c9cff}.u-warning[data-v-722602bc]{color:#f9ae3d}.u-success[data-v-722602bc]{color:#5ac725}.u-error[data-v-722602bc]{color:#f56c6c}.u-info[data-v-722602bc]{color:#909399}.u-primary-bg[data-v-722602bc]{background-color:#3c9cff}.u-warning-bg[data-v-722602bc]{background-color:#f9ae3d}.u-success-bg[data-v-722602bc]{background-color:#5ac725}.u-error-bg[data-v-722602bc]{background-color:#f56c6c}.u-info-bg[data-v-722602bc]{background-color:#909399}.u-main-color[data-v-722602bc]{color:#303133}.u-content-color[data-v-722602bc]{color:#606266}.u-tips-color[data-v-722602bc]{color:#909193}.u-light-color[data-v-722602bc]{color:#c0c4cc}.u-safe-area-inset-top[data-v-722602bc]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-722602bc]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-722602bc]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-722602bc]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-722602bc]{z-index:10090}uni-toast .uni-toast[data-v-722602bc]{z-index:10090}[data-v-722602bc]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-page-body[data-v-722602bc]{background-color:#f5f5f5;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}body.?%PAGE?%[data-v-722602bc]{background-color:#f5f5f5}.query-search[data-v-722602bc]{min-height:100vh;display:flex;flex-direction:column;background-color:#f5f5f5;position:relative}\r\n/* 顶部导航区域 */.header-section[data-v-722602bc]{background-color:#fff;position:fixed;top:0;left:0;right:0;z-index:101}\r\n/* 中间表单区域 */.form-section[data-v-722602bc]{position:fixed;top:0;left:0;right:0;bottom:%?120?%;\r\n  /* 底部按钮区域高度 */box-sizing:border-box;background-color:#f5f5f5;z-index:99}.form-content[data-v-722602bc]{padding:%?30?%;background-color:#fff;border-radius:%?16?%;margin-bottom:env(safe-area-inset-bottom)\r\n  /* 适配底部安全区域 */}.form-item[data-v-722602bc]{margin-bottom:%?24?%}.form-item[data-v-722602bc]:last-child{margin-bottom:0}.form-label[data-v-722602bc]{font-size:%?30?%;font-weight:400;color:#262626;margin-bottom:%?12?%;display:block}.form-content[data-v-722602bc]{width:100%}.form-input[data-v-722602bc]{height:%?80?%}\r\n/* 底部按钮区域 */.footer-section[data-v-722602bc]{position:fixed;bottom:0;left:0;right:0;background-color:#fff;z-index:100;border-top:%?1?% solid #f5f5f5;padding-bottom:env(safe-area-inset-bottom)\r\n  /* 兼容安卓底部导航栏 */}\r\n/* 按钮组样式 */.btn-group[data-v-722602bc]{display:flex;padding:%?20?% %?40?%}.btn[data-v-722602bc]{flex:1;height:%?80?%;display:flex;align-items:center;justify-content:center;border-radius:%?50?%;font-size:%?32?%;font-weight:400;transition:all .2s}.btn-reset[data-v-722602bc]{background-color:initial;color:#07ac7c;border:none;margin-right:%?40?%}.btn-reset[data-v-722602bc]:active{opacity:.8}.btn-confirm[data-v-722602bc]{background-color:#07ac7c;color:#fff}.btn-confirm[data-v-722602bc]:active{background-color:#33a085}\r\n/* 为小屏幕预留滚动空间 */.form-space[data-v-722602bc]{height:%?60?%\r\n  /* 安卓端键盘弹出时增加额外空间 */}\r\n/* 修改导航栏样式 */[data-v-722602bc] .custom-navbar{background-color:#fff!important;box-shadow:none!important}[data-v-722602bc] .navbar-left .svg-icon{color:#000!important}[data-v-722602bc] .navbar-title{color:#000!important}\r\n/* 自定义u-input样式 */[data-v-722602bc] .u-input{height:%?80?%}[data-v-722602bc] .u-input__input{height:%?80?%;font-size:%?30?%;color:#333;padding:0 %?24?%}[data-v-722602bc] .u-input--border{border-color:#e0e0e0;border-width:%?1?%;border-radius:%?16?%;height:%?80?%}[data-v-722602bc] .u-input__placeholder-style{color:#ccc;font-size:%?30?%}\r\n/* 自定义u-subsection样式 */[data-v-722602bc] .u-subsection{margin-top:%?10?%}\r\n/* 解决安卓输入框问题 */[data-v-722602bc] .u-input__content__field{display:flex;align-items:center;height:100%}',""]),t.exports=a}}]);