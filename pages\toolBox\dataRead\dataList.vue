<template>
	<view class="data-read-container">
		<!-- 自定义导航栏 -->
		<custom-navbar title="抄读助手" :showBack="true">
			<template #right>
				<!-- <view class="setting-icon">
					<u-icon name="setting" color="#333333" :size="size"></u-icon>
				</view> -->
				<!-- <view class="bluetooth-icon">
					<svg-icon name="bluetooth" size="40"></svg-icon>
				</view> -->
			</template>
		</custom-navbar>
		
		<!-- 内容区域 -->
		<view class="content-section" :style="{ height: contentHeight + 'px' }">
			<!-- 电能表信息卡片 -->
			<view class="meter-card">
				<view class="card-title">
					<view class="title-indicator"></view>
					<text>电能表信息</text>
				</view>
				
				<view class="scan-item">
					<text class="scan-label">扫描电能表条码</text>
					<view class="form-value" v-if="meterInfo.meterBarCode">
						<u-input
							v-model="meterInfo.meterBarCode"
							border="none"
							fontSize="28rpx"
							class="form-input"
						></u-input>
					</view>
					<view class="scan-button" @click="scanBarcode" v-else>
						<u-icon name="scan" color="#00BF6B" size="25"></u-icon>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">资产编号</text>
					<view class="form-value">
						<u-input
							v-model="meterInfo.meterNo"
							border="none"
							fontSize="28rpx"
							class="form-input"
						></u-input>
					</view>
				</view>
				
				<view class="form-item" @click="selectMeterAgreement">
					<text class="form-label">规约类型</text>
					<view class="form-value-with-arrow">
						<text>{{meterInfo.meterAgreementName || '请选择'}}</text>
						<view class="arrow-icon">
							<uni-icons type="right" color="#262626" size="16"></uni-icons>
						</view>
					</view>
				</view>
				<view class="form-item" @click="goDataItem">
					<text class="form-label">抄读数据项</text>
					<view class="form-value-with-arrow">
						<text>{{meterInfo.dataItems || '查看'}}</text>
						<view class="arrow-icon">
							<uni-icons type="right" color="#262626" size="16"></uni-icons>
						</view>
					</view>
				</view>
				
				<view class="form-item important">
					<view class="font"><text class="form-label">*</text>执行权限</view>
					<view class="form-value success" @click="pockertClipAuth">
						<text>{{meterInfo.permission}}</text>
					</view>
				</view>
				<view class="form-item">
					<u-button type="primary" text="执行" color="#07ac7c" @click="exectTask"></u-button>
				</view>
				<u-button type="primary" text="抄读结果" color="#07ac7c" @click="goResult"></u-button>
			</view>
			<u-loading-page :loading="loading" loading-text="执行任务中..." loading-mode="semicircle" font-size="16" bg-color="#fff" color="#666"></u-loading-page>
			<!-- 底部区域 -->
			<view class="bottom-area">
				<!-- 在这里可以添加更多内容，如操作按钮等 -->
			</view>
		</view>
	</view>
</template>

<script>
import {
	bizCode,
	url,
	getUUID,
	getCurrentTimestamp,
	port,
	} from '@/static/commonJs/util.js'

	export default {
		data() {
			return {
				contentHeight: 0,
				statusBarHeight: 0,
				navbarHeight: 44,
				size:"40rpx",
				loading:false,
				meterInfo: {
					meterBarCode: '',
					meterAddress:'',
					meterAgreement: '',
					meterNo:"",
					meterAgreementName:'',
					dataItems:'',
					permission: '获取权限'
				},
				readDataItems:null,
				meterAgreementList:[
					{
						text:"645协议09版本",
						value:"03"
					},
					{
						text:"645协议13版本",
						value:"01"
					},
					{
						text:"698版本",
						value:"04"
					},
				],
				token:null
			};
		},
		onLoad() {
			// 获取状态栏高度等信息
			this.getSystemInfo();
			// 计算内容区域高度
			this.calcContentHeight();
			  // 获取全局应用实例
			const app = getApp();
			console.log(app)
			this.init();
			// 调用全局方法
			// app.globalData.getData('页面加载完成');
		},
		onReady() {
			// onReady生命周期
		},
		mounted() {
			// window.getFun = () => {
			// 	this.exectTask()
			// }
			window.getFun = this.exectTask.bind(this)
			console.log(window)
		},
		onShow() {
			// 页面显示时可以执行的逻辑
			const readDataItems = uni.getStorageSync('readDataItems');
			console.log(readDataItems)
			if(readDataItems) {
				this.readDataItems = JSON.parse(readDataItems);
				uni.removeStorageSync('readDataItems')
			}
		},
		methods: {
			init() {
				let vm = this;
				wx.invoke("ext_DataCache_GetInfo", {
				  data: { key: '1002838.userInfo' }
				}, (res) => {
					let data = JSON.parse(res.result);
					console.log('获取用户信息',data)
					vm.token = data?.token;
				});
			},
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
				this.windowHeight = systemInfo.windowHeight || 0;
			},
			getMeterNo() {
				if(!this.meterInfo.meterBarCode) {
					return;
				}
				uni.request({
					url: `http://127.0.0.1:${port}/xczs/forward/for`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json',
						'token': this.token
					},
					data: {
						token: this.token,
						method:"PutHuaYun",
						uri:url,
						data:JSON.stringify({
							"bizCode":bizCode,
							"espFlowId":getUUID(),
							"espRsvField1":"",
							"espRsvField2":"",
							"espRsvField3":"",
							"espSign":"",
							"espTimestamp":getCurrentTimestamp(),
							"espInformation": {
								"service":"AseCommonController",
								"method":"queryAssetInfo",
								"data": {
									"barCode": this.meterInfo.meterBarCode
								},
							}
						})
					},
					success: (res) => {
						console.log('获取返回值--',res)
						 if (res && res.data.Tag === 1) {
							 const rtnData = res.data.Data.espInformation;
							 if(rtnData && rtnData.code == 200) {
							 	this.meterInfo.meterAddress = rtnData.data.commAddr;
							 	this.meterInfo.meterNo = rtnData.data.asseNo;
							 }
						 }
					},
					fail: (error) => {
						console.error('请求失败:', error);
						uni.showToast({
							title: '暂无数据',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			
			// 计算内容区域高度
			calcContentHeight() {
				// 获取系统信息
				if (!this.windowHeight) {
					this.getSystemInfo();
				}
				
				// 计算顶部高度（状态栏 + 导航栏）
				const topHeight = this.statusBarHeight + this.navbarHeight;
				
				// 设置内容区域高度为屏幕高度减去顶部高度
				this.contentHeight = this.windowHeight - topHeight;
			},
			
			// 扫描条码
			scanBarcode() {
				var vm = this;
				wx.scanQRCode({
					desc: 'scanQRCode desc',
					needResult: 1, // 默认为0，扫描结果由i国网处理，1则直接返回扫描结果，
					scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
					success: function(res) {
						// 回调
						console.log("扫一扫回调", JSON.stringify(res))
						vm.meterInfo.meterBarCode = res.resultStr;
						vm.getMeterNo();
					},
					error: function(res) {
						if (res.errMsg.indexOf('function_not_exist') > 0) {
							alert('版本过低请升级')
						}
					}
				});
			},
			selectMeterAgreement() {
				uni.showActionSheet({
					itemList: this.meterAgreementList.map(item => item.text),
					success: res => {
						const index = res.tapIndex;
						this.meterInfo.meterAgreement = this.meterAgreementList[index].value;
						this.meterInfo.meterAgreementName = this.meterAgreementList[index].text;
					}
				})
			},
			//获取权限
			pockertClipAuth() {
				var vm = this;
				uni.showLoading({
					title:"执行中...",
					mask:true
				})
				var param = {
					'isOneKeyExecute':'01',
					'taskType':'01',
					'pluginParam':""
				}
				wx.invoke("request3rdApp",{
					'scheme':"hycontroler",
					'needToken': 0,
					'param':param
				},function(res) {
					// vm.loading = false;
					uni.hideLoading();
					console.log("response:",res)
					if(res.err_msg === 'request3rdApp:ok') {
						var data = JSON.parse(res.data);
						if(data.ret === 0) {
							vm.meterInfo.permission = '获取成功'
						}else{
							vm.meterInfo.permission = data.msg;
						}
					}else{
						uni.showToast({
							title: '调用i国网外设安全接入应用失败',
							icon: 'none'
						});
					}
				})
			},
			//执行抄读
			exectTask() {
				var vm= this;
				// vm.loading = true;
				uni.showLoading({
					title:"执行中...",
					mask:true
				})
				var pluginParam = vm.buildMeterParam({
					"meterAddress": vm.meterInfo.meterAddress || '010036086701',
					"meterNo": vm.meterInfo.meterNo || '',
					"meterAgreement":vm.meterInfo.meterAgreement || '04',
					"optItems": vm.readDataItems || []
				});
				console.log(pluginParam)
				var taskData = [];
				var param = {
					'isOneKeyExecute':"01",
					'taskType':'02',
					'pluginParam': JSON.stringify(pluginParam)
				}
				wx.invoke("request3rdApp",{
					'scheme':"hycontroler",
					'needToken':0,
					'param': param
				},function(res) {
					// vm.loading = false;
					uni.hideLoading();
					console.log('抄读res', res)
					if(res.err_msg === 'request3rdApp:ok') {
						var data = JSON.parse(res.data);
						console.log('抄读返回结果',data)
						if(data.ret === 0) {
							console.log(data.data)
							this.touchao(data.data);
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
							//需跳转抄读结果页
							uni.setStorageSync('readResults',JSON.stringify(data.data))
							uni.navigateTo({
								url: `./dataResult?item=${encodeURIComponent(JSON.stringify(data.data))}`
							});
						}else{
							uni.showToast({
								title: data.msg,
								icon: 'none'
							});
						}
					}
				})
			},
			touchao(data) {
				wx.invoke("multiWindows_publish",
				  {
					channelId: 'touchao',
					message: '透抄信息',// 消息体
					data: {
					  param1: JSON.stringify(data),
					  random: Math.random(),
					  currentTime: new Date().getTime(),
					  message: '抄表信息'
					}
				  },
				  function (res) {
				  })
			},
			goResult() {
				uni.navigateTo({
					url: './dataResult'
				});
			},
			buildMeterParam(params) {
				var meterAddress =params.meterAddress;//电能表地址
				var meterAgreement = params.meterAgreement;//通讯规约01-645协议13表，03-645协议09表，04-698协议
				var meterNo = params.meterNo;//表号
				var optItems = params.optItems;//抄读项
				var offline = params.offline;//离线数据包
				var is13Meter = "01";//01是 00否
				if(meterAgreement === "03") {
					is13Meter = "00";
					meterAgreement = '01'
				}
				var buildParam = {
					module:"business",
					method:"shMeterRead",
					meterAddress: meterAddress,
					meterNo:meterNo,
					commAgreement: meterAgreement,
					is13Meter:is13Meter,
					commModel:'01',
					optItems:optItems,
					//checkFlag:"0"
				}
				if(offline) {
					buildParam.offline = btoa(offline);
					buildParam.operationMode = "02";//开始离线模式 01在线02离线03混合
				}
				
				return buildParam;
			},
			goDataItem() {
				uni.navigateTo({
				    url: './dataItem'
				});
			}
		}
	}
</script>

<style lang="scss">
.data-read-container {
	display: flex;
	flex-direction: column;
	background-color: #f8f8f8;
	height: 100vh;
	
	/* 覆盖默认导航栏样式，使其与设计图一致 */
	/deep/ .custom-navbar {
		background-color: #FFFFFF !important;
		
		.navbar-title text {
			color: #333333;
		}
	}
	
	.bluetooth-icon {
		height: 42rpx;
		margin-left: 20rpx;
	}
}

/* 内容区域 */
.content-section {
	flex: 1;
	box-sizing: border-box;
	background-color: #f8f8f8;
	padding: 30rpx;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 电能表信息卡片 */
.meter-card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	margin-bottom: 30rpx;
}
.form-input {
	/deep/ .u-input__content__field-wrapper__field {
		text-align:right !important;
	}
}
.card-title {
	display: flex;
	align-items: center;
	
	.title-indicator {
		width: 6rpx;
		height: 30rpx;
		background-color: #00BF6B;
		margin-right: 14rpx;
		border-radius: 3rpx;
	}
	
	text {
		font-size: 30rpx;
		color: #333333;
		font-weight: bold;
	}
}

.scan-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f2f2f2;
}

.scan-label {
	font-size: 28rpx;
	color: #333333;
}

.scan-button {
	padding: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.form-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f2f2f2;
	
	&:last-child {
		border-bottom: none;
	}
	
	&.important {
		.form-label {
			color: #FF5A5F;
		}
		.font {
			font-size: 24rpx;
		}
	}
}

.form-label {
	font-size: 28rpx;
	color: #333333;
}

.form-value {
	font-size: 28rpx;
	color: #666666;
	
	&.success {
		color: #00BF6B;
	}
}
.form-value-with-arrow {
	font-size:28rpx;
	color:#333;
	display:flex;
	align-items: center;
	.arrow-icon {
		margin-left:10rpx;
	}
}
/* 底部区域 */
.bottom-area {
	padding: 20rpx 0;
	flex: 1;
}
</style>
