(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-fileQuery-index"],{"0faf":function(a,t,o){var r=o("c86c");t=r(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */.u-line-1[data-v-133b81dc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:1;-webkit-box-orient:vertical!important}.u-line-2[data-v-133b81dc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:2;-webkit-box-orient:vertical!important}.u-line-3[data-v-133b81dc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:3;-webkit-box-orient:vertical!important}.u-line-4[data-v-133b81dc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:4;-webkit-box-orient:vertical!important}.u-line-5[data-v-133b81dc]{display:-webkit-box!important;overflow:hidden;text-overflow:ellipsis;word-break:break-all;-webkit-line-clamp:5;-webkit-box-orient:vertical!important}.u-border[data-v-133b81dc]{border-width:.5px!important;border-color:#dadbde!important;border-style:solid}.u-border-top[data-v-133b81dc]{border-top-width:.5px!important;border-color:#dadbde!important;border-top-style:solid}.u-border-left[data-v-133b81dc]{border-left-width:.5px!important;border-color:#dadbde!important;border-left-style:solid}.u-border-right[data-v-133b81dc]{border-right-width:.5px!important;border-color:#dadbde!important;border-right-style:solid}.u-border-bottom[data-v-133b81dc]{border-bottom-width:.5px!important;border-color:#dadbde!important;border-bottom-style:solid}.u-border-top-bottom[data-v-133b81dc]{border-top-width:.5px!important;border-bottom-width:.5px!important;border-color:#dadbde!important;border-top-style:solid;border-bottom-style:solid}.u-reset-button[data-v-133b81dc]{padding:0;background-color:initial;font-size:inherit;line-height:inherit;color:inherit}.u-reset-button[data-v-133b81dc]::after{border:none}.u-hover-class[data-v-133b81dc]{opacity:.7}.u-primary-light[data-v-133b81dc]{color:#ecf5ff}.u-warning-light[data-v-133b81dc]{color:#fdf6ec}.u-success-light[data-v-133b81dc]{color:#f5fff0}.u-error-light[data-v-133b81dc]{color:#fef0f0}.u-info-light[data-v-133b81dc]{color:#f4f4f5}.u-primary-light-bg[data-v-133b81dc]{background-color:#ecf5ff}.u-warning-light-bg[data-v-133b81dc]{background-color:#fdf6ec}.u-success-light-bg[data-v-133b81dc]{background-color:#f5fff0}.u-error-light-bg[data-v-133b81dc]{background-color:#fef0f0}.u-info-light-bg[data-v-133b81dc]{background-color:#f4f4f5}.u-primary-dark[data-v-133b81dc]{color:#398ade}.u-warning-dark[data-v-133b81dc]{color:#f1a532}.u-success-dark[data-v-133b81dc]{color:#53c21d}.u-error-dark[data-v-133b81dc]{color:#e45656}.u-info-dark[data-v-133b81dc]{color:#767a82}.u-primary-dark-bg[data-v-133b81dc]{background-color:#398ade}.u-warning-dark-bg[data-v-133b81dc]{background-color:#f1a532}.u-success-dark-bg[data-v-133b81dc]{background-color:#53c21d}.u-error-dark-bg[data-v-133b81dc]{background-color:#e45656}.u-info-dark-bg[data-v-133b81dc]{background-color:#767a82}.u-primary-disabled[data-v-133b81dc]{color:#9acafc}.u-warning-disabled[data-v-133b81dc]{color:#f9d39b}.u-success-disabled[data-v-133b81dc]{color:#a9e08f}.u-error-disabled[data-v-133b81dc]{color:#f7b2b2}.u-info-disabled[data-v-133b81dc]{color:#c4c6c9}.u-primary[data-v-133b81dc]{color:#3c9cff}.u-warning[data-v-133b81dc]{color:#f9ae3d}.u-success[data-v-133b81dc]{color:#5ac725}.u-error[data-v-133b81dc]{color:#f56c6c}.u-info[data-v-133b81dc]{color:#909399}.u-primary-bg[data-v-133b81dc]{background-color:#3c9cff}.u-warning-bg[data-v-133b81dc]{background-color:#f9ae3d}.u-success-bg[data-v-133b81dc]{background-color:#5ac725}.u-error-bg[data-v-133b81dc]{background-color:#f56c6c}.u-info-bg[data-v-133b81dc]{background-color:#909399}.u-main-color[data-v-133b81dc]{color:#303133}.u-content-color[data-v-133b81dc]{color:#606266}.u-tips-color[data-v-133b81dc]{color:#909193}.u-light-color[data-v-133b81dc]{color:#c0c4cc}.u-safe-area-inset-top[data-v-133b81dc]{padding-top:0;padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.u-safe-area-inset-right[data-v-133b81dc]{padding-right:0;padding-right:constant(safe-area-inset-right);padding-right:env(safe-area-inset-right)}.u-safe-area-inset-bottom[data-v-133b81dc]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-safe-area-inset-left[data-v-133b81dc]{padding-left:0;padding-left:constant(safe-area-inset-left);padding-left:env(safe-area-inset-left)}uni-toast[data-v-133b81dc]{z-index:10090}uni-toast .uni-toast[data-v-133b81dc]{z-index:10090}[data-v-133b81dc]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/* 主题色 */uni-page-body[data-v-133b81dc]{background-color:#f8f8f8;font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}body.?%PAGE?%[data-v-133b81dc]{background-color:#f8f8f8}.file-query-container[data-v-133b81dc]{min-height:100vh;display:flex;flex-direction:column;background:#f8f8f8}\r\n/* 内容区域 */.content-area[data-v-133b81dc]{flex:1;overflow-y:auto;background:#f8f8f8}\r\n/* 卡片网格 */.card-grid[data-v-133b81dc]{padding:%?30?% %?32?%}.card-row[data-v-133b81dc]{display:flex;justify-content:space-between;margin-bottom:%?30?%}.card-item[data-v-133b81dc]{width:48%;border-radius:%?24?%;overflow:hidden;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.03);background-color:#fff}.card-hover[data-v-133b81dc]{-webkit-transform:scale(.98);transform:scale(.98);transition:all .2s;opacity:.9}.card-inner[data-v-133b81dc]{height:%?280?%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?30?% %?20?%;background-color:#fff}\r\n/* 渐变背景 - 完全匹配原型图 */.orange-green-gradient[data-v-133b81dc]{background:linear-gradient(180deg,#f68d04,#6dbd4e)}.icon-box[data-v-133b81dc]{display:flex;align-items:center;justify-content:center;width:%?160?%;height:%?160?%;border-radius:50%;background-color:#f5f5f5;overflow:hidden;box-sizing:border-box}.icon-green[data-v-133b81dc]{background-color:#dcf7eb}.icon-orange[data-v-133b81dc]{background-color:#feead1}.icon-orange-dark[data-v-133b81dc]{background-color:#feead1}.icon-blue[data-v-133b81dc]{background-color:#e1eefb}.icon-purple[data-v-133b81dc]{background-color:#e9e1ff}.icon-cyan[data-v-133b81dc]{background-color:#d7f6f5}.icon-red[data-v-133b81dc]{background-color:#ffe5e5}.card-text[data-v-133b81dc]{font-size:%?32?%;color:#1d2129;font-weight:400;text-align:center;line-height:1.4;margin-top:%?24?%;width:100%;padding:0 %?10?%;box-sizing:border-box;word-break:break-word}.small-text[data-v-133b81dc]{font-size:%?30?%;letter-spacing:%?-0.6?%}\r\n/* 功能服务卡片样式 */.function-card[data-v-133b81dc]{background-color:#fff;border-radius:%?24?%;margin:%?30?%;padding:%?30?%;box-shadow:0 %?4?% %?24?% rgba(0,0,0,.06)}',""]),a.exports=t},"6cd3":function(a,t,o){"use strict";o("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("c223");var r={data:function(){return{iconSize:80,path:window.location.hash}},onLoad:function(){},methods:{navigateTo:function(a){uni.navigateTo({url:"/pages/fileQuery/".concat(a,"/").concat(a)})}}};t.default=r},"924a":function(a,t,o){"use strict";o.r(t);var r=o("d37b"),e=o("b312");for(var i in e)["default"].indexOf(i)<0&&function(a){o.d(t,a,(function(){return e[a]}))}(i);o("9feb");var d=o("828b"),n=Object(d["a"])(e["default"],r["b"],r["c"],!1,null,"133b81dc",null,!1,r["a"],void 0);t["default"]=n.exports},"9fd2":function(a,t,o){var r=o("0faf");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[a.i,r,""]]),r.locals&&(a.exports=r.locals);var e=o("967d").default;e("49189160",r,!0,{sourceMap:!1,shadowMode:!1})},"9feb":function(a,t,o){"use strict";var r=o("9fd2"),e=o.n(r);e.a},b312:function(a,t,o){"use strict";o.r(t);var r=o("6cd3"),e=o.n(r);for(var i in r)["default"].indexOf(i)<0&&function(a){o.d(t,a,(function(){return r[a]}))}(i);t["default"]=e.a},d37b:function(a,t,o){"use strict";o.d(t,"b",(function(){return r})),o.d(t,"c",(function(){return e})),o.d(t,"a",(function(){}));var r=function(){var a=this,t=a.$createElement,o=a._self._c||t;return o("v-uni-view",{staticClass:"file-query-container"},[o("custom-navbar",{attrs:{title:"档案查询",path:a.path}}),o("v-uni-view",{staticClass:"content-area"},[o("v-uni-view",{staticClass:"card-grid"},[o("v-uni-view",{staticClass:"card-row"},[o("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.navigateTo("userDataQuery")}}},[o("v-uni-view",{staticClass:"card-inner"},[o("v-uni-view",{staticClass:"icon-box icon-green"},[o("svg-icon",{attrs:{name:"yonghushuju",color:"#3CC792",size:a.iconSize}})],1),o("v-uni-text",{staticClass:"card-text"},[a._v("用户数据查询")])],1)],1),o("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.navigateTo("areaView")}}},[o("v-uni-view",{staticClass:"card-inner"},[o("v-uni-view",{staticClass:"icon-box icon-orange"},[o("svg-icon",{attrs:{name:"taiqushitu",color:"#F49B2C",size:a.iconSize}})],1),o("v-uni-text",{staticClass:"card-text"},[a._v("台区视图")])],1)],1)],1),o("v-uni-view",{staticClass:"card-row"},[o("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.navigateTo("keyAreaScreen")}}},[o("v-uni-view",{staticClass:"card-inner"},[o("v-uni-view",{staticClass:"icon-box icon-red"},[o("svg-icon",{attrs:{name:"zhongdiantaiqu",color:"#FF5A5F",size:a.iconSize}})],1),o("v-uni-text",{staticClass:"card-text"},[a._v("重点台区筛查")])],1)],1),o("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.navigateTo("areaVisitDetail")}}},[o("v-uni-view",{staticClass:"card-inner"},[o("v-uni-view",{staticClass:"icon-box icon-cyan"},[o("svg-icon",{attrs:{name:"gongzuoxiangqing",color:"#1CBBB4",size:a.iconSize}})],1),o("v-uni-text",{staticClass:"card-text small-text"},[a._v("台区违窃工作详情")])],1)],1)],1),o("v-uni-view",{staticClass:"card-row"},[o("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.navigateTo("jobMonitor")}}},[o("v-uni-view",{staticClass:"card-inner"},[o("v-uni-view",{staticClass:"icon-box icon-blue"},[o("svg-icon",{attrs:{name:"jiankong",color:"#4A89DC",size:a.iconSize}})],1),o("v-uni-text",{staticClass:"card-text"},[a._v("工单监控")])],1)],1),o("v-uni-view",{staticClass:"card-item",attrs:{"hover-class":"card-hover"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.navigateTo("indicatorBoard")}}},[o("v-uni-view",{staticClass:"card-inner"},[o("v-uni-view",{staticClass:"icon-box icon-purple"},[o("svg-icon",{attrs:{name:"zhibiao",color:"#8A70D6",size:a.iconSize}})],1),o("v-uni-text",{staticClass:"card-text"},[a._v("指标看板")])],1)],1)],1)],1)],1)],1)},e=[]}}]);