const _0x91c6=['getZipAppDirectory','getMinutes','taskType','invoke','HZMeterPower.setMeterTime','HZMeterPower.keepElectricity','length','HZMeterPower.switchOn','meterAddress','HZMeterSetBalaneDay.setBalaneDay','resume','明文+随机数抄读','0001','log','HZMeterAdjustPrice.adjustPrice','result','optType','gatewayType','thirdDay','valueOf','0006','event','taskData','getMonth','getInstallState','HZMeterPower.powerRelease','HZMeter.readData','orderNo','routeUrl','抄读负荷曲线','readData\x20params：','version_name','data','isSkipIDAuth','version_code','stringify','getFullYear','setMeterTime\x20params：','progress','baudRate','optData','beginDate','consNo','download','ext_appInstall_start','获取权限参数getBusiAuth：','readLoadData\x20params：','meterType','fillMoney','is_installed','安全作业返回:','setSettlementDay\x20params：','directory','meterAgreement','isSkipIdentity','closeVpn','unClockSafetyUnit\x20params：','unified','ext_Net_Download','getInstallState\x20\x20end','com.sgcc.pda.drive','readDate','test','endDate','readDatas','errorMsg','202207191911','HZMeter.readLoadData','commType','安全作业app未安装，请手动安装','抄读整点冻结数据','esamAuth\x20params：','HZMeter.readPlainDataRn','gatewayUrl','HZMeterPower.switchOff','appType','firstDay','设置结算日','0008','itemCodes','appInstall','userCode','err_msg','fillNum','secondDay','appPageName','跳转安全作业传参：'];const _0x1f70=function(_0x91c6c5,_0x1f704d){_0x91c6c5=_0x91c6c5-0x0;let _0x4ca2da=_0x91c6[_0x91c6c5];return _0x4ca2da;};const otherPackageName='com.sgcc.pda.hwhost';const readAppType=_0x1f70('0xc');const setMeterTimeAppType=_0x1f70('0x14');const switchPowerAppType='0004';const setParamsAppType='0009';const accountAppType=_0x1f70('0x4e');const adjustPriceAppType='0002';const settlementDayType='0005';const SUCCESS=0x1;const PROGRESS=0x2;const FAIL=0x0;const PMN_UNIFORM='ext_securityComp_uniform';const PMN_INIT='ext_securityComp_init';var consoleLogEnable=!![];var closeVpn;var gatewayType;var routeUrl;var gatewayUrl;var appPageName;function init(_0x4be6e2,_0x842d10){console[_0x1f70('0xd')]('JSversion',_0x1f70('0x42'));if(_0x4be6e2!=null&&_0x4be6e2!=undefined){consoleLogEnable=_0x4be6e2['logEnable'];closeVpn=_0x4be6e2[_0x1f70('0x37')]==undefined?![]:_0x4be6e2[_0x1f70('0x37')];gatewayType=_0x4be6e2[_0x1f70('0x11')]==undefined?'uniform':_0x4be6e2['gatewayType'];routeUrl=_0x4be6e2['routeUrl'];gatewayUrl=_0x4be6e2['gatewayUrl'];appPageName=_0x4be6e2[_0x1f70('0x55')]==undefined?_0x1f70('0x3c'):_0x4be6e2[_0x1f70('0x55')];}getInstallState(otherPackageName)['then'](_0x4b0f5a=>{consoleLog(JSON['stringify'](_0x4b0f5a));let _0x490026=_0x4b0f5a[_0x1f70('0x31')];let _0x2da850=_0x4b0f5a[_0x1f70('0x1f')];let _0x49241f=_0x4b0f5a[_0x1f70('0x22')];let _0xdb205a={'ret':SUCCESS};if(_0x490026){_0x842d10(_0xdb205a);}else{_0xdb205a['ret']=FAIL;_0xdb205a['errorMsg']=_0x1f70('0x45');_0x842d10(_0xdb205a);}});}function esamAuth(_0x27ab87,_0x6f608d){consoleLog(_0x1f70('0x47'),_0x27ab87);sendMsgToOtherApp(encode(spliceParam('HZDeviceEsam.esamAuth',_0x27ab87,'')),_0x6f608d);}function getAuth(_0x4c6399,_0x23f931){consoleLog('getAuth\x20params：',_0x4c6399);let _0x259ea1=!![];let _0xd469b7={};switch(_0x4c6399[_0x1f70('0x4b')]){case readAppType:_0xd469b7=getReadData(_0x4c6399['meterAddress'],_0x4c6399['meterAgreement'],_0x4c6399['taskData']);break;case switchPowerAppType:case setMeterTimeAppType:_0xd469b7=getSwitchPower(_0x4c6399[_0x1f70('0x8')],_0x4c6399['meterAgreement']);break;case setParamsAppType:_0xd469b7=getSetParams(_0x4c6399[_0x1f70('0x8')],_0x4c6399[_0x1f70('0x35')],_0x4c6399[_0x1f70('0x16')]);break;case accountAppType:_0xd469b7=getAccount(_0x4c6399['meterAddress'],_0x4c6399[_0x1f70('0x35')],_0x4c6399[_0x1f70('0x16')]);break;case adjustPriceAppType:_0xd469b7=getAdjustPrice(_0x4c6399[_0x1f70('0x8')],_0x4c6399['meterAgreement'],_0x4c6399['taskData']);break;case settlementDayType:_0xd469b7=getSettlementDay(_0x4c6399['meterAddress'],_0x4c6399[_0x1f70('0x35')],_0x4c6399[_0x1f70('0x16')]);break;default:_0x259ea1=![];let _0x2f87b1={};_0x2f87b1['ret']=FAIL;_0x2f87b1[_0x1f70('0x41')]='暂无此任务类型';_0x23f931(_0x2f87b1);break;}if(_0x259ea1){let _0x49c1fe={'appType':_0x4c6399[_0x1f70('0x4b')],'orderNo':_0x4c6399[_0x1f70('0x1b')],'meterAgreement':_0x4c6399['meterAgreement'],'taskData':_0xd469b7,'taskType':_0x4c6399[_0x1f70('0x2')]};consoleLog(_0x1f70('0x2d'),_0x49c1fe);sendMsgToOtherApp(encode(spliceParam('HZMeterAuth.getBusiAuth',_0x4c6399,_0x49c1fe)),_0x23f931);}}function readData(_0x274e01,_0x5dc3de){consoleLog(_0x1f70('0x1e'),_0x274e01);let _0x2b8791={'appType':readAppType,'orderNo':_0x274e01[_0x1f70('0x1b')],'meterAddress':_0x274e01[_0x1f70('0x8')],'meterAgreement':_0x274e01['meterAgreement'],'isSkipIdentity':![],'isSkipIDAuth':![],'meterType':_0x274e01['meterType'],'commType':_0x274e01['commType'],'baudRate':_0x274e01['baudRate'],'readDatas':_0x274e01['readDatas'],'unified':_0x274e01[_0x1f70('0x39')]==undefined?!![]:_0x274e01[_0x1f70('0x39')]};consoleLog('抄读参数',_0x2b8791);sendMsgToOtherApp(encode(spliceParam(_0x1f70('0x1a'),_0x274e01,_0x2b8791)),_0x5dc3de);}function readLoadData(_0x4c58c5,_0x2d2e3e){consoleLog(_0x1f70('0x2e'),_0x4c58c5);let _0x134937={'appType':readAppType,'orderNo':_0x4c58c5[_0x1f70('0x1b')],'meterAddress':_0x4c58c5[_0x1f70('0x8')],'meterAgreement':_0x4c58c5[_0x1f70('0x35')],'meterType':_0x4c58c5['meterType'],'commType':_0x4c58c5['commType'],'baudRate':_0x4c58c5['baudRate'],'isSkipIdentity':_0x4c58c5['isSkipIdentity'],'isSkipIDAuth':_0x4c58c5['isSkipIDAuth'],'beginDate':_0x4c58c5[_0x1f70('0x29')],'endDate':_0x4c58c5[_0x1f70('0x3f')],'failBreak':_0x4c58c5['failBreak'],'itemCodes':_0x4c58c5[_0x1f70('0x4f')],'unified':_0x4c58c5['unified']==undefined?!![]:_0x4c58c5['unified']};consoleLog(_0x1f70('0x1d'),_0x134937);sendMsgToOtherApp(encode(spliceParam(_0x1f70('0x43'),_0x4c58c5,_0x134937)),_0x2d2e3e);}function readHourFreezeData(_0x922722,_0x1e4345){consoleLog('readHourFreezeData\x20params：',_0x922722);let _0x48050f={'appType':readAppType,'orderNo':_0x922722['orderNo'],'meterAddress':_0x922722[_0x1f70('0x8')],'meterAgreement':_0x922722[_0x1f70('0x35')],'meterType':_0x922722['meterType'],'commType':_0x922722['commType'],'baudRate':_0x922722['baudRate'],'isSkipIdentity':_0x922722[_0x1f70('0x36')],'isSkipIDAuth':_0x922722[_0x1f70('0x21')],'beginDate':_0x922722[_0x1f70('0x29')],'endDate':_0x922722['endDate'],'itemCodes':_0x922722[_0x1f70('0x4f')],'unified':_0x922722[_0x1f70('0x39')]==undefined?!![]:_0x922722[_0x1f70('0x39')]};consoleLog(_0x1f70('0x46'),_0x48050f);sendMsgToOtherApp(encode(spliceParam('HZMeter.readHourFreezeData',_0x922722,_0x48050f)),_0x1e4345);}function oiRead(_0x227eaf,_0x59b99b){let _0x3d0881={'appType':readAppType,'orderNo':_0x227eaf[_0x1f70('0x1b')],'meterAddress':_0x227eaf['meterAddress'],'meterAgreement':_0x227eaf[_0x1f70('0x35')],'isSkipIdentity':_0x227eaf['isSkipIdentity'],'isSkipIDAuth':_0x227eaf[_0x1f70('0x21')],'meterType':_0x227eaf['meterType'],'commType':_0x227eaf[_0x1f70('0x44')],'baudRate':_0x227eaf[_0x1f70('0x27')],'readDatas':_0x227eaf[_0x1f70('0x40')],'unified':_0x227eaf['unified']==undefined?!![]:_0x227eaf[_0x1f70('0x39')]};consoleLog('OI抄读',_0x3d0881);sendMsgToOtherApp(encode(spliceParam('HZMeter.readOIData',_0x227eaf,_0x3d0881)),_0x59b99b);}function rnRead(_0x263188,_0x190a90){let _0x2837fa={'appType':readAppType,'orderNo':_0x263188['orderNo'],'meterAddress':_0x263188[_0x1f70('0x8')],'meterAgreement':_0x263188[_0x1f70('0x35')],'isSkipIdentity':_0x263188['isSkipIdentity'],'isSkipIDAuth':_0x263188[_0x1f70('0x21')],'meterType':_0x263188[_0x1f70('0x2f')],'commType':_0x263188[_0x1f70('0x44')],'baudRate':_0x263188['baudRate'],'dataCode':_0x263188['dataCode'],'data':_0x263188[_0x1f70('0x20')],'unified':_0x263188[_0x1f70('0x39')]==undefined?!![]:_0x263188['unified']};consoleLog(_0x1f70('0xb'),_0x2837fa);sendMsgToOtherApp(encode(spliceParam(_0x1f70('0x48'),_0x263188,_0x2837fa)),_0x190a90);}function readDataD(_0x348a62,_0x146e66){consoleLog('readDataD\x20params：',_0x348a62);let _0xe36ecb={'appType':readAppType,'orderNo':_0x348a62[_0x1f70('0x1b')],'meterAddress':_0x348a62['meterAddress'],'meterAgreement':_0x348a62[_0x1f70('0x35')],'meterType':_0x348a62[_0x1f70('0x2f')],'isSkipIdentity':_0x348a62[_0x1f70('0x36')],'isSkipIDAuth':_0x348a62['isSkipIDAuth'],'commType':_0x348a62['commType'],'baudRate':_0x348a62['baudRate'],'readDatas':_0x348a62[_0x1f70('0x40')]};consoleLog('抄读参数',_0xe36ecb);sendMsgToOtherApp(encode(spliceParam('HZMeter.readData',_0x348a62,_0xe36ecb)),_0x146e66);}function switchPower(_0x164ea1,_0x2e2e17){consoleLog('switchPower\x20params：',_0x164ea1);let _0x3e5b0a={'appType':switchPowerAppType,'orderNo':_0x164ea1['orderNo'],'isSkipIdentity':_0x164ea1[_0x1f70('0x36')],'isSkipIDAuth':_0x164ea1['isSkipIDAuth'],'meterAddress':_0x164ea1['meterAddress'],'meterAgreement':_0x164ea1[_0x1f70('0x35')],'meterType':_0x164ea1['meterType'],'commType':_0x164ea1[_0x1f70('0x44')],'baudRate':_0x164ea1['baudRate']};let _0x247030;switch(_0x164ea1[_0x1f70('0x2')]){case'03':_0x247030=_0x1f70('0x4a');break;case'04':_0x247030=_0x1f70('0x7');break;case'06':_0x247030='HZMeterPower.switchOnPermit';break;case'07':_0x247030=_0x1f70('0x19');break;case'08':_0x247030=_0x1f70('0x5');break;default:_0x247030=_0x164ea1['methodName'];break;}consoleLog('停复电参数',_0x3e5b0a);sendMsgToOtherApp(encode(spliceParam(_0x247030,_0x164ea1,_0x3e5b0a)),_0x2e2e17);}function setMeterTime(_0x2e532f,_0x3ac3c4){consoleLog(_0x1f70('0x25'),_0x2e532f);let _0x4c267c={'appType':setMeterTimeAppType,'orderNo':_0x2e532f['orderNo'],'isSkipIdentity':_0x2e532f[_0x1f70('0x36')],'isSkipIDAuth':_0x2e532f[_0x1f70('0x21')],'meterAddress':_0x2e532f[_0x1f70('0x8')],'meterAgreement':_0x2e532f['meterAgreement'],'meterType':_0x2e532f['meterType'],'commType':_0x2e532f['commType'],'baudRate':_0x2e532f[_0x1f70('0x27')]};consoleLog('校时',_0x4c267c);sendMsgToOtherApp(encode(spliceParam(_0x1f70('0x4'),_0x2e532f,_0x4c267c)),_0x3ac3c4);}function setParams(_0x3d133f,_0x3eabaa){consoleLog('setParams\x20params：',_0x3d133f);let _0x35d43d={'appType':setParamsAppType,'orderNo':_0x3d133f['orderNo'],'isSkipIdentity':_0x3d133f['isSkipIdentity'],'isSkipIDAuth':_0x3d133f[_0x1f70('0x21')],'meterAddress':_0x3d133f[_0x1f70('0x8')],'meterAgreement':_0x3d133f[_0x1f70('0x35')],'meterType':_0x3d133f['meterType'],'commType':_0x3d133f[_0x1f70('0x44')],'baudRate':_0x3d133f['baudRate'],'paramsSetData':_0x3d133f['paramsSetData']};consoleLog('统一设参',_0x35d43d);sendMsgToOtherApp(encode(spliceParam('HzMeterSetParams.setParams',_0x3d133f,_0x35d43d)),_0x3eabaa);}function fillMoney(_0x4baeb5,_0x31bcd8){consoleLog('fillMoney\x20params：',_0x4baeb5);let _0xa489b={'appType':accountAppType,'orderNo':_0x4baeb5['orderNo'],'isSkipIdentity':_0x4baeb5['isSkipIdentity'],'isSkipIDAuth':_0x4baeb5[_0x1f70('0x21')],'meterAddress':_0x4baeb5[_0x1f70('0x8')],'meterAgreement':_0x4baeb5['meterAgreement'],'meterType':_0x4baeb5['meterType'],'commType':_0x4baeb5['commType'],'baudRate':_0x4baeb5[_0x1f70('0x27')],'taskData':_0x4baeb5['taskData']};consoleLog('充值',_0xa489b);sendMsgToOtherApp(encode(spliceParam('HZMeterFill.fillMoney',_0x4baeb5,_0xa489b)),_0x31bcd8);}function openAccount(_0x5a814c,_0x1047ea){consoleLog('openAccount\x20params：',_0x5a814c);let _0x9c20d3={'appType':accountAppType,'orderNo':_0x5a814c['orderNo'],'isSkipIdentity':_0x5a814c[_0x1f70('0x36')],'isSkipIDAuth':_0x5a814c[_0x1f70('0x21')],'meterAddress':_0x5a814c[_0x1f70('0x8')],'meterAgreement':_0x5a814c[_0x1f70('0x35')],'meterType':_0x5a814c['meterType'],'commType':_0x5a814c[_0x1f70('0x44')],'baudRate':_0x5a814c[_0x1f70('0x27')],'taskData':_0x5a814c['taskData']};consoleLog('开户',_0x9c20d3);sendMsgToOtherApp(encode(spliceParam('HZMeterFill.openAccount',_0x5a814c,_0x9c20d3)),_0x1047ea);}function adjustPrice(_0x56a2da,_0x22400a){consoleLog('adjustPrice\x20params：',_0x56a2da);_0x56a2da['taskData'][_0x1f70('0x28')][_0x1f70('0x10')]=_0x56a2da[_0x1f70('0x16')]['optType'];let _0x4cb406={'appType':adjustPriceAppType,'orderNo':_0x56a2da['orderNo'],'isSkipIdentity':_0x56a2da[_0x1f70('0x36')],'isSkipIDAuth':_0x56a2da[_0x1f70('0x21')],'meterAddress':_0x56a2da['meterAddress'],'meterAgreement':_0x56a2da['meterAgreement'],'meterType':_0x56a2da['meterType'],'commType':_0x56a2da[_0x1f70('0x44')],'baudRate':_0x56a2da['baudRate'],'optType':_0x56a2da[_0x1f70('0x16')]['optType'],'taskData':_0x56a2da[_0x1f70('0x16')]};consoleLog('电价调整',_0x4cb406);sendMsgToOtherApp(encode(spliceParam(_0x1f70('0xe'),_0x56a2da,_0x4cb406)),_0x22400a);}function setSettlementDay(_0x4906fd,_0x58fa81){consoleLog(_0x1f70('0x33'),_0x4906fd);let _0x21dd9e={'appType':settlementDayType,'orderNo':_0x4906fd[_0x1f70('0x1b')],'isSkipIdentity':_0x4906fd['isSkipIdentity'],'isSkipIDAuth':_0x4906fd['isSkipIDAuth'],'meterAddress':_0x4906fd['meterAddress'],'meterAgreement':_0x4906fd['meterAgreement'],'meterType':_0x4906fd[_0x1f70('0x2f')],'commType':_0x4906fd[_0x1f70('0x44')],'baudRate':_0x4906fd[_0x1f70('0x27')],'optType':_0x4906fd[_0x1f70('0x16')][_0x1f70('0x10')],'taskData':_0x4906fd[_0x1f70('0x16')]};consoleLog(_0x1f70('0x4d'),_0x21dd9e);sendMsgToOtherApp(encode(spliceParam(_0x1f70('0x9'),_0x4906fd,_0x21dd9e)),_0x58fa81);}function getReadData(_0x5c5f7a,_0x2037b0,_0x5132f4){let _0x24e776={};_0x24e776[_0x1f70('0x8')]=_0x5c5f7a;_0x24e776['meterAgreement']=_0x2037b0;_0x24e776[_0x1f70('0x3d')]=_0x5132f4['readData'];return _0x24e776;}function getSwitchPower(_0x12a6e4,_0x15dc8b){let _0x2e4dc9={};_0x2e4dc9[_0x1f70('0x8')]=_0x12a6e4;_0x2e4dc9[_0x1f70('0x35')]=_0x15dc8b;return _0x2e4dc9;}function getSetParams(_0x516e5a,_0x1be74f,_0x2bc077){let _0x2119cf={};_0x2119cf['meterAddress']=_0x516e5a;_0x2119cf['meterAgreement']=_0x1be74f;_0x2119cf['paramsSetData']=_0x2bc077['setParams'];return _0x2119cf;}function getAccount(_0x499588,_0x19ad83,_0x5d838f){let _0xd75e7b={};_0xd75e7b[_0x1f70('0x8')]=_0x499588;_0xd75e7b['meterAgreement']=_0x19ad83;_0xd75e7b[_0x1f70('0x2a')]=_0x5d838f[_0x1f70('0x2a')];_0xd75e7b[_0x1f70('0x30')]=_0x5d838f[_0x1f70('0x30')];_0xd75e7b[_0x1f70('0x53')]=_0x5d838f['fillNum'];return _0xd75e7b;}function getAdjustPrice(_0x4ee183,_0x50c31e,_0x2a3998){let _0x22d1a0={};_0x22d1a0['meterAddress']=_0x4ee183;_0x22d1a0['meterAgreement']=_0x50c31e;_0x22d1a0['optData']=_0x2a3998['optData'];_0x22d1a0['optType']=_0x2a3998[_0x1f70('0x10')];_0x22d1a0['optData']['optType']=_0x2a3998[_0x1f70('0x10')];_0x22d1a0['optData']['isReadOnly']=![];return _0x22d1a0;}function getSettlementDay(_0x2f3f3a,_0x2b07d8,_0x1615e0){let _0x4b4150={};_0x4b4150[_0x1f70('0x8')]=_0x2f3f3a;_0x4b4150['meterAgreement']=_0x2b07d8;_0x4b4150['firstDay']=_0x1615e0[_0x1f70('0x4c')];_0x4b4150[_0x1f70('0x54')]=_0x1615e0[_0x1f70('0x54')];_0x4b4150[_0x1f70('0x12')]=_0x1615e0[_0x1f70('0x12')];return _0x4b4150;}function sendMsgToOtherApp(_0x581c4c,_0x5350a8){wx[_0x1f70('0x3')]('request3rdApp',{'scheme':'sgccpdasws','needToken':0x0,'param':_0x581c4c},function(_0x135548){consoleLog(_0x1f70('0x32'),_0x135548);if(_0x135548[_0x1f70('0x52')]=='request3rdApp:ok'){_0x5350a8(_0x135548['data']);}else{_0x5350a8(_0x135548);}});}function encode(_0x5c8809){consoleLog(_0x1f70('0x56'),_0x5c8809);let _0x4f6528=btoa(_0x5c8809);return _0x4f6528;}function decode(_0x7cc2b6){let _0x326a6a=atob(_0x7cc2b6);let _0x375861=decodeURI(_0x326a6a);return _0x375861;}function spliceParam(_0x58bc73,_0x12a2e1,_0x730100){let _0x10574c={'userCode':_0x12a2e1[_0x1f70('0x51')],'appPageName':_0x12a2e1['appPageName']==undefined?appPageName:_0x12a2e1['appPageName'],'functionCode':_0x58bc73,'paraData':_0x730100};let _0xdf7bbb={'gatewayUrl':_0x12a2e1['gatewayUrl']==undefined?gatewayUrl:_0x12a2e1[_0x1f70('0x49')],'url':_0x12a2e1['routeUrl']==undefined?routeUrl:_0x12a2e1[_0x1f70('0x1c')],'gatewayType':_0x12a2e1[_0x1f70('0x11')]==undefined?gatewayType:_0x12a2e1['gatewayType'],'closeVpn':_0x12a2e1['closeVpn']==undefined?closeVpn:_0x12a2e1['closeVpn'],'header':_0x12a2e1['header'],'body':_0x12a2e1['body'],'params':_0x10574c};return JSON[_0x1f70('0x23')](_0xdf7bbb);}function getInstallState(_0x1def66){return new Promise(function(_0x194b38,_0x1b6635){wx['invoke'](_0x1f70('0x18'),{'packageName':_0x1def66},function(_0x2ae9bb){consoleLog(_0x1f70('0x3b'),JSON[_0x1f70('0x23')](_0x2ae9bb));if(_0x2ae9bb['err_msg']!='getInstallState:ok'){consoleLog(_0x2ae9bb);_0x1b6635(_0x2ae9bb);}else{_0x194b38(_0x2ae9bb);}});});}function downApp(_0x5cbe3b){let _0x312ff8=new Date()['Format']('yyyy-M-d\x20H:m:s.S');wx['invoke']('multiWindows_subscribe',{'channelId':_0x312ff8},function(_0x379380){consoleLog(new Date()[_0x1f70('0x13')]()+'upload_subscribe',_0x379380);if(_0x379380[_0x1f70('0x20')]['event']==='progress'){consoleLog(_0x379380['data'][_0x1f70('0x26')]);_0x5cbe3b(PROGRESS,_0x379380[_0x1f70('0x20')]['progress']);}if(_0x379380[_0x1f70('0x20')][_0x1f70('0x15')]==='success'){consoleLog('文件本地路径：',_0x379380['data']['result']);_0x5cbe3b(SUCCESS,_0x379380['data'][_0x1f70('0xf')]);}if(_0x379380[_0x1f70('0x20')][_0x1f70('0x15')]==='fail'){_0x5cbe3b(FAIL,_0x379380[_0x1f70('0x20')]['msg']);}if(_0x379380[_0x1f70('0x20')]['event']===_0x1f70('0xa')){}});wx[_0x1f70('0x3')](_0x1f70('0x0'),{'type':'APP_ROOT_DIR'},function(_0x4fb114){let _0x50412e=_0x4fb114[_0x1f70('0x34')];wx[_0x1f70('0x3')](_0x1f70('0x3a'),{'data':{'cmd':_0x1f70('0x2b'),'url':'','header':{},'data':{},'id':_0x312ff8,'timeout':0xa,'fileName':'hwHost.apk','path':_0x50412e}},function(_0x42358b){});});}function installApp(_0x4e3540,_0x19b640){wx['invoke'](_0x1f70('0x2c'),{'data':{'Path':_0x4e3540,'rootPkgName':otherPackageName}},function(_0x140235){consoleLog(_0x1f70('0x50'),JSON[_0x1f70('0x23')](_0x140235));if(_0x140235['error_msg']==='ext_appInstall_start:ok'){let _0x18710f={'ret':0x1};_0x19b640(_0x18710f);}if(_0x140235['error_msg']==='ext_appInstall_start:fail'){consoleLog(_0x140235['msg']);let _0x27dd51={'ret':0x0,'errorMsg':_0x140235['msg'],'errorCode':0x0};_0x19b640(_0x27dd51);}});}function extendPrototype_Date(){Date['prototype']['Format']=function(_0xee76cd){var _0x4c9e90={'M+':this[_0x1f70('0x17')]()+0x1,'d+':this['getDate'](),'H+':this['getHours'](),'m+':this[_0x1f70('0x1')](),'s+':this['getSeconds'](),'q+':Math['floor']((this[_0x1f70('0x17')]()+0x3)/0x3),'S':this['getMilliseconds']()};if(/(y+)/[_0x1f70('0x3e')](_0xee76cd)){_0xee76cd=_0xee76cd['replace'](RegExp['$1'],(this[_0x1f70('0x24')]()+'')['substr'](0x4-RegExp['$1']['length']));}for(var _0x3f3208 in _0x4c9e90)if(new RegExp('('+_0x3f3208+')')[_0x1f70('0x3e')](_0xee76cd))_0xee76cd=_0xee76cd['replace'](RegExp['$1'],RegExp['$1'][_0x1f70('0x6')]==0x1?_0x4c9e90[_0x3f3208]:('00'+_0x4c9e90[_0x3f3208])['substr']((''+_0x4c9e90[_0x3f3208])[_0x1f70('0x6')]));return _0xee76cd;};}function unClockSafetyUnit(_0x19c329,_0x4d45b6){consoleLog(_0x1f70('0x38'),_0x19c329);sendMsgToOtherApp(encode(spliceParam('HZDeviceEsam.clearHandset',_0x19c329,'')),_0x4d45b6);}function consoleLog(_0x2eb637,_0x508ec6){if(consoleLogEnable===!![]){console[_0x1f70('0xd')](_0x2eb637,_0x508ec6);}}export default{'init':init,'esamAuth':esamAuth,'getAuth':getAuth,'readData':readData,'readLoadData':readLoadData,'readDataD':readDataD,'switchPower':switchPower,'readAppType':readAppType,'switchPowerAppType':switchPowerAppType,'setMeterTimeAppType':setMeterTimeAppType,'setParamsAppType':setParamsAppType,'setParams':setParams,'accountAppType':accountAppType,'fillMoney':fillMoney,'adjustPrice':adjustPrice,'setMeterTime':setMeterTime,'openAccount':openAccount,'setSettlementDay':setSettlementDay,'oiRead':oiRead,'rnRead':rnRead,'readLoadData':readLoadData,'readHourFreezeData':readHourFreezeData,'unClockSafetyUnit':unClockSafetyUnit};