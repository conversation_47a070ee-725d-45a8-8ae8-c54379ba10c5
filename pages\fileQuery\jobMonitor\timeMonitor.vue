<template>
	<!-- 工单时效监控 -->
	<view class="mainContainer">
		<custom-navbar title="工单时效监控"></custom-navbar>
		<view style="margin:20rpx 0 0 20rpx;font-weight: bold;">
			超时环节
		</view>
		<view class="chuliContainer">
			<view class="" style="padding: 20rpx;">
				<!-- 查询 -->
				<view class="">
					<view class="calendarContainer">
						<uni-datetime-picker v-model="queryDateStart" type="date" placeholder="开始日期" :end="queryDateEnd"
							@change="handleStartDate" style="background-color: white;">
						</uni-datetime-picker>
						<view style="margin: 0 10rpx;">-</view>
						<uni-datetime-picker v-model="queryDateEnd" type="date" placeholder="结束日期"
							:start="queryDateStart" @change="handleEndDate" style="background-color: white;">
						</uni-datetime-picker>
					</view>
					<view class="subsectionContainer">
						<u-subsection :list="subsectionlist" :current="curNow" mode="button"
							@change="sectionChange"></u-subsection>
					</view>
				</view>
				<view class="buttonContainer">
					<u-button type="primary" style="color: #fcfefd;width: 350rpx;" color="#07ac7c" text="查询"
						@click="search"></u-button>
					<u-button type="default" style="border: 1px solid lightgray;width: 350rpx;" text="重置"
						@click="reset"></u-button>
				</view>
			</view>

			<!-- 本单位时效情况 -->

			<view class="tablecontainer" style="margin-top: 20rpx;">
				<view class="tabletitle">
					<view class="titletag"></view>
					<view class="titletext">
						本单位时效情况
					</view>
				</view>
				<view class="rowcontainer" style="padding: 20rpx;">
					<view class="">
						<text>工单总数：</text>
						<text style="color: #2bb98f;">{{total}}</text>
					</view>
					<view class="" style="width: 300rpx;">
						<text>生成超时：</text>
						<text style="color: red;">{{createNum}}</text>
					</view>
				</view>
				<view class="rowcontainer" style="padding: 20rpx;">
					<view class="">
						<text>派工超时：</text>
						<text style="color: red;">{{dispatchNum}}</text>
					</view>
					<view class="" style="width: 300rpx;">
						<text>检查超时：</text>
						<text style="color: red;">{{checkNum}}</text>
					</view>
				</view>
			</view>


			<!-- 柱状图 -->
			<view class="charts-box" v-if="chartMain">
				<view class="tabletitle">
					<view class="" style="display: flex;justify-content: left;align-items: center;">
						<view class="titletag"></view>
						<view class="titletext">
							下级单位处理情况
						</view>
					</view>
					<uni-icons @click="shownextList" type="tune" size="20" color="gray" />
				</view>
				<view v-if="dataShow"
					style="height: 500rpx;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
					<img style="width: 70%;height: 80%;" src="@/static/icons/nodata.jpg" alt="nodata" />
					<view style="color: gainsboro;">
						数据为空
					</view>
				</view>
				<qiun-data-charts v-if="chartShow" type="column" :opts="opts2" :chartData="chartData2"
					:ontouch="true" />
			</view>
			<view style="height: 600rpx;overflow-y: auto;margin-top: 40rpx;" v-if="nextshow">
				<view class="tablecontainer" v-for="(item,index) in shixiaolist" :key="index">
					<view class="tabletitle" style="justify-content: space-between;">
						<view class="" style="display: flex;justify-content: left;align-items: center;">
							<view class="titletag"></view>
							<view class="titletext">
								{{item.mgtOrgName}}时效情况
							</view>
						</view>
						<uni-icons @click="shownextList" type="tune" size="20" color="gray" />
					</view>
					<view class="rowcontainer" style="padding: 20rpx;">
						<view class="">
							<text>工单总数：</text>
							<text style="color: #2bb98f;">{{item.total}}</text>
						</view>
						<view class="" style="width: 300rpx;">
							<text>生成超时：</text>
							<text style="color: red;">{{item.createNum}}</text>
						</view>
					</view>
					<view class="rowcontainer" style="padding: 20rpx;">
						<view class="">
							<text>派工超时：</text>
							<text style="color: red;">{{item.dispatchNum}}</text>
						</view>
						<view class="" style="width: 300rpx;">
							<text>检查超时：</text>
							<text style="color: red;">{{item.checkNum}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import qiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
	import utils from '@/static/commonJs/util.js'
	export default {
		components: {
			qiunDataCharts
		},
		data() {
			return {
				nextshow:false,
				chartMain: true,
				testdev: true,
				chartShow: false,
				dataShow: true,
				checkNum: "",
				dispatchNum: "",
				createNum: "",
				unCharge: "",
				unHandle: "",
				unchecked: "",
				undispatch: "",
				filed: "",
				stealType: "00",
				shixiaolist: [],
				total: "",
				stealRate: "",
				mgtOrgCode: "31102",
				queryDateStart: "2025-01-01",
				queryDateEnd: "2026-06-26",
				subsectionlist: ['全部', '窃电', '违约用电'],
				curNow: 0,
				chuliContainershow: false,
				chashiContainershow: true,
				inputdate: "2025-06-13 - 2025-06-29",
				list1: [{
						name: '查实',
					},
					{
						name: '处理',
					}
				],
				taiqunum: '',
				value: '',
				chartData2: {},
				opts: {
					color: ["#3CA272", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					legend: {
						show: false
					},
					padding: [15, 15, 15, 5],
					touchMoveLimit: 24,
					enableScroll: true,
					xAxis: {
						disableGrid: true,
						scrollShow: true,
						itemCount: 4,
					},
					yAxis: {
						data: [{
							min: 0
						}]
					},
					extra: {
						column: {
							type: "stack",
							width: 30,
							activeBgColor: "#000000",
							activeBgOpacity: 0.08,
							labelPosition: "center",
							labelShow: false,
							// linearType:"custom"
						}
					}
				},
				opts2: {
					color: ["#3CA272", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					legend: {
						show: false
					},
					padding: [15, 15, 15, 5],
					touchMoveLimit: 24,
					enableScroll: true,
					xAxis: {
						disableGrid: true,
						scrollShow: true,
						itemCount: 4,
					},
					yAxis: {
						data: [{
							min: 0
						}]
					},
					extra: {
						column: {
							type: "stack",
							width: 30,
							activeBgColor: "#000000",
							activeBgOpacity: 0.08,
							labelPosition: "center"
						}
					}
				}
			}
		},
		onLoad() {},
		onReady() {
			// this.init();
			this.search();
		},
		methods: {
			shownextList(){
				this.nextshow = !this.nextshow;
				this.chartMain = !this.chartMain;
			},
			reset() {
				this.queryDateStart = ""
				this.queryDateEnd = ""
				this.stealType = "00"
				this.curNow = 0
			},
			async init() {
				try {
					const res = await uni.request({
						// url: '/eas-master-app/interactive/handle',
						url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'token': utils.token
						},
						data: {
							method: "PutHuaYun",
							token: utils.token,
							uri: utils.url,
							data: JSON.stringify({
								"bizCode": utils.bizCode,
								"espFlowId": utils.getUUID(),
								"espRsvField1": "",
								"espRsvField2": "",
								"espRsvField3": "",
								"espSign": "",
								"espTimestamp": utils.getCurrentTimestamp(),
								"espInformation": {
									"service": "AseCommonController",
									"method": "queryUserInfoByBody",
									"data": {
										"operatorId": utils.userInfo?.nameCode,
									}
								}
							})
						}
					});
					const {
						code,
						message,
						data
					} = res.data;
					if (code === 200) {
						this.mgtOrgCode = data.mgtOrgCode;
					}
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				}
			},
			// 处理搜索
			async search() {
				if (this.testdev) {
					this.chartShow = true;
					this.dataShow = false;
					this.shixiaolist = [{
							mgtOrgName: '市区供电公司',
							total: '125',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '市南供电公司',
							total: '224',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '浦东供电公司',
							total: '205',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '崇明供电公司',
							total: '13',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '长兴供电公司',
							total: '2',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '市北供电公司',
							total: '315',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '嘉定供电公司',
							total: '35',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '奉贤供电公司',
							total: '43',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '松江供电公司',
							total: '35',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '金山供电公司',
							total: '17',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
						{
							mgtOrgName: '青浦供电公司',
							total: '23',
							createNum: '0',
							dispatchNum: '0',
							checkNum: '0'
						},
					];
					this.total = 1037;
					this.createNum = 0;
					this.dispatchNum = 0;
					this.checkNum = 0;
					let mgtOrgNameArray = ["市区", "市南", "浦东", "崇明", "长兴", "市北", "嘉定", "奉贤", "松江", "金山", "青浦"];
					let checkNumArray = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					let createNumArray = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					let dispatchNumArray = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					let shixiaodata = {
						categories: mgtOrgNameArray,
						series: [{
								name: "检查超时",
								textColor: "#FFFFFF",
								data: checkNumArray,
								// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							},
							{
								name: "生成超时",
								textColor: "#FFFFFF",
								data: createNumArray,
								// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							},
							{
								name: "派工超时",
								textColor: "#FFFFFF",
								data: dispatchNumArray,
								// data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
							}
						]
					};
					this.chartData2 = JSON.parse(JSON.stringify(shixiaodata));
				} else {
					// 调用后端登录接口
					try {
						const res = await uni.request({
							// url: '/eas-master-app/interactive/handle',
							url: `http://127.0.0.1:${utils.port}/xczs/forward/for`,
							method: 'POST',
							header: {
								'Content-Type': 'application/json',
								'token': utils.token
							},
							data: {
								method: "PutHuaYun",
								token: utils.token,
								uri: utils.url,
								data: JSON.stringify({
									"bizCode": utils.bizCode,
									"espFlowId": utils.getUUID(),
									"espRsvField1": "",
									"espRsvField2": "",
									"espRsvField3": "",
									"espSign": "",
									"espTimestamp": utils.getCurrentTimestamp(),
									"espInformation": {
										"service": "AppOrderController",
										"method": "queryTimelessStatus",
										"data": {
											"stealType": this.stealType,
											"mgtOrgCode": this.mgtOrgCode,
											"queryDateEnd": this.queryDateEnd,
											"queryDateStart": this.queryDateStart
										}
									}
								})
							}
						});
						const {
							code,
							message,
							data
						} = res.data;
						if (code === 200) {
							uni.showToast({
								title: '查询成功',
								icon: 'success'
							});
							this.chartShow = true;
							this.dataShow = false;
							this.shixiaolist = data.list;
							this.total = data.total;
							this.createNum = data.createNum;
							this.dispatchNum = data.dispatchNum;
							this.checkNum = data.checkNum;
							let mgtOrgNameArray = [];
							let checkNumArray = [];
							let createNumArray = [];
							let dispatchNumArray = [];
							data.list.forEach(item => {
								mgtOrgNameArray.push(item.mgtOrgName.substring(0, 2))
								if (item.checkNum == null) {
									checkNumArray.push(0)
								} else {
									checkNumArray.push(Number(item.checkNum))
								}
								if (item.createNum == null) {
									createNumArray.push(0)
								} else {
									createNumArray.push(Number(item.createNum))
								}
								if (item.dispatchNum == null) {
									dispatchNumArray.push(0)
								} else {
									dispatchNumArray.push(Number(item.dispatchNum))
								}
							})
							let shixiaodata = {
								categories: mgtOrgNameArray,
								series: [{
										name: "检查超时",
										textColor: "#FFFFFF",
										data: checkNumArray,
										// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									},
									{
										name: "生成超时",
										textColor: "#FFFFFF",
										data: createNumArray,
										// data: [8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									},
									{
										name: "派工超时",
										textColor: "#FFFFFF",
										data: dispatchNumArray,
										// data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
									}
								]
							};
							this.chartData2 = JSON.parse(JSON.stringify(shixiaodata));
						} else {
							this.chartShow = false;
							this.dataShow = true;
						}
					} catch (error) {
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
						console.log(error)
						this.chartShow = false;
						this.dataShow = true;
					}
				}
			},
			sectionChange(index) {
				this.curNow = index;
				if (index == 0) {
					this.stealType = "00"
				} else if (index == 1) {
					this.stealType = "01"
				} else if (index == 2) {
					this.stealType = "02"
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	* {
		margin: 0;
		padding: 0;
	}

	page {
		background-color: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	.mainContainer {
		::v-deep .u-popup__content .u-popup__content__close {
			display: none !important;
		}

		.chuliContainer {
			.calendarContainer {
				display: flex;
				align-items: center;
			}

			.subsectionContainer {
				padding: 20rpx 0;
			}

			.buttonContainer {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.charts-box {
				background-color: white;
				border-radius: 20rpx;
				padding: 20rpx;
				margin: 40rpx 20rpx;

				.tabletitle {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 10rpx 0 20rpx 10rpx;

					// margin-bottom: 40rpx;
					.titletag {
						width: 10rpx;
						height: 30rpx;
						background-color: #07ac7c;
						border-radius: 4rpx;
					}

					.titletext {
						font-weight: bold;
						margin-left: 20rpx;
					}
				}

				.chartstitle {
					font-weight: bold;
				}
			}

			.tablecontainer {
				background-color: white;
				border-radius: 20rpx;
				padding: 20rpx;
				margin: 0 20rpx 20rpx 20rpx;
				font-weight: bold;

				.tabletitle {
					.titletag {
						width: 10rpx;
						height: 30rpx;
						background-color: #07ac7c;
						border-radius: 4rpx;
					}

					.titletext {
						margin-left: 20rpx;
					}

					display: flex;
					justify-content: left;
					align-items: center;
					padding: 10rpx 0 20rpx 10rpx;
				}

				.rowcontainer {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 30rpx;
				}
			}
		}
	}
</style>